package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4252")]
   public class Restart extends MovieClip
   {
      
      public var _close_btn:透明按钮;
      
      public function Restart()
      {
         super();
         this._close_btn.addEventListener(MouseEvent.CLICK,this.onClose);
      }
      
      private function onClose(e:*) : *
      {
         navigateToURL(new URLRequest("javascript:location.reload(); "),"_self");
      }
   }
}

