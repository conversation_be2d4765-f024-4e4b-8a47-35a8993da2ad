package src
{
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol795")]
   public class Head extends MovieClip
   {
      
      public var frame:int = 1;
      
      public function Head()
      {
         super();
         addFrameScript(0,this.frame1);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         if(this.parent.parent is Player || this.parent.parent is Player2)
         {
            this.frame = this.parent.parent.headFrame;
            gotoAndStop(this.frame);
         }
         if(this.parent is Dead && this.parent.visible)
         {
            gotoAndStop(this.frame);
         }
         if(this.parent.parent is PlayerShow)
         {
            this.frame = this.parent.parent.headFrame;
            gotoAndStop(this.frame);
         }
      }
      
      private function onREMOVED_FROM_STAGE(e:*) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         var who:* = this.parent.parent;
         if(who is Player || who is Player2)
         {
            if(who.noHead)
            {
               this.visible = false;
               stop();
            }
            else
            {
               this.visible = true;
               this.frame = who.headFrame;
               gotoAndStop(this.frame);
            }
         }
         else if(who is PlayerShow)
         {
            this.frame = who.headFrame;
            gotoAndStop(this.frame);
            if(this.frame == 0)
            {
               this.visible = false;
            }
            else
            {
               this.visible = true;
            }
         }
      }
      
      internal function frame1() : *
      {
         stop();
      }
   }
}

