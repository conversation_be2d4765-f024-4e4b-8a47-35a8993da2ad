package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.media.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class ACT extends MovieClip
   {
      
      public function ACT()
      {
         super();
      }
      
      public static function Play(p:Object, e:Object, name:String = "") : *
      {
         var classRef:Class = null;
         var newMC:* = undefined;
         var i:int = 0;
         var st:String = null;
         if(name == "火眼金睛效果")
         {
            classRef = ChongWu.chongWu_Data[33].getClass(name) as Class;
            newMC = new classRef();
         }
         else if(name != "")
         {
            classRef = NewLoad.XiaoGuoData.getClass(name) as Class;
            newMC = new classRef();
         }
         else
         {
            i = int(p.data.skinArr[p.data.skinNum]);
            st = "ACT_" + i;
            classRef = NewLoad.XiaoGuoData.getClass(st) as Class;
            newMC = new classRef();
         }
         newMC.x = e.x;
         if(e is Enemy && e.id == 31)
         {
            newMC.y = 160;
         }
         else
         {
            newMC.y = e.y - 50;
         }
         Main.world.moveChild_Other.addChild(newMC);
      }
      
      public static function Play2(xx:int, e:Enemy) : *
      {
         var st:String = "ACT_" + xx;
         var classRef:* = NewLoad.XiaoGuoData.getClass(st) as Class;
         var act:ACT = new classRef();
         act.x = e.x;
         if(e.id == 31)
         {
            act.y = 160;
         }
         else
         {
            act.y = e.y - 50;
         }
         Main.world.moveChild_Other.addChild(act);
      }
   }
}

