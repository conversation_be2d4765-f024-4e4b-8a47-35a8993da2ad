package com.hotpoint.braveManIII.views.skillPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.skillCondition.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   import src.tool.*;
   
   public class SkillPanel extends MovieClip
   {
      
      public static var _instance:SkillPanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      private static var aginNum:Number = 1;
      
      private static var loadName:String = "Panel_skill_v1200.swf";
      
      public static var buy:Boolean = false;
      
      private var idArrOne:Array = ["a8","a9","a10","a11","a12","a13","a14","a15"];
      
      private var idArrTow:Array = ["b8","b9","b10","b11","b12","b13","b14","b15"];
      
      private var idArrThree:Array = ["c8","c9","c10","c11","c12","c13","c14","c15"];
      
      private var idArrFoure:Array = ["d1","d2","d3","d4","d5","d6","d7"];
      
      private var idArrFive:Array = ["d9","d10","d11","d12","d13","d14","d15","d16"];
      
      private var idArr4:Array = ["k8","k9","k10","k11","k12","k13","k14","k15"];
      
      private var p1condArr:Array = [];
      
      private var p2condArr:Array = [];
      
      private var p1finishCondArr:* = [];
      
      private var p2finishCondArr:* = [];
      
      private var p1BtnfinishCondArr:* = [];
      
      private var p2BtnfinishCondArr:* = [];
      
      private var playerStata:Boolean = false;
      
      private var PlayerDataOneArr:Array = [];
      
      private var playerTowArr:Array = [];
      
      private var bagStata:Array = [1,1];
      
      private var arrowArr:Array = [0,1];
      
      private var arrowrightNum1:* = 0;
      
      private var arrowrightNum2:* = 0;
      
      private var player1:PlayerData;
      
      private var player2:PlayerData;
      
      private var _textFormat:TextFormat;
      
      private var _textFormat1:TextFormat;
      
      private var oldSkillLevel:Number;
      
      private var oldPlayerData:PlayerData;
      
      private var oldSkill:String;
      
      private var oldPoint:Number;
      
      private var oldGold:Number;
      
      private var oldArr:Array;
      
      private var oldStataNum:Number;
      
      private var targetTypeId:String;
      
      private var aginBo:Boolean = true;
      
      private var pickNum:Number;
      
      private var targetId:uint = 0;
      
      public var p1_0:*;
      
      public var p1_1:*;
      
      public var p1_2:*;
      
      public var p1_3:*;
      
      public var p1_4:*;
      
      public var p1_5:*;
      
      public var p1_6:*;
      
      public var p1_7:*;
      
      public var p2_0:*;
      
      public var p2_1:*;
      
      public var p2_2:*;
      
      public var p2_3:*;
      
      public var p2_4:*;
      
      public var p2_5:*;
      
      public var p2_6:*;
      
      public var p2_7:*;
      
      public var t1_0:*;
      
      public var t1_1:*;
      
      public var t1_2:*;
      
      public var t1_3:*;
      
      public var t1_4:*;
      
      public var t1_5:*;
      
      public var t1_6:*;
      
      public var t1_7:*;
      
      public var t2_0:*;
      
      public var t2_1:*;
      
      public var t2_2:*;
      
      public var t2_3:*;
      
      public var t2_4:*;
      
      public var t2_5:*;
      
      public var t2_6:*;
      
      public var t2_7:*;
      
      public var b1_0:*;
      
      public var b1_1:*;
      
      public var b1_2:*;
      
      public var b1_3:*;
      
      public var b1_4:*;
      
      public var b2_0:*;
      
      public var b2_1:*;
      
      public var b2_2:*;
      
      public var b2_3:*;
      
      public var b2_4:*;
      
      public var s1_0:*;
      
      public var s1_1:*;
      
      public var s1_2:*;
      
      public var s1_3:*;
      
      public var s1_4:*;
      
      public var s1_5:*;
      
      public var s1_6:*;
      
      public var s1_7:*;
      
      public var s2_0:*;
      
      public var s2_1:*;
      
      public var s2_2:*;
      
      public var s2_3:*;
      
      public var s2_4:*;
      
      public var s2_5:*;
      
      public var s2_6:*;
      
      public var s2_7:*;
      
      public var prompt_btn:*;
      
      public var prompt_btn2:*;
      
      public var prompt_btn3:*;
      
      public var xs_mc1:*;
      
      public var xs_mc2:*;
      
      public var point1:*;
      
      public var point2:*;
      
      public var py1_SetKey:*;
      
      public var py2_SetKey:*;
      
      public var a1_7:*;
      
      public var a2_7:*;
      
      public var p2Skill_mc:*;
      
      public var mast_mc:*;
      
      public var _txt1:*;
      
      public var _txt2:*;
      
      public var _BLACK_mc:*;
      
      public var sel_p1:*;
      
      public var sel_p2:*;
      
      public function SkillPanel(pvt:PrivateClass)
      {
         super();
         this.prompt_btn.close_btn.addEventListener(MouseEvent.CLICK,this.closeMc);
         this.prompt_btn2.close_btn.addEventListener(MouseEvent.CLICK,this.closeMc);
         this.prompt_btn3.close_btn.addEventListener(MouseEvent.CLICK,this.closeMc);
         this.sel_p1.addEventListener(MouseEvent.CLICK,this.sel_Player1);
         this.sel_p2.addEventListener(MouseEvent.CLICK,this.sel_Player2);
         this._BLACK_mc.visible = false;
         this.sel_Player1();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            if(Play_Interface.interfaceX)
            {
               LoadInGame.Open(loadData);
            }
         }
      }
      
      public static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("com.hotpoint.braveManIII.views.skillPanel.SkillPanel") as Class;
         SkillPanel._instance = new classRef(new PrivateClass());
         SkillPanel._instance.addMc();
         SkillPanel._instance.initSkill();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      public static function open() : void
      {
         if(SkillPanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         Main.allClosePanel();
         Main.stopXX = true;
         SkillPanel._instance.setXY();
         Main._stage.addChild(SkillPanel._instance);
         SkillPanel._instance.updateSkill();
         SkillPanel._instance.visible = true;
         if(Main.JiNeng_mc)
         {
            Main.JiNeng_mc.visible = false;
         }
         SkillPanel._instance.sel_Player1();
      }
      
      public static function close() : void
      {
         if(SkillPanel._instance == null)
         {
            open_yn = false;
            return;
         }
         Main.stopXX = false;
         if(SkillPanel._instance != null)
         {
            if(SkillPanel._instance.visible == true)
            {
               SkillPanel._instance.visible = false;
               SkillPanel._instance.closeMc();
            }
         }
      }
      
      public static function reSkill_buy() : *
      {
         if(Boolean(_instance) && buy)
         {
            TiaoShi.txtShow("技能遗忘 = " + aginNum);
            if(aginNum == 1)
            {
               _instance.aginP1();
               Main.player_1.GetAllSkillCD();
            }
            else if(aginNum == 2)
            {
               _instance.aginP2();
               Main.player_2.GetAllSkillCD();
            }
            _instance.prompt_btn3.visible = false;
            _instance._BLACK_mc.visible = false;
            buy = false;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能点已重置，请重新学习技能");
         }
      }
      
      private function sel_Player1(e:* = null) : *
      {
         for(var i:int = 0; i < 8; i++)
         {
            this["t1_" + i].visible = this["p1_" + i].visible = this["s1_" + i].visible = true;
            this["t2_" + i].visible = this["p2_" + i].visible = this["s2_" + i].visible = false;
            if(i < 5)
            {
               this["b1_" + i].visible = true;
               this["b2_" + i].visible = false;
            }
         }
         this["point1"].visible = true;
         this["point2"].visible = false;
         this["xs_mc1"].visible = true;
         this["xs_mc2"].visible = false;
         this.sel_p1.visible = false;
         this.sel_p2.visible = true;
         if(!Main.P1P2)
         {
            this.sel_p2.visible = false;
         }
         this["py1_SetKey"].visible = true;
         this["py2_SetKey"].visible = false;
         this["a1_7"].visible = true;
         this["a2_7"].visible = false;
         aginNum = 1;
         this.plBtnfalse();
         this.initSkillFrame1();
      }
      
      private function sel_Player2(e:* = null) : *
      {
         for(var i:int = 0; i < 8; i++)
         {
            this["t1_" + i].visible = this["p1_" + i].visible = this["s1_" + i].visible = false;
            this["t2_" + i].visible = this["p2_" + i].visible = this["s2_" + i].visible = true;
            if(i < 5)
            {
               this["b1_" + i].visible = false;
               this["b2_" + i].visible = true;
            }
         }
         this["point1"].visible = false;
         this["point2"].visible = true;
         this["xs_mc1"].visible = false;
         this["xs_mc2"].visible = true;
         this.sel_p1.visible = true;
         this.sel_p2.visible = false;
         this["py1_SetKey"].visible = false;
         this["py2_SetKey"].visible = true;
         this["a1_7"].visible = false;
         this["a2_7"].visible = true;
         aginNum = 2;
         this.p2Btnfalse();
         this.initSkillFrame2();
      }
      
      public function closeMc(e:* = null) : void
      {
         this.prompt_btn.visible = false;
         this.prompt_btn2.visible = false;
         this.prompt_btn3.visible = false;
      }
      
      private function setXY() : void
      {
         var stageWidth:Number = Main._stage.stageWidth / 2;
         var stageHeight:Number = Main._stage.stageHeight / 2;
      }
      
      public function initSkill() : void
      {
         if(Main.player2 != null)
         {
            this.playerStata = true;
         }
         else
         {
            this.playerStata = false;
         }
         this.initPlayerData();
         this.addEvent();
      }
      
      private function addMc() : void
      {
         this.prompt_btn.visible = false;
         this.prompt_btn2.visible = false;
         this.prompt_btn3.visible = false;
         this._textFormat = new TextFormat();
         this._textFormat.color = 16711680;
         this._textFormat1 = new TextFormat();
         this._textFormat1.color = 16777215;
         explain = new Explain();
         this.addChild(explain);
         explain.visible = false;
         explainTow = new Explain_Tow();
         this.addChild(explainTow);
         explainTow.visible = false;
      }
      
      private function addEvent() : void
      {
         this.addEventListener(BtnEvent.DO_OVER,this.overHandle);
         this.addEventListener(BtnEvent.DO_OUT,this.outHandle);
         this.addEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         this.addEventListener(BtnEvent.DO_CLICK,this.clickHandle);
         this.addEventListener(BtnEvent.DO_CLOSE,this.closeHanle);
      }
      
      private function initPlayerData() : void
      {
         if(!this.playerStata)
         {
            this.player1 = Main.player1;
            this.PlayerDataOneArr = this.player1.getPickSkill();
            trace("PlayerDataOneArr ===> ",this.PlayerDataOneArr);
            this.mast_mc.visible = true;
            this.p2AllBtnfalse();
            this.plBtnfalse();
            this.p2TextVisible();
            this.p2StuBtnVisible();
            this.textinitp1();
            this.initSkillFrame1();
            this.p1GoldText();
         }
         else
         {
            this.player1 = Main.player1;
            this.PlayerDataOneArr = this.player1.getPickSkill();
            this.player2 = Main.player2;
            this.playerTowArr = this.player2.getPickSkill();
            trace("PlayerDataOneArr ===> ",this.PlayerDataOneArr);
            trace("PlayerDataOneArr2 ===> ",this.playerTowArr);
            this.mast_mc.visible = false;
            this.p2Btnfalse();
            this.plBtnfalse();
            this.textinitp2();
            this.textinitp1();
            this.initSkillFrame2();
            this.initSkillFrame1();
            this.p2GoldText();
            this.p1GoldText();
         }
      }
      
      public function updateSkill() : void
      {
         if(Main.player_2 != null)
         {
            this.p2Btnfalse();
            this.p2GoldText();
            this.initSkillFrame2();
         }
         this.plBtnfalse();
         this.p1GoldText();
         this.initSkillFrame1();
      }
      
      private function initSkillFrame1() : void
      {
         var arr:* = [this.idArrOne,this.idArrTow,this.idArrThree,this.idArr4];
         for(var i:uint = 0; i < this.PlayerDataOneArr.length; i++)
         {
            if(this.PlayerDataOneArr[i])
            {
               this.dotext("t1_",arr[i],this.player1);
               this.cionFrame("p1_",arr[i]);
               this.doCond(this.player1,arr[i],this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,i + 1);
               this.stuGoto(this.player1,"s1_",arr[i],this.p1BtnfinishCondArr);
               this.cionVisible(this.player1,"p1_",arr[i]);
               this.bagStata[0] = i + 1;
               this.xs_mc1.gotoAndStop(i + 1);
               return;
            }
         }
      }
      
      private function initSkillFrame2() : void
      {
         var arr:* = [this.idArrOne,this.idArrTow,this.idArrThree,this.idArr4];
         for(var i:uint = 0; i < this.playerTowArr.length; i++)
         {
            if(this.playerTowArr[i])
            {
               this.dotext("t2_",arr[i],this.player2);
               this.cionFrame("p2_",arr[i]);
               this.doCond(this.player2,arr[i],this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,i + 1);
               this.stuGoto(this.player2,"s2_",arr[i],this.p2BtnfinishCondArr);
               this.cionVisible(this.player2,"p2_",arr[i]);
               this.bagStata[1] = i + 1;
               this.xs_mc2.gotoAndStop(i + 1);
               return;
            }
         }
      }
      
      private function textinitp1() : void
      {
         for(var i:uint = 0; i < 8; i++)
         {
            this["t1_" + i].text = "未学习";
            this["p1_" + i].gotoAndStop(1);
            this.p1condArr[i] = -1;
         }
      }
      
      private function textinitp2() : void
      {
         for(var i:uint = 0; i < 8; i++)
         {
            this["t2_" + i].text = "未学习";
            this["p2_" + i].gotoAndStop(1);
            this.p2condArr[i] = -1;
         }
      }
      
      private function clickHandle(e:BtnEvent) : void
      {
         var mc:MovieClip = e.target as MovieClip;
         var targetMcName:* = mc.name;
         var nameId:String = mc.parent.name.substr(1,1);
         var id:String = mc.parent.name.substr(3,1);
         var aginId:String = mc.name.substr(3,1);
         var aginNameId:String = mc.name.substr(1,1);
         trace("clickHandle??",targetMcName,nameId,id,aginId,aginNameId,this.bagStata[0]);
         aginNum = int(aginNameId);
         if(targetMcName == "py1_SetKey")
         {
            SetKeyPanel.open();
         }
         if(targetMcName == "py2_SetKey")
         {
            SetKeyPanel.open(2);
         }
         if(nameId == "1")
         {
            if(this.prompt_btn.visible == false)
            {
               switch(this.bagStata[0])
               {
                  case 1:
                     this.addSkill(this.player1,this.idArrOne,1,id);
                     break;
                  case 2:
                     this.addSkill(this.player1,this.idArrTow,2,id);
                     break;
                  case 3:
                     this.addSkill(this.player1,this.idArrThree,3,id);
                     break;
                  case 4:
                     this.addSkill(this.player1,this.idArr4,4,id);
                     break;
                  case 5:
                     this.addSkill(this.player1,this.idArrFoure,5,id);
                     break;
                  case 7:
               }
            }
         }
         if(nameId == "2")
         {
            if(this.prompt_btn.visible == false)
            {
               switch(this.bagStata[1])
               {
                  case 1:
                     this.addSkill(this.player2,this.idArrOne,1,id);
                     break;
                  case 2:
                     this.addSkill(this.player2,this.idArrTow,2,id);
                     break;
                  case 3:
                     this.addSkill(this.player2,this.idArrThree,3,id);
                     break;
                  case 4:
                     this.addSkill(this.player2,this.idArr4,4,id);
                     break;
                  case 5:
                     this.addSkill(this.player2,this.idArrFoure,5,id);
               }
            }
         }
         if(aginNameId == "1")
         {
            if(this.anginOk1())
            {
               if(this.player1.getBag().isHaveOtherobj(63101))
               {
                  aginNum = 1;
                  this.aginBasic();
               }
               else
               {
                  this.prompt_btn2.visible = true;
               }
            }
            else
            {
               trace("无法重置");
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"您未消耗技能点，无法重置");
            }
         }
         if(aginNameId == "2")
         {
            if(this.anginOk2())
            {
               if(this.player2.getBag().isHaveOtherobj(63101))
               {
                  aginNum = 2;
                  this.aginBasic();
               }
               else
               {
                  trace("没有道具");
                  this.prompt_btn2.visible = true;
               }
            }
            else
            {
               trace("没加过点无法重置");
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"您未消耗技能点，无法重置");
            }
         }
      }
      
      public function aginBasic() : void
      {
         this.prompt_btn3.visible = true;
      }
      
      public function aginP1() : void
      {
         trace("重置P1");
         var oldAllGod:uint = this.aginGold(this.player1,this.idArrOne) + this.aginGold(this.player1,this.idArrTow) + this.aginGold(this.player1,this.idArrThree) + this.aginGold(this.player1,this.idArrFoure) + this.aginGold(this.player1,this.idArr4);
         if(oldAllGod >= 2934)
         {
            oldAllGod -= 2934;
         }
         trace("返回金币all:" + oldAllGod);
         this.player1.addGold(oldAllGod);
         var aginPointXX:* = this.aginPoint(this.player1);
         TiaoShi.txtShow("返回技能点:" + aginPointXX);
         this.player1.addPoint(aginPointXX);
         this.p1GoldText();
         switch(this.bagStata[0])
         {
            case 1:
               this.dotext("t1_",this.idArrOne,this.player1);
               this.doCond(this.player1,this.idArrOne,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,1);
               this.cionVisible(this.player1,"p1_",this.idArrOne);
               this.stuGoto(this.player1,"s1_",this.idArrOne,this.p1BtnfinishCondArr);
               break;
            case 2:
               this.dotext("t1_",this.idArrTow,this.player1);
               this.doCond(this.player1,this.idArrTow,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,2);
               this.cionVisible(this.player1,"p1_",this.idArrTow);
               this.stuGoto(this.player1,"s1_",this.idArrTow,this.p1BtnfinishCondArr);
               break;
            case 3:
               this.dotext("t1_",this.idArrThree,this.player1);
               this.doCond(this.player1,this.idArrThree,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,3);
               this.cionVisible(this.player1,"p1_",this.idArrThree);
               this.stuGoto(this.player1,"s1_",this.idArrThree,this.p1BtnfinishCondArr);
               break;
            case 4:
               this.dotext("t1_",this.idArr4,this.player1);
               this.doCond(this.player1,this.idArr4,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,4);
               this.cionVisible(this.player1,"p1_",this.idArr4);
               this.stuGoto(this.player1,"s1_",this.idArr4,this.p1BtnfinishCondArr);
               this.p1tlStu();
               break;
            case 5:
               this.dotext("t1_",this.idArrFoure,this.player1);
               this.doCond(this.player1,this.idArrFoure,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,5);
               this.cionVisible(this.player1,"p1_",this.idArrFoure);
               this.stuGoto(this.player1,"s1_",this.idArrFoure,this.p1BtnfinishCondArr);
               this.p1tlStu();
         }
      }
      
      public function aginP2() : void
      {
         oldAllGod = this.aginGold(this.player2,this.idArrOne) + this.aginGold(this.player2,this.idArrTow) + this.aginGold(this.player2,this.idArrThree) + this.aginGold(this.player2,this.idArrFoure) + this.aginGold(this.player2,this.idArr4);
         if(oldAllGod >= 2934)
         {
            oldAllGod -= 2934;
         }
         trace("返回金币all:" + oldAllGod);
         this.player2.addGold(oldAllGod);
         var aginPointXX:* = this.aginPoint(this.player2);
         trace("返回技能点:" + aginPointXX);
         this.player2.addPoint(aginPointXX);
         this.p2GoldText();
         switch(this.bagStata[1])
         {
            case 1:
               this.dotext("t2_",this.idArrOne,this.player2);
               this.doCond(this.player2,this.idArrOne,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,1);
               this.cionVisible(this.player2,"p2_",this.idArrOne);
               this.stuGoto(this.player2,"s2_",this.idArrOne,this.p2BtnfinishCondArr);
               break;
            case 2:
               this.dotext("t2_",this.idArrTow,this.player2);
               this.doCond(this.player2,this.idArrTow,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,2);
               this.cionVisible(this.player2,"p2_",this.idArrTow);
               this.stuGoto(this.player2,"s2_",this.idArrTow,this.p2BtnfinishCondArr);
               break;
            case 3:
               this.dotext("t2_",this.idArrThree,this.player2);
               this.doCond(this.player2,this.idArrThree,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,3);
               this.cionVisible(this.player2,"p2_",this.idArrThree);
               this.stuGoto(this.player2,"s2_",this.idArrThree,this.p2BtnfinishCondArr);
               break;
            case 4:
               this.dotext("t2_",this.idArr4,this.player2);
               this.doCond(this.player2,this.idArr4,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,4);
               this.cionVisible(this.player2,"p2_",this.idArr4);
               this.stuGoto(this.player2,"s2_",this.idArr4,this.p2BtnfinishCondArr);
               this.p2tlStu();
               break;
            case 5:
               this.dotext("t2_",this.idArrFoure,this.player2);
               this.doCond(this.player2,this.idArrFoure,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,5);
               this.cionVisible(this.player2,"p2_",this.idArrFoure);
               this.stuGoto(this.player2,"s2_",this.idArrFoure,this.p2BtnfinishCondArr);
               this.p2tlStu();
         }
      }
      
      private function anginOk1() : Boolean
      {
         for(var i:uint = 0; i < 8; i++)
         {
            if(this.player1.getSkillLevel(this.idArrOne[i]) > 0)
            {
               return true;
            }
            if(this.player1.getSkillLevel(this.idArrTow[i]) > 0)
            {
               return true;
            }
            if(this.player1.getSkillLevel(this.idArrThree[i]) > 0)
            {
               return true;
            }
            if(this.player1.getSkillLevel(this.idArrFoure[i]) > 0)
            {
               return true;
            }
            if(this.player1.getSkillLevel(this.idArr4[i]) > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      private function anginOk2() : Boolean
      {
         for(var i:uint = 0; i < 8; i++)
         {
            if(this.player2.getSkillLevel(this.idArrOne[i]) > 0)
            {
               return true;
            }
            if(this.player2.getSkillLevel(this.idArrTow[i]) > 0)
            {
               return true;
            }
            if(this.player2.getSkillLevel(this.idArrThree[i]) > 0)
            {
               return true;
            }
            if(this.player2.getSkillLevel(this.idArrFoure[i]) > 0)
            {
               return true;
            }
            if(this.player2.getSkillLevel(this.idArr4[i]) > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      private function aginPoint(player:PlayerData) : Number
      {
         var allP:Number = NaN;
         var p1:Number = 0;
         var p2:Number = 0;
         var p3:Number = 0;
         var p4:Number = 0;
         var p5:Number = 0;
         for(var i:uint = 0; i < 8; i++)
         {
            if(Boolean(this.idArrOne[i]) && player.getSkillLevel(this.idArrOne[i]) != -1)
            {
               p1 += Number(player.getSkillLevel(this.idArrOne[i])) * SkillConditionFactory.getPoints(this.idArrOne[i]);
            }
            if(Boolean(this.idArrTow[i]) && player.getSkillLevel(this.idArrTow[i]) != -1)
            {
               p2 += Number(player.getSkillLevel(this.idArrTow[i])) * SkillConditionFactory.getPoints(this.idArrTow[i]);
            }
            if(Boolean(this.idArrThree[i]) && player.getSkillLevel(this.idArrThree[i]) != -1)
            {
               p3 += Number(player.getSkillLevel(this.idArrThree[i])) * SkillConditionFactory.getPoints(this.idArrThree[i]);
            }
            if(Boolean(this.idArrFoure[i]) && player.getSkillLevel(this.idArrFoure[i]) != -1)
            {
               p4 += Number(player.getSkillLevel(this.idArrFoure[i])) * SkillConditionFactory.getPoints(this.idArrFoure[i]);
            }
            if(Boolean(this.idArr4[i]) && player.getSkillLevel(this.idArr4[i]) != -1)
            {
               p5 += Number(player.getSkillLevel(this.idArr4[i])) * SkillConditionFactory.getPoints(this.idArr4[i]);
            }
         }
         if(p1 >= 3)
         {
            p1 -= 3;
         }
         if(p2 >= 3)
         {
            p2 -= 3;
         }
         if(p3 >= 3)
         {
            p3 -= 3;
         }
         if(p4 >= 7)
         {
            p4 -= 7;
         }
         if(p5 >= 3)
         {
            p5 -= 3;
         }
         allP = p1 + p2 + p3 + p4 + p5;
         trace("四个职业初始技能1级和通用第一个技能不重置 >>>>>>>>>" + allP);
         player.initSkillLevel();
         return allP;
      }
      
      private function aginGold(player:PlayerData, idArr:Array) : Number
      {
         var nowLevel:uint = 0;
         var i:uint = 0;
         var gold:Number = 0;
         for(var j:uint = 0; j < 8; j++)
         {
            if(idArr[j])
            {
               nowLevel = player.getSkillLevel(idArr[j]);
               if(nowLevel != -1)
               {
                  for(i = 1; i <= nowLevel; i++)
                  {
                     gold += SkillConditionFactory.getGold(idArr[j],i);
                  }
               }
            }
         }
         TiaoShi.txtShow("返回金币:" + gold);
         return gold;
      }
      
      private function addSkill(player:PlayerData, myArr:Array, stataNum:Number, id:String) : void
      {
         var skillLevel:Number = NaN;
         var gold:Number = NaN;
         var point:Number = NaN;
         var goldStr:String = null;
         var pointStr:String = null;
         if(player.getSkillLevel(myArr[id]) < SkillFactory.getSkillByTypeIAndLevel(myArr[id]).getskillLevelUp())
         {
            skillLevel = player.getSkillLevel(myArr[id]) + 1;
            gold = Number(SkillConditionFactory.getGold(myArr[id],skillLevel));
            point = Number(SkillConditionFactory.getPoints(myArr[id],skillLevel));
            trace("要花的技能点_________________" + point);
            this.oldPlayerData = player;
            this.oldPoint = point;
            this.oldGold = gold;
            this.oldStataNum = stataNum;
            this.targetTypeId = myArr[id];
            this.oldArr = myArr;
            this.prompt_btn.visible = true;
            this.prompt_btn.gotoAndStop(1);
            goldStr = gold.toString();
            pointStr = point.toString();
            this.mban(goldStr,pointStr);
         }
      }
      
      private function mban(goldStr:String, pointStr:String) : void
      {
         if(this.prompt_btn)
         {
            this.prompt_btn._txt1.text = goldStr;
            this.prompt_btn._txt2.text = pointStr;
         }
      }
      
      private function closeHanle(e:BtnEvent) : void
      {
         this.prompt_btn.visible = false;
         this.prompt_btn2.visible = false;
         if(e.target.id == 1)
         {
            if(this.oldPlayerData.getPoints() >= this.oldPoint)
            {
               if(this.oldPlayerData.getGold() >= this.oldGold)
               {
                  this.oldPlayerData.addSkillLevel(this.targetTypeId);
                  this.oldPlayerData.delPoints(this.oldPoint);
                  this.oldPlayerData.payGold(this.oldGold);
                  if(this.oldPlayerData == this.player1)
                  {
                     this.p1GoldText();
                     Main.player_1.GetAllSkillCD();
                     Main.player_1.LoadPlayerLvData();
                     if(this.oldStataNum < 6)
                     {
                        this.dotext("t1_",this.oldArr,this.player1);
                        this.doCond(this.player1,this.oldArr,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,this.oldStataNum);
                        this.cionVisible(this.player1,"p1_",this.oldArr);
                        this.stuGoto(this.player1,"s1_",this.oldArr,this.p1BtnfinishCondArr);
                        if(this.oldStataNum == 5)
                        {
                           this.p1tlStu();
                        }
                     }
                     else
                     {
                        this.dotext("t1_",this.oldArr,this.player1);
                        this.doCond(this.player1,this.oldArr,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,this.oldStataNum);
                        this.cionVisible(this.player1,"p1_",this.oldArr);
                        this.stuGoto(this.player1,"s1_",this.oldArr,this.p1BtnfinishCondArr);
                        this.p1tlStu();
                     }
                  }
                  if(this.oldPlayerData == this.player2)
                  {
                     this.p2GoldText();
                     Main.player_2.GetAllSkillCD();
                     Main.player_2.LoadPlayerLvData();
                     if(this.oldStataNum < 6)
                     {
                        this.dotext("t2_",this.oldArr,this.player2);
                        this.doCond(this.player2,this.oldArr,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,this.oldStataNum);
                        this.cionVisible(this.player2,"p2_",this.oldArr);
                        this.stuGoto(this.player2,"s2_",this.oldArr,this.p2BtnfinishCondArr);
                        if(this.oldStataNum == 5)
                        {
                           this.p2tlStu();
                        }
                     }
                     else
                     {
                        this.dotext("t2_",this.oldArr,this.player2);
                        this.doCond(this.player2,this.oldArr,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,this.oldStataNum);
                        this.cionVisible(this.player2,"p2_",this.oldArr);
                        this.stuGoto(this.player2,"s2_",this.oldArr,this.p2BtnfinishCondArr);
                        this.p2tlStu();
                     }
                  }
               }
               else
               {
                  trace("金币不足");
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
               }
            }
            else
            {
               trace("技能点数不足");
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能点数不足");
            }
         }
         if(e.target.id == 3)
         {
            TiaoShi.txtShow("请到商城 点券 ?= " + Shop4399.moneyAll.getValue());
            trace("请到商城");
            if(Shop4399.moneyAll.getValue() < 45)
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
               return;
            }
            this._BLACK_mc.visible = true;
            Api_4399_All.BuyObj(158);
            buy = true;
            TiaoShi.txtShow("请到商城XXXXXX 技能遗忘");
         }
         if(e.target.id == 4)
         {
            close();
         }
         if(e.target.id == 5)
         {
            this.reSkill();
            Main.Save();
         }
         if(e.target.id == 6)
         {
            this.prompt_btn3.visible = false;
         }
      }
      
      private function reSkill() : *
      {
         if(aginNum == 1)
         {
            this.aginP1();
            this.player1.getBag().getAndUseOtherobj(63101);
            Main.player_1.GetAllSkillCD();
         }
         else if(aginNum == 2)
         {
            this.aginP2();
            this.player2.getBag().getAndUseOtherobj(63101);
            Main.player_2.GetAllSkillCD();
         }
         this.prompt_btn3.visible = false;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能点已重置，请重新学习技能");
      }
      
      private function p1GoldText() : void
      {
         this.point1.text = String(this.player1.getPoints());
      }
      
      private function p2GoldText() : void
      {
         this.point2.text = String(this.player2.getPoints());
      }
      
      public function cionVisible(player:PlayerData, pstring:String, myarr:Array) : void
      {
         for(var i:uint = 0; i < 8; i++)
         {
            if(myarr[i])
            {
               if(player.getSkillLevel(myarr[i]) == 0)
               {
                  this[pstring + i].cion_mast.gotoAndStop(1);
               }
               else
               {
                  this[pstring + i].cion_mast.gotoAndStop(2);
               }
            }
         }
      }
      
      public function p2StuBtnVisible() : void
      {
         for(var i:uint = 0; i < 8; i++)
         {
            this["s2_" + i].gotoAndStop(2);
         }
      }
      
      private function stuGoto(player:PlayerData, stuStr:String, myArr:Array, BtnfinishCondArr:Array) : void
      {
         var lv1:* = undefined;
         var lv2:* = undefined;
         for(var i:uint = 0; i < 8; i++)
         {
            if(myArr[i])
            {
               lv1 = player.getSkillLevel(myArr[i]);
               lv2 = SkillFactory.getSkillByTypeIAndLevel(myArr[i]).getskillLevelUp();
               if(lv1 < lv2)
               {
                  if(BtnfinishCondArr[i])
                  {
                     this[stuStr + i].gotoAndStop(1);
                  }
                  else
                  {
                     this[stuStr + i].gotoAndStop(2);
                  }
               }
               else
               {
                  this[stuStr + i].gotoAndStop(2);
               }
               this[stuStr + i].visible = true;
            }
            else
            {
               this[stuStr + i].gotoAndStop(2);
               this[stuStr + i].visible = false;
            }
         }
      }
      
      private function doCond(player:PlayerData, idArr:Array, condArr:Array, finishCondArr:Array, finishBtnArr:Array, bagStataNum:Number) : void
      {
         for(var i:uint = 0; i < 8; i++)
         {
            if(idArr[i])
            {
               if(player.getSkillLevel(idArr[i]) < SkillFactory.getSkillByTypeIAndLevel(idArr[i]).getskillLevelUp())
               {
                  condArr[i] = SkillConditionFactory.getCondition(idArr[i],player.getSkillLevel(idArr[i]) + 1);
               }
               else
               {
                  condArr[i] = SkillConditionFactory.getCondition(idArr[i],player.getSkillLevel(idArr[i]));
               }
               finishCondArr[i] = this.contrast(player,condArr[i],bagStataNum - 1);
               finishBtnArr[i] = this.contrast(player,condArr[i],bagStataNum - 1,"btn");
            }
         }
      }
      
      private function p2TextVisible() : void
      {
         for(var i:uint = 0; i < 8; i++)
         {
            this["t2_" + i].text = "";
         }
      }
      
      private function dotext(tmc:String, idArr:Array, player:PlayerData) : *
      {
         for(var i:uint = 0; i < 8; i++)
         {
            if(idArr[i])
            {
               if(player.getSkillLevel(idArr[i]) == 0)
               {
                  this[tmc + i].text = "未学习";
               }
               else
               {
                  this[tmc + i].text = "LV." + player.getSkillLevel(idArr[i]).toString();
                  if(player.getSkillLevel(idArr[i]) == SkillFactory.getSkillByTypeIAndLevel(idArr[i]).getskillLevelUp())
                  {
                     this[tmc + i].setTextFormat(this._textFormat);
                  }
               }
            }
            else
            {
               this[tmc + i].text = "";
            }
         }
      }
      
      private function cionFrame(p_mc:String, idArr:Array) : void
      {
         var num:* = undefined;
         for(var i:uint = 0; i < 8; i++)
         {
            if(idArr[i])
            {
               num = SkillFactory.getSkillByTypeIAndLevel(idArr[i]).getFrame();
               this[p_mc + i].gotoAndStop(num);
               this[p_mc + i].visible = true;
            }
            else
            {
               this[p_mc + i].gotoAndStop(num);
               this[p_mc + i].visible = false;
            }
         }
      }
      
      private function contrast(player:PlayerData, skillArr:Array, type:Number, allorBtn:String = "all") : Boolean
      {
         var playerLevel:Number = player.getLevel();
         var points:Number = player.getPoints();
         var gold:Number = player.getGold();
         var rebirth:Boolean = player.isRebirth();
         var transfer:Boolean = player.isTransfer(type);
         var sureArr:Array = [];
         if(allorBtn == "all")
         {
            if(playerLevel >= skillArr[0] && points >= skillArr[1] && gold >= skillArr[2])
            {
               sureArr[0] = true;
            }
            else
            {
               sureArr[0] = false;
            }
         }
         if(allorBtn == "btn")
         {
            if(playerLevel >= skillArr[0])
            {
               sureArr[0] = true;
            }
            else
            {
               sureArr[0] = false;
            }
         }
         sureArr[1] = true;
         if(skillArr[4] != false)
         {
            if(transfer == true)
            {
               sureArr[2] = true;
            }
            else
            {
               sureArr[2] = false;
            }
         }
         else
         {
            sureArr[2] = true;
         }
         if(skillArr[5] != "null")
         {
            if(player.getSkillLevel(skillArr[5]) >= skillArr[6])
            {
               sureArr[3] = true;
            }
            else
            {
               sureArr[3] = false;
            }
         }
         else
         {
            sureArr[3] = true;
         }
         if(Boolean(sureArr[0]) && Boolean(sureArr[1]) && Boolean(sureArr[2]) && Boolean(sureArr[3]))
         {
            return true;
         }
         return false;
      }
      
      private function changeHandle(e:BtnEvent) : void
      {
         var mc:MovieClip = e.target as MovieClip;
         var nameId:String = mc.name.substr(1,1);
         var idXX:* = int(mc.name.substring(mc.name.lastIndexOf("_") + 1,mc.name.length)) + 1;
         trace("按钮灰化:",nameId,idXX);
         if(nameId == "1")
         {
            this.bagStata[0] = idXX;
            if(idXX == 1)
            {
               this.b1_1.isClick = false;
               this.b1_2.isClick = false;
               this.b1_3.isClick = false;
               this.arrowrightNum1 = 0;
               this.xs_mc1.gotoAndStop(1);
               this.cionFrame("p1_",this.idArrOne);
               this.dotext("t1_",this.idArrOne,this.player1);
               this.doCond(this.player1,this.idArrOne,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,idXX);
               this.cionVisible(this.player1,"p1_",this.idArrOne);
               this.stuGoto(this.player1,"s1_",this.idArrOne,this.p1BtnfinishCondArr);
            }
            if(idXX == 2)
            {
               this.b1_0.isClick = false;
               this.b1_2.isClick = false;
               this.b1_3.isClick = false;
               this.arrowrightNum1 = 0;
               this.xs_mc1.gotoAndStop(2);
               this.cionFrame("p1_",this.idArrTow);
               this.dotext("t1_",this.idArrTow,this.player1);
               this.doCond(this.player1,this.idArrTow,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,idXX);
               this.cionVisible(this.player1,"p1_",this.idArrTow);
               this.stuGoto(this.player1,"s1_",this.idArrTow,this.p1BtnfinishCondArr);
            }
            if(idXX == 3)
            {
               this.b1_0.isClick = false;
               this.b1_1.isClick = false;
               this.b1_3.isClick = false;
               this.arrowrightNum1 = 0;
               this.xs_mc1.gotoAndStop(3);
               this.cionFrame("p1_",this.idArrThree);
               this.dotext("t1_",this.idArrThree,this.player1);
               this.doCond(this.player1,this.idArrThree,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,idXX);
               this.cionVisible(this.player1,"p1_",this.idArrThree);
               this.stuGoto(this.player1,"s1_",this.idArrThree,this.p1BtnfinishCondArr);
            }
            if(idXX == 4)
            {
               this.b1_0.isClick = false;
               this.b1_1.isClick = false;
               this.b1_2.isClick = false;
               this.arrowrightNum1 = 0;
               this.xs_mc1.gotoAndStop(4);
               this.cionFrame("p1_",this.idArr4);
               this.dotext("t1_",this.idArr4,this.player1);
               this.doCond(this.player1,this.idArr4,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,idXX);
               this.cionVisible(this.player1,"p1_",this.idArr4);
               this.stuGoto(this.player1,"s1_",this.idArr4,this.p1BtnfinishCondArr);
            }
            if(idXX == 5)
            {
               this.b1_0.isClick = false;
               this.b1_1.isClick = false;
               this.b1_2.isClick = false;
               this.arrowrightNum1 = 0;
               this.xs_mc1.gotoAndStop(5);
               this.p1tlStu();
               this.cionFrame("p1_",this.idArrFoure);
               this.dotext("t1_",this.idArrFoure,this.player1);
               this.doCond(this.player1,this.idArrFoure,this.p1condArr,this.p1finishCondArr,this.p1BtnfinishCondArr,idXX);
               this.cionVisible(this.player1,"p1_",this.idArrFoure);
               this.stuGoto(this.player1,"s1_",this.idArrFoure,this.p1BtnfinishCondArr);
            }
         }
         if(nameId == "2")
         {
            this.bagStata[1] = idXX;
            if(idXX == 1)
            {
               this.b2_1.isClick = false;
               this.b2_2.isClick = false;
               this.b2_3.isClick = false;
               this.arrowrightNum2 = 0;
               this.xs_mc2.gotoAndStop(1);
               this.cionFrame("p2_",this.idArrOne);
               this.dotext("t2_",this.idArrOne,this.player2);
               this.doCond(this.player2,this.idArrOne,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,idXX);
               this.cionVisible(this.player2,"p2_",this.idArrOne);
               this.stuGoto(this.player2,"s2_",this.idArrOne,this.p2BtnfinishCondArr);
            }
            if(idXX == 2)
            {
               this.b2_0.isClick = false;
               this.b2_2.isClick = false;
               this.b2_3.isClick = false;
               this.arrowrightNum2 = 0;
               this.xs_mc2.gotoAndStop(2);
               this.cionFrame("p2_",this.idArrTow);
               this.dotext("t2_",this.idArrTow,this.player2);
               this.doCond(this.player2,this.idArrTow,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,idXX);
               this.cionVisible(this.player2,"p2_",this.idArrTow);
               this.stuGoto(this.player2,"s2_",this.idArrTow,this.p2BtnfinishCondArr);
            }
            if(idXX == 3)
            {
               this.b2_0.isClick = false;
               this.b2_1.isClick = false;
               this.b2_3.isClick = false;
               this.arrowrightNum2 = 0;
               this.xs_mc2.gotoAndStop(3);
               this.cionFrame("p2_",this.idArrThree);
               this.dotext("t2_",this.idArrThree,this.player2);
               this.doCond(this.player2,this.idArrThree,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,idXX);
               this.cionVisible(this.player2,"p2_",this.idArrThree);
               this.stuGoto(this.player2,"s2_",this.idArrThree,this.p2BtnfinishCondArr);
            }
            if(idXX == 4)
            {
               this.b2_0.isClick = false;
               this.b2_1.isClick = false;
               this.b2_2.isClick = false;
               this.arrowrightNum2 = 0;
               this.cionFrame("p2_",this.idArr4);
               this.dotext("t2_",this.idArr4,this.player2);
               this.doCond(this.player2,this.idArr4,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,idXX);
               this.cionVisible(this.player2,"p2_",this.idArr4);
               this.stuGoto(this.player2,"s2_",this.idArr4,this.p2BtnfinishCondArr);
            }
            if(idXX == 5)
            {
               this.b2_0.isClick = false;
               this.b2_1.isClick = false;
               this.b2_2.isClick = false;
               this.arrowrightNum2 = 0;
               this.xs_mc2.gotoAndStop(5);
               this.p2tlStu();
               this.cionFrame("p2_",this.idArrFoure);
               this.dotext("t2_",this.idArrFoure,this.player2);
               this.doCond(this.player2,this.idArrFoure,this.p2condArr,this.p2finishCondArr,this.p2BtnfinishCondArr,idXX);
               this.cionVisible(this.player2,"p2_",this.idArrFoure);
               this.stuGoto(this.player2,"s2_",this.idArrFoure,this.p2BtnfinishCondArr);
            }
         }
      }
      
      private function p1tlStu() : void
      {
         this.idArrFoure = ["d1","d2","d3","d4","d5"];
         arr = ["d6","d7","d8","k16"];
         for(var i:uint = 0; i < 4; i++)
         {
            if(this.PlayerDataOneArr[i])
            {
               this.idArrFoure.push(arr[i]);
            }
         }
      }
      
      private function p2tlStu() : void
      {
         this.idArrFoure = ["d1","d2","d3","d4","d5"];
         arr = ["d6","d7","d8","k16"];
         for(var i:uint = 0; i < 4; i++)
         {
            if(this.playerTowArr[i])
            {
               this.idArrFoure.push(arr[i]);
            }
         }
      }
      
      private function plBtnfalse() : void
      {
         for(var i:uint = 0; i < 4; i++)
         {
            if(i != 4 && this.PlayerDataOneArr[i] == false)
            {
               this["b1_" + i].gotoAndStop(4);
               this["b1_" + i].isClick = false;
               this["b1_" + i];
               this["b1_" + i].visible = false;
               this.p1tlStu();
            }
            else
            {
               this["b1_" + i].visible = true;
               this["b1_" + i].isClick = true;
               this.bagStata[0] = i + 1;
               this.xs_mc1.visible = true;
               this.xs_mc1.gotoAndStop(i + 1);
            }
            this["b2_" + i].visible = false;
         }
      }
      
      private function p2Btnfalse() : void
      {
         for(var i:uint = 0; i < 4; i++)
         {
            if(i != 4 && this.playerTowArr[i] == false)
            {
               this["b2_" + i].gotoAndStop(4);
               this["b2_" + i].isClick = false;
               this["b2_" + i].visible = false;
               this.p2tlStu();
            }
            else
            {
               this["b2_" + i].visible = true;
               this["b2_" + i].isClick = true;
               this.bagStata[1] = i + 1;
               this.xs_mc2.visible = true;
               this.xs_mc2.gotoAndStop(i + 1);
            }
            this["b1_" + i].visible = false;
         }
      }
      
      private function p2AllBtnfalse() : void
      {
         for(var i:uint = 0; i < 5; i++)
         {
            this["b2_" + i].gotoAndStop(4);
         }
         this.xs_mc2.visible = false;
         this.py2_SetKey.gotoAndStop(4);
         this.a2_7.gotoAndStop(4);
         this.point2.text = "";
      }
      
      private function pointY(mc:MovieClip) : void
      {
         var myPoint:Point = new Point(mc.x,mc.y);
         myPoint = this.localToGlobal(myPoint);
         if(myPoint.y + mc.height > 580)
         {
            mc.y = 580 - mc.height - 80;
         }
         if(myPoint.y < 0)
         {
            mc.y = 0;
         }
      }
      
      private function initExplain(player:PlayerData, my_arr:Array, id:String, nameId:Number) : void
      {
         var nowLevel:Number = NaN;
         var idtype2:String = null;
         var idtype:String = null;
         var kid:Number = NaN;
         var beforeName2:String = null;
         var beforeLevel2:String = null;
         var beforeName:String = null;
         var beforeLevel:Number = NaN;
         var xx1:* = player.getSkillLevel(my_arr[id]);
         var xx2:* = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getskillLevelUp();
         if(player.getSkillLevel(my_arr[id]) == 0 || player.getSkillLevel(my_arr[id]) == SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getskillLevelUp())
         {
            if(player.getSkillLevel(my_arr[id]) == 0)
            {
               nowLevel = player.getSkillLevel(my_arr[id]) + 1;
            }
            if(player.getSkillLevel(my_arr[id]) == SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getskillLevelUp())
            {
               nowLevel = player.getSkillLevel(my_arr[id]);
            }
            explain.visible = true;
            if(nameId == 1)
            {
               explain.x = mouseX - 240;
               if(this.p1BtnfinishCondArr[id])
               {
                  explain.beforeSkillName.setTextFormat(this._textFormat1);
               }
               else
               {
                  explain.beforeSkillName.setTextFormat(this._textFormat);
               }
            }
            if(nameId == 2)
            {
               explain.x = mouseX - explain.width - 250;
            }
            explain.y = mouseY;
            this.pointY(explain);
            this.exText(my_arr,id,nowLevel,player);
         }
         else
         {
            nowLevel = player.getSkillLevel(my_arr[id]);
            explainTow.visible = true;
            if(nameId == 1)
            {
               explainTow.x = mouseX - 240;
            }
            if(nameId == 2)
            {
               explainTow.x = mouseX - explain.width - 250;
            }
            explainTow.y = mouseY;
            this.pointY(explainTow);
            if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getProfessional() != "无")
            {
               explainTow.skillName.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getSkillName() + " (" + SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getProfessional() + ")";
            }
            else
            {
               explainTow.skillName.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getSkillName();
            }
            explainTow.typName.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getSkillTouchOff();
            explainTow.nowLevel.text = "技能等级:LV." + nowLevel + "/";
            explainTow.maxLevel.text = "LV." + SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getskillLevelUp();
            explainTow.playerLevel.text = "需求人物等级:LV." + SkillConditionFactory.getPlayerDataLevel(my_arr[id],nowLevel);
            if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getSkillCd() != 0)
            {
               explainTow.nowCd.text = "冷却时间:" + int(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getSkillCd() / 27) + "秒";
            }
            else
            {
               explainTow.nowCd.text = "";
            }
            if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getMp() != 0)
            {
               explainTow.mpNumber.text = "消耗MP:" + SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getMp();
            }
            else if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getWeapon() != "无")
            {
               explainTow.mpNumber.text = "武器限制:" + SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getWeapon();
            }
            else
            {
               explainTow.mpNumber.text = "";
            }
            explainTow.pointNumber.text = "需求技能点数:" + SkillConditionFactory.getPoints(my_arr[id],nowLevel);
            explainTow.nowEx.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getIntroduction();
            idtype2 = SkillConditionFactory.getBeforeLevelId(my_arr[id],nowLevel);
            if(idtype2 != "null")
            {
               beforeName2 = SkillFactory.getSkillByTypeIAndLevel(idtype2).getSkillName();
               beforeLevel2 = SkillConditionFactory.getBeforeLevel(my_arr[id],nowLevel).toString();
               explainTow.beforeSkillName.text = beforeName2 + " " + "LV." + beforeLevel2.toString();
            }
            else
            {
               explainTow.beforeSkillName.text = "无";
            }
            explainTow.nextLevel.text = "技能等级:LV." + (nowLevel + 1) + "/";
            explainTow.nextMaxLevel.text = "LV." + SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getskillLevelUp();
            explainTow.nextPlayerLevel.text = "LV." + SkillConditionFactory.getPlayerDataLevel(my_arr[id],nowLevel + 1);
            explainTow.nextPoint.text = SkillConditionFactory.getPoints(my_arr[id],nowLevel + 1);
            if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel + 1).getMp() != -1)
            {
               explainTow.nextmpNumber.text = "消耗MP:" + int(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel + 1).getMp());
            }
            else
            {
               explainTow.nextmpNumber.text = "";
            }
            if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel + 1).getSkillCd() != -1)
            {
               explainTow.nextCd.text = "冷却时间:" + int(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel + 1).getSkillCd() / 27) + "秒";
            }
            else
            {
               explainTow.nextCd.text = "";
            }
            idtype = SkillConditionFactory.getBeforeLevelId(my_arr[id],nowLevel + 1);
            if(idtype != "null")
            {
               beforeName = SkillFactory.getSkillByTypeIAndLevel(idtype).getSkillName();
               beforeLevel = Number(SkillConditionFactory.getBeforeLevel(my_arr[id],nowLevel + 1));
               explainTow.nexLevelbeforeSkillName.text = "前置技能:" + beforeName + " " + "LV." + beforeLevel;
            }
            else
            {
               explainTow.nexLevelbeforeSkillName.text = "前置技能:无";
            }
            explainTow.nextEx.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel + 1).getIntroduction();
            kid = Number(id);
         }
      }
      
      public function exText(my_arr:Array, id:String, nowLevel:Number, player:PlayerData) : void
      {
         var beforeName:String = null;
         var beforeLevel:String = null;
         if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getProfessional() != "无")
         {
            explain.skillName.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getSkillName() + " (" + SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getProfessional() + ")";
         }
         else
         {
            explain.skillName.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getSkillName();
         }
         explain.typName.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getSkillTouchOff();
         explain.nowLevel.text = "技能等级:LV." + nowLevel + "/";
         explain.maxLevel.text = "LV." + SkillFactory.getSkillByTypeIAndLevel(my_arr[id]).getskillLevelUp();
         explain.playerLevel.text = "LV." + SkillConditionFactory.getPlayerDataLevel(my_arr[id],nowLevel);
         if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getSkillCd() != 0)
         {
            explain.nowCd.text = "冷却时间:" + int(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getSkillCd() / 27) + "秒";
         }
         else
         {
            explain.nowCd.text = "";
         }
         if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getMp() != 0)
         {
            explain.mpNumber.text = "消耗MP:" + int(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getMp());
         }
         else if(SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getWeapon() != "无")
         {
            explain.mpNumber.text = "武器限制:" + SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getWeapon();
         }
         else
         {
            explain.mpNumber.text = "";
         }
         explain.pointNumber.text = SkillConditionFactory.getPoints(my_arr[id],nowLevel);
         explain.nowEx.text = SkillFactory.getSkillByTypeIAndLevel(my_arr[id],nowLevel).getIntroduction();
         var idtype:String = SkillConditionFactory.getBeforeLevelId(my_arr[id],nowLevel);
         if(idtype != "null")
         {
            beforeName = SkillFactory.getSkillByTypeIAndLevel(idtype).getSkillName();
            beforeLevel = SkillConditionFactory.getBeforeLevel(my_arr[id],nowLevel).toString();
            explain.beforeSkillName.text = beforeName + " " + "LV." + beforeLevel.toString();
         }
         else
         {
            explain.beforeSkillName.text = "无";
         }
      }
      
      private function setKeyFunction(ex:MovieClip, id:Number, keyArr:Array, type:* = 1) : void
      {
         var xx:Number = NaN;
         var keyNum:String = null;
         if(type == 1)
         {
            if(id < 4)
            {
               this.keyMcVible(ex);
               xx = Number(keyArr[id + 7]);
               keyNum = "k" + String(xx);
               ex.skillKey.gotoAndStop(keyNum);
            }
            else if(id == 4)
            {
               this.keyMcVible(ex,1);
               ex.skillKey.gotoAndStop("k" + String(keyArr[14]));
            }
            else if(id == 5)
            {
               this.keyMcVible(ex,2);
               ex.skillKey1.gotoAndStop("k" + String(keyArr[14]));
               ex.skillKey.gotoAndStop("k" + String(keyArr[0]));
            }
            else if(id == 6)
            {
               this.keyMcVible(ex,2);
               ex.skillKey1.gotoAndStop("k" + String(keyArr[14]));
               ex.skillKey.gotoAndStop("k" + String(keyArr[1]));
            }
            else if(id == 7)
            {
               this.keyMcVible(ex,3);
               ex.skillKey.gotoAndStop("k" + String(keyArr[0]));
               ex.skillKey1.gotoAndStop("k" + String(keyArr[0]));
               ex.skillKey2.gotoAndStop("k" + String(keyArr[14]));
            }
         }
         else if(type == 2)
         {
            ex.add1.text = "";
            ex.add2.text = "";
            if(ex.skillKey1)
            {
               ex.skillKey1.visible = false;
            }
            if(ex.skillKey2)
            {
               ex.skillKey2.visible = false;
            }
            if(ex.skillKey)
            {
               ex.skillKey.gotoAndStop("kx");
            }
         }
      }
      
      private function keyMcVible(ex:MovieClip, num:Number = 1) : void
      {
         if(ex.skillKey)
         {
            ex.skillKey.visible = true;
         }
         if(num == 1)
         {
            ex.add1.text = "";
            ex.add2.text = "";
            if(ex.skillKey1)
            {
               ex.skillKey1.visible = false;
            }
            if(ex.skillKey2)
            {
               ex.skillKey2.visible = false;
            }
         }
         else if(num == 2)
         {
            ex.add1.text = "+";
            ex.add2.text = "";
            if(ex.skillKey2)
            {
               ex.skillKey2.visible = false;
            }
            if(ex.skillKey1)
            {
               ex.skillKey1.visible = true;
            }
         }
         else if(num == 3)
         {
            ex.add1.text = "+";
            ex.add2.text = "+";
            if(ex.skillKey1)
            {
               ex.skillKey1.visible = true;
            }
            if(ex.skillKey2)
            {
               ex.skillKey2.visible = true;
            }
         }
      }
      
      private function redText(myArr:Array, strId:String, num:Number = 1) : void
      {
         var nowLevel:Number = NaN;
         var skilllevel:Number = NaN;
         var nextLevel:Number = 1;
         var id:Number = Number(strId);
         var maxLevel:Number = Number(SkillFactory.getSkillByTypeIAndLevel(myArr[id]).getskillLevelUp());
         if(num == 1)
         {
            nowLevel = Number(this.player1.getSkillLevel(myArr[id]));
            if(nowLevel == 0)
            {
               nowLevel = 1;
            }
            else
            {
               nowLevel = Number(this.player1.getSkillLevel(myArr[id]));
               if(nowLevel < maxLevel)
               {
                  nextLevel = nowLevel + 1;
               }
               else
               {
                  nextLevel = maxLevel;
               }
            }
            if(this.player1.getLevel() >= SkillConditionFactory.getPlayerDataLevel(myArr[id],nowLevel))
            {
               explain.playerLevel.setTextFormat(this._textFormat1);
            }
            else
            {
               explain.playerLevel.setTextFormat(this._textFormat);
            }
            if(this.player1.getPoints() >= SkillConditionFactory.getPoints(myArr[id],nowLevel))
            {
               explain.pointNumber.setTextFormat(this._textFormat1);
            }
            else
            {
               explain.pointNumber.setTextFormat(this._textFormat);
            }
            skilllevel = Number(SkillConditionFactory.getPlayerDataLevel(myArr[id],nextLevel));
            if(this.player1.getLevel() >= SkillConditionFactory.getPlayerDataLevel(myArr[id],nextLevel))
            {
               explainTow.nextPlayerLevel.setTextFormat(this._textFormat1);
            }
            else
            {
               explainTow.nextPlayerLevel.setTextFormat(this._textFormat);
            }
            if(this.player1.getPoints() >= SkillConditionFactory.getPoints(myArr[id],nextLevel))
            {
               explainTow.nextPoint.setTextFormat(this._textFormat1);
            }
            else
            {
               explainTow.nextPoint.setTextFormat(this._textFormat);
            }
         }
         else if(num == 2)
         {
            nowLevel = Number(this.player2.getSkillLevel(myArr[id]));
            if(nowLevel == 0)
            {
               nowLevel = 1;
            }
            else
            {
               nowLevel = Number(this.player2.getSkillLevel(myArr[id]));
               if(nowLevel < maxLevel)
               {
                  nextLevel = nowLevel + 1;
               }
               else
               {
                  nextLevel = maxLevel;
               }
            }
            if(this.player2.getLevel() >= SkillConditionFactory.getPlayerDataLevel(myArr[id],nowLevel))
            {
               explain.playerLevel.setTextFormat(this._textFormat1);
            }
            else
            {
               explain.playerLevel.setTextFormat(this._textFormat);
            }
            if(this.player2.getPoints() >= SkillConditionFactory.getPoints(myArr[id],nowLevel))
            {
               explain.pointNumber.setTextFormat(this._textFormat1);
            }
            else
            {
               explain.pointNumber.setTextFormat(this._textFormat);
            }
            if(this.player2.getLevel() >= SkillConditionFactory.getPlayerDataLevel(myArr[id],nextLevel))
            {
               explainTow.nextPlayerLevel.setTextFormat(this._textFormat1);
            }
            else
            {
               explainTow.nextPlayerLevel.setTextFormat(this._textFormat);
            }
            if(this.player2.getPoints() >= SkillConditionFactory.getPoints(myArr[id],nextLevel))
            {
               explainTow.nextPoint.setTextFormat(this._textFormat1);
            }
            else
            {
               explainTow.nextPoint.setTextFormat(this._textFormat);
            }
         }
      }
      
      public function overHandle(e:BtnEvent) : void
      {
         var mc:MovieClip = e.target as MovieClip;
         var nameId:String = mc.name.substr(1,1);
         var id:String = mc.name.substr(3,1);
         var kid:Number = Number(id);
         if(nameId == "1")
         {
            switch(this.bagStata[0])
            {
               case 1:
                  this.initExplain(this.player1,this.idArrOne,id,1);
                  this.redText(this.idArrOne,id);
                  this.setKeyFunction(explain,kid,this.player1._keyArr);
                  this.setKeyFunction(explainTow,kid,this.player1._keyArr);
                  break;
               case 2:
                  this.initExplain(this.player1,this.idArrTow,id,1);
                  this.redText(this.idArrTow,id);
                  this.setKeyFunction(explain,kid,this.player1._keyArr);
                  this.setKeyFunction(explainTow,kid,this.player1._keyArr);
                  break;
               case 3:
                  this.initExplain(this.player1,this.idArrThree,id,1);
                  this.redText(this.idArrThree,id);
                  this.setKeyFunction(explain,kid,this.player1._keyArr);
                  this.setKeyFunction(explainTow,kid,this.player1._keyArr);
                  break;
               case 4:
                  this.initExplain(this.player1,this.idArr4,id,1);
                  this.redText(this.idArr4,id);
                  this.setKeyFunction(explain,kid,this.player1._keyArr,2);
                  this.setKeyFunction(explainTow,kid,this.player1._keyArr,2);
                  break;
               case 5:
                  this.initExplain(this.player1,this.idArrFoure,id,1);
                  this.redText(this.idArrFoure,id);
                  this.setKeyFunction(explain,kid,this.player1._keyArr,2);
                  this.setKeyFunction(explainTow,kid,this.player1._keyArr,2);
            }
         }
         if(nameId == "2")
         {
            switch(this.bagStata[1])
            {
               case 1:
                  this.initExplain(this.player2,this.idArrOne,id,2);
                  this.redText(this.idArrOne,id,2);
                  this.setKeyFunction(explain,kid,this.player2._keyArr);
                  this.setKeyFunction(explainTow,kid,this.player2._keyArr);
                  break;
               case 2:
                  this.initExplain(this.player2,this.idArrTow,id,2);
                  this.redText(this.idArrTow,id,2);
                  this.setKeyFunction(explain,kid,this.player2._keyArr);
                  this.setKeyFunction(explainTow,kid,this.player2._keyArr);
                  break;
               case 3:
                  this.initExplain(this.player2,this.idArrThree,id,2);
                  this.redText(this.idArrThree,id,2);
                  this.setKeyFunction(explain,kid,this.player2._keyArr);
                  this.setKeyFunction(explainTow,kid,this.player2._keyArr);
                  break;
               case 4:
                  this.initExplain(this.player2,this.idArr4,id,1);
                  this.redText(this.idArr4,id);
                  this.setKeyFunction(explain,kid,this.player2._keyArr,2);
                  this.setKeyFunction(explainTow,kid,this.player2._keyArr,2);
                  break;
               case 5:
                  this.initExplain(this.player2,this.idArrFoure,id,2);
                  this.redText(this.idArrFoure,id,2);
                  this.setKeyFunction(explain,kid,this.player2._keyArr,2);
                  this.setKeyFunction(explainTow,kid,this.player2._keyArr,2);
            }
         }
      }
      
      private function outHandle(e:BtnEvent) : void
      {
         if(explain.visible)
         {
            explain.visible = false;
         }
         if(explainTow.visible)
         {
            explainTow.visible = false;
         }
      }
   }
}

class PrivateClass
{
   
   public function PrivateClass()
   {
      super();
   }
}
