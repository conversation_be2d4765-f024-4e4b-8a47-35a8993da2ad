package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class <PERSON><PERSON><PERSON><PERSON> extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      private static var _this:ZhuanPan;
      
      public static var saveYN:Boolean;
      
      private static var timeXX:Timer;
      
      public static var overTime:int = 20231010;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "ZhuanPan_v1331.swf";
      
      private static var show_NUM:int = 1;
      
      private static var sel_NUM:int = 1;
      
      private static var ZPtime:int = 0;
      
      private static var ZPSpeed:int = 2;
      
      public static var saveNum:int = 0;
      
      public static var saveTime:int = 0;
      
      public static var Num1YN2:int = 0;
      
      public static var Num1:int = 0;
      
      public static var Num1time:int = 1800;
      
      private static var dianQuanYN:Boolean = false;
      
      private var skin:MovieClip;
      
      public function ZhuanPan()
      {
         super();
         this.LoadSkin();
         _this = this;
         this.Init();
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
            _this.Show();
            _this.skin.tishi_mc.visible = false;
         }
         else
         {
            InitOpen();
         }
      }
      
      private static function InitOpen() : *
      {
         var temp:ZhuanPan = new ZhuanPan();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function CloseX() : *
      {
         var i:int = 0;
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
            for(i = 1; i <= 10; i++)
            {
               _this.skin["mc" + i].gotoAndStop(1);
            }
         }
      }
      
      public static function DianquanGo() : *
      {
         if(dianQuanYN)
         {
            dianQuanYN = false;
            _this.ZhuanPan_GO();
         }
      }
      
      private function Init() : *
      {
         var time:int = int(Main.serverTime.getValue());
         TiaoShi.txtShow("ZhuanPan time = " + time + ", saveTime = " + saveTime);
         if(time > saveTime)
         {
            saveTime = time;
            Num1YN2 = 1;
            Num1 = 1;
         }
      }
      
      private function Num1Time_fun(e:*) : *
      {
         var temp:int = 0;
         var s1:int = 0;
         var s2:int = 0;
         var s2t:String = null;
         if(Num1YN2 == 2)
         {
            --Num1time;
            if(Num1time <= 0)
            {
               Num1 = 1;
               timeXX.stop();
               this.Show();
            }
            else
            {
               temp = Num1time;
               s1 = temp / 60;
               s2 = temp % 60;
               s2t = s2;
               if(s2 < 10)
               {
                  s2t = "0" + s2;
               }
               this.skin._txt.text = s1 + ":" + s2t;
               if(s1 == 0 && s2 == 0)
               {
                  this.skin._txt.text = "免费抽奖";
               }
            }
         }
         else
         {
            timeXX.stop();
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         Play_Interface.interfaceX["load_mc"].visible = false;
         var classRef:Class = loadData.getClass("ZhuanPan_mc") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         for(var i:int = 1; i <= 10; i++)
         {
            this.skin["mc" + i].gotoAndStop(1);
         }
         this.skin._btn.addEventListener(MouseEvent.MOUSE_DOWN,this.onCLICK);
         this.skin._btn.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOVE);
         this.skin._btn.addEventListener(MouseEvent.MOUSE_OUT,this.onOUT);
         this.skin.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         timeXX = new Timer(1000,0);
         timeXX.addEventListener(TimerEvent.TIMER,this.Num1Time_fun);
         timeXX.start();
         this.Show();
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(e:* = null) : *
      {
         CloseX();
      }
      
      private function Show() : *
      {
         this.skin._txt.text = Num1;
         var num:int = int(Main.player1.getBag().getOtherobjNum(63299));
         if(Main.P1P2)
         {
            num += Main.player2.getBag().getOtherobjNum(63299);
         }
         this.skin._txt2.text = num;
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         ++ZPtime;
         ZPSpeed = 1;
         if(ZPtime < 15 || saveYN && ZPtime > 100)
         {
            ZPSpeed = 3;
            if(saveYN && ZPtime > 100)
            {
               --saveNum;
            }
         }
         for(var i:int = 1; i <= 10; i++)
         {
            this.skin["mc" + i].gotoAndStop(1);
         }
         if(ZPtime % ZPSpeed == 0)
         {
            ++show_NUM;
         }
         if(show_NUM > 10)
         {
            show_NUM = 1;
         }
         this.skin["mc" + show_NUM].gotoAndPlay(2);
         if(saveNum < 0 && show_NUM == sel_NUM)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            this.skin._btn.addEventListener(MouseEvent.MOUSE_DOWN,this.onCLICK);
            this.skin._btn.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOVE);
            this.skin._btn.addEventListener(MouseEvent.MOUSE_OUT,this.onOUT);
            this.skin.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
            this.skin._btn.visible = true;
            saveYN = false;
            NewMC.Open("文字提示",this,480,290,30,0,true,2,"物品已放入背包");
         }
      }
      
      private function onMOVE(e:*) : *
      {
         if(Num1 < 1 && Main.player1.getBag().getOtherobjNum(63299) < 1)
         {
            this.skin.tishi_mc.visible = true;
         }
      }
      
      private function onOUT(e:*) : *
      {
         this.skin.tishi_mc.visible = false;
      }
      
      private function onCLICK(e:*) : *
      {
         this.skin.tishi_mc.visible = false;
         if(Main.player1.getBag().backOtherBagNum() < 1 || Main.player1.getBag().backGemBagNum() < 1 || Main.player1.getBag().backSuppliesBagNum() < 1)
         {
            NewMC.Open("文字提示",this,480,290,30,0,true,2,"背包空间不足");
            return;
         }
         if(Num1 > 0)
         {
            --Num1;
            ++Num1YN2;
            this.ZhuanPan_GO();
         }
         else if(Shop4399.moneyAll.getValue() >= 12)
         {
            Api_4399_All.BuyObj(231);
            dianQuanYN = true;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
         }
         this.Show();
      }
      
      private function ZhuanPan_GO() : *
      {
         ZPtime = 0;
         var xx:int = Math.random() * 100;
         if(xx < 10)
         {
            sel_NUM = 1;
         }
         else if(xx < 20)
         {
            sel_NUM = 2;
         }
         else if(xx < 35)
         {
            sel_NUM = 3;
         }
         else if(xx < 37)
         {
            sel_NUM = 4;
         }
         else if(xx < 48)
         {
            sel_NUM = 5;
         }
         else if(xx < 68)
         {
            sel_NUM = 6;
         }
         else if(xx < 78)
         {
            sel_NUM = 7;
         }
         else if(xx < 80)
         {
            sel_NUM = 8;
         }
         else if(xx < 95)
         {
            sel_NUM = 9;
         }
         else
         {
            sel_NUM = 10;
         }
         saveNum = 30;
         saveYN = false;
         TiaoShi.txtShow("sel_NUM = " + sel_NUM);
         this.GetObj();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.skin._btn.visible = false;
         this.skin._btn.removeEventListener(MouseEvent.MOUSE_DOWN,this.onCLICK);
         this.skin._btn.removeEventListener(MouseEvent.MOUSE_MOVE,this.onMOVE);
         this.skin._btn.removeEventListener(MouseEvent.MOUSE_OUT,this.onOUT);
         this.skin.Close_btn.removeEventListener(MouseEvent.CLICK,this.Close);
         timeXX.start();
         Main.Save2();
      }
      
      private function GetObj() : *
      {
         var num:int = 0;
         var i:int = 0;
         if(sel_NUM == 1)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
         }
         else if(sel_NUM == 2)
         {
            Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
         }
         else if(sel_NUM == 3)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
         }
         else if(sel_NUM == 4)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
         }
         else if(sel_NUM == 5)
         {
            Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
         }
         else if(sel_NUM == 6)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63290));
         }
         else if(sel_NUM == 7)
         {
            Main.player1.getBag().addGemBag(GemFactory.creatGemById(31217));
         }
         else if(sel_NUM == 8)
         {
            num = Math.random() * 100;
            if(num < 20)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
            }
            else if(num < 45)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63237));
            }
            else if(num < 70)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
            }
            else if(num < 75)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63267));
            }
            else if(num < 95)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63271));
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63280));
            }
         }
         else if(sel_NUM == 9)
         {
            for(i = 0; i < 3; i++)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
            }
         }
         else if(sel_NUM == 10)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
         }
      }
   }
}

