package src
{
   import com.*;
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.Tool.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.other.*;
   import src.tool.*;
   
   public class <PERSON><PERSON>uiRenWu extends MovieClip
   {
      
      public static var myXml:XML;
      
      public static var rwArr:Array;
      
      public static var rwArr2:Array;
      
      public static const data:Class = GongHuiRenWu_data;
      
      public static var rwArrData:Array = new Array();
      
      public static var CZ_yn:Boolean = false;
      
      public function GongHuiRenWu()
      {
         super();
      }
      
      public static function initData() : *
      {
         if(!myXml)
         {
            myXml = XMLAsset.createXML(GongHuiRenWu.data);
         }
         if(!rwArr2 || rwArr2[0] < Main.serverTime.getValue())
         {
            rwArr2 = [Main.serverTime.getValue(),false,false,false,false];
         }
      }
      
      public static function SX(lv:int) : *
      {
         var i:int = 0;
         var lvXX:int = 0;
         var idX:int = 0;
         var arr:Array = null;
         var num:int = 0;
         CZ_yn = true;
         if(lv >= 86)
         {
            lv = 6;
         }
         else if(lv >= 76)
         {
            lv = 5;
         }
         else if(lv >= 66)
         {
            lv = 4;
         }
         else if(lv >= 51)
         {
            lv = 3;
         }
         else if(lv >= 31)
         {
            lv = 2;
         }
         else
         {
            lv = 1;
         }
         var tempArr:Array = new Array();
         for(i in myXml.公会任务)
         {
            lvXX = int(myXml.公会任务[i].级数);
            if(lv == lvXX)
            {
               arr = [int(myXml.公会任务[i].ID),0,0,false,false];
               tempArr[tempArr.length] = arr;
            }
            idX = int(myXml.公会任务[i].ID);
            arr = [int(myXml.公会任务[i].ID),int(myXml.公会任务[i].级数),String(myXml.公会任务[i].需求),int(myXml.公会任务[i].地图id),int(myXml.公会任务[i].地图星级),int(myXml.公会任务[i].通关次数),int(myXml.公会任务[i].怪物id),int(myXml.公会任务[i].怪物数量),int(myXml.公会任务[i].金币),int(myXml.公会任务[i].经验),int(myXml.公会任务[i].任务类型)];
            rwArrData[idX] = arr;
         }
         TiaoShi.txtShow("1筛选 = " + tempArr.length);
         if(!rwArr || rwArr[0] < Main.serverTime.getValue())
         {
            rwArr = new Array();
            rwArr[0] = Main.serverTime.getValue();
            TiaoShi.txtShow("重置公会任务!!!!!!!!!!!!" + Main.serverTime.getValue());
            for(i = 0; i < 3; i++)
            {
               num = Math.random() * tempArr.length;
               rwArr[i + 1] = DeepCopyUtil.clone(tempArr[num]);
               tempArr.splice(num,1);
               TiaoShi.txtShow("2筛选:" + i + " = " + rwArr[i + 1]);
            }
            TiaoShi.txtShow("3筛选 rwArr.length = " + rwArr.length);
            rwArr[4] = false;
            TiaoShi.txtShow("rwArr ============> " + rwArr);
            Main.Save(false);
         }
      }
      
      public static function isType(id:int) : Array
      {
         if(rwArrData[id])
         {
            return rwArrData[id];
         }
         return null;
      }
      
      public static function enemyOK(id:int) : *
      {
         var tempArr:Array = null;
         if(!CZ_yn)
         {
            return;
         }
         for(var i:int = 1; i <= 3; i++)
         {
            if(!rwArr[i][3])
            {
               tempArr = isType(rwArr[i][0]);
               if(tempArr[10] == 2 && id == tempArr[6])
               {
                  ++rwArr[i][2];
                  TiaoShi.txtShow("击杀数:" + rwArr[i][2]);
                  if(rwArr[i][2] >= tempArr[7])
                  {
                     rwArr[i][3] = true;
                     Api_4399_GongHui.setRW(67);
                     TiaoShi.txtShow("~~~~~~~~~~2公会任务完成:" + i);
                     Api_4399_GongHui.upNum(157);
                  }
               }
            }
         }
      }
      
      public static function gameOK(id:int, id2:int) : *
      {
         var tempArr:Array = null;
         if(!CZ_yn)
         {
            return;
         }
         for(var i:int = 1; i <= 3; i++)
         {
            if(!rwArr[i][3])
            {
               tempArr = isType(rwArr[i][0]);
               if(tempArr[10] == 1 && id == tempArr[3] && id2 == tempArr[4])
               {
                  ++rwArr[i][1];
                  TiaoShi.txtShow("通关数:" + rwArr[i][1]);
                  if(rwArr[i][1] >= tempArr[5])
                  {
                     rwArr[i][3] = true;
                     Api_4399_GongHui.setRW(67);
                     TiaoShi.txtShow("~~~~~~~~~~1公会任务完成:" + i);
                     Api_4399_GongHui.upNum(157);
                  }
               }
            }
         }
      }
   }
}

