package src
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.achievement.*;
   import com.hotpoint.braveManIII.repository.chest.*;
   import com.hotpoint.braveManIII.repository.elves.*;
   import com.hotpoint.braveManIII.repository.equipShop.*;
   import com.hotpoint.braveManIII.repository.jingLingCatch.*;
   import com.hotpoint.braveManIII.repository.monsterCard.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.petEquip.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.task.*;
   import com.hotpoint.braveManIII.repository.title.*;
   import com.hotpoint.braveManIII.repository.tuijianShop.*;
   import com.hotpoint.braveManIII.repository.wantedTask.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import src._data.*;
   import src.tool.*;
   
   public class Data2
   {
      
      public static var Enemy_md5_2:String;
      
      public static var skill_md5_2:String;
      
      public static var renWu_md5_2:String;
      
      public static const petHit_data:Class = Data2_petHit_data;
      
      public static const pet_set:Class = Data2_pet_set;
      
      public static const AchNum:Class = Data2_AchNum;
      
      public static const title:Class = Data2_title;
      
      public static const reward:Class = Data2_reward;
      
      public static const taskData:Class = Data2_taskData;
      
      public static const elves:Class = Data2_elves;
      
      public static const chestData:Class = Data2_chestData;
      
      public static const probability:Class = Data2_probability;
      
      public static const property:Class = Data2_property;
      
      public static const monster:Class = Data2_monster;
      
      public static const xingLing:Class = Data2_xingLing;
      
      public static const jihua:Class = Data2_jihua;
      
      public static const petEquip:Class = Data2_petEquip;
      
      public static const shopData:Class = Data2_shopData;
      
      public static const gameObjData:Class = Data2_gameObjData;
      
      public static const duiHuanData:Class = Data2_duiHuanData;
      
      public static const eShop:Class = Data2_eShop;
      
      public static const jinglingbuzhuo:Class = Data2_jinglingbuzhuo;
      
      public static const sengJi:Class = Data2_sengJi;
      
      public static const tjShop:Class = Data2_tjShop;
      
      public static const zhuFu2:Class = Data2_zhuFu2;
      
      public static const gkData1:Class = Data2_gkData1;
      
      public static const gkData2:Class = Data2_gkData2;
      
      public static const gkData3:Class = Data2_gkData3;
      
      public static const gkData4:Class = Data2_gkData4;
      
      public static const gkData5:Class = Data2_gkData5;
      
      public static const gkData6:Class = Data2_gkData6;
      
      public static const gkData7:Class = Data2_gkData7;
      
      public static const gwData1:Class = Data2_gwData1;
      
      public static const gwData2:Class = Data2_gwData2;
      
      public static const gwData3:Class = Data2_gwData3;
      
      public static const gwData4:Class = Data2_gwData4;
      
      public static const gwData5:Class = Data2_gwData5;
      
      public static const gwData6:Class = Data2_gwData6;
      
      public static const gwData7:Class = Data2_gwData7;
      
      public static const gwgjData1:Class = Data2_gwgjData1;
      
      public static const gwgjData2:Class = Data2_gwgjData2;
      
      public static const gwgjData3:Class = Data2_gwgjData3;
      
      public static const gwgjData4:Class = Data2_gwgjData4;
      
      public static const gwgjData5:Class = Data2_gwgjData5;
      
      public static const gwgjData6:Class = Data2_gwgjData6;
      
      public static const gwgjData7:Class = Data2_gwgjData7;
      
      public static const jnData0:Class = Data2_jnData0;
      
      public static const jnData1:Class = Data2_jnData1;
      
      public static const jnData2:Class = Data2_jnData2;
      
      public static const jnData3:Class = Data2_jnData3;
      
      public static const Fly_dataX:Class = Data2_Fly_dataX;
      
      public static const data1XX:Class = Data2_data1XX;
      
      public static const data2XX:Class = Data2_data2XX;
      
      public static const data_mosen:Class = Data2_data_mosen;
      
      public static var Enemy_md5_1:String = "53ef0d462d952e174e6c6bc70167ba87";
      
      public static var skill_md5_1:String = "4403453a814caa72e285c76e9ec5084a";
      
      public static var renWu_md5_1:String = "6f1c05828a0014f9a8fe297a9f5d1eec";
      
      public function Data2()
      {
         super();
      }
      
      public static function Init() : *
      {
         Data_mosen.xml = XMLAsset.createXML(Data2.data_mosen);
         Skin.PlayerXml[0] = XMLAsset.createXML(Data2.jnData0);
         Skin.PlayerXml[1] = XMLAsset.createXML(Data2.jnData1);
         Skin.PlayerXml[2] = XMLAsset.createXML(Data2.jnData2);
         Skin.PlayerXml[3] = XMLAsset.createXML(Data2.jnData3);
         GameData.GameDataXmlArr[1] = XMLAsset.createXML(Data2.gkData1);
         GameData.GameDataXmlArr[2] = XMLAsset.createXML(Data2.gkData2);
         GameData.GameDataXmlArr[3] = XMLAsset.createXML(Data2.gkData3);
         GameData.GameDataXmlArr[4] = XMLAsset.createXML(Data2.gkData4);
         GameData.GameDataXmlArr[5] = XMLAsset.createXML(Data2.gkData5);
         GameData.GameDataXmlArr[6] = XMLAsset.createXML(Data2.gkData6);
         GameData.GameDataXmlArr[7] = XMLAsset.createXML(Data2.gkData7);
         Enemy.EnemyXmlArr[1] = XMLAsset.createXML(Data2.gwData1);
         Enemy.EnemyXmlArr[2] = XMLAsset.createXML(Data2.gwData2);
         Enemy.EnemyXmlArr[3] = XMLAsset.createXML(Data2.gwData3);
         Enemy.EnemyXmlArr[4] = XMLAsset.createXML(Data2.gwData4);
         Enemy.EnemyXmlArr[5] = XMLAsset.createXML(Data2.gwData5);
         Enemy.EnemyXmlArr[6] = XMLAsset.createXML(Data2.gwData6);
         Enemy.EnemyXmlArr[7] = XMLAsset.createXML(Data2.gwData7);
         EnemySkin.EnemySkinXmlArr[1] = new Array();
         EnemySkin.EnemySkinXmlArr[1][0] = XMLAsset.createXML(Data2.gwgjData1);
         EnemySkin.EnemySkinXmlArr[2] = new Array();
         EnemySkin.EnemySkinXmlArr[2][0] = XMLAsset.createXML(Data2.gwgjData2);
         EnemySkin.EnemySkinXmlArr[3] = new Array();
         EnemySkin.EnemySkinXmlArr[3][0] = XMLAsset.createXML(Data2.gwgjData3);
         EnemySkin.EnemySkinXmlArr[4] = new Array();
         EnemySkin.EnemySkinXmlArr[4][0] = XMLAsset.createXML(Data2.gwgjData4);
         EnemySkin.EnemySkinXmlArr[5] = new Array();
         EnemySkin.EnemySkinXmlArr[5][0] = XMLAsset.createXML(Data2.gwgjData5);
         EnemySkin.EnemySkinXmlArr[6] = new Array();
         EnemySkin.EnemySkinXmlArr[6][0] = XMLAsset.createXML(Data2.gwgjData6);
         EnemySkin.EnemySkinXmlArr[7] = new Array();
         EnemySkin.EnemySkinXmlArr[7][0] = XMLAsset.createXML(Data2.gwgjData7);
         EnemySkinXML_Init();
         PaiHang_Data.creatFactory();
         PetEquipFactory.creatpetEquipFactory();
         PetFactory.creatPetFactory();
         ChongWu.get_XML_data();
         AchNumFactory.creatAchNumFactory();
         TitleFactory.creatTitleFactory();
         WantedFactory.creatWantedFactory();
         TaskFactory.creatTaskFactory();
         ElvesFactory.creatElvesFactory();
         ChestFactory.creatChestFactory();
         EquipProbabilityFactory.creatProbabilltyData();
         EquipPropertyFactory.creatEquipData();
         MonsterFactory.creatMonsterFactory();
         PlanFactory.creatPlanFactory();
         XingLingFactory.creatFactory();
         GaneObjFactory.creatFactory();
         ShopFactory.creatFactory();
         equipShopFactory.creatEquipShopFactory();
         tuijianFactory.creatTuiJianFactory();
         JLCFactory.creatJLCFactory();
         Zhufu2Factory.creatZhufu2Factory();
         Fly.FlyData = XMLAsset.createXML(Data2.Fly_dataX);
         new_flydata();
         DataMD5();
      }
      
      public static function EnemySkinXML_Init() : *
      {
         var xmlnode:XML = null;
         var x:* = undefined;
         var gameNum:int = 0;
         var tempArr:XML = null;
         var newNode:XML = null;
         var max:int = EnemySkin.EnemySkinXmlArr.length - 1;
         TiaoShi.txtShow("怪物攻击表分割关卡优化 max = " + max);
         for(var i:int = 1; i <= max; i++)
         {
            xmlnode = EnemySkin.EnemySkinXmlArr[i][0];
            for(x in xmlnode.怪物攻击)
            {
               gameNum = int(xmlnode.怪物攻击[x].关卡);
               if(!EnemySkin.EnemySkinXmlArr[i][gameNum])
               {
                  EnemySkin.EnemySkinXmlArr[i][gameNum] = new XML();
                  EnemySkin.EnemySkinXmlArr[i][gameNum] = <root></root>;
               }
               tempArr = EnemySkin.EnemySkinXmlArr[i][gameNum];
               newNode = xmlnode.怪物攻击[x];
               tempArr.appendChild(newNode);
            }
         }
      }
      
      public static function DataMD5() : *
      {
         for(var i:int = 0; i < Enemy.EnemyXmlArr.length + 1; i++)
         {
            Enemy.EnemyXmlArrMd5[i] = Obj_Compare.getObj_ByteArray(Enemy.EnemyXmlArr[i]);
         }
         Enemy_md5_2 = MD5contrast.getObj_MD5String(Enemy.EnemyXmlArr);
         if(Enemy_md5_1 != Enemy_md5_2)
         {
            Main.NoGame("怪物数据验证错误");
         }
         skill_md5_2 = MD5contrast.getObj_MD5String(SkillFactory.myXml);
         if(skill_md5_1 != skill_md5_2)
         {
            Main.NoGame("技能列表数据异常");
         }
         renWu_md5_2 = MD5contrast.getObj_MD5String(TaskFactory.myXml);
         if(renWu_md5_1 != renWu_md5_2)
         {
            Main.NoGame("任务列表数据异常");
         }
      }
      
      public static function EmenyDataMD5() : *
      {
         for(var i:int = 0; i < Enemy.EnemyXmlArr.length + 1; i++)
         {
            Enemy.EnemyXmlArrMd5[i] = Obj_Compare.getObj_ByteArray(Enemy.EnemyXmlArr[i]);
         }
         Enemy_md5_2 = MD5contrast.getObj_MD5String(Enemy.EnemyXmlArr);
         if(Enemy_md5_1 != Enemy_md5_2)
         {
            Main.NoGame("怪物数据验证错误");
         }
      }
      
      public static function new_flydata() : *
      {
         var i:* = undefined;
         var name:String = null;
         var d0:* = undefined;
         var str:* = undefined;
         var d1:* = undefined;
         var str2:* = undefined;
         var d2:* = undefined;
         var d3:* = undefined;
         var d4:* = undefined;
         var d5:* = undefined;
         for(i in Fly.FlyData.飞行道具)
         {
            name = String(Fly.FlyData.飞行道具[i].名称);
            d0 = int(Fly.FlyData.飞行道具[i].HP);
            str = String(Fly.FlyData.飞行道具[i].循环).toString();
            d1 = str == "true" || str == "ture" ? true : false;
            str2 = String(Fly.FlyData.飞行道具[i].穿越).toString();
            d2 = str2 == "true" || str2 == "ture" ? true : false;
            d3 = int(Fly.FlyData.飞行道具[i].时间);
            d4 = int(Fly.FlyData.飞行道具[i].移动x);
            d5 = int(Fly.FlyData.飞行道具[i].移动y);
            if(!Fly.FlyData2[name])
            {
               Fly.FlyData2[name] = [d0,d1,d2,d3,d4,d5];
            }
         }
      }
   }
}

