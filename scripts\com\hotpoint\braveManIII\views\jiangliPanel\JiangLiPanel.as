package com.hotpoint.braveManIII.views.jiangliPanel
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class JiangLiPanel extends MovieClip
   {
      
      public static var jlp:JiangLiPanel;
      
      public static var jlPanel:MovieClip;
      
      private static var loadData:ClassLoader;
      
      public static var LQtimes:VT = VT.createVT(0);
      
      private static var loadName:String = "LVJiangLi_v1010.swf";
      
      public function JiangLiPanel()
      {
         super();
      }
      
      private static function LoadSkin() : *
      {
         if(!jlPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("JiangLiShow") as Class;
         jlPanel = new classRef();
         jlp.addChild(jlPanel);
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         jlp = new JiangLiPanel();
         LoadSkin();
         Main._stage.addChild(jlp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         jlp = new JiangLiPanel();
         Main._stage.addChild(jlp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(jlPanel)
         {
            Main.stopXX = true;
            jlp.x = 0;
            jlp.y = 0;
            addListenerP1();
            jlp.visible = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(jlPanel)
         {
            jlp.visible = false;
            Main.stopXX = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         jlPanel["jiangli_mc"].stop();
         jlPanel["close"].addEventListener(MouseEvent.CLICK,closeJL);
         jlPanel["lingqu"].addEventListener(MouseEvent.CLICK,lingqu);
         show();
      }
      
      public static function closeJL(e:*) : *
      {
         close();
      }
      
      public static function lingqu(e:*) : *
      {
         if(LQtimes.getValue() == 0)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player1.getBag().backSuppliesBagNum() >= 3)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.addGold(5000);
               if(Main.P1P2)
               {
                  Main.player2.addGold(5000);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         if(LQtimes.getValue() == 1 && Main.player1.getLevel() >= 8)
         {
            if(Main.player1.getBag().backequipBagNum() >= 1 && Main.player1.getBag().backSuppliesBagNum() >= 3)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14450));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.addGold(5000);
               if(Main.P1P2)
               {
                  Main.player2.addGold(5000);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 2 && Main.player1.getLevel() >= 15)
         {
            if(Main.player1.getBag().backequipBagNum() >= 1 && Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               Main.player1.addGold(5000);
               if(Main.P1P2)
               {
                  Main.player2.addGold(5000);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 3 && Main.player1.getLevel() >= 20)
         {
            if(Main.player1.getBag().backequipBagNum() >= 1 && Main.player1.getBag().backOtherBagNum() >= 2)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14450));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63106));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63294));
               Main.player1.addGold(10000);
               if(Main.P1P2)
               {
                  Main.player2.addGold(10000);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 4 && Main.player1.getLevel() >= 25)
         {
            if(Main.player1.getBag().backequipBagNum() >= 3 && Main.player1.getBag().backOtherBagNum() >= 1 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31212));
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 5 && Main.player1.getLevel() >= 30)
         {
            if(Main.player1.getBag().backSuppliesBagNum() >= 3 && Main.player1.getBag().backOtherBagNum() >= 1 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31212));
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 6 && Main.player1.getLevel() >= 35)
         {
            if(Main.player1.getBag().backequipBagNum() >= 1 && Main.player1.getBag().backOtherBagNum() >= 1 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14450));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31212));
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 7 && Main.player1.getLevel() >= 40)
         {
            if(Main.player1.getBag().backequipBagNum() >= 3 && Main.player1.getBag().backOtherBagNum() >= 1 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31212));
               }
               Main.player1.AddKillPoint(100);
               if(Main.P1P2)
               {
                  Main.player2.AddKillPoint(100);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 8 && Main.player1.getLevel() >= 45)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               for(i = 0; i < 3; ++i)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               }
               for(i = 0; i < 2; ++i)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(32212));
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 9 && Main.player1.getLevel() >= 50)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 2)
            {
               for(i = 0; i < 3; ++i)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(31217));
               }
               Main.player1.getBag().addGemBag(GemFactory.creatGemById(33314));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 10 && Main.player1.getLevel() >= 55)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 2 && Main.player1.getBag().backequipBagNum() >= 1)
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63234));
               Main.player1.addGold(80000);
               if(Main.P1P2)
               {
                  Main.player2.addGold(80000);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 11 && Main.player1.getLevel() >= 60)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 3 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
               Main.player1.getBag().addGemBag(GemFactory.creatGemById(34710));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63242));
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 12 && Main.player1.getLevel() >= 65)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 1)
            {
               for(i = 0; i < 5; ++i)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
               }
               Main.player1.getBag().addGemBag(GemFactory.creatGemById(34613));
               Main.player1.addGold(100000);
               if(Main.P1P2)
               {
                  Main.player2.addGold(100000);
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         else if(LQtimes.getValue() == 13 && Main.player1.getLevel() >= 70)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 3)
            {
               for(i = 0; i < 3; ++i)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63326));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63368));
               }
               LQtimes.setValue(LQtimes.getValue() + 1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功！");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满，请整理背包！");
            }
         }
         show();
      }
      
      public static function show() : *
      {
         if(LQtimes.getValue() == 0)
         {
            if(Main.player1.getLevel() >= 1)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(1);
         }
         if(LQtimes.getValue() == 1)
         {
            if(Main.player1.getLevel() >= 8)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(2);
         }
         else if(LQtimes.getValue() == 2)
         {
            if(Main.player1.getLevel() >= 15)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(3);
         }
         else if(LQtimes.getValue() == 3)
         {
            if(Main.player1.getLevel() >= 20)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(4);
         }
         else if(LQtimes.getValue() == 4)
         {
            if(Main.player1.getLevel() >= 25)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(5);
         }
         else if(LQtimes.getValue() == 5)
         {
            if(Main.player1.getLevel() >= 30)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(6);
         }
         else if(LQtimes.getValue() == 6)
         {
            if(Main.player1.getLevel() >= 35)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(7);
         }
         else if(LQtimes.getValue() == 7)
         {
            if(Main.player1.getLevel() >= 40)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(8);
         }
         else if(LQtimes.getValue() == 8)
         {
            if(Main.player1.getLevel() >= 45)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(9);
         }
         else if(LQtimes.getValue() == 9)
         {
            if(Main.player1.getLevel() >= 50)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(10);
         }
         else if(LQtimes.getValue() == 10)
         {
            if(Main.player1.getLevel() >= 55)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(11);
         }
         else if(LQtimes.getValue() == 11)
         {
            if(Main.player1.getLevel() >= 60)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(12);
         }
         else if(LQtimes.getValue() == 12)
         {
            if(Main.player1.getLevel() >= 65)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(13);
         }
         else if(LQtimes.getValue() == 13)
         {
            if(Main.player1.getLevel() >= 70)
            {
               jlPanel["lingqu"].visible = true;
            }
            else
            {
               jlPanel["lingqu"].visible = false;
            }
            jlPanel["jiangli_mc"].gotoAndStop(14);
         }
         else if(LQtimes.getValue() == 14)
         {
            jlPanel["lingqu"].visible = false;
         }
      }
   }
}

