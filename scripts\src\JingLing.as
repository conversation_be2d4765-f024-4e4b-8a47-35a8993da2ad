package src
{
   import com.hotpoint.braveManIII.models.elves.Elves;
   import com.hotpoint.braveManIII.repository.elves.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.other.*;
   
   public class JingLing extends MovieClip
   {
      
      public var who:Player;
      
      public var data:Elves;
      
      public var RL:Boolean = true;
      
      public var skin:MovieClip;
      
      private var distanceX_Max:int = 900;
      
      private var distanceY_Max:int = 500;
      
      private var distanceX2:int = 100;
      
      private var distanceY2:int = 30;
      
      public var jiNengZD_ID:int = 1;
      
      public var jiNengBD_ID:int = 0;
      
      private var jiNengZD_Data:Array = [];
      
      private var jiNengBD_Data:Array = [];
      
      public var jiNengZD_num:int = 0;
      
      public var jiNengZD_Time:int = 0;
      
      public var runType:String = "移动";
      
      public var lianJiNumArr:Array = [0,0,0,"",0];
      
      private var timeX:int = 0;
      
      private var timeY:int = 0;
      
      private var MoveX:int = 2;
      
      private var MoveY:int = 2;
      
      private var BD1Time:int = 0;
      
      private var ZD1time:int;
      
      public var ZD1num:int = 0;
      
      private var ZD2Time:int = 0;
      
      private var ZD1012_Time:int = 0;
      
      private var ZD1014_Time:int = 0;
      
      private var ZD1016_Time:int = 0;
      
      private var ZD1018_Time:int = 0;
      
      private var shuiMC:MovieClip;
      
      public function JingLing(_data:Elves, p:Player)
      {
         super();
         this.who = p;
         this.who.data.playerJL_Data = this.data = _data;
         this.who.playerJL = this;
         this.addSkin();
         mouseChildren = mouseEnabled = false;
         this.getID();
      }
      
      public static function lianJiNumXX1010(who:Player) : *
      {
         var jl:JingLing = null;
         if(who.playerJL)
         {
            jl = who.playerJL;
            if(jl.jiNengBD_ID == 1010)
            {
               jl.lianJiNumArr[1] = who.lianJi.getValue();
            }
         }
      }
      
      public static function ACT_Stop(who:Player, str:String) : *
      {
         var jl:JingLing = null;
         var numX:int = 0;
         var num:int = 0;
         if(who.playerJL)
         {
            jl = who.playerJL;
            if(jl.jiNengBD_ID == 1010)
            {
               numX = int(jl.jiNengBD_Data[0].getValue());
               num = int(jl.jiNengBD_Data[1].getValue());
               if(jl.lianJiNumArr[0] == 0)
               {
                  if(jl.lianJiNumArr[1] < numX)
                  {
                     jl.lianJiNumArr[2] = 0;
                  }
                  if(jl.lianJiNumArr[1] - jl.lianJiNumArr[2] >= numX)
                  {
                     jl.lianJiNumArr[0] = 1;
                  }
               }
               if(jl.lianJiNumArr[0] == 1)
               {
                  if(str.substr(0,2) == "攻击")
                  {
                     jl.lianJiNumArr[3] = str;
                     jl.lianJiNumArr[2] = jl.lianJiNumArr[1];
                     jl.lianJiNumArr[0] = 2;
                     who.hpXX2Str = str;
                     who.hpXX2num = who.use_gongji.getValue() * num;
                  }
               }
               if(jl.lianJiNumArr[0] == 2)
               {
                  if(jl.lianJiNumArr[3] != str)
                  {
                     jl.lianJiNumArr[3] = "";
                     jl.lianJiNumArr[0] = 0;
                     who.hpXX2num = 0;
                  }
               }
            }
            else
            {
               jl.lianJiNumArr = [0,0,0,"",0];
               who.hpXX2num = 0;
            }
         }
      }
      
      public static function QingChuLengQue() : *
      {
         if(Boolean(Main.player_1) && Boolean(Main.player_1.playerJL))
         {
            Main.player_1.playerJL.jiNengZD_num = 0;
         }
         if(Main.P1P2 && Main.player_2 && Boolean(Main.player_2.playerJL))
         {
            Main.player_2.playerJL.jiNengZD_num = 0;
         }
      }
      
      public function getID() : *
      {
         if(this.data.getSID3() > 0)
         {
            this.jiNengZD_ID = SkillFactory.getSkillById(this.data.getSKILL3()).getSkillActOn();
            this.jiNengZD_Data = SkillFactory.getSkillById(this.data.getSKILL3()).getSkillValueArray();
         }
         else
         {
            this.jiNengZD_ID = 0;
         }
         if(this.data.getSID2() > 0)
         {
            this.jiNengBD_ID = SkillFactory.getSkillById(this.data.getSKILL2()).getSkillActOn();
            this.jiNengBD_Data = SkillFactory.getSkillById(this.data.getSKILL2()).getSkillValueArray();
         }
         else
         {
            this.jiNengBD_ID = 0;
         }
         trace("精灵被动技能 >>>>>",this.jiNengBD_ID);
      }
      
      public function InterfaceShow() : *
      {
         if(this.who == Main.player_1)
         {
            Play_Interface.interfaceX.jlNum_mc1.gotoAndStop("a" + this.jiNengZD_ID);
            Play_Interface.interfaceX.jlNum_txt1.text = this.jiNengZD_num + "%";
            if(this.jiNengZD_num >= 100)
            {
               Play_Interface.interfaceX.jlNum_mc_X1.gotoAndStop(2);
            }
            else
            {
               Play_Interface.interfaceX.jlNum_mc_X1.gotoAndStop(1);
            }
         }
         else if(Boolean(Main.P1P2) && this.who == Main.player_2)
         {
            Play_Interface.interfaceX.jlNum_mc2.gotoAndStop("a" + this.jiNengZD_ID);
            Play_Interface.interfaceX.jlNum_txt2.text = this.jiNengZD_num + "%";
            if(this.jiNengZD_num >= 100)
            {
               Play_Interface.interfaceX.jlNum_mc_X2.gotoAndStop(2);
            }
            else
            {
               Play_Interface.interfaceX.jlNum_mc_X2.gotoAndStop(1);
            }
         }
      }
      
      public function JingLingOPEN() : *
      {
         if(Boolean(this.who) && this.who.visible)
         {
            Main.world.moveChild_ChongWu.addChild(this);
            this.x = this.who.x + 110;
            this.y = this.who.y - 60;
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      public function Close() : *
      {
         this.who.hpXX2num = 0;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.parent.removeChild(this);
         this.who.playerJL = null;
         this.who.data.playerJL_Data = null;
      }
      
      public function addSkin() : *
      {
         var classStr:String = ElvesFactory.getClassName(this.data.getId());
         var classRef:Class = ChongWu.loadData.getClass(classStr) as Class;
         this.skin = new classRef();
         addChild(this.skin);
         if(this.who)
         {
            Main.world.moveChild_ChongWu.addChild(this);
            this.x = this.who.x + 110;
            this.y = this.who.y - 60;
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         this.ShowPlay();
         this.Move();
         this.BD();
         --this.jiNengZD_Time;
         this.InterfaceShow();
      }
      
      public function JNnumUP(xx:int, yy:int, where:MovieClip) : *
      {
         var num:int = 0;
         var num2:int = 0;
         var str:String = null;
         var xmc:GDpic = null;
         if(this.jiNengZD_ID != 0 && this.jiNengZD_Time <= 0)
         {
            num = Math.random() * 100;
            if(num < 50)
            {
               num2 = Math.random() * 4 + 3;
               this.jiNengZD_num += num2;
               if(this.jiNengZD_num > 100)
               {
                  this.jiNengZD_num = 100;
               }
               str = "获得能量:" + num2 + " 当前:" + this.jiNengZD_num;
               xmc = new GDpic(xx,yy,where,this.who);
               trace("精灵技能条 >>>>",str);
            }
            else
            {
               this.jiNengZD_Time = 27 * 2;
            }
         }
      }
      
      public function ShowPlay() : *
      {
         if(this.skin)
         {
            if(this.skin.currentLabel != this.runType)
            {
               if(this.runType == "攻击")
               {
                  this.runType = "移动";
               }
               this.skin.gotoAndPlay(this.runType);
            }
         }
      }
      
      private function Move() : *
      {
         var distance_X:int = this.x - this.who.x;
         var distance_Y:int = this.y - this.who.y + 70;
         if(Math.abs(distance_X) > this.distanceX_Max || Math.abs(distance_Y) > this.distanceY_Max)
         {
            this.SearchPlayer();
         }
         var num:int = Math.abs(distance_X) / 35 + 1;
         if(distance_X > this.distanceX2)
         {
            this.MoveX = -num;
            this.skin.scaleX = 1;
         }
         else if(distance_X < -this.distanceX2)
         {
            this.MoveX = num;
            this.skin.scaleX = -1;
         }
         if(this.MoveX > 0)
         {
            this.skin.scaleX = -1;
         }
         else
         {
            this.skin.scaleX = 1;
         }
         this.x += this.MoveX;
         var num2:int = Math.abs(distance_Y) / 80 + 1;
         if(distance_Y > this.distanceY2)
         {
            this.MoveY = -num2;
         }
         else if(distance_Y < -this.distanceX2)
         {
            this.MoveY = num2;
         }
         this.y += this.MoveY;
      }
      
      private function WhoDead() : *
      {
         if(!this.who || this.who.hp.getValue() <= 0)
         {
            this.Close();
         }
      }
      
      private function SearchPlayer() : *
      {
         this.x = this.who.x + Math.random() * 200 - 100;
         this.y = this.who.y - 60;
      }
      
      public function ZD() : *
      {
         if(Boolean(this.jiNengZD_ID) && this.jiNengZD_num >= 100)
         {
            this["ZD" + this.jiNengZD_ID]();
            this.jiNengZD_num = 0;
            this.runType = "攻击";
            this.skin.gotoAndPlay(this.runType);
         }
      }
      
      private function BD() : *
      {
         if(this.jiNengBD_ID == 1007)
         {
            this.BD1007();
         }
      }
      
      public function BD1020(num:int) : int
      {
         var r:int = 0;
         if(this.jiNengBD_ID == 1020)
         {
            r = Math.random() * 100;
            if(r < this.jiNengBD_Data[0].getValue())
            {
               num += this.who.use_gongji.getValue() * this.jiNengBD_Data[1].getValue() / 100;
            }
         }
         return num;
      }
      
      private function BD1007() : *
      {
         var hpNum:int = 0;
         if(this.who.hp.getValue() < this.who.use_hp_Max.getValue() * this.jiNengBD_Data[0].getValue())
         {
            ++this.BD1Time;
            if(this.BD1Time >= this.jiNengBD_Data[1].getValue())
            {
               this.BD1Time = 0;
               hpNum = this.who.hp.getValue() * this.jiNengBD_Data[2].getValue();
               this.who.HpUp(hpNum);
            }
         }
      }
      
      public function BD1001(hpDown:int) : *
      {
         var num:int = 0;
         var hpNum:int = 0;
         if(this.jiNengBD_ID == 1001)
         {
            num = Math.random() * 100;
            if(num < this.jiNengBD_Data[0].getValue())
            {
               hpNum = hpDown * this.jiNengBD_Data[1].getValue();
               this.who.HpUp(hpNum);
            }
         }
      }
      
      public function BD1009(en:Enemy, hpDown:int) : *
      {
         var num:int = 0;
         if(this.jiNengBD_ID == 1009)
         {
            num = hpDown * this.jiNengBD_Data[0].getValue();
            if(num > 50000)
            {
               num = 50000;
            }
            en.HpXX2(num);
         }
      }
      
      public function BD1010() : *
      {
      }
      
      public function BD1017(tempCd:int) : *
      {
         var num1:int = 0;
         var num2:int = 0;
         var r:int = 0;
         var cdNum:int = 0;
         if(this.jiNengBD_ID == 1017)
         {
            num1 = int(this.jiNengBD_Data[0].getValue());
            num2 = int(this.jiNengBD_Data[1].getValue());
            r = Math.random() * 100;
            if(r < num1)
            {
               cdNum = num2 * 27;
               if(this.who.AllSkillCD[tempCd][1] < cdNum)
               {
                  cdNum = int(this.who.AllSkillCD[tempCd][1]);
               }
               this.who.AllSkillCDXX[tempCd][1] = cdNum;
            }
         }
      }
      
      public function ZD1002() : *
      {
         this.ZD1time = this.jiNengZD_Data[2].getValue();
         NewMC.Open("萨泽效果",this.who);
         this.ZD1num = 0;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1002);
      }
      
      private function onENTER_FRAME_ZD1002(e:*) : *
      {
         var xNum:Number = NaN;
         var hpNum:int = 0;
         if(this.ZD1time > 0 && this.ZD1num >= this.jiNengZD_Data[0].getValue())
         {
            xNum = this.jiNengZD_Data[1].getValue() + 0.0001;
            hpNum = this.who.use_hp_Max.getValue() * xNum;
            this.who.HpUp(hpNum);
            this.ZD1num -= 10;
         }
         if(this.ZD1time <= 0)
         {
            this.ZD1num = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1002);
         }
         --this.ZD1time;
      }
      
      public function ZD1005() : *
      {
         var hxx:HitXX = null;
         this.ZD2Time = this.jiNengZD_Data[2].getValue();
         for(i in Enemy.All)
         {
            if(Enemy.All[i])
            {
               hxx = new HitXX();
               hxx.type = 507;
               hxx.totalTime = 3 * 27;
               hxx.space = 3 * 27;
               hxx.numValue = this.jiNengZD_Data[0].getValue();
               new BuffEnemy(hxx,Enemy.All[i]);
               NewMC.Open("露娜效果",Enemy.All[i],0,-50);
            }
         }
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1005);
      }
      
      private function onENTER_FRAME_ZD1005(e:*) : *
      {
         var hpDown:int = 0;
         var en:Enemy = null;
         if(this.ZD2Time % 27 == 0)
         {
            hpDown = this.who.use_gongji.getValue() * this.jiNengZD_Data[1].getValue();
            for(i in Enemy.All)
            {
               if(Enemy.All[i])
               {
                  en = Enemy.All[i];
                  en.HpXX2(hpDown);
               }
            }
         }
         --this.ZD2Time;
         if(this.ZD2Time <= 0)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1005);
            this.jiNengZD_num = 0;
         }
      }
      
      public function ZD1012() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1012);
      }
      
      private function onENTER_FRAME_ZD1012(e:*) : *
      {
         var hpDown:int = 0;
         var en:Enemy = null;
         var classRef:Class = null;
         var mcX:* = undefined;
         ++this.ZD1012_Time;
         if(this.ZD1012_Time >= 23)
         {
            hpDown = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
            for(i in Enemy.All)
            {
               if(Enemy.All[i])
               {
                  en = Enemy.All[i];
                  en.HpXX2(hpDown);
                  classRef = ChongWu.loadData.getClass("act_mc") as Class;
                  mcX = new classRef();
                  Main.world.moveChild_Other.addChild(mcX);
                  mcX.x = en.x;
                  mcX.y = en.y;
               }
            }
            this.ZD1012_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1012);
         }
      }
      
      public function ZD1014() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1014);
      }
      
      private function onENTER_FRAME_ZD1014(e:*) : *
      {
         var hpDown:int = 0;
         var en:Enemy = null;
         ++this.ZD1014_Time;
         if(this.ZD1014_Time >= 60)
         {
            hpDown = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
            for(i in Enemy.All)
            {
               if(Boolean(Enemy.All[i]) && Math.abs(this.x - Enemy.All[i].x) < 190)
               {
                  en = Enemy.All[i];
                  en.HpXX2(hpDown);
               }
            }
            this.ZD1014_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1014);
         }
      }
      
      public function ZD1016() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1016);
      }
      
      private function onENTER_FRAME_ZD1016(e:*) : *
      {
         var hpDown:int = 0;
         var en:Enemy = null;
         var classRef:Class = null;
         var mcX:* = undefined;
         var hxx:HitXX = null;
         ++this.ZD1016_Time;
         if(this.ZD1016_Time >= 70)
         {
            hpDown = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
            for(i in Enemy.All)
            {
               if(Enemy.All[i] && (Enemy.All[i] as Enemy).life.getValue() > 0 && Math.abs(this.x - Enemy.All[i].x) < 300)
               {
                  en = Enemy.All[i];
                  en.HpXX2(hpDown);
                  classRef = ChongWu.loadData.getClass("xx2016") as Class;
                  mcX = new classRef();
                  en.addChild(mcX);
                  mcX.y = -120;
                  hxx = new HitXX();
                  hxx.type = 606;
                  hxx.totalTime = 135;
                  hxx.numValue = this.jiNengZD_Data[1].getValue();
                  new BuffEnemy(hxx,Enemy.All[i]);
               }
            }
            this.ZD1016_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1016);
         }
      }
      
      public function ZD1018() : *
      {
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1018);
      }
      
      private function onENTER_FRAME_ZD1018(e:*) : *
      {
         var hpDown:int = 0;
         var en:Enemy = null;
         var classRef:Class = null;
         ++this.ZD1018_Time;
         if(this.ZD1018_Time >= 20)
         {
            if((this.ZD1018_Time - 20) % 37 == 0)
            {
               hpDown = this.who.use_gongji.getValue() * this.jiNengZD_Data[0].getValue();
               for(i in Enemy.All)
               {
                  if(Enemy.All[i] && (Enemy.All[i] as Enemy).life.getValue() > 0 && Math.abs(this.x - Enemy.All[i].x) < 300)
                  {
                     en = Enemy.All[i];
                     en.HpXX2(hpDown);
                  }
               }
               this.who.HpUp(this.jiNengZD_Data[1].getValue(),2);
            }
         }
         if(this.ZD1018_Time == 20)
         {
            classRef = ChongWu.loadData.getClass("shui") as Class;
            this.shuiMC = new classRef();
            Main.world.moveChild_Other.addChild(this.shuiMC);
            this.shuiMC.y = 530;
            this.shuiMC.x = this.who.x;
         }
         else if(this.ZD1018_Time > 20 && this.ZD1018_Time <= 40)
         {
            this.shuiMC.y -= 9;
         }
         else if(this.ZD1018_Time > 115 && this.ZD1018_Time <= 135)
         {
            this.shuiMC.y += 9;
         }
         else if(this.ZD1018_Time > 136)
         {
            if(this.shuiMC.parent)
            {
               this.shuiMC.parent.removeChild(this.shuiMC);
            }
            this.shuiMC == null;
            this.ZD1018_Time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_ZD1018);
         }
      }
      
      public function ZD1022() : *
      {
         var rrr:int = 0;
         var en:Enemy = null;
         var class_JG:Class = NewLoad.XiaoGuoData.getClass("星河之灵星灵脉冲") as Class;
         var flyX:* = new class_JG();
         this.who.skin_W.addChild(flyX);
         if(Enemy.All.length > 0)
         {
            rrr = Math.random() * Enemy.All.length;
            en = Enemy.All[rrr];
            flyX.x = en.x;
            flyX.y = en.y;
         }
         else
         {
            flyX.x = this.who.x;
            flyX.y = this.who.y;
         }
         flyX.gongJi_hp = this.jiNengZD_Data[0].getValue() / 100;
         flyX.attTimes = 7;
         trace("精灵主动技能1022 >>>>>",this.jiNengZD_Data[0].getValue(),this.jiNengZD_Data[1].getValue(),flyX.gongJi_hp);
      }
   }
}

