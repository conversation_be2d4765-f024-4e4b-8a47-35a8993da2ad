package src
{
   import com.*;
   import com.efnx.fps.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4216")]
   public class ChongZhi_Interface3 extends MovieClip
   {
      
      public static var _this:ChongZhi_Interface3;
      
      public static var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-41420207.html");
      
      public var close_btn:SimpleButton;
      
      public var go_btn:SimpleButton;
      
      public var lingqu_btn:SimpleButton;
      
      public function ChongZhi_Interface3()
      {
         super();
         _this = this;
         this.close_btn.addEventListener(MouseEvent.CLICK,Close);
         this.lingqu_btn.addEventListener(MouseEvent.CLICK,lingQuFun);
      }
      
      public static function onENTER_FRAME(e:*) : *
      {
         if(!openYn && Main.gameNum.getValue() == 0)
         {
            openYn = true;
            _this.x = _this.y = 0;
         }
      }
      
      public static function Close(e:* = null) : *
      {
         if(!_this)
         {
            new ChongZhi_Interface3();
         }
         _this.x = _this.y = -5000;
      }
      
      public static function Open() : *
      {
         if(!_this)
         {
            new ChongZhi_Interface3();
         }
         _this.x = _this.y = 0;
         Main._this.addChild(_this);
      }
      
      public static function lingQuFun(e:* = null) : *
      {
         navigateToURL(request,"_blank");
         Close();
      }
   }
}

