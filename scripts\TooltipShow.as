package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4717")]
   public dynamic class TooltipShow extends MovieClip
   {
      
      public var down_mc:MovieClip;
      
      public var explain:TextField;
      
      public var gemslot_mc:MovieClip;
      
      public var huanhua_mc:MovieClip;
      
      public var line0:MovieClip;
      
      public var line1:MovieClip;
      
      public var lv2_txt:TextField;
      
      public var lv_txt:TextField;
      
      public var middle_mc:MovieClip;
      
      public var name_txt:TextField;
      
      public var price:TextField;
      
      public var right_mc:MovieClip;
      
      public var star_0:MovieClip;
      
      public var star_1:MovieClip;
      
      public var star_2:MovieClip;
      
      public var star_3:MovieClip;
      
      public var star_4:MovieClip;
      
      public var star_5:MovieClip;
      
      public var txt_1:TextField;
      
      public var txt_10:TextField;
      
      public var txt_11:TextField;
      
      public var txt_12:TextField;
      
      public var txt_13:TextField;
      
      public var txt_14:TextField;
      
      public var txt_15:TextField;
      
      public var txt_2:TextField;
      
      public var txt_3:TextField;
      
      public var txt_4:TextField;
      
      public var txt_5:TextField;
      
      public var txt_6:TextField;
      
      public var txt_7:TextField;
      
      public var txt_8:TextField;
      
      public var txt_88:TextField;
      
      public var txt_9:TextField;
      
      public var txt_qhmax:TextField;
      
      public var type_txt:TextField;
      
      public function TooltipShow()
      {
         super();
      }
   }
}

