package com.hotpoint.braveManIII.views.equipShopPanel
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.equipShop.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.ui.*;
   import src.*;
   
   public class EquipShopPanel extends MovieClip
   {
      
      public static var equipShopPanel:MovieClip;
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var dragObj:MovieClip;
      
      public static var clickObj:MovieClip;
      
      private static var pointx:Number;
      
      private static var pointy:Number;
      
      private static var oldNum:Number;
      
      public static var esp:EquipShopPanel;
      
      public static var myPlayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      private static var ee:Equip;
      
      private static var arr:Array = [];
      
      public static var isDown:Boolean = false;
      
      public static var noRMBOK:Boolean = false;
      
      public static var equipShopOK:Boolean = false;
      
      public static var returnx:Number = 0;
      
      public static var returny:Number = 0;
      
      private static var yeshu:int = 1;
      
      private static var clickTime:int = 0;
      
      private static var myMenu:ContextMenu = new ContextMenu();
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "NewEquipShop_v3.swf";
      
      public function EquipShopPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!equipShopPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var mm:MovieClip = null;
         var num:int = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = equipShopPanel.getChildIndex(equipShopPanel["s1_" + i]);
            mm.x = equipShopPanel["s1_" + i].x;
            mm.y = equipShopPanel["s1_" + i].y;
            mm.name = "s1_" + i;
            equipShopPanel.removeChild(equipShopPanel["s1_" + i]);
            equipShopPanel["s1_" + i] = mm;
            equipShopPanel.addChild(mm);
            equipShopPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         mm.x = equipShopPanel["getZB"]["showPic"].x;
         mm.y = equipShopPanel["getZB"]["showPic"].y;
         mm.name = "showPic";
         equipShopPanel["getZB"].removeChild(equipShopPanel["getZB"]["showPic"]);
         equipShopPanel["getZB"]["showPic"] = mm;
         equipShopPanel["getZB"].addChild(mm);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("EquipShopShow") as Class;
         equipShopPanel = new classRef();
         esp.addChild(equipShopPanel);
         InitIcon();
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         esp = new EquipShopPanel();
         LoadSkin();
         Main._stage.addChild(esp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         esp = new EquipShopPanel();
         Main._stage.addChild(esp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(equipShopPanel)
         {
            Main.stopXX = true;
            esp.x = 0;
            esp.y = 0;
            myPlayer = Main.player1;
            addListenerP1();
            esp.visible = true;
            JiHua_Interface.ppp1_12 = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(equipShopPanel)
         {
            esp.visible = false;
            Main.stopXX = false;
            removeListenerP1();
         }
         else
         {
            InitClose();
         }
      }
      
      private static function CloseYP(e:MouseEvent) : void
      {
         close();
      }
      
      public static function addListenerP1() : *
      {
         var j:uint = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            equipShopPanel["s1_" + i].mouseChildren = false;
            equipShopPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            equipShopPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            equipShopPanel["s1_" + i].addEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(j = 0; j < 6; j++)
         {
            equipShopPanel["se_" + j]["buy_btn"].addEventListener(MouseEvent.MOUSE_OVER,BuyOVER);
            equipShopPanel["se_" + j]["buy_btn"].addEventListener(MouseEvent.MOUSE_OUT,BuyOUT);
            equipShopPanel["se_" + j]["buy_btn"].addEventListener(MouseEvent.CLICK,BuyEquip);
            equipShopPanel["se_" + j].mouseEnabled = false;
         }
         equipShopPanel["mySell"]["sell_btn"].addEventListener(MouseEvent.CLICK,sellEquip);
         for(j = 0; j < 6; j++)
         {
            equipShopPanel["se_" + j].stop();
         }
         if(Main.P1P2)
         {
            equipShopPanel["bag1"].visible = true;
            equipShopPanel["bag2"].visible = true;
            equipShopPanel["xbag1"].visible = true;
            equipShopPanel["xbag2"].visible = true;
            equipShopPanel["bag1"].addEventListener(MouseEvent.CLICK,changeToOne);
            equipShopPanel["bag2"].addEventListener(MouseEvent.CLICK,changeToTwo);
         }
         else
         {
            equipShopPanel["bag1"].visible = false;
            equipShopPanel["bag2"].visible = false;
            equipShopPanel["xbag1"].visible = false;
            equipShopPanel["xbag2"].visible = false;
         }
         equipShopPanel["NoMoney_mc"]["no_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         equipShopPanel["NoMoney_mc"]["yes_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         equipShopPanel["NoMoney_mc"]["addMoney_btn"].addEventListener(MouseEvent.CLICK,addMoney_btn);
         equipShopPanel["leftPage"].addEventListener(MouseEvent.CLICK,upPage);
         equipShopPanel["rightPage"].addEventListener(MouseEvent.CLICK,downPage);
         equipShopPanel["closeYP"].addEventListener(MouseEvent.CLICK,CloseYP);
         equipShopPanel["mySell"].visible = false;
         equipShopPanel["getZB"].visible = false;
         equipShopPanel["getZB"].mouseChildren = false;
         equipShopPanel["getZB"].mouseEnabled = false;
         equipShopPanel["NoMoney_mc"].visible = false;
         equipShopPanel["touming"].visible = false;
         equipShopPanel["goumaitishi"].mouseEnabled = false;
         equipShopPanel["npc_mc"].stop();
         allShow();
      }
      
      public static function removeListenerP1() : *
      {
         var j:uint = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            equipShopPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            equipShopPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            equipShopPanel["s1_" + i].removeEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(j = 0; j < 6; j++)
         {
            equipShopPanel["se_" + j]["buy_btn"].removeEventListener(MouseEvent.MOUSE_OVER,BuyOVER);
            equipShopPanel["se_" + j]["buy_btn"].removeEventListener(MouseEvent.MOUSE_OUT,BuyOUT);
            equipShopPanel["se_" + j]["buy_btn"].removeEventListener(MouseEvent.CLICK,BuyEquip);
         }
         equipShopPanel["mySell"]["sell_btn"].removeEventListener(MouseEvent.CLICK,sellEquip);
         for(j = 0; j < 6; j++)
         {
            equipShopPanel["se_" + j].stop();
         }
         if(Main.P1P2)
         {
            equipShopPanel["bag1"].removeEventListener(MouseEvent.CLICK,changeToOne);
            equipShopPanel["bag2"].removeEventListener(MouseEvent.CLICK,changeToTwo);
         }
         equipShopPanel["leftPage"].removeEventListener(MouseEvent.CLICK,upPage);
         equipShopPanel["rightPage"].removeEventListener(MouseEvent.CLICK,downPage);
         equipShopPanel["closeYP"].removeEventListener(MouseEvent.CLICK,CloseYP);
      }
      
      private static function closeNORMB(e:*) : void
      {
         equipShopPanel["NoMoney_mc"].visible = false;
      }
      
      private static function addMoney_btn(e:*) : void
      {
         Main.ChongZhi();
      }
      
      private static function BuyOVER(e:MouseEvent) : void
      {
         var num:int = int(e.target.parent.name.substr(3,2));
         var temp:int = num + (yeshu - 1) * 6 + 1;
         for(i in equipShopFactory.allData)
         {
            if(equipShopFactory.allData[i][1] == temp)
            {
               equipShopPanel["goumaitishi"].visible = true;
               equipShopPanel["goumaitishi"].x = equipShopPanel.mouseX + 10;
               equipShopPanel["goumaitishi"].y = equipShopPanel.mouseY;
               equipShopPanel["goumaitishi"]["rmb_txt"].text = "金币不足时可用" + equipShopFactory.allData[i][4] + "点卷购买";
               break;
            }
         }
      }
      
      private static function BuyOUT(e:MouseEvent) : void
      {
         equipShopPanel["goumaitishi"].visible = false;
      }
      
      private static function changeToOne(e:*) : *
      {
         myPlayer = Main.player1;
         allShow();
      }
      
      private static function changeToTwo(e:*) : *
      {
         myPlayer = Main.player2;
         allShow();
      }
      
      private static function upPage(e:*) : *
      {
         if(yeshu > 1)
         {
            --yeshu;
         }
         allShow();
      }
      
      private static function downPage(e:*) : *
      {
         if(yeshu < Math.ceil(equipShopPanel["se_0"].totalFrames / 6))
         {
            ++yeshu;
         }
         allShow();
      }
      
      public static function allShow() : void
      {
         var i:uint = 0;
         var temp:int = 0;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            equipShopPanel["npc_mc"].gotoAndStop(1);
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 3)
         {
            equipShopPanel["npc_mc"].gotoAndStop(2);
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 4)
         {
            equipShopPanel["npc_mc"].gotoAndStop(3);
         }
         if(Main.P1P2)
         {
            if(myPlayer == Main.player1)
            {
               equipShopPanel["bag1"].visible = false;
               equipShopPanel["bag2"].visible = true;
            }
            else
            {
               equipShopPanel["bag1"].visible = true;
               equipShopPanel["bag2"].visible = false;
            }
         }
         equipShopPanel["gold_txt"].text = myPlayer.getGold();
         equipShopPanel["kill_txt"].text = myPlayer.getKillPoint();
         equipShopPanel["rmb_txt"].text = Shop4399.moneyAll.getValue();
         myPlayer.getBag().zhengliBag();
         for(i = 0; i < 24; i++)
         {
            equipShopPanel["s1_" + i]["t_txt"].visible = false;
            if(myPlayer.getBag().getEquipFromBag(i) != null)
            {
               equipShopPanel["s1_" + i].gotoAndStop(myPlayer.getBag().getEquipFromBag(i).getFrame());
               equipShopPanel["s1_" + i].visible = true;
            }
            else
            {
               equipShopPanel["s1_" + i].visible = false;
            }
         }
         equipShopPanel["pageNum"].text = yeshu + "/" + Math.ceil(equipShopPanel["se_0"].totalFrames / 6);
         for(i = 0; i < 6; i++)
         {
            temp = i + (yeshu - 1) * 6 + 1;
            if(temp <= equipShopPanel["se_0"].totalFrames)
            {
               equipShopPanel["se_" + i].visible = true;
               equipShopPanel["se_" + i].gotoAndStop(temp);
            }
            else
            {
               equipShopPanel["se_" + i].visible = false;
            }
         }
      }
      
      private static function menuOpen(e:MouseEvent) : void
      {
         itemsTooltip.visible = false;
         clickObj = e.target as MovieClip;
         equipShopPanel.addChild(equipShopPanel["mySell"]);
         equipShopPanel["mySell"].visible = true;
         equipShopPanel["mySell"].x = clickObj.x + 50;
         equipShopPanel["mySell"].y = clickObj.y + 60;
      }
      
      private static function sellEquip(e:MouseEvent) : void
      {
         var num:int = int(clickObj.name.substr(3,2));
         var str:String = clickObj.name.substr(0,2);
         if(str == "s1")
         {
            myPlayer.addGold(myPlayer.getBag().getEquipFromBag(num).getPrice());
            myPlayer.getBag().delEquip(num);
         }
         equipShopPanel["mySell"].visible = false;
         allShow();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         equipShopPanel["mySell"].visible = false;
         equipShopPanel.addChild(itemsTooltip);
         var overNum:uint = uint(e.target.name.substr(3,2));
         var str:String = e.target.name.substr(0,2);
         itemsTooltip.x = equipShopPanel.mouseX;
         itemsTooltip.y = equipShopPanel.mouseY + 20;
         if(equipShopPanel.mouseX > 770)
         {
            itemsTooltip.x -= 250;
         }
         if(equipShopPanel.mouseY > 400)
         {
            itemsTooltip.y -= 400;
         }
         if(str == "s1")
         {
            if(myPlayer.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(myPlayer.getBag().getEquipFromBag(overNum),1);
            }
         }
         itemsTooltip.visible = true;
      }
      
      private static function tooltipClose(e:MouseEvent) : void
      {
         itemsTooltip.visible = false;
      }
      
      private static function BuyEquip(e:*) : *
      {
         var rdm:int = 0;
         var num:int = int(e.target.parent.name.substr(3,2));
         var temp:int = num + (yeshu - 1) * 6 + 1;
         arr = [];
         for(i in equipShopFactory.allData)
         {
            if(equipShopFactory.allData[i][1] == temp)
            {
               arr.push(equipShopFactory.allData[i]);
            }
         }
         rdm = int(Math.random() * (arr as Array).length);
         ee = EquipFactory.createEquipByID(arr[rdm][0]);
         if(myPlayer.getBag().backequipBagNum() > 0)
         {
            if(myPlayer.getGold() >= arr[rdm][2])
            {
               myPlayer.payGold(arr[rdm][2]);
               noRMBOK = true;
               Main.Save();
            }
            else if(Shop4399.moneyAll.getValue() >= arr[rdm][4])
            {
               Api_4399_All.BuyObj(arr[rdm][3]);
               equipShopOK = true;
               equipShopPanel["touming"].visible = true;
            }
            else
            {
               equipShopPanel["NoMoney_mc"].visible = true;
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
         }
         allShow();
      }
      
      public static function equipShopRMBOK() : *
      {
         if(equipShopOK)
         {
            myPlayer.getBag().addEquipBag(ee);
            equipShopPanel["getZB"].alpha = 1;
            equipShopPanel["getZB"].visible = true;
            equipShopPanel.addChild(equipShopPanel["getZB"]);
            equipShopPanel["getZB"]["showPic"].gotoAndStop(ee.getFrame());
            equipShopPanel.addEventListener(Event.ENTER_FRAME,jianbian);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功");
            equipShopOK = false;
            allShow();
            equipShopPanel["touming"].visible = false;
         }
      }
      
      public static function equipShopNoRMBOK() : *
      {
         if(noRMBOK)
         {
            myPlayer.getBag().addEquipBag(ee);
            equipShopPanel["getZB"].alpha = 1;
            equipShopPanel["getZB"].visible = true;
            equipShopPanel.addChild(equipShopPanel["getZB"]);
            equipShopPanel["getZB"]["showPic"].gotoAndStop(ee.getFrame());
            equipShopPanel.addEventListener(Event.ENTER_FRAME,jianbian);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功");
            noRMBOK = false;
            allShow();
            equipShopPanel["touming"].visible = false;
         }
      }
      
      private static function jianbian(e:*) : *
      {
         equipShopPanel["getZB"].alpha -= 0.02;
         if(equipShopPanel["getZB"].alpha < 0.02)
         {
            equipShopPanel["getZB"].visible = false;
            equipShopPanel.removeEventListener(Event.ENTER_FRAME,jianbian);
         }
      }
   }
}

