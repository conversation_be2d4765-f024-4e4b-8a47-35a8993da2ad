package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class Panel_XianHua extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      private static var _this:Panel_XianHua;
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var huaArr:Array;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_xianhua.swf";
      
      public static var objId_Arr:Array = [63181,63210,63235,63204,63455];
      
      public static var num_arr:Array = [1,5,5,1,1];
      
      public static var gailv_arr:Array = [25,55,94,97,100];
      
      public static var overTime:int = 20231010;
      
      public static var overTimeTxt:String = "至2023年8月27日";
      
      private var skin:MovieClip;
      
      private var buyok:Boolean = false;
      
      public function Panel_XianHua()
      {
         super();
         this.LoadSkin();
         _this = this;
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.mouseEnabled = false;
         itemsTooltip.visible = false;
      }
      
      private static function InitOpen() : *
      {
         var temp:Panel_XianHua = new Panel_XianHua();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function xianHuaUp() : *
      {
         var numXXXXX:* = undefined;
         if(Main.serverTime.getValue() > Panel_XianHua.overTime)
         {
            Panel_XianHua.huaArr = null;
            return;
         }
         if(!Panel_XianHua.huaArr)
         {
            Panel_XianHua.huaArr = [1,Panel_XianHua.rOBJ(),0];
         }
         else
         {
            numXXXXX = Panel_XianHua.huaArr[0];
            Panel_XianHua.huaArr[0] = numXXXXX + 1;
         }
         NewMC.Open("文字提示",Main._stage,480,400,60,0,true,1,"获得 情人节鲜花!");
      }
      
      public static function Open() : *
      {
         Main.DuoKai_Fun();
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
         }
      }
      
      public static function rOBJ() : int
      {
         var r:* = Math.random() * 100;
         for(var i:* = 0; i < gailv_arr.length; i++)
         {
            if(r < gailv_arr[i])
            {
               TiaoShi.txtShow("概率:" + i);
               if(huaArr && huaArr[2] < 15 && i < 4)
               {
                  ++huaArr[2];
                  if(huaArr[2] >= 15)
                  {
                     return 4;
                  }
               }
               return i;
            }
         }
         return 0;
      }
      
      public static function GetBuy() : *
      {
         if(Boolean(_this) && Boolean(_this.buyok))
         {
            _this.skin._BLACK_mc.visible = false;
            _this.buyok = false;
            NewMC.Open("文字提示",_this,460,400,60,0,true,1,"鲜花购买成功");
            ++Panel_XianHua.huaArr[0];
            Main.Save2();
            _this.Show();
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         Play_Interface.interfaceX["load_mc"].visible = false;
         var classRef:Class = loadData.getClass("xianHua") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         var classRef2:Class = loadData.getClass("情人节活动日期") as Class;
         var mcXX:MovieClip = new classRef2();
         this.skin.guize_mc.addChild(mcXX);
         var strXX:String = overTime;
         var str1:String = strXX.substr(0,4);
         var str2:String = strXX.substr(4,2);
         var str3:String = strXX.substr(6,2);
         trace("情人节活动日期>>",str1,str2,str3);
         mcXX.huodongriqi_txt.text = "活动日期:至" + str1 + "年" + str2 + "月" + str3 + "日";
         mcXX.x = -277;
         mcXX.y = -145;
         this.skin.close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.skin.song_btn.addEventListener(MouseEvent.CLICK,this.Song);
         this.skin.buy_btn.addEventListener(MouseEvent.CLICK,this.Buy);
         this.skin.guieZe_btn.addEventListener(MouseEvent.CLICK,this.Open2);
         this.skin.guize_mc.close_btn.addEventListener(MouseEvent.CLICK,this.Close2);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(e:* = null) : *
      {
         CloseX();
      }
      
      private function Open2(e:* = null) : *
      {
         this.skin.guize_mc.visible = true;
      }
      
      private function Close2(e:* = null) : *
      {
         this.skin.guize_mc.visible = false;
      }
      
      private function Show() : *
      {
         var num:int = 0;
         var mm:MovieClip = null;
         var numXX:* = undefined;
         if(!Panel_XianHua.huaArr)
         {
            Panel_XianHua.huaArr = [0,Panel_XianHua.rOBJ(),0];
         }
         this.skin._BLACK_mc.visible = false;
         num = huaArr[0] + 1;
         if(huaArr[0] > 9)
         {
            num = 10;
         }
         this.skin.pic_mc.gotoAndStop(num);
         this.skin.song_btn.visible = true;
         if(num < 10)
         {
            this.skin.song_btn.visible = false;
         }
         for(var i:uint = 0; i < 5; i++)
         {
            num = int(this.skin.guize_mc.getChildIndex(this.skin.guize_mc["s" + i]));
            mm = new Shop_picNEW();
            mm.x = this.skin.guize_mc["s" + i].x;
            mm.y = this.skin.guize_mc["s" + i].y;
            mm.name = "s" + i;
            this.skin.guize_mc.removeChild(this.skin.guize_mc["s" + i]);
            this.skin.guize_mc["s" + i] = mm;
            this.skin.guize_mc.addChild(mm);
            this.skin.guize_mc.setChildIndex(mm,num);
            numXX = OtherFactory.creatOther(objId_Arr[i]).getFrame();
            this.skin.guize_mc["s" + i].gotoAndStop(numXX);
            this.skin.guize_mc["s" + i].addEventListener(MouseEvent.MOUSE_OVER,this.s_over);
            this.skin.guize_mc["s" + i].addEventListener(MouseEvent.MOUSE_OUT,this.c_out);
            this.skin.guize_mc["s" + i].mouseChildren = false;
         }
      }
      
      public function Song(e:*) : *
      {
         var id:* = undefined;
         var name:* = undefined;
         var num:* = undefined;
         var i:* = undefined;
         var str:* = undefined;
         if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         else
         {
            id = objId_Arr[huaArr[1]];
            name = OtherFactory.creatOther(id).getName();
            num = num_arr[huaArr[1]];
            Panel_XianHua.huaArr[0] -= 9;
            Panel_XianHua.huaArr[1] = rOBJ();
            for(i = 0; i < num; i++)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(id));
            }
            str = "获得 " + name + " 物品已放入背包";
            NewMC.Open("文字提示",Main._stage,480,400,60,0,true,1,str);
            this.Show();
            Main.Save2();
         }
      }
      
      public function Buy(e:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 10)
         {
            this.buyok = true;
            this.skin._BLACK_mc.visible = true;
            Api_4399_All.BuyObj(284);
         }
         else
         {
            NewMC.Open("文字提示",_this,460,400,60,0,true,1,"点券不足");
         }
      }
      
      private function s_over(e:*) : *
      {
         this.skin.guize_mc.addChild(itemsTooltip);
         var overNum:uint = uint(int(e.target.name.substr(1,1)));
         var ppp:uint = 1;
         itemsTooltip.x = this.skin.guize_mc.mouseX;
         itemsTooltip.y = this.skin.guize_mc.mouseY - 100;
         if(this.skin.guize_mc.mouseX > 770)
         {
            itemsTooltip.x -= 250;
         }
         var id:* = objId_Arr[overNum];
         itemsTooltip.otherTooltip(OtherFactory.creatOther(id));
         itemsTooltip.visible = true;
      }
      
      private function c_out(e:*) : *
      {
         itemsTooltip.visible = false;
      }
   }
}

