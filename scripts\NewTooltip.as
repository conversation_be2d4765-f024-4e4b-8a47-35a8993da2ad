package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol149")]
   public dynamic class NewTooltip extends MovieClip
   {
      
      public var down_mc:MovieClip;
      
      public var explain:TextField;
      
      public var gemslot_mc:MovieClip;
      
      public var line0:MovieClip;
      
      public var line1:MovieClip;
      
      public var lv2_txt:TextField;
      
      public var lv_txt:TextField;
      
      public var middle_mc:MovieClip;
      
      public var name_txt:TextField;
      
      public var price:TextField;
      
      public var star_0:MovieClip;
      
      public var star_1:MovieClip;
      
      public var star_2:MovieClip;
      
      public var star_3:MovieClip;
      
      public var star_4:MovieClip;
      
      public var star_5:MovieClip;
      
      public var time_txt:TextField;
      
      public var txt_1:TextField;
      
      public var txt_2:TextField;
      
      public var txt_3:TextField;
      
      public var txt_4:TextField;
      
      public var txt_5:TextField;
      
      public var txt_6:TextField;
      
      public var txt_7:TextField;
      
      public var txt_8:TextField;
      
      public var txt_qhmax:TextField;
      
      public var txt_zf:TextField;
      
      public var type_txt:TextField;
      
      public function NewTooltip()
      {
         super();
      }
   }
}

