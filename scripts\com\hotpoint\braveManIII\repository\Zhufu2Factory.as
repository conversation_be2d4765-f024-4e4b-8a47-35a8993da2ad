package com.hotpoint.braveManIII.repository
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   
   public class Zhufu2Factory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function Zhufu2Factory()
      {
         super();
      }
      
      public static function creatZhufu2Factory() : *
      {
         var zf:Zhufu2Factory = new Zhufu2Factory();
         myXml = XMLAsset.createXML(Data2.zhuFu2);
         zf.creatFactory();
      }
      
      private function creatFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var a1:Number = NaN;
         var a2:Number = NaN;
         var a3:Number = NaN;
         var a4:Number = NaN;
         var a5:Number = NaN;
         var a6:Number = NaN;
         var a7:Number = NaN;
         var a8:Number = NaN;
         var a9:Number = NaN;
         var a10:Number = NaN;
         var a11:Number = NaN;
         var a12:Number = NaN;
         var data:Array = null;
         for each(property in myXml.祝福)
         {
            id = Number(property.等级);
            a1 = Number(property.成功率);
            a2 = Number(property.材料1);
            a3 = Number(property.材料2);
            a4 = Number(property.材料3);
            a5 = Number(property.材料4);
            a6 = Number(property.攻击);
            a7 = Number(property.防御);
            a8 = Number(property.暴击);
            a9 = Number(property.魔抗);
            a10 = Number(property.破魔);
            a11 = Number(property.生命);
            a12 = Number(property.魔力);
            data = [id,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12];
            allData[id] = data;
         }
      }
   }
}

