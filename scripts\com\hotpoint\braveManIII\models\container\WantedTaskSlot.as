package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.wantedTask.*;
   import com.hotpoint.braveManIII.repository.wantedTask.*;
   import src.*;
   import src.tool.*;
   
   public class WantedTaskSlot
   {
      
      private var _slot:Array = new Array();
      
      private var jl_Times:VT = VT.createVT(0);
      
      private var jl2_Times:VT = VT.createVT(0);
      
      private var jl3_Times:VT = VT.createVT(0);
      
      private var jl4_Times:VT = VT.createVT(0);
      
      private var jl5_Times:VT = VT.createVT(0);
      
      private var jl6_Times:VT = VT.createVT(0);
      
      private var jl7_Times:VT = VT.createVT(0);
      
      private var jl8_Times:VT = VT.createVT(0);
      
      private var jl9_Times:VT = VT.createVT(0);
      
      private var jl10_Times:VT = VT.createVT(0);
      
      public function WantedTaskSlot()
      {
         super();
      }
      
      public static function creatWantedTaskSlot() : WantedTaskSlot
      {
         var wts:WantedTaskSlot = new WantedTaskSlot();
         for(var i:int = 0; i < 30; i++)
         {
            wts._slot[i] = null;
         }
         wts.addToSlot(WantedFactory.creatWantedTask(1));
         wts.addToSlot(WantedFactory.creatWantedTask(2));
         wts.addToSlot(WantedFactory.creatWantedTask(3));
         wts.addToSlot(WantedFactory.creatWantedTask(4));
         wts.addToSlot(WantedFactory.creatWantedTask(5));
         wts.addToSlot(WantedFactory.creatWantedTask(6));
         wts.addToSlot(WantedFactory.creatWantedTask(7));
         wts.addToSlot(WantedFactory.creatWantedTask(8));
         wts.addToSlot(WantedFactory.creatWantedTask(9));
         wts.addToSlot(WantedFactory.creatWantedTask(10));
         wts.addToSlot(WantedFactory.creatWantedTask(11));
         wts.addToSlot(WantedFactory.creatWantedTask(12));
         wts.addToSlot(WantedFactory.creatWantedTask(13));
         wts.addToSlot(WantedFactory.creatWantedTask(14));
         wts.addToSlot(WantedFactory.creatWantedTask(15));
         wts.addToSlot(WantedFactory.creatWantedTask(16));
         wts.addToSlot(WantedFactory.creatWantedTask(17));
         wts.addToSlot(WantedFactory.creatWantedTask(18));
         wts.addToSlot(WantedFactory.creatWantedTask(19));
         wts.addToSlot(WantedFactory.creatWantedTask(20));
         wts.addToSlot(WantedFactory.creatWantedTask(21));
         wts.addToSlot(WantedFactory.creatWantedTask(22));
         wts.addToSlot(WantedFactory.creatWantedTask(23));
         wts.addToSlot(WantedFactory.creatWantedTask(24));
         wts.addToSlot(WantedFactory.creatWantedTask(25));
         wts.addToSlot(WantedFactory.creatWantedTask(26));
         wts.addToSlot(WantedFactory.creatWantedTask(27));
         wts.addToSlot(WantedFactory.creatWantedTask(28));
         wts.addToSlot(WantedFactory.creatWantedTask(29));
         wts.addToSlot(WantedFactory.creatWantedTask(30));
         return wts;
      }
      
      public static function JLTimes10XX() : Number
      {
         Main.wts.jl10_Times.setValue(0);
      }
      
      public function get slot() : Array
      {
         return this._slot;
      }
      
      public function set slot(value:Array) : void
      {
         this._slot = value;
      }
      
      public function get Times() : VT
      {
         return this.jl_Times;
      }
      
      public function set Times(value:VT) : void
      {
         this.jl_Times = value;
      }
      
      public function get Times2() : VT
      {
         return this.jl2_Times;
      }
      
      public function set Times2(value:VT) : void
      {
         this.jl2_Times = value;
      }
      
      public function get Times3() : VT
      {
         return this.jl3_Times;
      }
      
      public function set Times3(value:VT) : void
      {
         this.jl3_Times = value;
      }
      
      public function get Times4() : VT
      {
         return this.jl4_Times;
      }
      
      public function set Times4(value:VT) : void
      {
         this.jl4_Times = value;
      }
      
      public function get Times5() : VT
      {
         return this.jl5_Times;
      }
      
      public function set Times5(value:VT) : void
      {
         this.jl5_Times = value;
      }
      
      public function get Times6() : VT
      {
         return this.jl6_Times;
      }
      
      public function set Times6(value:VT) : void
      {
         this.jl6_Times = value;
      }
      
      public function get Times7() : VT
      {
         return this.jl7_Times;
      }
      
      public function set Times7(value:VT) : void
      {
         this.jl7_Times = value;
      }
      
      public function get Times8() : VT
      {
         return this.jl8_Times;
      }
      
      public function set Times8(value:VT) : void
      {
         this.jl8_Times = value;
      }
      
      public function get Times9() : VT
      {
         return this.jl9_Times;
      }
      
      public function set Times9(value:VT) : void
      {
         this.jl9_Times = value;
      }
      
      public function get Times10() : VT
      {
         return this.jl10_Times;
      }
      
      public function set Times10(value:VT) : void
      {
         this.jl10_Times = value;
      }
      
      public function getWantedTaskFromSlot(num:int) : WantedTask
      {
         if(this._slot[num] == null)
         {
            this.addToSlot(WantedFactory.creatWantedTask(num + 1));
         }
         else if(Boolean(this._slot[3]) && Boolean(this._slot[4]) && this._slot[3].getFrame() == this._slot[4].getFrame())
         {
            this._slot[3] = WantedFactory.creatWantedTask(4);
         }
         if(this._slot[num] != null)
         {
            return this._slot[num];
         }
         return null;
      }
      
      public function addToSlot(value:WantedTask) : Boolean
      {
         for(var i:uint = 0; i < 30; i++)
         {
            if(this._slot[i] == null)
            {
               this._slot[i] = value;
               return true;
            }
         }
         return false;
      }
      
      public function delSlot(num:Number) : WantedTask
      {
         var wt:WantedTask = null;
         if(this._slot[num] != null)
         {
            wt = this._slot[num];
            this._slot[num] = null;
         }
         return wt;
      }
      
      public function wantedTaskComplete() : *
      {
         for(var i:int = 0; i < 30; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getState() == 1)
               {
                  (this._slot[i] as WantedTask).setState(2);
                  (this._slot[i] as WantedTask).addTimes();
               }
            }
         }
      }
      
      public function wantedTaskTimesLV1() : Number
      {
         var count:int = 0;
         for(var i:int = 0; i < 3; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV2() : Number
      {
         var count:int = 0;
         for(var i:int = 3; i < 6; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV3() : Number
      {
         var count:int = 0;
         for(var i:int = 6; i < 9; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV4() : Number
      {
         var count:int = 0;
         for(var i:int = 9; i < 12; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV5() : Number
      {
         var count:int = 0;
         for(var i:int = 12; i < 15; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV6() : Number
      {
         var count:int = 0;
         for(var i:int = 15; i < 18; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV7() : Number
      {
         var count:int = 0;
         for(var i:int = 18; i < 21; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV8() : Number
      {
         var count:int = 0;
         for(var i:int = 21; i < 24; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV9() : Number
      {
         var count:int = 0;
         for(var i:int = 24; i < 27; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function wantedTaskTimesLV10() : Number
      {
         var count:int = 0;
         for(var i:int = 27; i < 30; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getTimes() > 0)
               {
                  count += (this._slot[i] as WantedTask).nowTimes();
               }
            }
         }
         return count;
      }
      
      public function addJLTimes() : *
      {
         this.jl_Times.setValue(this.jl_Times.getValue() + 1);
      }
      
      public function addJLTimes2() : *
      {
         this.jl2_Times.setValue(this.jl2_Times.getValue() + 1);
      }
      
      public function addJLTimes3() : *
      {
         this.jl3_Times.setValue(this.jl3_Times.getValue() + 1);
      }
      
      public function addJLTimes4() : *
      {
         this.jl4_Times.setValue(this.jl4_Times.getValue() + 1);
      }
      
      public function addJLTimes5() : *
      {
         this.jl5_Times.setValue(this.jl5_Times.getValue() + 1);
      }
      
      public function addJLTimes6() : *
      {
         this.jl6_Times.setValue(this.jl6_Times.getValue() + 1);
      }
      
      public function addJLTimes7() : *
      {
         this.jl7_Times.setValue(this.jl7_Times.getValue() + 1);
      }
      
      public function addJLTimes8() : *
      {
         this.jl8_Times.setValue(this.jl8_Times.getValue() + 1);
      }
      
      public function addJLTimes9() : *
      {
         this.jl9_Times.setValue(this.jl9_Times.getValue() + 1);
      }
      
      public function addJLTimes10() : *
      {
         this.jl10_Times.setValue(this.jl10_Times.getValue() + 1);
      }
      
      public function getJLTimes() : Number
      {
         return this.jl_Times.getValue();
      }
      
      public function getJLTimes2() : Number
      {
         return this.jl2_Times.getValue();
      }
      
      public function getJLTimes3() : Number
      {
         return this.jl3_Times.getValue();
      }
      
      public function getJLTimes4() : Number
      {
         return this.jl4_Times.getValue();
      }
      
      public function getJLTimes5() : Number
      {
         return this.jl5_Times.getValue();
      }
      
      public function getJLTimes6() : Number
      {
         return this.jl6_Times.getValue();
      }
      
      public function getJLTimes7() : Number
      {
         return this.jl7_Times.getValue();
      }
      
      public function getJLTimes8() : Number
      {
         return this.jl8_Times.getValue();
      }
      
      public function getJLTimes9() : Number
      {
         return this.jl9_Times.getValue();
      }
      
      public function getJLTimes10() : Number
      {
         return this.jl10_Times.getValue();
      }
      
      public function setWantedTaskSlot(num:Number) : *
      {
         if(this._slot[num] != null)
         {
            (this._slot[num] as WantedTask).setState(1);
         }
      }
      
      public function getAccess() : Number
      {
         for(var i:int = 0; i < 30; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getState() == 1 || (this._slot[i] as WantedTask).getState() == 2)
               {
                  return i;
               }
            }
         }
         return -1;
      }
      
      public function getISCD() : Number
      {
         for(var i:int = 0; i < 30; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getState() == 3)
               {
                  return i;
               }
            }
         }
         return -1;
      }
      
      public function delCD(num:Number) : *
      {
         if(this._slot[num] != null)
         {
            if((this._slot[num] as WantedTask).getState() == 3)
            {
               (this._slot[num] as WantedTask).setState(0);
            }
         }
      }
      
      public function getBoss() : Number
      {
         for(var i:int = 0; i < 30; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as WantedTask).getState() == 1)
               {
                  if(Main.gameNum.getValue() == (this._slot[i] as WantedTask).getMap1() || (this._slot[i] as WantedTask).getMap2() == Main.gameNum.getValue() || (this._slot[i] as WantedTask).getMap3() == Main.gameNum.getValue())
                  {
                     return (this._slot[i] as WantedTask).getName();
                  }
               }
            }
         }
         return -1;
      }
   }
}

