package src.tool
{
   import com.adobe.serialization.json.*;
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol94")]
   public class JiFenLingQue2 extends MovieClip
   {
      
      private static var _this:JiFenLingQue2;
      
      private static var logYN:Boolean = false;
      
      private static var urlLoader:URLLoader = new URLLoader();
      
      private static var phpUrl:URLRequest = new URLRequest("http://my.4399.com/events/2014/fiveyear/login-counter");
      
      private static var dayNum:int = 0;
      
      public var close_btn:SimpleButton;
      
      public var day1:MovieClip;
      
      public var day2:MovieClip;
      
      public var day3:MovieClip;
      
      public var lingQu_btn:SimpleButton;
      
      public function JiFenLingQue2()
      {
         super();
         _this = this;
         this.visible = false;
      }
      
      public static function DengLuUp() : *
      {
         TiaoShi.txtShow("连续登录调用");
         if(Boolean(logYN) || TeShuHuoDong.TeShuHuoDongArr[30] >= 1)
         {
            return;
         }
         phpUrl.method = URLRequestMethod.POST;
         var uv:URLVariables = new URLVariables();
         uv.app_id = 38;
         uv.game_id = 10;
         TiaoShi.txtShow("Main.serverTimeStr = " + Main.serverTimeStr);
         uv.time = String(Date.parse(Main.serverTimeStr)).substr(0,10);
         TiaoShi.txtShow("uv.time = " + uv.time);
         uv.uid = Main.userId;
         uv.uri = "/events/2014/fiveyear/login-counter";
         var tempStr:* = uv.app_id + "||" + uv.game_id + "||" + uv.time + "||" + uv.uid + "||" + uv.uri + "||" + "919e2199989011d08e1c37f03017a27b";
         var tokenStr:String = MD5contrast.GetScoreMD5(tempStr);
         uv.token = tokenStr;
         phpUrl.data = uv;
         urlLoader.load(phpUrl);
         urlLoader.addEventListener(Event.COMPLETE,completeHandler_All);
         logYN = true;
      }
      
      private static function completeHandler_All(e:Event) : void
      {
         var str:* = (e.currentTarget as URLLoader).data;
         TiaoShi.txtShow("连续登录时间 返回: " + str);
         var objX:Object = JSONs.decode(str,true);
         if(objX.code == 100)
         {
            str = "连续登录: " + objX.result + "天";
         }
         else if(objX.code == 1)
         {
            str = "未知错误";
         }
         else if(objX.code == 2)
         {
            str = "参数错误";
         }
         else if(objX.code == 3)
         {
            str = "没有权限";
         }
         else if(objX.code == 61)
         {
            str = "Token过期";
         }
         else if(objX.code == 62)
         {
            str = "Token错误";
         }
         else if(objX.code == 63)
         {
            str = "需要APP_ID";
         }
         else if(objX.code == 64)
         {
            str = "无法识别APP_ID";
         }
         else if(objX.code == 65)
         {
            str = "请求方式错误";
         }
         else
         {
            str = "未知错误2";
         }
         TiaoShi.txtShow("连续登录时间 = " + str);
         _this.Show(objX.result);
      }
      
      public function Show(num:int = 0) : *
      {
         this.visible = true;
         dayNum = num;
         for(var i:int = 1; i < 4; i++)
         {
            this["day" + i].gotoAndStop(1);
            if(i <= num)
            {
               this["day" + i].gotoAndStop(2);
            }
         }
         this.lingQu_btn.addEventListener(MouseEvent.CLICK,this.LingQu_fun);
         this.close_btn.addEventListener(MouseEvent.CLICK,this.Close_fun);
      }
      
      private function LingQu_fun(e:*) : *
      {
         var request:URLRequest = null;
         if(dayNum >= 3)
         {
            request = new URLRequest("http://my.4399.com/events/2014/fiveyear/login");
            navigateToURL(request,"_blank");
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"需连续登录三天方可领取");
         }
      }
      
      private function Close_fun(e:*) : *
      {
         this.x = this.y = -5000;
         this.visible = false;
      }
   }
}

