package com.hotpoint.braveManIII.repository.monsterCard
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.monsterCard.MonsterCard;
   import src.*;
   import src.tool.*;
   
   public class MonsterFactory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function MonsterFactory()
      {
         super();
      }
      
      public static function creatMonsterFactory() : *
      {
         var mst:MonsterFactory = new MonsterFactory();
         myXml = XMLAsset.createXML(Data2.monster);
         mst.creatMonsterFactory();
      }
      
      public static function getMonsterById(id:Number) : MonsterBasicData
      {
         var monsterData:MonsterBasicData = null;
         var data:MonsterBasicData = null;
         for each(data in allData)
         {
            if(data.getId() == id)
            {
               monsterData = data;
            }
         }
         if(monsterData == null)
         {
            TiaoShi.txtShow("getMonsterById 数据找不到1");
         }
         return monsterData;
      }
      
      public static function getMonsterByType(type:Number) : MonsterBasicData
      {
         var monsterData:MonsterBasicData = null;
         var data:MonsterBasicData = null;
         if(type == 207 || type == 208 || type == 209)
         {
            type = 207;
         }
         else if(type == 210 || type == 211 || type == 212)
         {
            type = 210;
         }
         else if(type == 213 || type == 214 || type == 215)
         {
            type = 213;
         }
         else if(type == 216 || type == 217 || type == 218)
         {
            type = 216;
         }
         else if(type == 222 || type == 223 || type == 224)
         {
            type = 222;
         }
         else if(type == 234 || type == 235 || type == 236)
         {
            type = 234;
         }
         else if(type == 237 || type == 238 || type == 239)
         {
            type = 237;
         }
         for each(data in allData)
         {
            if(data.getType() == type)
            {
               monsterData = data;
            }
         }
         if(monsterData == null)
         {
            TiaoShi.txtShow("getMonsterByType 数据找不到2 id = " + type);
         }
         return monsterData;
      }
      
      public static function getId(id:Number) : Number
      {
         return getMonsterById(id).getId();
      }
      
      public static function getName(id:Number) : String
      {
         return getMonsterById(id).getName();
      }
      
      public static function getType(id:Number) : Number
      {
         return getMonsterById(id).getType();
      }
      
      public static function getFrame(id:Number) : Number
      {
         return getMonsterById(id).getFrame();
      }
      
      public static function getFrame2(id:Number) : Number
      {
         return getMonsterById(id).getFrame2();
      }
      
      public static function getIntroduction(id:Number) : String
      {
         return getMonsterById(id).getIntroduction();
      }
      
      public static function getAttup(id:Number) : Number
      {
         return getMonsterById(id).getAttup();
      }
      
      public static function getDefup(id:Number) : Number
      {
         return getMonsterById(id).getDefup();
      }
      
      public static function getCritup(id:Number) : Number
      {
         return getMonsterById(id).getCritup();
      }
      
      public static function getHpup(id:Number) : Number
      {
         return getMonsterById(id).getHpup();
      }
      
      public static function getMpup(id:Number) : Number
      {
         return getMonsterById(id).getMpup();
      }
      
      public static function creatMonster(id:Number) : MonsterCard
      {
         return getMonsterById(id).creatMonsterCard();
      }
      
      public static function creatMonsterType(type:Number) : MonsterCard
      {
         if(getMonsterByType(type))
         {
            return getMonsterByType(type).creatMonsterCard();
         }
         return null;
      }
      
      private function creatMonsterFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var name:String = null;
         var type:Number = NaN;
         var frame:Number = NaN;
         var frame2:Number = NaN;
         var introduction:String = null;
         var attup:Number = NaN;
         var defup:Number = NaN;
         var critup:Number = NaN;
         var hpup:Number = NaN;
         var mpup:Number = NaN;
         var data:MonsterBasicData = null;
         for each(property in myXml.怪物图鉴)
         {
            id = Number(property.编号);
            name = String(property.名字);
            type = Number(property.类型);
            frame = Number(property.帧数);
            frame2 = Number(property.帧数二);
            introduction = String(property.描述);
            attup = Number(property.攻击成长);
            defup = Number(property.防御成长);
            critup = Number(property.暴击成长);
            hpup = Number(property.生命成长);
            mpup = Number(property.魔法成长);
            data = MonsterBasicData.creatMonsterBasicData(id,type,name,frame,frame2,introduction,attup,defup,critup,hpup,mpup);
            allData.push(data);
         }
      }
   }
}

