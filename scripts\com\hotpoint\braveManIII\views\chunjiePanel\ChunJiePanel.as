package com.hotpoint.braveManIII.views.chunjiePanel
{
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class ChunJiePanel extends MovieClip
   {
      
      public static var fpp:ChunJiePanel;
      
      public static var cjPanel:MovieClip;
      
      private static var loadData:ClassLoader;
      
      public static var varXX:int = 3;
      
      public static var overTime:int = 20231010;
      
      public static var saveNew:int = 0;
      
      public static var saveArr_2022:Array = [0,0];
      
      public static var saveArr2_2022:Array = [1,0,0,0];
      
      public static var max1:int = 5;
      
      public static var max2:int = 20;
      
      public static var save_OK:Boolean = false;
      
      public static var CJOK:Boolean = false;
      
      private static var loadName:String = "Chunjie_v1720.swf";
      
      private static var OpenYN:Boolean = false;
      
      public static var jifenTime:int = 0;
      
      public static var start:int = 0;
      
      public static var end:int = 0;
      
      public function ChunJiePanel()
      {
         super();
      }
      
      private static function LoadSkin() : *
      {
         if(!cjPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("ChunjieShow") as Class;
         cjPanel = new classRef();
         fpp.addChild(cjPanel);
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         fpp = new ChunJiePanel();
         LoadSkin();
         Main._stage.addChild(fpp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         fpp = new ChunJiePanel();
         Main._stage.addChild(fpp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         var strXX:String = null;
         var str1:String = null;
         var str2:String = null;
         var str3:String = null;
         Init();
         trace("马刷 >>>>>>>>>>>>>>>>>>",saveArr_2022);
         Main.allClosePanel();
         if(cjPanel)
         {
            Main.stopXX = true;
            fpp.x = 0;
            fpp.y = 0;
            addListenerP1();
            fpp.visible = true;
            strXX = overTime;
            str1 = strXX.substr(0,4);
            str2 = strXX.substr(4,2);
            str3 = strXX.substr(6,2);
            cjPanel.huodongriqi_txt.text = "活动日期:至" + str1 + "年" + str2 + "月" + str3 + "日";
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Init() : *
      {
         if(saveNew != varXX)
         {
            trace("马刷活动重置",saveNew,saveArr_2022);
            saveArr_2022 = [0,0];
            saveArr2_2022 = [1,0,0,0];
            saveNew = varXX;
         }
      }
      
      public static function close() : void
      {
         if(cjPanel)
         {
            fpp.visible = false;
            Main.stopXX = false;
            removeListenerP1();
         }
         else
         {
            InitClose();
         }
      }
      
      public static function closeCP(e:*) : *
      {
         close();
      }
      
      public static function initJiFen() : *
      {
         saveArr2_2022[1] = saveArr2_2022[2] = saveArr2_2022[3] = 0;
      }
      
      public static function removeListenerP1() : *
      {
      }
      
      public static function addListenerP1() : *
      {
         if(saveArr2_2022[0] < Main.serverTime.getValue())
         {
            initJiFen();
            saveArr2_2022[0] = Main.serverTime.getValue();
         }
         cjPanel["baiju"].stop();
         cjPanel["lingqu"].visible = false;
         cjPanel["wenben"].visible = false;
         cjPanel["lingqu"].addEventListener(MouseEvent.CLICK,lingQu);
         cjPanel["shua"].addEventListener(MouseEvent.CLICK,shua);
         cjPanel["shua"].addEventListener(MouseEvent.MOUSE_OVER,shuaOVER);
         cjPanel["shua"].addEventListener(MouseEvent.MOUSE_OUT,shuaOUT);
         cjPanel["shuaRMB"].addEventListener(MouseEvent.CLICK,shuaRMB);
         cjPanel["close"].addEventListener(MouseEvent.CLICK,closeCP);
         show();
      }
      
      public static function show() : *
      {
         cjPanel["piaochong"]["jindu"].text = saveArr_2022[0];
         cjPanel["mashua"].text = "魔法马刷:" + saveArr2_2022[3] + "个";
         cjPanel["dianjuan"].text = "点卷:" + Shop4399.moneyAll.getValue();
         cjPanel["piaochong"].x = 405 + saveArr_2022[0] - saveArr_2022[0] / 10;
         if(saveArr_2022[0] < 50)
         {
            cjPanel["baiju"].gotoAndStop(1);
         }
         else if(saveArr_2022[0] >= 50 && saveArr_2022[0] < 100)
         {
            cjPanel["baiju"].gotoAndStop(2);
         }
         else if(saveArr_2022[0] >= 100 && saveArr_2022[0] < 150)
         {
            cjPanel["baiju"].gotoAndStop(3);
         }
         else if(saveArr_2022[0] >= 150 && saveArr_2022[0] < 200)
         {
            cjPanel["baiju"].gotoAndStop(4);
         }
         else if(saveArr_2022[0] >= 200 && saveArr_2022[0] < 250)
         {
            cjPanel["baiju"].gotoAndStop(5);
         }
         else if(saveArr_2022[0] >= 250 && saveArr_2022[0] < 300)
         {
            cjPanel["baiju"].gotoAndStop(6);
         }
         else if(saveArr_2022[0] >= 300 && saveArr_2022[0] < 350)
         {
            cjPanel["baiju"].gotoAndStop(7);
         }
         else
         {
            cjPanel["baiju"].gotoAndStop(8);
         }
         var numXXX:int = saveArr_2022[0] / 25;
         if(numXXX > saveArr_2022[1])
         {
            cjPanel["lingqu"].visible = true;
         }
         else
         {
            cjPanel["lingqu"].visible = false;
         }
      }
      
      public static function shuaRMB(e:*) : *
      {
         if(saveArr_2022[0] >= 350)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"清洁度已满！");
            return;
         }
         if(Shop4399.moneyAll.getValue() >= 9)
         {
            Api_4399_All.BuyObj(InitData.chunjie143.getValue());
            CJOK = true;
            cjPanel["shuaRMB"].visible = false;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
         }
      }
      
      public static function chunjieOK() : *
      {
         if(CJOK)
         {
            ++saveArr_2022[0];
            cjPanel["dianjuan"].text = "点卷:" + Shop4399.moneyAll.getValue();
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功");
            CJOK = false;
            cjPanel["shuaRMB"].visible = true;
            show();
         }
      }
      
      public static function shuaOVER(e:*) : *
      {
         cjPanel["wenben"]["txt_1"].text = "在线获得:" + saveArr2_2022[1] + "/" + max1;
         cjPanel["wenben"]["txt_2"].text = "打boss获得:" + saveArr2_2022[2] + "/" + max2;
         cjPanel["wenben"].visible = true;
      }
      
      public static function shuaOUT(e:*) : *
      {
         cjPanel["wenben"].visible = false;
      }
      
      public static function shua(e:*) : *
      {
         if(saveArr2_2022[3] > 0)
         {
            ++saveArr_2022[0];
            --saveArr2_2022[3];
         }
         show();
      }
      
      public static function rdmLQ() : *
      {
         var rdm:int = 0;
         var i:int = 0;
         if(Main.player1.getBag().backSuppliesBagNum() >= 2 && Main.player1.getBag().backGemBagNum() >= 1 && Main.player1.getBag().backOtherBagNum() >= 1)
         {
            rdm = int(Math.random() * 9);
            if(rdm == 0)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + SuppliesFactory.getSuppliesById(21223).getName());
            }
            else if(rdm == 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63204).getName());
            }
            else if(rdm == 2)
            {
               Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(33213).getName());
            }
            else if(rdm == 3)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63138).getName());
            }
            else if(rdm == 4)
            {
               for(i = 0; i < 10; i++)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63210).getName());
            }
            else if(rdm == 5)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + SuppliesFactory.getSuppliesById(21221).getName());
            }
            else if(rdm == 6)
            {
               Main.player1.getBag().addGemBag(GemFactory.creatGemById(33511));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(33511).getName());
            }
            else if(rdm == 7)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63106));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63106).getName());
            }
            else if(rdm == 8)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63100).getName());
            }
            ++saveArr_2022[1];
            show();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
      }
      
      public static function lingQu(e:*) : *
      {
         cjPanel["lingqu"].visible = false;
         save_OK = true;
         Main.Save();
         var numXXX:int = (350 - saveArr_2022[0]) / 25;
         trace("马刷领取 >>>>>>>>>",numXXX,"?",saveArr_2022);
      }
      
      public static function lingQuOK() : *
      {
         if(save_OK)
         {
            if(saveArr_2022[1] == 13)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63266));
                  ++saveArr_2022[1];
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63266).getName());
                  show();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else
            {
               rdmLQ();
            }
            save_OK = false;
         }
      }
      
      public static function addZaiXianJiFen(e:* = null) : *
      {
         var mydate:Date = null;
         var hour:* = undefined;
         var minute:* = undefined;
         var second:* = undefined;
         ++jifenTime;
         if(jifenTime == 2)
         {
            mydate = new Date();
            hour = mydate.getHours();
            minute = mydate.getMinutes();
            second = mydate.getSeconds();
         }
         if(jifenTime / 12960 > 1)
         {
            mydate = new Date();
            hour = mydate.getHours();
            minute = mydate.getMinutes();
            second = mydate.getSeconds();
            end = (hour * 3600 + minute * 60 + second) * 27;
            if(jifenTime - (end - start) < 600 * 27)
            {
               if(saveArr2_2022[1] < max1 && Main.serverTime.getValue() <= overTime)
               {
                  saveArr2_2022[1] += 1;
                  saveArr2_2022[3] += 1;
               }
            }
            jifenTime = 1;
         }
      }
      
      public static function addShaGuaiJiFen() : *
      {
         if(saveArr2_2022[2] < max2 && Main.serverTime.getValue() <= overTime)
         {
            saveArr2_2022[2] += 1;
            saveArr2_2022[3] += 1;
         }
      }
   }
}

