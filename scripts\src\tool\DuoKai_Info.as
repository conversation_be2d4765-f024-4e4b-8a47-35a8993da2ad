package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol6591")]
   public class DuoKai_Info extends MovieClip
   {
      
      private static var _this:DuoKai_Info;
      
      public static var _noSave:Boolean = false;
      
      public var _txt:TextField;
      
      public function DuoKai_Info()
      {
         super();
      }
      
      public static function Open(num:int = 1, str:String = "") : *
      {
         var _this:* = new DuoKai_Info();
         Main._stage.addChild(_this);
         _this.gotoAndStop(num);
         _noSave = true;
         _this._txt.text = "uid:" + Main.userId + "/" + str;
         var intervalId:uint = uint(setTimeout(TimeFun,2000));
      }
      
      public static function TimeFun() : void
      {
         Main._stage.frameRate = 0;
      }
   }
}

