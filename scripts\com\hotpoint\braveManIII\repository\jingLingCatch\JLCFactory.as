package com.hotpoint.braveManIII.repository.jingLingCatch
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   
   public class JLCFactory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function JLCFactory()
      {
         super();
      }
      
      public static function creatJLCFactory() : *
      {
         var es:JLCFactory = new JLCFactory();
         myXml = XMLAsset.createXML(Data2.jinglingbuzhuo);
         es.creatFactory();
      }
      
      private function creatFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var bossId:Number = NaN;
         var jinglingId:Number = NaN;
         var gailv:Number = NaN;
         var guanka:Number = NaN;
         var vipGL:Number = NaN;
         var data:Array = null;
         for each(property in myXml.精灵捕捉)
         {
            bossId = Number(property.编号);
            jinglingId = Number(property.精灵编号);
            gailv = Number(property.概率);
            guanka = Number(property.关卡);
            vipGL = Number(property.增加概率);
            data = [bossId,jinglingId,gailv,guanka,vipGL];
            allData.push(data);
         }
      }
   }
}

