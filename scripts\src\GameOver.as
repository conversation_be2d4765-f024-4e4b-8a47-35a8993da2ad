package src
{
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4265")]
   public class GameOver extends MovieClip
   {
      
      public static var noFuHuo:Boolean = false;
      
      public var back_btn:SimpleButton;
      
      public var re_btn:SimpleButton;
      
      public function GameOver()
      {
         super();
         var gNum:int = int(Main.gameNum.getValue());
         this.back_btn.addEventListener(MouseEvent.CLICK,this.goHome);
         if(gNum == 2015 || gNum == 888 || gNum > 5000 && gNum < 5100)
         {
            this.re_btn.visible = false;
         }
         else
         {
            this.re_btn.visible = true;
         }
         this.re_btn.addEventListener(MouseEvent.CLICK,this.RePlay);
         AchData.gkOk();
         TaskData.isOk();
         noFuHuo = true;
         JingLing.QingChuLengQue();
      }
      
      private function goHome(e:* = null) : *
      {
         Main._stage.frameRate = 27;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Player.一起信春哥();
         this.visible = false;
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         removeEventListener(MouseEvent.CLICK,this.goHome);
         removeEventListener(MouseEvent.CLICK,this.RePlay);
         Main._this.Loading();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         noFuHuo = false;
      }
      
      private function RePlay(e:*) : *
      {
         var num:int = 0;
         Player.skillYAO = 0;
         Main._stage.frameRate = 27;
         Main.gameNum2.setValue(1);
         Player.一起信春哥();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         this.parent.removeChild(this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         if(Main.gameNum.getValue() >= 18 && Main.gameNum.getValue() < 50)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.sel_GuanKaXX_Open();
         }
         else if(Main.gameNum.getValue() == 17)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 1000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 2000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(0);
            Main._this.GameStart();
            CaiYaoPanel.open();
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            SelMap.Open(0,0,3,3);
            SelMap.selMapX.Sel_nanDu_mc3.Open();
         }
         else if(GameData.gameLV == 5)
         {
            num = 1;
            if(Main.gameNum.getValue() <= 9)
            {
               num = 1;
            }
            else if(Main.gameNum.getValue() <= 16)
            {
               num = 2;
            }
            else if(Main.gameNum.getValue() <= 70)
            {
               num = 3;
            }
            SelMap.Open(0,0,3,num);
         }
         else
         {
            removeEventListener(MouseEvent.CLICK,this.goHome);
            removeEventListener(MouseEvent.CLICK,this.RePlay);
            Main._this.GameStart();
         }
         noFuHuo = false;
      }
   }
}

