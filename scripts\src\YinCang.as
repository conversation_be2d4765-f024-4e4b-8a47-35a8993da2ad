package src
{
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol73")]
   public class Yin<PERSON>ang extends MovieClip
   {
      
      private static var _this:YinCang;
      
      public var P1_1_btn:SimpleButton;
      
      public var P1_1_txt:TextField;
      
      public var P1_2_btn:SimpleButton;
      
      public var P1_2_txt:TextField;
      
      public var P2_1_btn:SimpleButton;
      
      public var P2_1_txt:TextField;
      
      public var P2_2_btn:SimpleButton;
      
      public var P2_2_txt:TextField;
      
      public var X1_1_btn:SimpleButton;
      
      public var X1_2_btn:SimpleButton;
      
      public var X2_1_btn:SimpleButton;
      
      public var X2_2_btn:SimpleButton;
      
      public var close_btn:SimpleButton;
      
      public var load_mc:游戏初始化;
      
      private var arr:Array = [[20000,100],[20000,100],[40000,100],[40000,100],[60000,150],[100000,200],[150000,250],[200000,300],[250000,400],[300000,500]];
      
      private var arr2:Array = [[30,167],[40,168],[50,169],[50,169]];
      
      private var buyOk:Boolean;
      
      private var buyP1P2:int = 1;
      
      public function YinCang()
      {
         super();
         this.P1_1_btn.addEventListener(MouseEvent.CLICK,this.p1QieHuan);
         this.P2_1_btn.addEventListener(MouseEvent.CLICK,this.p2QieHuan);
         this.P1_2_btn.addEventListener(MouseEvent.CLICK,this.p1QieHuan2);
         this.P2_2_btn.addEventListener(MouseEvent.CLICK,this.p2QieHuan2);
         this.X1_1_btn.addEventListener(MouseEvent.CLICK,this.XianShi);
         this.X2_1_btn.addEventListener(MouseEvent.CLICK,this.XianShi);
         this.X1_2_btn.addEventListener(MouseEvent.CLICK,this.XianShi);
         this.X2_2_btn.addEventListener(MouseEvent.CLICK,this.XianShi);
         this.close_btn.addEventListener(MouseEvent.CLICK,this.Close2);
         this.load_mc.visible = false;
         this.Show();
      }
      
      public static function Open(e:* = null) : *
      {
         if(!_this)
         {
            _this = new YinCang();
            Main._stage.addChild(_this);
         }
         _this.x = _this.y = 0;
         _this.Show();
      }
      
      public static function Close(e:* = null) : *
      {
         if(_this)
         {
            _this.x = _this.y = -5000;
         }
      }
      
      public static function BuyGo() : *
      {
         if(Boolean(_this) && Boolean(_this.buyOk))
         {
            _this.load_mc.visible = false;
            _this.buyOk = false;
            Main.yinCangP1P2 = _this.buyP1P2;
            _this.QieHuan();
         }
      }
      
      public static function Init() : *
      {
         if(Main.P1P2)
         {
            if(Main.yinCangP1P2 == 0)
            {
               return;
            }
            if(Main.yinCangP1P2 == 1)
            {
               Main.player_1.visible = false;
               Main.player_1.hp.setValue(0);
               Main.player_2.visible = true;
               Main.player_2.x = Main.player_1.x;
               Main.player_2.y = 450;
            }
            else if(Main.yinCangP1P2 == 2)
            {
               Main.player_1.visible = true;
               Main.player_2.hp.setValue(0);
               Main.player_2.visible = false;
               Main.player_1.x = Main.player_2.x;
               Main.player_1.y = 450;
            }
            if(Main.gameNum.getValue() == 0)
            {
               Player.一起信春哥();
            }
            Player.All = new Array();
            if(Main.player_1.hp.getValue() > 0)
            {
               Player.All.push(Main.player_1);
            }
            if(Main.player_2.hp.getValue() > 0)
            {
               Player.All.push(Main.player_2);
            }
         }
      }
      
      public function Close2(e:* = null) : *
      {
         Close();
      }
      
      private function p1QieHuan(e:*) : *
      {
         var money:int = int(this.arr[int((Main.player2.level.getValue() - 1) / 10)][0]);
         var Kpoint:int = int(this.arr[int((Main.player2.level.getValue() - 1) / 10)][1]);
         if(Main.player2.getGold() < money)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P金币不足");
            return;
         }
         if(Main.player2.killPoint.getValue() < Kpoint)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"2P击杀点不足");
            return;
         }
         Main.yinCangP1P2 = 1;
         Main.player2.payGold(money);
         Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - Kpoint);
         this.QieHuan();
      }
      
      private function p2QieHuan(e:*) : *
      {
         var money:int = int(this.arr[int((Main.player1.level.getValue() - 1) / 10)][0]);
         var Kpoint:int = int(this.arr[int((Main.player1.level.getValue() - 1) / 10)][1]);
         if(Main.player1.getGold() < money)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"1P金币不足");
            return;
         }
         if(Main.player1.killPoint.getValue() < Kpoint)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"1P击杀点不足");
            return;
         }
         Main.yinCangP1P2 = 2;
         Main.player1.payGold(money);
         Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - Kpoint);
         this.QieHuan();
      }
      
      private function p1QieHuan2(e:*) : *
      {
         var point:int = int(this.arr2[int((Main.player2.level.getValue() - 1) / 30)][0]);
         var pointID:int = int(this.arr2[int((Main.player2.level.getValue() - 1) / 30)][1]);
         if(Shop4399.moneyAll.getValue() >= point)
         {
            this.buyOk = true;
            this.buyP1P2 = 1;
            Api_4399_All.BuyObj(pointID);
            this.load_mc.visible = true;
         }
      }
      
      private function p2QieHuan2(e:*) : *
      {
         var point:int = int(this.arr2[int((Main.player1.level.getValue() - 1) / 30)][0]);
         var pointID:int = int(this.arr2[int((Main.player1.level.getValue() - 1) / 30)][1]);
         if(Shop4399.moneyAll.getValue() >= point)
         {
            this.buyOk = true;
            this.buyP1P2 = 2;
            Api_4399_All.BuyObj(pointID);
            this.load_mc.visible = true;
         }
      }
      
      public function Show() : *
      {
         var p2_money:int = int(this.arr[int((Main.player1.level.getValue() - 1) / 10)][0]);
         var p1_money:int = int(this.arr[int((Main.player2.level.getValue() - 1) / 10)][0]);
         var p2_Kpoint:int = int(this.arr[int((Main.player1.level.getValue() - 1) / 10)][1]);
         var p1_Kpoint:int = int(this.arr[int((Main.player2.level.getValue() - 1) / 10)][1]);
         var p2_point:int = int(this.arr2[int((Main.player1.level.getValue() - 1) / 30)][0]);
         var p1_point:int = int(this.arr2[int((Main.player2.level.getValue() - 1) / 30)][0]);
         this.P1_1_txt.text = "金币" + p1_money + "，击杀点" + p1_Kpoint;
         this.P2_1_txt.text = "金币" + p2_money + "，击杀点" + p2_Kpoint;
         this.P1_2_txt.text = p1_point + "点券";
         this.P2_2_txt.text = p2_point + "点券";
         this.P1_1_btn.visible = this.P2_1_btn.visible = this.P1_2_btn.visible = this.P2_2_btn.visible = true;
         this.X1_1_btn.visible = this.X2_1_btn.visible = this.X1_2_btn.visible = this.X2_2_btn.visible = false;
         if(Main.yinCangP1P2 == 1)
         {
            this.P1_1_btn.visible = this.P1_2_btn.visible = this.P2_1_btn.visible = this.P2_2_btn.visible = false;
            this.X1_1_btn.visible = this.X1_2_btn.visible = true;
         }
         else if(Main.yinCangP1P2 == 2)
         {
            this.P1_1_btn.visible = this.P1_2_btn.visible = this.P2_1_btn.visible = this.P2_2_btn.visible = false;
            this.X2_1_btn.visible = this.X2_2_btn.visible = true;
         }
      }
      
      private function XianShi(e:*) : *
      {
         if(Main.yinCangP1P2 == 1)
         {
            Main.player_1.x = Main.player_2.x;
            Main.player_1.y = Main.player_2.y;
         }
         else if(Main.yinCangP1P2 == 2)
         {
            Main.player_2.x = Main.player_1.x;
            Main.player_2.y = Main.player_1.y;
         }
         Main.yinCangP1P2 = 0;
         this.QieHuan();
      }
      
      private function QieHuan() : *
      {
         if(Main.yinCangP1P2 == 1)
         {
            Main.player_1.visible = false;
            Main.player_1.hp.setValue(0);
            Main.player_2.visible = true;
            if(Main.player_1.playerCW)
            {
               Main.player_1.playerCW = null;
            }
            if(Main.player_1.playerCW2)
            {
               Main.player_1.playerCW2.DeadX();
            }
            if(Main.player_1.playerJL)
            {
               Main.player_1.playerJL.parent.removeChild(Main.player_1.playerJL);
            }
         }
         else if(Main.yinCangP1P2 == 2)
         {
            Main.player_1.visible = true;
            Main.player_2.hp.setValue(0);
            Main.player_2.visible = false;
            if(Main.player_2.playerCW)
            {
               Main.player_2.playerCW = null;
            }
            if(Main.player_2.playerCW2)
            {
               Main.player_2.playerCW2.DeadX();
            }
            if(Main.player_2.playerJL)
            {
               Main.player_2.playerJL.parent.removeChild(Main.player_2.playerJL);
            }
         }
         else
         {
            Main.player_1.visible = true;
            if(Main.player_1.playerJL)
            {
               Main.world.moveChild_ChongWu.addChild(Main.player_1.playerJL);
            }
            Main.player_2.visible = true;
            if(Main.player_2.playerJL)
            {
               Main.world.moveChild_ChongWu.addChild(Main.player_2.playerJL);
            }
         }
         Player.一起信春哥();
         Player.All = new Array();
         if(Main.player_1.hp.getValue() > 0)
         {
            Player.All.push(Main.player_1);
         }
         if(Main.player_2.hp.getValue() > 0)
         {
            Player.All.push(Main.player_2);
         }
         if(_this)
         {
            this.Show();
         }
      }
   }
}

