package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.other.*;
   
   public class BadgeSlot
   {
      
      private var _slot:Array = new Array();
      
      public function BadgeSlot()
      {
         super();
      }
      
      public static function createBadgeSlot() : BadgeSlot
      {
         var bs:BadgeSlot = new BadgeSlot();
         for(var i:int = 0; i < 6; i++)
         {
            bs._slot[i] = null;
         }
         return bs;
      }
      
      public function getBadgeFromSlot(num:Number) : Otherobj
      {
         if(this._slot[num] != null)
         {
            return this._slot[num];
         }
         return null;
      }
      
      public function addToSlot(value:Otherobj, num:Number) : Boolean
      {
         if(this._slot[num] == null)
         {
            this._slot[num] = value;
            return true;
         }
         return false;
      }
      
      public function getMP() : Number
      {
         if(this._slot[0])
         {
            return (this._slot[0] as Otherobj).getValue_1();
         }
         return 0;
      }
      
      public function getHP() : Number
      {
         if(this._slot[1])
         {
            return (this._slot[1] as Otherobj).getValue_1();
         }
         return 0;
      }
      
      public function getATT() : Number
      {
         if(this._slot[2])
         {
            return (this._slot[2] as Otherobj).getValue_1();
         }
         return 0;
      }
      
      public function getDEF() : Number
      {
         if(this._slot[3])
         {
            return (this._slot[3] as Otherobj).getValue_1();
         }
         return 0;
      }
      
      public function getCRIT() : Number
      {
         if(this._slot[4])
         {
            return (this._slot[4] as Otherobj).getValue_1();
         }
         return 0;
      }
      
      public function getSPEED() : Number
      {
         if(this._slot[5])
         {
            return (this._slot[5] as Otherobj).getValue_1();
         }
         return 0;
      }
      
      public function plan6_14() : Boolean
      {
         for(var i:int = 0; i < 6; i++)
         {
            if(this._slot[i])
            {
               return true;
            }
         }
         return false;
      }
      
      public function get slot() : Array
      {
         return this._slot;
      }
      
      public function set slot(value:Array) : void
      {
         this._slot = value;
      }
   }
}

