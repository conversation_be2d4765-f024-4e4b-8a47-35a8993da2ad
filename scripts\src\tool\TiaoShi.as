package src.tool
{
   import com.*;
   import com.ByteArrayXX.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.pet.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.plan.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.task.*;
   import com.hotpoint.braveManIII.repository.chest.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.petEquip.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.repository.quest.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.setProfession.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   import src._data.*;
   import src.other.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol7071")]
   public class TiaoShi extends MovieClip
   {
      
      public static var _this:TiaoShi;
      
      public static var Hpup:int;
      
      public static var tempVarTime:int;
      
      public static var tempVar:String;
      
      public static var qhXX:Boolean;
      
      private static var traceYN:Boolean = true;
      
      public static var chuanSongYN:Boolean = false;
      
      public static var XXX:Array = [];
      
      public static var cwArr:Array = new Array();
      
      public var CJ_0_btn:SimpleButton;
      
      public var ChongWu1_btn:SimpleButton;
      
      public var ChongWu2_btn:SimpleButton;
      
      public var ChongWu_close1_btn:SimpleButton;
      
      public var ChongWu_close2_btn:SimpleButton;
      
      public var GoGame_btn:SimpleButton;
      
      public var GuanKa1_txt:TextField;
      
      public var GuanKa2_txt:TextField;
      
      public var GuanKa3_txt:TextField;
      
      public var JieChu_btn:SimpleButton;
      
      public var JieChu_txt:TextField;
      
      public var KaiQi_All_btn:SimpleButton;
      
      public var KaiQi_btn:SimpleButton;
      
      public var Map0_btn:SimpleButton;
      
      public var Map0_txt:TextField;
      
      public var NewData_btn:SimpleButton;
      
      public var Op_btn:MovieClip;
      
      public var P1_btn:SimpleButton;
      
      public var P2_btn:SimpleButton;
      
      public var PaiHangID_txt:TextField;
      
      public var PaiHangTiJiao_btn:SimpleButton;
      
      public var PaiHangTiJiao_txt:TextField;
      
      public var Q_buy1:SimpleButton;
      
      public var Q_buy_txt:TextField;
      
      public var QingKong_btn:SimpleButton;
      
      public var Sel_btn:SimpleButton;
      
      public var X61_btn:SimpleButton;
      
      public var X61_txt:TextField;
      
      public var X61info_txt:TextField;
      
      public var buy1:SimpleButton;
      
      public var buy2:SimpleButton;
      
      public var buy3:SimpleButton;
      
      public var buy4:SimpleButton;
      
      public var chunSong_btn:SimpleButton;
      
      public var get_1_btn:SimpleButton;
      
      public var get_1_txt:TextField;
      
      public var get_2_btn:SimpleButton;
      
      public var get_2_txt:TextField;
      
      public var get_3_txt:TextField;
      
      public var get_4_txt:TextField;
      
      public var get_5_txt:TextField;
      
      public var get_num1_txt:TextField;
      
      public var get_num2_txt:TextField;
      
      public var get_num3_txt:TextField;
      
      public var get_num4_txt:TextField;
      
      public var get_num5_txt:TextField;
      
      public var jihua_txt:TextField;
      
      public var liBaoPoint2_btn:SimpleButton;
      
      public var liBaoPoint2_txt:TextField;
      
      public var liBaoPoint3_txt:TextField;
      
      public var liBaoPoint_btn:SimpleButton;
      
      public var liBaoPoint_txt:TextField;
      
      public var name_btn:SimpleButton;
      
      public var name_txt:TextField;
      
      public var other2_btn:SimpleButton;
      
      public var other_btn:SimpleButton;
      
      public var p1_txt:TextField;
      
      public var p2_txt:TextField;
      
      public var p3_txt:TextField;
      
      public var p4_txt:TextField;
      
      public var p5_txt:TextField;
      
      public var p6_txt:TextField;
      
      public var p7_txt:TextField;
      
      public var p8_txt:TextField;
      
      public var p9_txt:TextField;
      
      public var sysTime_btn:SimpleButton;
      
      public var sysTime_txt:TextField;
      
      public var temp_btn:SimpleButton;
      
      public var temp_txt:TextField;
      
      public var tet_btn:SimpleButton;
      
      public var tiaozan_btn:SimpleButton;
      
      public var tiaozhan0:TextField;
      
      public var trace_btn:SimpleButton;
      
      public var type2_txt:TextField;
      
      public var type3_txt:TextField;
      
      public var type4_txt:TextField;
      
      public var type_txt:TextField;
      
      public var var_txt:TextField;
      
      private var lunTan8Time:Timer;
      
      public function TiaoShi()
      {
         super();
         _this = this;
         if(!CeShi._this || !Main.tiaoShiYN)
         {
            this.x = this.y = -5000;
            this.visible = false;
            return;
         }
         if(Main.NoLog)
         {
            this.JieChu_txt.text = "已锁定";
         }
         else
         {
            this.JieChu_txt.text = "未锁定";
         }
         this.x = 916;
         this.y = 556;
         this.get_1_btn.addEventListener(MouseEvent.CLICK,this.GetP1Obj);
         this.get_2_btn.addEventListener(MouseEvent.CLICK,this.GetP2Obj);
         this.Op_btn.addEventListener(MouseEvent.CLICK,this.OP);
         this.KaiQi_btn.addEventListener(MouseEvent.CLICK,this.KaiQi);
         this.KaiQi_All_btn.addEventListener(MouseEvent.CLICK,this.KaiQiAll);
         this.P1_btn.addEventListener(MouseEvent.CLICK,this.P1_ZhuangTai);
         this.P2_btn.addEventListener(MouseEvent.CLICK,this.P2_ZhuangTai);
         this.tet_btn.addEventListener(MouseEvent.CLICK,this.Txt_show);
         this.sysTime_btn.addEventListener(MouseEvent.CLICK,this.sysTime);
         this.buy1.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         this.buy2.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         this.buy3.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         this.buy4.addEventListener(MouseEvent.CLICK,this.BuyOpenNum);
         this.Q_buy1.addEventListener(MouseEvent.CLICK,this.Q_BuyOpenNum);
         this.name_btn.addEventListener(MouseEvent.CLICK,this.NewName);
         this.JieChu_btn.addEventListener(MouseEvent.CLICK,this.JieChu_Fun);
         this.ChongWu1_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun1);
         this.ChongWu2_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun2);
         this.ChongWu_close1_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun1_C);
         this.ChongWu_close2_btn.addEventListener(MouseEvent.CLICK,this.CW_Fun2_C);
         this.other_btn.addEventListener(MouseEvent.CLICK,this.other_Fun);
         this.other2_btn.addEventListener(MouseEvent.CLICK,this.other_Fun2);
         this.X61_btn.addEventListener(MouseEvent.CLICK,this.XX61Fun);
         this.CJ_0_btn.addEventListener(MouseEvent.CLICK,this.CJ0_Fun);
         this.liBaoPoint_btn.addEventListener(MouseEvent.CLICK,this.SetLiBaoPoint);
         this.liBaoPoint2_btn.addEventListener(MouseEvent.CLICK,this.SetLiBaoPoint2);
         this.PaiHangTiJiao_btn.addEventListener(MouseEvent.CLICK,this.PaiHangTiJiao);
         this.Sel_btn.addEventListener(MouseEvent.CLICK,this.SEL_PaiHangTiJiao);
         this.NewData_btn.addEventListener(MouseEvent.CLICK,this.NewDataXXX);
         this.temp_btn.addEventListener(MouseEvent.CLICK,this.TempFun);
         this.QingKong_btn.addEventListener(MouseEvent.CLICK,this.QingKong_Fun);
         this.chunSong_btn.addEventListener(MouseEvent.CLICK,this.ChuanSong_Fun);
         this.GoGame_btn.addEventListener(MouseEvent.CLICK,this.GoGame_Fun);
         this.Map0_btn.addEventListener(MouseEvent.CLICK,this.Map0Fun);
         this.sysTime_txt.text = Main.serverTime.getValue();
         this.tiaozan_btn.addEventListener(MouseEvent.CLICK,this.TiaoZhan0);
         this.trace_btn.addEventListener(MouseEvent.CLICK,this.TraceFun);
      }
      
      public static function ShowPaiHang(str:String = "") : *
      {
         if(Boolean(_this) && Boolean(_this.temp_txt))
         {
            _this.temp_txt.text += "\n回调信息:" + str;
         }
      }
      
      public static function txtShow(str:String) : *
      {
         if(_this && _this.temp_txt && Boolean(traceYN))
         {
            _this.temp_txt.text += "\n" + str;
         }
      }
      
      private function TraceFun(e:*) : *
      {
         if(traceYN)
         {
            traceYN = false;
         }
         else
         {
            traceYN = true;
         }
      }
      
      private function TiaoZhan0(e:*) : *
      {
         var i:int = 0;
         var i2:int = 0;
         for(i in PaiHang_Data.sArr2)
         {
            if(PaiHang_Data.sArr2[i])
            {
               for(i2 in PaiHang_Data.sArr2[i])
               {
                  if(PaiHang_Data.sArr2[i][i2])
                  {
                     TiaoShi.txtShow("挑战模式成绩>>>" + i + "," + i2 + " = " + PaiHang_Data.sArr2[i][i2].getValue());
                  }
               }
            }
         }
      }
      
      private function Map0Fun(e:*) : *
      {
         if(Main.water.getValue() == 1)
         {
            this.Map0_txt.text = "海底";
            Main.water.setValue(Math.random() * 999 + 1);
         }
         else
         {
            this.Map0_txt.text = "地面";
            Main.water = VT.createVT(1);
         }
      }
      
      private function GoGame_Fun(e:*) : *
      {
         GameData.winYN = false;
         Main.gameNum.setValue(uint(this.GuanKa1_txt.text));
         Main.gameNum2.setValue(uint(this.GuanKa2_txt.text));
         GameData.gameLV = uint(this.GuanKa3_txt.text);
         Main._this.Loading();
      }
      
      private function ChuanSong_Fun(e:*) : *
      {
         if(chuanSongYN)
         {
            chuanSongYN = false;
         }
         else
         {
            chuanSongYN = true;
         }
      }
      
      private function QingKong_Fun(e:*) : *
      {
         this.temp_txt.text = "";
      }
      
      private function OP(e:*) : *
      {
         if(this.x != 0)
         {
            this.x = this.y = 0;
         }
         else
         {
            this.x = 916;
            this.y = 556;
         }
      }
      
      private function GetP1Obj(e:*) : *
      {
         var i:int = 0;
         if(this.get_1_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num1_txt.text); i++)
            {
               Main.player_1.data.getBag().addEquipBag(EquipFactory.createEquipByID(int(this.get_1_txt.text)));
            }
         }
         if(this.get_2_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num2_txt.text); i++)
            {
               Main.player_1.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(int(this.get_2_txt.text)));
            }
         }
         if(this.get_3_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num3_txt.text); i++)
            {
               Main.player_1.data.getBag().addGemBag(GemFactory.creatGemById(int(this.get_3_txt.text)));
            }
         }
         if(this.get_4_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num4_txt.text); i++)
            {
               Main.player_1.data.getBag().addOtherobjBag(OtherFactory.creatOther(int(this.get_4_txt.text)));
            }
         }
         if(this.get_5_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num5_txt.text); i++)
            {
               Main.player_1.data.getBag().addQuestBag(QuestFactory.creatQust(int(this.get_5_txt.text)));
            }
            TaskData.setOk(int(this.get_5_txt.text));
         }
      }
      
      private function GetP2Obj(e:*) : *
      {
         var i:int = 0;
         if(!Main.P1P2)
         {
            return;
         }
         if(this.get_1_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num1_txt.text); i++)
            {
               Main.player_2.data.getBag().addEquipBag(EquipFactory.createEquipByID(int(this.get_1_txt.text)));
            }
         }
         if(this.get_2_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num2_txt.text); i++)
            {
               Main.player_2.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(int(this.get_2_txt.text)));
            }
         }
         if(this.get_3_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num3_txt.text); i++)
            {
               Main.player_2.data.getBag().addGemBag(GemFactory.creatGemById(int(this.get_3_txt.text)));
            }
         }
         if(this.get_4_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num4_txt.text); i++)
            {
               Main.player_2.data.getBag().addOtherobjBag(OtherFactory.creatOther(int(this.get_4_txt.text)));
            }
         }
         if(this.get_5_txt.text != -1)
         {
            for(i = 0; i < int(this.get_num5_txt.text); i++)
            {
               Main.player_2.data.getBag().addQuestBag(QuestFactory.creatQust(int(this.get_5_txt.text)));
            }
            TaskData.setOk(int(this.get_5_txt.text));
         }
      }
      
      private function KaiQiAll(e:*) : *
      {
         for(var i:int = 0; i < 150; i++)
         {
            Main.guanKa[i] = int(this.GuanKa3_txt.text);
         }
      }
      
      private function KaiQi(e:*) : *
      {
         Main.guanKa[int(this.GuanKa1_txt.text)] = int(this.GuanKa2_txt.text);
      }
      
      private function P1_ZhuangTai(e:*) : *
      {
         if(int(this.p1_txt.text) != -1)
         {
            Main.player_1.data.setLevel(int(this.p1_txt.text));
         }
         if(int(this.p2_txt.text) != -1)
         {
            Main.player_1.data.killPoint.setValue(int(this.p2_txt.text));
         }
         if(int(this.p3_txt.text) != -1)
         {
            Main.player1.gold.setValue(int(this.p3_txt.text));
         }
         if(int(this.p4_txt.text) != -1)
         {
            Main.player_1.data.points.setValue(int(this.p4_txt.text));
         }
         if(int(this.p5_txt.text) != -1)
         {
            Enemy.gongJiPOWER.setValue(int(this.p5_txt.text));
         }
         if(int(this.p6_txt.text) != -1)
         {
            Main.player_1.fangYuPOWER.setValue(int(this.p6_txt.text));
         }
         if(int(this.p7_txt.text) != -1)
         {
            Main.player_1.ExpUP(int(this.p7_txt.text),2);
         }
         if(int(this.p8_txt.text) != -1)
         {
            HitXX.baoJiUP.setValue(int(this.p8_txt.text));
         }
         if(int(this.p9_txt.text) != -1)
         {
            Hpup = int(this.p9_txt.text);
         }
      }
      
      private function P2_ZhuangTai(e:*) : *
      {
         if(!Main.P1P2)
         {
            return;
         }
         if(int(this.p1_txt.text) != -1)
         {
            Main.player_2.data.setLevel(int(this.p1_txt.text));
         }
         if(int(this.p2_txt.text) != -1)
         {
            Main.player_2.data.AddKillPoint(int(this.p2_txt.text));
         }
         if(int(this.p3_txt.text) != -1)
         {
            Main.player2.addGold(int(this.p3_txt.text));
         }
         if(int(this.p4_txt.text) != -1)
         {
            Main.player_2.data.addPoint(int(this.p4_txt.text));
         }
         if(int(this.p5_txt.text) != -1)
         {
            Enemy.gongJiPOWER.setValue(int(this.p5_txt.text));
         }
         if(int(this.p6_txt.text) != -1)
         {
            Main.player_2.fangYuPOWER.setValue(int(this.p6_txt.text));
         }
         if(int(this.p7_txt.text) != -1)
         {
            Main.player_2.ExpUP(int(this.p7_txt.text),2);
         }
         if(int(this.p8_txt.text) != -1)
         {
            HitXX.baoJiUP.setValue(int(this.p8_txt.text));
         }
         if(int(this.p9_txt.text) != -1)
         {
            Hpup = int(this.p9_txt.text);
         }
      }
      
      private function sysTime(e:*) : *
      {
         if(int(this.sysTime_txt.text) != -1)
         {
            Main.serverTime.setValue(int(this.sysTime_txt.text));
         }
         QianDao.year = this.sysTime_txt.text.substr(0,4);
         QianDao.month = this.sysTime_txt.text.substr(4,2);
         QianDao.day = this.sysTime_txt.text.substr(6,2);
         Main.saveTimeX = int(this.sysTime_txt.text);
         txtShow("设置时间: " + Main.saveTimeX);
      }
      
      private function BuyOpenNum(e:MouseEvent) : *
      {
         ShopKillPoint.KaiQiShopArr2[0] = 0;
         var i:int = int((e.target.name as String).substr(3,1));
         if(ShopKillPoint.KaiQiShopArr2[i] == true)
         {
            ShopKillPoint.KaiQiShopArr2[i] = false;
            ShopKillPoint.shopX["shopX_" + i]["skin"].visible = true;
         }
         else
         {
            ShopKillPoint.KaiQiShopArr2[i] = true;
            ShopKillPoint.shopX["shopX_" + i]["skin"].visible = false;
         }
      }
      
      private function Q_BuyOpenNum(e:MouseEvent) : *
      {
         if(this.Q_buy_txt.text == "1")
         {
            Main.player1.getStampSlot().setKeySlot11();
         }
         else if(this.Q_buy_txt.text == "2")
         {
            Main.player2.getStampSlot().setKeySlot11();
         }
         else if(this.Q_buy_txt.text == "0")
         {
            Main.player1.getStampSlot().setKeySlot11();
            Main.player2.getStampSlot().setKeySlot11();
         }
      }
      
      private function NewName(e:*) : *
      {
         if(this.name_txt.text != 0)
         {
            Main.logName = this.name_txt.text;
            Main.logNameSave = "";
            Main.varX_old = 0;
         }
         if(this.var_txt.text != 0)
         {
            Main.varX = int(this.var_txt.text);
         }
      }
      
      private function JieChu_Fun(e:*) : *
      {
         Main.noSave = -43998785;
         Main.NoLog = 0;
         Main.NoLogInfo = new Array();
      }
      
      private function CW_Fun1(e:*) : *
      {
         Main.player_1.playerCW = null;
         Main.player1.getPetSlot().addPetSlot(PetFactory.creatPet(73101));
         Main.player1.getPetSlot().addPetSlot(PetFactory.creatPet(73500));
      }
      
      private function CW_Fun2(e:*) : *
      {
         Main.player_2.playerCW = null;
         Main.player2.getPetSlot().addPetSlot(PetFactory.creatPet(73101));
         Main.player2.getPetSlot().addPetSlot(PetFactory.creatPet(73500));
      }
      
      private function CW_Fun1_C(e:*) : *
      {
         if(Boolean(Main.player_1.playerCW) && Boolean(Main.player_1.playerCW.data.addExp(99999)))
         {
            NewMC.Open("宠物升级",Main.player_1.playerCW);
         }
         if(Boolean(Main.player_1.playerJL) && Boolean(Main.player_1.playerJL.data.addExp(99999)))
         {
            NewMC.Open("宠物升级",Main.player_1.playerCW);
         }
      }
      
      private function CW_Fun2_C(e:*) : *
      {
         if(Main.player_2.playerCW.data.addExp(99999))
         {
            NewMC.Open("宠物升级",Main.player_2.playerCW);
         }
      }
      
      private function ShowInfo() : *
      {
         this.temp_txt.text += "Play_Interface.interfaceX._Move_mc.x = " + Play_Interface.interfaceX._Move_mc.x + "\n";
         this.temp_txt.text += "logName = " + Main.logName + "\n";
         this.temp_txt.text += "Uid = " + Main.userId + "\n";
         this.temp_txt.text += "当前版本:" + Main.varX + " ,存档版本:" + Main.varX_old + "\n";
         this.temp_txt.text += "存档日期 = " + TiaoShi.tempVarTime + "\n";
         if(Main.logNameSave == "" && Main.varX_old < 182 || Boolean(MD5contrast.contrast(Main.logName,Main.logNameSave,Main.saveNum)))
         {
            this.temp_txt.text += "存档:" + (int(Main.logNameSave.substr(5,1)) + 1) + ", sn:" + Main.logNameSave.substr(6,8) + "\n" + "验证信息: " + MD5contrast.contrastID + "\n";
         }
         else
         {
            this.temp_txt.text += "验证失败!!\n" + "存档:" + (int(Main.logNameSave.substr(5,1)) + 1) + ", sn:" + Main.logNameSave.substr(6,8) + "\n" + "验证信息: 错误#" + MD5contrast.errorID + "\n";
         }
         if(Main.NoLog == 0)
         {
            this.temp_txt.text += "作弊检测:验证通过\n";
         }
         else if(Main.NoLog >= 2)
         {
            this.temp_txt.text += "\n>>>>>>>>>>>>>>>>>>>\n\n\n 作弊检测:已锁定 \n";
            if(Main.NoLogInfo[2])
            {
               this.temp_txt.text += "\n #2 修改攻击倍数" + Main.NoLogInfo[2] + "\n";
            }
            if(Main.NoLogInfo[3])
            {
               this.temp_txt.text += "\n #3 修改金币" + Main.NoLogInfo[3] + "\n";
            }
            if(Main.NoLogInfo[4])
            {
               this.temp_txt.text += "\n #4 物品堆叠上限" + Main.NoLogInfo[4] + "\n";
            }
            if(Main.NoLogInfo[5])
            {
               this.temp_txt.text += "\n #5 防复制" + Main.NoLogInfo[5] + "\n";
            }
            if(Main.NoLogInfo[7])
            {
               this.temp_txt.text += "\n #7 宝石类复制" + Main.NoLogInfo[7] + "\n";
            }
            if(Main.NoLogInfo[8])
            {
               this.temp_txt.text += "\n #8 其他类复制" + Main.NoLogInfo[8] + "\n";
            }
            if(Main.NoLogInfo[10])
            {
               this.temp_txt.text += "\n #10 修改技能表XML \n";
            }
            if(Main.NoLogInfo[11])
            {
               this.temp_txt.text += "\n #11 修改暴击闪避" + Main.NoLogInfo[11] + " \n";
            }
            if(Main.NoLogInfo[12] is int)
            {
               this.temp_txt.text += "\n #12 点券不足 领取礼包\n";
            }
            if(Main.NoLogInfo[13])
            {
               this.temp_txt.text += "\n #13 Vip称号\n";
            }
            if(Main.NoLogInfo[15])
            {
               this.temp_txt.text += "\n #15 跳关修改!\n";
            }
            this.temp_txt.text += "\n #Main.NoLog = " + Main.NoLog + ", NoLogInfo = " + Main.NoLogInfo;
            this.temp_txt.text += "\n\n\n>>>>>>>>>>>>>>>>>>>\n";
         }
         this.temp_txt.text += "\n已领取礼包 = " + Main.lingQueArr + ", length" + Main.lingQueArr.length;
         this.temp_txt.text += "\n已领取累计礼包 = " + Main.lingQueArr2 + ", length" + Main.lingQueArr2.length;
         this.temp_txt.text += "\n活动礼包领取 = " + TeShuHuoDong.TeShuHuoDongArr + ", length" + TeShuHuoDong.TeShuHuoDongArr.length + "\n";
         if(Shop4399.totalRecharged.getValue() != -2)
         {
            this.temp_txt.text += "\n累计充值:" + Shop4399.totalRecharged.getValue() + "点券";
            this.temp_txt.text += "\n礼包前充值:" + Shop_LiBao.HDpoint.getValue() + "点券";
            this.temp_txt.text += "\n活动赠送卷:" + TeShuHuoDong.TeShuHuoDongArr[0] + "点券";
         }
         else
         {
            this.temp_txt.text += "请稍候再试\n";
         }
         CheckTest.Testing();
         CheckTest.TestingOther();
         Main.wts.wantedTaskComplete();
         this.temp_txt.text += "\n Main.noSave = " + Main.noSave;
         this.temp_txt.text += "Shop4399.ShopArr.length = " + ShopFactory.GetObjData_AllNum();
         var pX:Player = Player.All[0];
         var pDX:PlayerData = pX.data;
      }
      
      private function XX61Fun(e:*) : *
      {
         AchData.cjPoint_1.setValue(int(this.X61info_txt.text));
         AchData.cjPoint_2.setValue(int(this.X61_txt.text));
      }
      
      private function other_Fun(e:*) : *
      {
         Main.lingQueArr = [];
         Main.lingQueArr2 = [];
         TeShuHuoDong.TeShuHuoDongArr = [];
      }
      
      private function other_Fun2(e:*) : *
      {
      }
      
      private function CJ0_Fun(e:*) : *
      {
         AchData.initAllAc();
      }
      
      private function SetLiBaoPoint(e:*) : *
      {
         Shop_LiBao.HDpoint.setValue(int(this.liBaoPoint2_txt.text));
      }
      
      private function SetLiBaoPoint2(e:*) : *
      {
         Shop4399.totalRecharged.setValue(int(this.liBaoPoint3_txt.text));
      }
      
      private function SEL_PaiHangTiJiao(e:*) : *
      {
         PK_UI.Open();
      }
      
      private function PaiHangTiJiao(e:*) : *
      {
         var arr:Array = [];
         arr[0] = new Object();
         arr[0].rId = int(this.PaiHangID_txt.text);
         arr[0].score = uint(this.PaiHangTiJiao_txt.text);
         Api_4399_All.SubmitScore(Main.saveNum,arr);
      }
      
      private function NewDataXXX(e:*) : *
      {
         TiaoShi.txtShow(TimeBox.TimeXX(this.CeShiMd5_PLayerData));
      }
      
      private function CeShiMd5_PLayerData() : *
      {
         TiaoShi.txtShow("Main.player1 - md5:" + MD5contrast.getObj_MD5String(Main.player1));
      }
      
      private function TempShow() : *
      {
         this.temp_txt.text = "";
         MD5contrast.getObj_MD5String(Main.player_1.data.getBag());
      }
      
      private function TempFun(e:*) : *
      {
         TiaoShi.txtShow("怪物数据验证 md5:" + Data2.Enemy_md5_2);
         TiaoShi.txtShow("技能列表验证 md5:" + Data2.skill_md5_2);
         TiaoShi.txtShow("任务列表验证 md5:" + Data2.renWu_md5_2);
      }
      
      public function testXX() : *
      {
         Mosen_Interface.Open();
      }
      
      private function Txt_show(e:*) : *
      {
         var tempArr:Array = null;
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         var num:int = 0;
         var i:int = 0;
         var i2:int = 0;
         var petX:Pet = null;
         var petX2:Pet = null;
         var en:Enemy = null;
         var arr:Array = null;
         var timeXXX:int = 0;
         var ysArr:Array = null;
         var ysArr2:Array = null;
         this.temp_txt.text = "";
         var numX1:int = int(this.type_txt.text);
         var numX2:int = int(this.type2_txt.text);
         var numX3:int = int(this.type3_txt.text);
         var numX4:int = int(this.type4_txt.text);
         if(numX1 == 1)
         {
            this.ShowInfo();
            this.testXX();
         }
         else if(numX1 == 2)
         {
            ChongWu2.cw2save[14][1] += 1;
            ChongWu2.cw2save[14][2] += 1;
            ChongWu2.cw2save[14][3] += 1;
         }
         else if(numX1 == 3)
         {
            if(numX2 == 1)
            {
               JinHuaPanel2.open(true);
            }
            else
            {
               JinHuaPanel.open(true,numX3,numX4);
            }
         }
         else if(numX1 == 4)
         {
            YueKa_Interface.yueKaTime = Main.serverDayNum + 30;
         }
         else if(numX1 == 5)
         {
            tempArr = [15];
            Api_4399_GongHui.getNum(tempArr);
         }
         else if(numX1 == 6)
         {
            NewPetPanel.tiaoshi();
         }
         else if(numX1 == 7)
         {
            NewPetPanel.testSkillxx();
         }
         else if(numX1 == 8)
         {
            Play_Interface.TiShiShow(false,110097);
         }
         else if(numX1 == 9)
         {
            NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(numX2));
         }
         else if(numX1 == 10)
         {
            Main.player1.getPetSlot().addPetSlot(PetFactory.creatPet(numX2));
         }
         else if(numX1 == 11)
         {
            Main.wts.getWantedTaskFromSlot(1).addTimes(50);
            Main.wts.getWantedTaskFromSlot(4).addTimes(50);
            Main.wts.getWantedTaskFromSlot(7).addTimes(50);
            Main.wts.getWantedTaskFromSlot(10).addTimes(50);
            Main.wts.getWantedTaskFromSlot(13).addTimes(50);
            Main.wts.getWantedTaskFromSlot(16).addTimes(50);
            Main.wts.getWantedTaskFromSlot(19).addTimes(50);
            Main.wts.getWantedTaskFromSlot(22).addTimes(50);
            Main.wts.getWantedTaskFromSlot(25).addTimes(50);
         }
         else if(numX1 == 12)
         {
            Yayale_LingQue.Open();
         }
         else if(numX1 == 13)
         {
            Yayale_LingQue.DuiHuanOpen(Main.player_1);
         }
         else if(numX1 == 14)
         {
            tempArr = [47];
            Api_4399_GongHui.getNum(tempArr);
         }
         else if(numX1 == 15)
         {
            tempArr = [135];
            Api_4399_GongHui.getNum(tempArr);
         }
         else if(numX1 == 16)
         {
            tempArr = [49];
            Api_4399_GongHui.getNum(tempArr);
         }
         else if(numX1 == 17)
         {
            tempArr = [50];
            Api_4399_GongHui.getNum(tempArr);
         }
         else if(numX1 == 18)
         {
            tempArr = [127,126];
            Api_4399_GongHui.getNum(tempArr);
         }
         else if(numX1 == 20)
         {
            SixOne_Interface.okTimes2021.setValue(numX2);
            txtShow("六一活动完成次数:" + SixOne_Interface.okTimes2021.getValue());
         }
         else if(numX1 == 21)
         {
            classRef = NewLoad.OtherData.getClass("_PK_UI") as Class;
            xxMov = new classRef();
            Main._stage.addChild(xx);
         }
         else if(numX1 == 22)
         {
            JiFenLingQue2.DengLuUp();
         }
         else if(numX1 == 23)
         {
            TiaoShi.txtShow("清除转职");
            num = 0;
            for(i = 0; i < 3; i++)
            {
               if(Main.player1._transferArr[i])
               {
                  num++;
               }
            }
            if(num > 1)
            {
               TiaoShi.txtShow("清除转职---------> 1P");
               SkillPanel.open();
               SkillPanel.close();
               SkillPanel._instance.aginP1();
               for(i = 0; i < 3; i++)
               {
                  if(Main.player1._transferArr[i])
                  {
                     Main.player1._transferArr[i] = false;
                  }
               }
               TiaoShi.txtShow("清除转职---------> 1P end");
            }
         }
         else if(numX1 == 24 && numX2 == 99)
         {
            GengXin.saveTime = "2014/07/10";
            Api_4399_GongHui.upNum(57);
            Api_4399_GongHui.upNum(58);
            Api_4399_GongHui.getNum([57,58]);
            GengXin.CaXun();
         }
         else if(numX1 == 25)
         {
            if(numX2 == 1)
            {
               JiFenLingQue3.Post(true);
            }
            else
            {
               JiFenLingQue3.Post();
            }
         }
         else if(numX1 == 26)
         {
            Main.player_1.playerJL.jiNengBD_ID = numX2;
            TiaoShi.txtShow("精灵被动技能 = " + numX2);
         }
         else if(numX1 == 27)
         {
            TiaoShi.txtShow("精灵主动技能 = " + int(Main.player_1.playerJL.jiNengZD_ID));
         }
         else if(numX1 == 28)
         {
            num = int(TaskData.zhuXianNum());
            TiaoShi.txtShow("主线任务完成数量 = " + num);
         }
         else if(numX1 == 29)
         {
            SJ_Interface.Open();
         }
         else if(numX1 == 30)
         {
            for(i = 0; i <= 4; i++)
            {
               if(i != 3)
               {
                  TiaoShi.txtShow("i=" + i + "\n");
                  if(PaiHang_Data.sArr2[i])
                  {
                     if(PaiHang_Data.sArr2[i].length > 0)
                     {
                        for(i2 in PaiHang_Data.sArr2[i])
                        {
                           TiaoShi.txtShow(PaiHang_Data.sArr2[i][i2].getValue() + ",");
                        }
                     }
                     TiaoShi.txtShow("\n");
                  }
               }
            }
         }
         else if(numX1 == 31)
         {
            ZhuanPan.saveTime = 0;
            ZhuanPan.Open();
         }
         else if(numX1 == 32)
         {
            ZhuanPan.Num1time = 5;
         }
         else if(numX1 == 33)
         {
            Main.player1.getElvesSlot().addSlotNum(1);
         }
         else if(numX1 == 34)
         {
            Main.player1.getElvesSlot().getElvesFromSlot(numX2).delPinkEquip();
         }
         else if(numX1 == 35)
         {
            Main.player1.getElvesSlot().getElvesFromSlot(0).delPinkEquip();
         }
         else if(numX1 == 36)
         {
            PK_UI.xPk_Player_min = numX2;
         }
         else if(numX1 == 37)
         {
            PK_UI.jiFenArr[1].setValue(numX2);
         }
         else if(numX1 == 38)
         {
            PK_UI.Pk_timeNum = numX2;
         }
         else if(numX1 == 40)
         {
            TiaoShi.txtShow("调试#40 !!竞技场重新刷新玩家~~~~~~~~~~~~~~~~~~~~~");
            PK_UI.reAddOtherPlayer();
         }
         else if(numX1 == 41)
         {
            ChongZhi_Interface.HDpoint9.setValue(numX2);
            TiaoShi.txtShow("调试HDpoint9 = " + ChongZhi_Interface.HDpoint9.getValue());
         }
         else if(numX1 == 42)
         {
            TiaoShi.txtShow(PK_UI.jiFenArr[7]);
            ++PK_UI.jiFenArr[7];
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63300));
         }
         else if(numX1 == 43)
         {
            TiaoShi.txtShow("查询" + Api_4399_All.xxxArr.length + ":" + Api_4399_All.xxxArr);
         }
         else if(numX1 == 44)
         {
            petX = Pet.creatPet(numX2);
            Main.player1.getPetSlot().addPetSlot(petX);
            Main.player1.getPetSlot().getPetFromSlot(0).setLv(20);
            if(Main.player2)
            {
               petX2 = Pet.creatPet(numX2);
               Main.player2.getPetSlot().addPetSlot(petX2);
               Main.player2.getPetSlot().getPetFromSlot(0).setLv(20);
            }
         }
         else if(numX1 == 45)
         {
            Main.player1.getPetSlot().getPetFromSlot(0).tiaoshiSkill(numX3);
         }
         else if(numX1 == 46)
         {
            NewYear_Interface.addJuan();
         }
         else if(numX1 == 47)
         {
            NewYear_Interface.addKey();
            for(i in Enemy.All)
            {
               (Enemy.All[i] as Enemy).life.setValue(2);
            }
         }
         else if(numX1 == 48)
         {
            ChestFactory.getDataByPro(numX2);
         }
         else if(numX1 == 49)
         {
            Main.player_1.playerJL.jiNengZD_num = 100;
         }
         else if(numX1 == 50)
         {
            Main.player1.getPetSlot().addPetSlot(Pet.creatPet(73126));
            Main.player1.getPetSlot().getPetFromSlot(0).setLv(20);
            Main.player1.getPetSlot().getPetFromSlot(0).setSkill(0);
            Main.player1.getPetSlot().getPetFromSlot(0).setSkill(1);
            Main.player1.getPetSlot().getPetFromSlot(0).setSkill(2);
            Main.player1.getPetSlot().getPetFromSlot(0).setSkill(3);
            NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17110));
            NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17105));
            NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17100));
            NewPetPanel.bag.addPetBag(PetEquipFactory.creatPetEquip(17095));
         }
         else if(numX1 == 51)
         {
            FiveOne_Interface.arrAll[2] += 5;
            FiveOne_Interface.arrAll[3] += 5;
            FiveOne_Interface.arrAll[4] += 5;
            if(numX1 == 99)
            {
               FiveOne_Interface.arrAll = [10,0,0,0,0,0,0,false,0];
            }
         }
         else if(numX1 == 52)
         {
            Main.player1.setEXP(-2147483645);
            TiaoShi.txtShow("-2147483645");
         }
         else if(numX1 == 53)
         {
            PK_UI.jiFenArr[0].setValue(999);
            txtShow("重置竞技场积分领取时间!");
         }
         else if(numX1 == 54)
         {
            PK_UI.jiFenArr[1].setValue(PK_UI.jiFenArr[1].getValue() + numX2);
         }
         else if(numX1 == 55)
         {
            TiaoShi.txtShow("技能石强化表长度 = " + GemProbabilityFactory.probabilityData.length);
         }
         else if(numX1 == 56)
         {
            if(qhXX)
            {
               qhXX = false;
            }
            else
            {
               qhXX = true;
            }
            TiaoShi.txtShow("强化XX = " + qhXX);
         }
         else if(numX1 == 57)
         {
            (PaiHang_Data.sArr2[numX3][numX4] as VT).setValue(numX2);
         }
         else if(numX1 == 58)
         {
            TiaoShi.txtShow("存档时间:" + Main.saveTimeX);
         }
         else if(numX1 == 61)
         {
            Main.HuiTie8Arr = [0,0,0,0,0,0,0,0,0,0];
            TiaoShi.txtShow("国庆活动领取奖励 清零!!!");
            Api_4399_GongHui.getNum([137]);
            TiaoShi.txtShow("国庆活动数值 查询~~~~~~~~~~~~~~~~~~~~~");
         }
         else if(numX1 == 62)
         {
            TiaoShi.txtShow("国庆活动数值 = " + Main.HuiTie8Arr);
         }
         else if(numX1 == 63)
         {
            if(numX2 == 2)
            {
               Main.player_2.data.getPetSlot().addPetSlotNum(1);
            }
            else
            {
               Main.player_1.data.getPetSlot().addPetSlotNum(1);
            }
         }
         else if(numX1 == 64)
         {
            TiaoShi.txtShow("当前剩余怪物数量:" + Enemy.All.length);
            for(i in Enemy.All)
            {
               en = Enemy.All[i];
               TiaoShi.txtShow(i + "怪物ID:" + en.id + ", life:" + en.life.getValue() + ", x:" + en.x + ", y:" + en.y);
            }
         }
         else if(numX1 == 65)
         {
            Enemy.All = new Array();
            TiaoShi.txtShow("当前剩余怪物数量清空");
         }
         else if(numX1 == 66)
         {
            TiaoShi.txtShow("杀死所有怪:" + Enemy.All.length);
            for(i in Enemy.All)
            {
               en = Enemy.All[i];
               TiaoShi.txtShow(i + "怪物ID:" + en.id + ", life:" + en.life.getValue() + ", x:" + en.x + ", y:" + en.y);
               en.HpXX2(9999999999);
            }
            trace("杀死所有怪?? >>",Enemy.All.length,GameData.刷怪总数.getValue(),Main.world.moveChild_Enemy.numChildren);
         }
         else if(numX1 == 100)
         {
            GongHui_Interface.Open();
         }
         else if(numX1 == 101)
         {
            Api_4399_GongHui.getList(1,50);
         }
         else if(numX1 == 102)
         {
            Api_4399_GongHui.SelUserInfo();
         }
         else if(numX1 == 103)
         {
            Api_4399_GongHui.DuiHuanGX(5);
         }
         else if(numX1 == 104)
         {
            Api_4399_GongHui.XiaoHaoGX(5);
         }
         else if(numX1 == 105)
         {
            Api_4399_GongHui.setRW(67);
         }
         else if(numX1 == 106)
         {
            Api_4399_GongHui.getBHCY_List(360);
         }
         else if(numX1 == 107)
         {
            for(i = 0; i < numX2; i++)
            {
               (PlanFactory.JiHuaData[i] as Plan).setState(1);
            }
         }
         else if(numX1 == 108)
         {
            GongHui_Interface.InfoXX();
         }
         else if(numX1 == 109)
         {
            Api_4399_GongHui.get_QX();
         }
         else if(numX1 == 110)
         {
            Api_4399_GongHui.set_QX();
         }
         else if(numX1 == 111)
         {
            arr = GongHuiRenWu.isType(numX2);
            TiaoShi.txtShow("GongHuiRenWu.isType" + numX2 + " = " + arr);
         }
         else if(numX1 == 112)
         {
            GongHuiRenWu.rwArr = null;
            GongHuiRenWu.SX(numX2);
         }
         else if(numX1 == 113)
         {
            GongHui_Interface.ListInfoPaiXu();
         }
         else if(numX1 == 114)
         {
            GongHui_Interface.xxArr[0] = numX2;
            GongHui_Interface.xxArr[1] = numX3;
            GongHui_Interface.xxArr[2] = numX4;
            TiaoShi.txtShow("GongHui_Interface.xxArr = " + GongHui_Interface.xxArr);
         }
         else if(numX1 == 115)
         {
            GongHuiTiaoZan.tzData = [0,[100,false],[100,false],[100,false]];
         }
         else if(numX1 == 116)
         {
            GongHui_jiTan.jiTanLv_arr[0] = true;
            GongHui_jiTan.jiTanLv_arr[1] = GongHui_jiTan.jiTanLv_arr[2] = GongHui_jiTan.jiTanLv_arr[3] = GongHui_jiTan.jiTanLv_arr[4] = numX2;
            TiaoShi.txtShow("公会祭坛技能等级 = " + GongHui_jiTan.jiTanLv_arr);
         }
         else if(numX1 == 117)
         {
            LingHunShi_Interface.pageNum = numX2;
            LingHunShi_Interface.Add_LHS(numX2);
         }
         else if(numX1 == 118)
         {
            LingHunShi_Interface.lhs_Data = null;
            LingHunShi_Interface.InitDataX();
         }
         else if(numX1 == 119)
         {
            en = new Enemy(numX2);
            Main.world.moveChild_Enemy.addChild(en);
            en.x = 500;
            en.y = 380;
         }
         else if(numX1 == 120)
         {
            WantedTaskSlot.JLTimes10XX();
         }
         else if(numX1 == 121)
         {
            TiaoShi.txtShow("logo播放: " + LogoGO.LogoGONum);
         }
         else if(numX1 == 122)
         {
            if(MonsterSlot.x100)
            {
               MonsterSlot.x100 = false;
               TiaoShi.txtShow("图鉴掉落100% 关闭");
            }
            else
            {
               MonsterSlot.x100 = true;
               TiaoShi.txtShow("图鉴掉落100% 开启");
            }
         }
         else if(numX1 == 123)
         {
            ChunJiePanel.saveArr2_2022[2] += 10;
            ChunJiePanel.saveArr2_2022[3] += 10;
         }
         else if(numX1 == 124)
         {
            NewPetPanel.bag.addPetBag(PetEquip.creatPetEquip(numX2));
         }
         else if(numX1 == 125)
         {
            AchData.cjPoint_1.setValue(numX2);
            AchData.cjPoint_2.setValue(numX2);
         }
         else if(numX1 == 126)
         {
            ChunJiePanel.saveArr_2022[0] += 25;
         }
         else if(numX1 == 127)
         {
            ChongZhi_Interface.type = numX2;
            ChongZhi_Interface.lingQuTime = 0;
            ChongZhi_Interface.HDpointNUM.setValue(9999);
            ChongZhi_Interface.Show();
         }
         else if(numX1 == 128)
         {
            Main.player1.getStampSlot().slot11_key = [1,1,1,1];
         }
         else if(numX1 == 129)
         {
            for(i = 1; i <= 3; i++)
            {
               PaiHang_Data.jiFenArr[i] = VT.createVT(499);
            }
         }
         else if(numX1 == 130)
         {
            Main.player1.stampSlot = StampSlot.createStampSlot();
         }
         else if(numX1 == 131)
         {
            trace("修正强化点数数值 测试");
            XiuZeng.XiuZengAll();
         }
         else if(numX1 == 888)
         {
            timeXXX = Main.serverTime.getValue() / 100;
            this.temp_txt.text = PaiHang_Data.newTime + "," + timeXXX + "," + PaiHang_Data.sArr2.length;
         }
         else if(numX1 == 999)
         {
            TiaoShi.txtShow("原先键位1:" + Main.player1._keyArr);
            if(Main.player2)
            {
               TiaoShi.txtShow("原先键位2:" + Main.player2._keyArr);
            }
            ysArr = [87,83,65,68,74,75,76,72,85,73,79,49,50,51,32,78];
            ysArr2 = [38,40,37,39,97,98,99,100,101,102,107,103,104,105,96,34];
            Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
            if(Main.player2)
            {
               Main.player2._keyArr = DeepCopyUtil.clone(ysArr2);
            }
         }
         else
         {
            TiaoShi.txtShow("参数错误");
         }
      }
      
      private function lunTan8Time_funXXX(e:*) : *
      {
         Api_4399_GongHui.upNum(137);
         TiaoShi.txtShow("国庆活动数值 增加!");
         this.lunTan8Time = new Timer(2000,1);
         this.lunTan8Time.addEventListener(TimerEvent.TIMER,this.lunTan8Time_fun);
         this.lunTan8Time.start();
      }
      
      private function lunTan8Time_fun(e:*) : *
      {
         Api_4399_GongHui.getNum([137]);
         TiaoShi.txtShow("国庆活动数值 查询~~~~~~~~~~~~~~~~~~~~~");
      }
   }
}

