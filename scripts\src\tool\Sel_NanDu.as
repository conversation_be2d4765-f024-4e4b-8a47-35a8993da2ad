package src.tool
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   import src._data.*;
   
   public class Sel_NanDu extends MovieClip
   {
      
      public var NanDu1_mc:MovieClip;
      
      public var NanDu2_mc:MovieClip;
      
      public var NanDu3_mc:MovieClip;
      
      public var Star2:MovieClip;
      
      public var Star3:MovieClip;
      
      public var NanDu1_btn:SimpleButton;
      
      public var NanDu2_btn:SimpleButton;
      
      public var NanDu3_btn:SimpleButton;
      
      public var info_mc:MovieClip;
      
      public var info_txt:*;
      
      public var name_txt:*;
      
      public var t_txt:*;
      
      public var cj_0:*;
      
      public var cj_1:*;
      
      public var cj_2:*;
      
      public var cj_3:*;
      
      public var cj_4:*;
      
      public var cj_5:*;
      
      public var obj_1:*;
      
      public var obj_2:*;
      
      public var obj_3:*;
      
      public var obj_4:*;
      
      public var obj_5:*;
      
      public var obj_6:*;
      
      public var obj_7:*;
      
      public var obj_8:*;
      
      public var obj_9:*;
      
      public var obj_10:*;
      
      public var obj_11:*;
      
      public var obj_12:*;
      
      public var gmeName_txt:*;
      
      public var Close_btn:SimpleButton;
      
      public var type:uint = 1;
      
      private var selLV:uint = 1;
      
      public var GameNumNameArr:Array;
      
      public function Sel_NanDu()
      {
         var mc:Shop_picNEW = null;
         this.GameNumNameArr = [[1,"落月之原"],[2,"落月之森"],[3,"冰雪废墟"],[4,"死亡流沙"],[5,"万年雪山"],[6,"废弃都市"],[7,"火山的噩梦"],[8,"堕落城堡"],[9,"幽灵船"],[10,"机械城"],[11,"雪狼巢穴"],[12,"火之祭坛"],[13,"暗影城"],[14,"暗夜遗迹"],[15,"机械试验场"],[16,"熔岩城堡"],[101,"神秘1"],[102,"神秘2"],[103,"神秘3"],[104,"神秘4"],[105,"神秘5"],[51,"艾尔之海"],[52,"安塔利亚"],[53,"阿肯色"],[54,"雅利安"],[55,"奥戈"],[56,"波塞笛亚"]];
         super();
         this.NanDu1_btn.addEventListener(MouseEvent.CLICK,this.NanDu1);
         this.NanDu2_btn.addEventListener(MouseEvent.CLICK,this.NanDu2);
         this.NanDu3_btn.addEventListener(MouseEvent.CLICK,this.NanDu3);
         this.NanDu1_btn.addEventListener(MouseEvent.ROLL_OVER,this.NanDu1_ROLL_OVER);
         this.NanDu2_btn.addEventListener(MouseEvent.ROLL_OVER,this.NanDu2_ROLL_OVER);
         this.NanDu3_btn.addEventListener(MouseEvent.ROLL_OVER,this.NanDu3_ROLL_OVER);
         for(var i:uint = 1; i <= 12; i++)
         {
            mc = new Shop_picNEW();
            this["obj_" + i] = mc;
            addChild(mc);
            mc.x = 116.4 + (i - 1) % 6 * 66.5;
            mc.y = 376 + uint((i - 1) / 6) * 69.5;
            this["obj_" + i].name = "obj_" + i;
            this["obj_" + i].addEventListener(MouseEvent.ROLL_OVER,this.SelObj);
            this["obj_" + i].addEventListener(MouseEvent.ROLL_OUT,this.SelObj_out);
         }
         addChild(this.info_mc);
         this.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.info_mc.x = this.info_mc.y = -2000;
      }
      
      public function Open(xx:uint = 0, yy:uint = 0, _type:uint = 1) : *
      {
         this.info_mc.x = this.info_mc.y = -2000;
         this.type = _type;
         this.NanDu1_mc.gotoAndStop("d" + Main.gameNum.getValue());
         this.NanDu2_mc.gotoAndStop("d" + Main.gameNum.getValue());
         this.NanDu3_mc.gotoAndStop("d" + Main.gameNum.getValue());
         this.x = xx;
         this.y = yy;
         this.visible = true;
         this.NanDu2_btn.visible = this.NanDu3_btn.visible = false;
         TweenMax.to(this.NanDu2_mc,0,{"colorMatrixFilter":{"saturation":0}});
         TweenMax.to(this.NanDu3_mc,0,{"colorMatrixFilter":{"saturation":0}});
         TweenMax.to(this.Star2,0,{"colorMatrixFilter":{"saturation":0}});
         TweenMax.to(this.Star3,0,{"colorMatrixFilter":{"saturation":0}});
         if(Main.guanKa[Main.gameNum.getValue()] > 1)
         {
            this.NanDu2_btn.visible = true;
            TweenMax.to(this.NanDu2_mc,0,{"colorMatrixFilter":{"saturation":1}});
            TweenMax.to(this.Star2,0,{"colorMatrixFilter":{"saturation":1}});
         }
         if(Main.guanKa[Main.gameNum.getValue()] > 2)
         {
            this.NanDu3_btn.visible = true;
            TweenMax.to(this.NanDu3_mc,0,{"colorMatrixFilter":{"saturation":1}});
            TweenMax.to(this.Star3,0,{"colorMatrixFilter":{"saturation":1}});
         }
         this.NanDu1_ROLL_OVER();
         this.CengJiuShow();
      }
      
      public function Close(e:* = null) : *
      {
         this.x = this.y = 5000;
         this.visible = false;
         Main._this.addChild(Play_Interface.interfaceX);
      }
      
      private function NanDu1(e:*) : *
      {
         GameData.gameLV = 1;
         this.GameGo();
      }
      
      private function NanDu2(e:*) : *
      {
         GameData.gameLV = 2;
         this.GameGo();
      }
      
      private function NanDu3(e:*) : *
      {
         GameData.gameLV = 3;
         this.GameGo();
      }
      
      private function NanDu1_ROLL_OVER(e:* = null) : *
      {
         this.selLV = 1;
         var arr:Array = GaneObjFactory.GetNumArr(Main.gameNum.getValue(),1);
         for(var i:uint = 1; i <= 12; i++)
         {
            this["obj_" + i].gotoAndStop(arr[i]);
         }
      }
      
      private function NanDu2_ROLL_OVER(e:*) : *
      {
         this.selLV = 2;
         var arr:Array = GaneObjFactory.GetNumArr(Main.gameNum.getValue(),2);
         for(var i:uint = 1; i <= 12; i++)
         {
            this["obj_" + i].gotoAndStop(arr[i]);
         }
      }
      
      private function NanDu3_ROLL_OVER(e:*) : *
      {
         this.selLV = 3;
         var arr:Array = GaneObjFactory.GetNumArr(Main.gameNum.getValue(),3);
         for(var i:uint = 1; i <= 12; i++)
         {
            this["obj_" + i].gotoAndStop(arr[i]);
         }
      }
      
      private function SelObj(e:MouseEvent) : *
      {
         var num:uint = uint((e.target.name as String).substr(4,2));
         var arr:Array = GaneObjFactory.GetInfo(Main.gameNum.getValue(),this.selLV,num);
         if(arr[0] != 0)
         {
            this.info_mc.x = e.target.x + 50;
            this.info_mc.y = e.target.y - 5;
            this.info_mc.name_txt.text = arr[0];
            this.info_mc.info_txt.text = arr[1];
            if(arr[2] == 2)
            {
               this.ColorX(this.info_mc.name_txt,"0x0066ff");
            }
            else if(arr[2] == 3)
            {
               this.ColorX(this.info_mc.name_txt,"0xFF3399");
            }
            else if(arr[2] == 4)
            {
               this.ColorX(this.info_mc.name_txt,"0xFF9933");
            }
         }
         else
         {
            this.info_mc.x = this.info_mc.y = -2000;
         }
      }
      
      private function SelObj_out(e:MouseEvent) : *
      {
         this.info_mc.x = this.info_mc.y = -2000;
      }
      
      private function GameGo() : *
      {
         this.Close();
         SelMap.Close();
         Main.gameNum2.setValue(1);
         Main._this.Loading();
      }
      
      private function CengJiuShow() : *
      {
         var i:uint = 0;
         this.gmeName_txt.text = "每日关卡成就";
         for(i = 0; i < this.GameNumNameArr.length; i++)
         {
            if(Main.gameNum.getValue() == this.GameNumNameArr[i][0])
            {
               this.gmeName_txt.text = this.GameNumNameArr[i][1] + "每日关卡成就";
               break;
            }
         }
         var arr:Array = AchPanel.getStateByGkId(Main.gameNum.getValue());
         for(i in arr)
         {
            if(arr[i] == 3)
            {
               this.ColorX(this["cj_" + i],"0x66FF00");
            }
            else
            {
               this.ColorX(this["cj_" + i],"0xFFFFFF");
            }
         }
      }
      
      private function ColorX(t:*, col:String) : *
      {
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         t.setTextFormat(myTextFormat);
      }
   }
}

