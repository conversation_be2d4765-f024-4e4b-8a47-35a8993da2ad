package src.tool
{
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.text.*;
   import src.*;
   
   public class Yayale_LingQue extends MovieClip
   {
      
      private static var skin:MovieClip;
      
      private static var skin2:MovieClip;
      
      private static var loadData:ClassLoader;
      
      private static var objX_ID:int;
      
      private static var who:Player;
      
      public static var _this:Yayale_LingQue = new Yayale_LingQue();
      
      public static var loadName:String = "bibabo_v1095.swf";
      
      public static var loadingOK:int = 0;
      
      private static var openNum:int = 0;
      
      private static var urlLoader:URLLoader = new URLLoader();
      
      private static var phpUrl:URLRequest = new URLRequest("http://my.4399.com/jifen/activation");
      
      public function Yayale_LingQue()
      {
         super();
      }
      
      public static function Open() : *
      {
      }
      
      public static function Close(e:* = null) : *
      {
         skin.parent.removeChild(skin);
      }
      
      public static function LoadSkin() : *
      {
         if(loadingOK == 0)
         {
            loadingOK = 1;
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = null;
         TiaoShi.txtShow("比巴卜魔力联盟 加载完成");
         loadingOK = 2;
         classRef = loadData.getClass("Skin_1") as Class;
         skin = new classRef();
         _this.addChild(skin);
         skin._txt.addEventListener(MouseEvent.CLICK,onTXT);
         skin._btn.addEventListener(MouseEvent.CLICK,onLingQue);
         skin._close_btn.addEventListener(MouseEvent.CLICK,Close);
         skin._txt2.addEventListener(MouseEvent.CLICK,onTXT2);
         skin._btn2.addEventListener(MouseEvent.CLICK,onLingQue);
         classRef = loadData.getClass("Skin_2") as Class;
         skin2 = new classRef();
         _this.addChild(skin2);
         skin2.dh1.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh2.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh3.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh4.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.dh5.addEventListener(MouseEvent.CLICK,DuiahuanX);
         skin2.close.addEventListener(MouseEvent.CLICK,close2);
         if(openNum == 1)
         {
            Open();
            openNum = 0;
         }
         else if(openNum == 2)
         {
            DuiHuanOpen(who);
            openNum = 0;
         }
      }
      
      private static function onTXT(e:*) : *
      {
         skin._txt.text = "";
      }
      
      private static function onTXT2(e:*) : *
      {
         skin._txt2.text = "";
      }
      
      private static function onLingQue(e:MouseEvent) : *
      {
         var str:String = null;
         if(Main.P1P2)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1 || Main.player2.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
               return;
            }
         }
         else if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
            return;
         }
         phpUrl.method = URLRequestMethod.POST;
         if(e.target.name == "_btn")
         {
            str = skin._txt.text;
         }
         else
         {
            str = skin._txt2.text;
         }
         TiaoShi.txtShow("e.target.name = " + e.target.name);
         var uv:URLVariables = new URLVariables();
         uv.activation = str;
         uv.uid = Main.userId;
         uv.uniqueId = 7;
         var tempStr:* = uv.activation + "-" + uv.uid + "-" + uv.uniqueId + "-" + "d7adb1789427ea38327ad146e92e1dd4";
         var tokenStr:String = MD5contrast.GetScoreMD5(tempStr);
         uv.token = tokenStr;
         phpUrl.data = uv;
         urlLoader.load(phpUrl);
         urlLoader.addEventListener(Event.COMPLETE,completeHandler_All);
         skin._btn.visible = skin._btn2.visible = false;
      }
      
      private static function completeHandler_All(e:Event) : void
      {
         var str:String = (e.currentTarget as URLLoader).data;
         var objX:Object = JSONs.decode(str,true);
         skin._btn.visible = skin._btn2.visible = true;
         if(objX.code == 100)
         {
            TiaoShi.txtShow("礼包ID = " + objX.result);
            if(objX.result == 75)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[7] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[7];
               str = "双节礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 25)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[4] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[4];
               str = "安全礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 78)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[6] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[6];
               str = "桌面礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 95)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[8] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[8];
               str = "速升礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 113)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[9] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[9];
               str = "豆娃礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 123)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[12] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[12];
               str = "绝世礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 154)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[14] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[14];
               str = "周年礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 190)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
               }
               str = "星灵礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 290)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[21] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[21];
               str = "双蛋豪华礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 269)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[22] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[22];
               str = "双蛋礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 314)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[23] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[23];
               str = "4399新春礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 570)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[25] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[25];
               str = "客户端专属礼包 领取成功!!";
               Api_4399_GongHui.upNum(15);
               Main.Save();
            }
            else if(objX.result == 894)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[26] >= 3)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取3次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[26];
               str = "五一礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 1362)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[29] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[29];
               str = "赛龙舟礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 1878)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[30] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[30];
               str = "尊享礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 2270)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[31] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[31];
               str = "七夕礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 2922)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[34] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
               }
               TeShuHuoDong.AddLiBaoNum(35);
               str = "中秋礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 7686)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[48] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
               }
               TeShuHuoDong.AddLiBaoNum(48);
               str = "羊年礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 10012)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[57] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
               }
               TeShuHuoDong.AddLiBaoNum(57);
               str = "4399游戏盒尊贵礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 10014)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[58] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
               }
               TeShuHuoDong.AddLiBaoNum(58);
               str = "4399游戏盒分享礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 10996)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[59] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
               }
               TeShuHuoDong.AddLiBaoNum(59);
               str = "勇士的信仰翻牌礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 13770)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[68] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
               }
               TeShuHuoDong.AddLiBaoNum(68);
               str = "豪华礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 13772)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[69] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
               }
               TeShuHuoDong.AddLiBaoNum(69);
               str = "伴我同行礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 19114)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[82] >= 2)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档每天只能领取2次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
               }
               TeShuHuoDong.AddLiBaoNum(82);
               str = "国庆礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 19422)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               }
               TeShuHuoDong.AddLiBaoNum(83);
               str = "星灵礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 19420)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
               }
               TeShuHuoDong.AddLiBaoNum(84);
               str = "神宠礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21050)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[97] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63374));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63374));
               }
               TeShuHuoDong.AddLiBaoNum(97);
               str = "双旦pk礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22122)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[102] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63380));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63380));
               }
               TeShuHuoDong.AddLiBaoNum(102);
               str = "猴年礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 20888)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[93] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63370));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63370));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63370));
               }
               TeShuHuoDong.AddLiBaoNum(93);
               str = "12月份1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 20890)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[94] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63371));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63371));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63371));
               }
               TeShuHuoDong.AddLiBaoNum(94);
               str = "12月份7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 20892)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[95] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63372));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63372));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63372));
               }
               TeShuHuoDong.AddLiBaoNum(95);
               str = "12月份15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 20894)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[96] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63373));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63373));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63373));
               }
               TeShuHuoDong.AddLiBaoNum(96);
               str = "12月份25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21482)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[98] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63354));
               TeShuHuoDong.AddLiBaoNum(98);
               str = "1月份1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21484)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[99] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63355));
               TeShuHuoDong.AddLiBaoNum(99);
               str = "1月份7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21486)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[100] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63356));
               TeShuHuoDong.AddLiBaoNum(100);
               str = "1月份15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21488)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[101] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63357));
               TeShuHuoDong.AddLiBaoNum(101);
               str = "1月份25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22520)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[98] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(98);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22522)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[99] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(99);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22524)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[100] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(100);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22526)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[101] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(101);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22528)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[107] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(107);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22530)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[108] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(108);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22532)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[109] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(109);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22534)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[110] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(110);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 23200)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[111] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63378));
               TeShuHuoDong.AddLiBaoNum(111);
               str = "比巴卜普通礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 23202)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[112] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63379));
               TeShuHuoDong.AddLiBaoNum(112);
               str = "比巴卜至尊礼包 领取成功!!";
               Main.Save();
            }
            else
            {
               str = "验证码错误!!";
            }
         }
         else if(objX.code == 101)
         {
            str = "参数错误";
         }
         else if(objX.code == 102)
         {
            str = "验证码不存在";
         }
         else if(objX.code == 103)
         {
            str = "验证码还没被兑换";
         }
         else if(objX.code == 104)
         {
            str = "验证码被使用过";
         }
         else if(objX.code == 105)
         {
            str = "验证码只能被领取者使用";
         }
         else if(objX.code == 106)
         {
            str = "您的账号已经使用过此礼包的激活码";
         }
         else if(objX.code == 107)
         {
            str = "token无效";
         }
         else if(objX.code == 108)
         {
            str = "激活码失效了";
         }
         else if(objX.code == 109)
         {
            str = "激活失败";
         }
         else if(objX.code == 110)
         {
            str = "您的账号今天已使用过激活码";
         }
         else
         {
            str = "未知错误";
         }
         NewMC.Open("文字提示",Main._stage,480,450,60,0,true,1,str);
      }
      
      public static function DuiHuanOpen(p:Player) : *
      {
         LoadSkin();
         if(loadingOK == 2)
         {
            Main._this.addChild(skin2);
            ItemsPanel.close();
            skin2.visible = true;
         }
         else
         {
            NewMC.Open("文字提示",Main._this,400,400,30,0,true,2,"伢牙乐兑换界面加载中,请稍候...");
            openNum = 2;
         }
         who = p;
      }
      
      private static function DuiahuanX(e:MouseEvent) : *
      {
         var num:int = int((e.target.name as String).substr(2,1));
         if(num == 1)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14643));
               for(i = 0; i < 5; ++i)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(num == 2)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14644));
               for(i = 0; i < 5; ++i)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(num == 3)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14649));
               for(i = 0; i < 5; ++i)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(num == 4)
         {
            if(who.getBag().backOtherBagNum() >= 2 && who.getBag().backequipBagNum() >= 1)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addEquipBag(EquipFactory.createEquipByID(14652));
               for(i = 0; i < 5; ++i)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               }
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(num == 5)
         {
            if(who.getBag().backOtherBagNum() >= 2)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[28] <= 0)
               {
                  who.getBag().addOtherobjBag(OtherFactory.creatOther(63279));
                  ++TeShuHuoDong.TeShuHuoDongArr[28];
               }
               who.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
               who.getBag().delOtherById(63277);
               skin2.visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功!");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
      }
      
      private static function close2(e:*) : *
      {
         skin2.visible = false;
      }
   }
}

