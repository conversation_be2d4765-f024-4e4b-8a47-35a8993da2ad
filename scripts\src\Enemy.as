package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.quest.Quest;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.quest.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.Skin.*;
   import src.other.*;
   import src.tool.*;
   
   public class Enemy extends MovieClip
   {
      
      public static var EnemyXml:XML;
      
      public static var hit_x:int;
      
      public static var hit_y:int;
      
      public static var xuanshang5:Boolean = true;
      
      public static var wscd:int = 0;
      
      public static var EnemyArr:Array = new Array();
      
      public static var EnemyXmlArrMd5:Array = new Array();
      
      public static var EnemyXmlArr:Array = new Array();
      
      public static var All:Array = [];
      
      public static var only:Boolean = true;
      
      public static var gongJiPOWER:VT = VT.createVT();
      
      public static var hpNumX:Number = -1;
      
      public static var addTimes:int = 0;
      
      public static var bool_5:Boolean = true;
      
      public static var bool_6:Boolean = true;
      
      public static var boss54:int = 0;
      
      public var RLXX:String = "null";
      
      public var buffTime_jl2016:int;
      
      public var buffTime_jl2016_X2:Number;
      
      public var jianShangPER:Number = 0;
      
      public var cengshu:Array;
      
      public var rebornTimes:int = 0;
      
      public var iii:int = 0;
      
      public var zhuoshaoTimes:int = 0;
      
      public var shanghaiTemp:int = 0;
      
      public var yingzhiTemp:int = 0;
      
      public var id:int;
      
      public var className:String;
      
      public var isFunction:Boolean = true;
      
      public var lifeMAX:VT;
      
      public var life:VT;
      
      public var isIce:Boolean = false;
      
      public var RL:Boolean = true;
      
      public var over:Boolean;
      
      public var skinArr:Array;
      
      public var skinNum:int = 0;
      
      public var skin:EnemySkin;
      
      public var hitXX:HitXX;
      
      public var hit:MovieClip = null;
      
      public var jumptemp:int;
      
      public var att_temp:* = 0;
      
      public var walk_temp:* = 0;
      
      public var walk_power:* = 3;
      
      public var jump_power:* = 120;
      
      public var jump_time:* = 8;
      
      public var parabola:Number = 0.2;
      
      public var jumping:Boolean;
      
      public var jumpType:int = 2;
      
      public var gravity:int = 2;
      
      public var gravityXX:int = 2;
      
      public var gravityNum:* = 0;
      
      public var lastTime:int;
      
      public var 硬直:VT;
      
      public var 攻击力:VT;
      
      public var 防御力:VT;
      
      public var 魔攻力:VT;
      
      public var 魔防力:VT;
      
      public var 震退值:VT;
      
      public var 经验:VT;
      
      public var 等级:VT;
      
      public var 掉落物品:Array;
      
      public var 掉落概率:Array;
      
      public var 金钱:VT;
      
      public var 掉落数量:VT;
      
      public var 浮动硬直:Number = 0;
      
      public var 关卡:int = 0;
      
      public var changeMode:Boolean = true;
      
      public var distance_X:int;
      
      public var distance_Y:int;
      
      public var guard_XY:Array;
      
      public var attack_XY:Array;
      
      public var attack_Time:Array;
      
      public var attack_TimeNum:Array;
      
      public var runType:String = "自由";
      
      public var noMove:Boolean = false;
      
      public var lifeMC:EnemyLife;
      
      public var noHitMap:Boolean = false;
      
      public var gjcd_Time:int = 0;
      
      public var heightXXX:int = 0;
      
      public var noGongJiTime:*;
      
      public var hitHpX:Number = 1;
      
      private var ZhenShe_Mc:MovieClip;
      
      public var hitHpDownUP:Number = 1;
      
      public var temp2:VT;
      
      public var temp3:VT;
      
      public var temp10:VT;
      
      public var tempXXX2:VT;
      
      public var rdom:int = 0;
      
      public var cj_arr:Array;
      
      internal var iceTime:int = 0;
      
      internal var temp:String;
      
      internal var tempXX:String;
      
      internal var tempTimeXX:int = 0;
      
      public var runX:int = 0;
      
      public var runY:int = 0;
      
      public var runArr:Array;
      
      public var EnemySkinXml:XML;
      
      public var 特殊掉落物品:Array;
      
      public var 特殊掉落概率:Array;
      
      public function Enemy(i:int = 0)
      {
         var name:String = null;
         this.cengshu = [];
         this.lifeMAX = VT.createVT();
         this.life = VT.createVT();
         this.skinArr = [0,1];
         this.硬直 = VT.createVT();
         this.攻击力 = VT.createVT();
         this.防御力 = VT.createVT();
         this.魔攻力 = VT.createVT();
         this.魔防力 = VT.createVT();
         this.震退值 = VT.createVT();
         this.经验 = VT.createVT();
         this.等级 = VT.createVT();
         this.掉落物品 = [];
         this.掉落概率 = [];
         this.金钱 = VT.createVT();
         this.掉落数量 = VT.createVT();
         this.guard_XY = [10,10];
         this.attack_XY = [];
         this.attack_Time = [];
         this.attack_TimeNum = [];
         this.lifeMC = new EnemyLife();
         this.temp2 = VT.createVT(2);
         this.temp3 = VT.createVT(3);
         this.temp10 = VT.createVT(10);
         this.tempXXX2 = VT.createVT(VT.GetTempVT("8/4"));
         this.cj_arr = [[3,3000],[6,3500],[9,4000],[13,4200],[16,4400],[19,4600],[23,4700],[28,4800],[32,4900],[35,5000],[41,5100],[46,6000],[50,5100],[55,5200],[58,5400],[62,5500],[65,5600],[203,6500],[201,6500],[202,6500],[204,6500],[205,6500],[206,6500],[207,6500],[208,6500],[209,6500],[210,6500],[211,6500],[212,6500],[213,6500],[214,6500],[215,6500],[68,7200],[71,7300],[216,6500],[217,6500],[218,6500],[74,7700],[78,7800]];
         this.runArr = new Array();
         this.EnemySkinXml = new XML();
         this.特殊掉落物品 = [];
         this.特殊掉落概率 = [];
         super();
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         if(i != 0)
         {
            this.id = i;
            name = this.SkinData(i);
            if(name == null)
            {
               TiaoShi.txtShow("怪物信息错误!! 找不到ID" + i);
               this.Dead();
               return;
            }
            this.addSkin(name);
            All[All.length] = this;
            this.skin.EnemySkinXml = EnemySkin.EnemySkinXmlArr[GameData.gameLV][this.关卡];
         }
         else
         {
            TiaoShi.txtShow("无法创建 怪物0");
         }
      }
      
      public static function hpNUMXXX(hpDown:int) : *
      {
         if(hpNumX >= 0)
         {
            hpNumX += hpDown * 0.04;
         }
      }
      
      public static function hpNUMXXX2(_this:Object, getWho:Object, hpDown:int) : *
      {
         var num:int = 0;
         if(_this is Fly && _this is Cw17_Fly3x && getWho is ChongWu && getWho.who is Player)
         {
            num = hpDown * 0.008;
            (getWho.who as Player).HpUp(num);
         }
      }
      
      public static function getEnemyNum(strf:String) : int
      {
         var num:int = 0;
         for(i in All)
         {
            if(All[i].className == strf)
            {
               num++;
            }
         }
         return num;
      }
      
      public function addSkin(str:String) : *
      {
         var classRef:Class = EnemyArr[this.关卡].getClass(str) as Class;
         this.skin = new classRef();
         addChild(this.skin);
         this.skin.who = this;
         if(GameData.gameLV == 6)
         {
            this.scaleX = this.scaleY = 1.5;
         }
         this.skin.id = this.id;
         this.set_attack_Time();
         addChild(this.lifeMC);
         this.lifeMC.stop();
         this.lifeMC.y = -this.skin.height;
         this.setMode();
      }
      
      public function setMode() : *
      {
         var classRef:Class = null;
         if(this.id == 2014 || this.id == 2018)
         {
            this.changeMode = false;
         }
         if(this.className == "罗生门" || this.id == 7004 || this.id == 7005 || this.id == 2018 || this.id == 2013 || this.id == 2014 || this.id == 5015 || this.id == 5016 || this.id == 5017 || this.id == 5018 || this.id == 5019 || this.id == 5020 || this.id == 5021 || this.id == 5022 || this.id == 2012 || this.id == 79 || this.id == 90 || this.id == 88)
         {
            this.isFunction = false;
         }
         if(this.className == "悬赏9")
         {
            classRef = EnemyArr[this.关卡].getClass("黑暗护罩") as Class;
            this.skin.addChild(new classRef());
         }
      }
      
      public function SkinData(num:int) : String
      {
         var i:* = undefined;
         var ID:int = 0;
         var 警戒:int = 0;
         var 警戒randomXX:int = 0;
         var j:int = 0;
         for(i in EnemyXml.怪物)
         {
            ID = int(EnemyXml.怪物[i].ID);
            if(ID == num)
            {
               this.className = String(EnemyXml.怪物[i].名称);
               this.lifeMAX.setValue(int(EnemyXml.怪物[i].HP));
               this.life.setValue(int(EnemyXml.怪物[i].HP));
               this.walk_power = this.walk_temp = int(EnemyXml.怪物[i].移动速度);
               this.jumptemp = int(EnemyXml.怪物[i].跳跃力);
               this.jump_power = int(EnemyXml.怪物[i].跳跃力);
               this.jump_time = int(EnemyXml.怪物[i].跳跃时间);
               this.gravity = int(EnemyXml.怪物[i].重力);
               this.gravityXX = int(EnemyXml.怪物[i].重力);
               this.攻击力.setValue(Number(EnemyXml.怪物[i].攻击力));
               this.att_temp = Number(EnemyXml.怪物[i].攻击力);
               this.防御力.setValue(int(EnemyXml.怪物[i].防御力));
               this.魔攻力.setValue(int(EnemyXml.怪物[i].魔攻力));
               this.魔防力.setValue(int(EnemyXml.怪物[i].魔防力));
               this.震退值.setValue(Number(EnemyXml.怪物[i].震退值));
               this.经验.setValue(int(EnemyXml.怪物[i].经验));
               this.等级.setValue(int(EnemyXml.怪物[i].等级));
               this.lastTime = int(EnemyXml.怪物[i].存在时间);
               this.金钱.setValue(int(EnemyXml.怪物[i].金钱));
               this.掉落数量.setValue(int(EnemyXml.怪物[i].掉落数量));
               this.硬直.setValue(Number(EnemyXml.怪物[i].硬直));
               this.关卡 = int(EnemyXml.怪物[i].关卡);
               警戒 = int(EnemyXml.怪物[i].警戒距离x);
               警戒randomXX = 警戒 - Math.random() * 51;
               if(警戒randomXX < 0)
               {
                  警戒randomXX = 0;
               }
               this.guard_XY = [警戒randomXX,int(EnemyXml.怪物[i].警戒距离y)];
               for(j = 0; j < 6; j++)
               {
                  if(int(EnemyXml.怪物[i]["攻击距离" + (j + 1) + "x"]) != -1)
                  {
                     this.attack_XY[j] = [int(EnemyXml.怪物[i]["攻击距离" + (j + 1) + "x"]),int(EnemyXml.怪物[i]["攻击距离" + (j + 1) + "y"])];
                  }
               }
               for(j = 0; j < 20; j++)
               {
                  this.掉落物品[j] = int(EnemyXml.怪物[i]["掉落物品" + (j + 1)]);
               }
               for(j = 0; j < 20; j++)
               {
                  this.掉落概率[j] = int(EnemyXml.怪物[i]["掉落概率" + (j + 1)]);
               }
               for(j = 0; j < 3; j++)
               {
                  this.特殊掉落物品[j] = int(EnemyXml.怪物[i]["特殊掉落ID" + (j + 1)]);
               }
               for(j = 0; j < 3; j++)
               {
                  this.特殊掉落概率[j] = int(EnemyXml.怪物[i]["特殊概率ID" + (j + 1)]);
               }
               return String(EnemyXml.怪物[i].名称);
            }
         }
         return null;
      }
      
      public function GongHuiHp_Num() : *
      {
         var numX:int = 0;
         var HPnum:int = 0;
         if(GongHuiTiaoZan.tzData)
         {
            if(GameData.gameLV == 6)
            {
               numX = int(GongHuiTiaoZan.tzData[Main.gameNum.getValue() - 5000][0]);
               HPnum = this.lifeMAX.getValue() * numX / 100;
               this.life.setValue(HPnum);
               TiaoShi.txtShow("GongHuiHp_Num 剩余血量 = " + HPnum);
            }
         }
      }
      
      public function getRandom(num:Number) : Boolean
      {
         var rd:Number = Math.random() * 100;
         if(rd < num)
         {
            return true;
         }
         return false;
      }
      
      public function HpXX(hitX:HitXX, bj:Boolean = false, back:Boolean = false) : *
      {
         var p:Player = null;
         var hpDown:int = 0;
         var xxx:int = 0;
         var xxx2:int = 0;
         var yyy:int = 0;
         var playHPxx:Number = NaN;
         var hpXX:Number = NaN;
         var xx1415:Number = NaN;
         var getAllSuitSkill:Array = null;
         var skillDown:int = 0;
         var i:int = 0;
         var arrX14:Array = null;
         var arrX15:Array = null;
         var hxx:HitXX = null;
         var hpNum:Number = NaN;
         var arr:Array = null;
         var str:String = null;
         var j:int = 0;
         var strXXX:String = null;
         var rrr:int = 0;
         var crit_temp:Number = NaN;
         var c_temp:Number = NaN;
         var numXX:Number = NaN;
         var baoJiXX:Number = NaN;
         var xxxxx:BuffEnemy = null;
         var pppwho:Player = null;
         var h:HitXX = null;
         var class_2:Class = null;
         var sheguan:Class = null;
         var hpX:* = undefined;
         var hpHP:int = 0;
         var CYjianshang:Number = NaN;
         var class_JG:Class = null;
         var class_1:Class = null;
         var cishu:int = 0;
         var classRef2:Class = null;
         var classRef3:Class = null;
         var classDark:Class = null;
         var fff:SkillDarkBoom = null;
         var ran:int = 0;
         var whoX2:Player = null;
         var pXX:Player = null;
         var num:int = 0;
         var whoX:Player = null;
         var lifeX:int = 0;
         var expXXX:int = 0;
         var numXXXX:int = 0;
         this.hitXX = hitX;
         var attTimes:int = hitX.times;
         var 职业附加:Number = 1;
         xxx = this.x + Math.random() * 100 - 50;
         xxx2 = this.x + Math.random() * 100 - 50;
         yyy = this.y + Math.random() * 100 - 50;
         var yyy2:int = this.y + Math.random() * 100;
         if(hitX.who is Player || hitX.who is ChongWu2)
         {
            if(hitX.who is Player && Main.gameNum.getValue() == 84 && Boss84FP.paiState == 4)
            {
               (hitX.who as Player).fbChengFa = true;
            }
            if(hitX.who is Player)
            {
               p = hitX.who;
            }
            else
            {
               p = (hitX.who as ChongWu2).who;
            }
            if(p.playerJL)
            {
               ++p.playerJL.ZD1num;
               p.playerJL.JNnumUP(this.x + Main.world.x,this.y,Play_Interface.interfaceX);
            }
            if(hitX.who == Main.player_1)
            {
               ChongWu.gongJiYn1 = true;
            }
            else
            {
               ChongWu.gongJiYn2 = true;
            }
            if(p.data.skinArr[p.data.skinNum] == 0)
            {
               职业附加 = Number(p.职业附加[0]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 1)
            {
               职业附加 = Number(p.职业附加[1]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 2)
            {
               职业附加 = Number(p.职业附加[2]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 3)
            {
               职业附加 = Number(p.职业附加[3]);
            }
            playHPxx = p.use_gongji.getValue();
            hpXX = playHPxx * hitX.gongJi_hp * 职业附加;
            hpXX += hpXX * Math.random() * InitData.X002.getValue();
            if(attTimes == 0)
            {
               attTimes = 1;
            }
            hpDown = hpXX - this.防御力.getValue() / attTimes;
            if(hpDown <= 0)
            {
               hpDown = 0;
            }
            if(bj)
            {
               hpDown *= this.tempXXX2.getValue();
               if(Math.random() * 100 < p.data.getElvesSlot().backElvesSkill3())
               {
                  hpDown *= 1 + p.data.getElvesSlot().backElvesSkill3_2();
               }
            }
            xx1415 = 0;
            if(hitX.jnGoYn)
            {
               arrX14 = p.data.getStampSlot().getValueSlot14();
               xx1415 += arrX14[1];
            }
            if(GameData.BOSSid.getValue() == this.id)
            {
               arrX15 = p.data.getStampSlot().getValueSlot15();
               xx1415 += arrX15[1];
            }
            hpDown *= 1 + xx1415;
            if(Math.random() * 100 < 20 && p.data.getElvesSlot().backElvesSkill6() > 0)
            {
               hxx = new HitXX();
               hxx.type = 5011;
               hxx.space = 27;
               hxx.totalTime = 27;
               hxx.numValue = int(p.use_gongji.getValue() * p.data.getElvesSlot().backElvesSkill6());
               new BuffEnemy(hxx,this);
            }
            p.energySlot.addEnergy(p,hpDown);
            getAllSuitSkill = p.data.getEquipSlot().getAllSuitSkill();
            skillDown = 0;
            for(i in getAllSuitSkill)
            {
               if(getAllSuitSkill[i] == 57359)
               {
                  if(this.getRandom(30) && p.flagTempSY)
                  {
                     p.setInTimeCDSY();
                     p.flagTempSY = false;
                     p.HpUp(p.use_hp_Max.getValue() * 0.035);
                     hpNum = p.use_hp_Max.getValue();
                     if(hitX.who is Player && Main.water.getValue() != 1)
                     {
                        hpNum = hpNum * (1 + p.use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
                     }
                     for(i in All)
                     {
                        this.HpSXX(All[i],hpNum);
                     }
                  }
               }
               else if(getAllSuitSkill[i] == 57360)
               {
                  if(this.getRandom(5) && p.flagTempSJ)
                  {
                     p.setInTimeCDSJ();
                     p.flagTempSJ = false;
                     arr = [];
                     str = "";
                     if(p.data.skinArr[p.data.skinNum] == 0)
                     {
                        str = "a";
                     }
                     else if(p.data.skinArr[p.data.skinNum] == 1)
                     {
                        str = "b";
                     }
                     else if(p.data.skinArr[p.data.skinNum] == 2)
                     {
                        str = "c";
                     }
                     else if(p.data.skinArr[p.data.skinNum] == 3)
                     {
                        str = "k";
                     }
                     for(j in p.AllSkillCDXX)
                     {
                        strXXX = p.AllSkillCDXX[j][0];
                        if(strXXX.substr(0,1) == str && int(strXXX.substr(1,2)) > 7 && int(strXXX.substr(1,2)) <= 15)
                        {
                           if(p.data.getSkillLevel(strXXX) > 0 && p.AllSkillCDXX[j][1] != p.AllSkillCD[j][1])
                           {
                              arr.push(j);
                           }
                        }
                     }
                     if(arr.length > 0)
                     {
                        rrr = Math.random() * arr.length;
                        p.AllSkillCDXX[arr[rrr]][1] = p.AllSkillCD[arr[rrr]][1];
                        p.AllSkillCDXX[arr[rrr]][2] = 50;
                     }
                  }
               }
               else if(getAllSuitSkill[i] == 57361)
               {
                  crit_temp = Math.pow(Number(p.use_baoji.getValue() / 70),0.8) / 100;
                  c_temp = Math.pow(1 + crit_temp,4.7);
                  if(this.getRandom(1.8) && p.flagTempBJ && bj)
                  {
                     p.setInTimeCDBJ();
                     p.flagTempBJ = false;
                     if(p.data.skinNum == 0)
                     {
                        skillDown = int(p.data.getEquipSlot().getEquipFromSlot(2).getAttack() * c_temp);
                     }
                     else
                     {
                        skillDown = int(p.data.getEquipSlot().getEquipFromSlot(5).getAttack() * c_temp);
                     }
                     if(hitX.who is Player && Main.water.getValue() != 1)
                     {
                        skillDown = skillDown * (1 + p.use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
                     }
                     if(skillDown > 650000)
                     {
                        skillDown = 650000;
                     }
                     if(skillDown > 200000 && GameData.gameLV == 5)
                     {
                        skillDown = 200000;
                     }
                  }
                  else if(bj)
                  {
                     numXX = Number(Math.pow(Number(p.use_baoji.getValue() / 70),0.8).toFixed(1));
                     baoJiXX = numXX / InitData.BuyNum_2000.getValue() + p.num_baoSang.getValue();
                     hpDown *= 1 + baoJiXX;
                  }
               }
               else if(getAllSuitSkill[i] != 57362)
               {
                  if(getAllSuitSkill[i] == 57363)
                  {
                     if(this.getRandom(15) && p.flagTempXL)
                     {
                        p.setInTimeCDXL();
                        p.flagTempXL = false;
                        skillDown = (p.use_mp_Max.getValue() + p.mp.getValue()) * ((p.use_mp_Max.getValue() + p.mp.getValue()) / 10000);
                        if(hitX.who is Player && Main.water.getValue() != 1)
                        {
                           skillDown = skillDown * (1 + p.use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
                        }
                        if(skillDown > 650000)
                        {
                           skillDown = 650000;
                        }
                        if(skillDown > 200000 && GameData.gameLV == 5)
                        {
                           skillDown = 200000;
                        }
                     }
                  }
               }
            }
         }
         else
         {
            hpDown = hitX.gongJi_hp;
            if(hpDown <= 0)
            {
               hpDown = 0;
            }
         }
         if(this.cengshu.length > 0)
         {
            for(i in this.cengshu)
            {
               if(this.cengshu[i].type == 605)
               {
                  this.skin.GoTo("被打",this.yingzhiTemp);
                  this.skin.continuousTime = this.yingzhiTemp;
               }
               else if(this.cengshu[i].type == 606)
               {
                  if(hitX.who is Player)
                  {
                     xxxxx = this.cengshu[i];
                     if(p.hp.getValue() > 0)
                     {
                        p.HpUp(xxxxx.numValue * hpDown);
                     }
                  }
               }
            }
         }
         if(hitX.who is ChongWu)
         {
            this.shanghaiTemp = 0;
            pppwho = (hitX.who as ChongWu).who;
            if((hitX.who as ChongWu).data.getPetEquip())
            {
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 4)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  hxx = new HitXX();
                  hxx.type = 502;
                  hxx.space = arr[1];
                  hxx.totalTime = arr[2];
                  hxx.numValue = pppwho.use_gongji.getValue() * arr[0];
                  hxx.speedValue = arr[4];
                  new BuffEnemy(hxx,this);
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 5)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[3])
                  {
                     hxx = new HitXX();
                     hxx.type = 503;
                     hxx.totalTime = 135;
                     hxx.space = hxx.totalTime;
                     hxx.numValue = pppwho.use_gongji.getValue() * arr[0];
                     hxx.speedValue = arr[4];
                     new BuffEnemy(hxx,this);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 8 && wscd > 135)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[1])
                  {
                     hxx = new HitXX();
                     hxx.type = 504;
                     hxx.space = 16;
                     hxx.totalTime = 16;
                     hxx.numValue = pppwho.use_gongji.getValue() * arr[0];
                     new BuffEnemy(hxx,this);
                     h = new HitXX();
                     h.type = 504;
                     h.space = 16;
                     h.totalTime = 16;
                     h.numValue = arr[2];
                     new BuffEffect(h,(hitX.who as ChongWu).who);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 9 && wscd > 80)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[1])
                  {
                     hxx = new HitXX();
                     hxx.type = 505;
                     hxx.totalTime = arr[0];
                     new BuffEnemy(hxx,this);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 17 && wscd > 54)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[0])
                  {
                     pppwho.HpUp(int(hitX.gongJi_hp * arr[1]));
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 18)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  pppwho.MpUp(Math.ceil(hitX.gongJi_hp * arr[0]));
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 19 && wscd > 54)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[0])
                  {
                     hxx = new HitXX();
                     hxx.type = 66;
                     hxx.totalTime = 81;
                     hxx.numValue = arr[1];
                     new BuffEffect(hxx,pppwho);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 21)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  this.shanghaiTemp = hitX.gongJi_hp * arr[1];
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 22 && wscd > 54)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[0])
                  {
                     hit_x = this.x;
                     hit_y = this.y;
                     class_2 = NewLoad.XiaoGuoData.getClass("奥加大剑") as Class;
                     pppwho.skin_W.addChild(new class_2());
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 23 && wscd > 135)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[0])
                  {
                     this.yingzhiTemp = arr[1];
                     hxx = new HitXX();
                     hxx.type = 605;
                     hxx.totalTime = 81;
                     new BuffEnemy(hxx,this);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 10 && wscd > 405)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[0])
                  {
                     ++addTimes;
                     if(addTimes >= 5)
                     {
                        addTimes = 0;
                        (hitX.who as ChongWu).who.HpUp(arr[1],2);
                     }
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 11 && wscd > 135)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[1])
                  {
                     hxx = new HitXX();
                     hxx.type = 506;
                     wscd = 0;
                     hxx.totalTime = arr[0];
                     new BuffEnemy(hxx,this);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 12 && GameData.gameLV == 5)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  hxx = new HitXX();
                  hxx.type = 501;
                  hxx.space = arr[0];
                  hxx.totalTime = arr[1];
                  hxx.numValue = int(this.lifeMAX.getValue() * 0.01);
                  new BuffEnemy(hxx,this);
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 13 && GameData.gameLV == 5)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  hitX.gongJi_hp = hitX.gongJi_Ctr * arr[0];
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 2 && GameData.gameLV == 5 && wscd > 135)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[0])
                  {
                     wscd = 0;
                     if(this.life.getValue() <= this.lifeMAX.getValue() * arr[1])
                     {
                        this.life.setValue(0);
                     }
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 3)
               {
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[1])
                  {
                     hxx = new HitXX();
                     hxx.type = 507;
                     hxx.totalTime = 135;
                     hxx.space = hxx.totalTime;
                     hxx.numValue = arr[2];
                     new BuffEnemy(hxx,this);
                  }
               }
               if((hitX.who as ChongWu).data.getPetEquip().getType() == 1 && wscd > 80)
               {
                  wscd = 0;
                  arr = (hitX.who as ChongWu).data.getPetEquip().getAffect();
                  this.rdom = Math.round(Math.random() * 100);
                  if(this.rdom <= arr[1])
                  {
                     for(i = 0; i < arr[0]; i++)
                     {
                        if(Enemy.All[i])
                        {
                           sheguan = NewLoad.XiaoGuoData.getClass("蛇冠") as Class;
                           hpX = int(hitX.gongJi_hp * 2);
                           hpHP = Enemy.All[i].life.getValue() - hpX;
                           Enemy.All[i].hpCount(hpX);
                           Enemy.All[i].skin.addChild(new sheguan());
                           HPdown.Open(hpX,Enemy.All[i].x,Enemy.All[i].y - Enemy.All[i].height);
                        }
                     }
                  }
               }
            }
            hpDown = hitX.gongJi_hp;
            if(this.shanghaiTemp > 0)
            {
               hpDown = this.shanghaiTemp;
            }
            hpDown = int(GongHui_jiTan.CW_XX(hpDown));
         }
         if(hitX.who is Player && Main.water.getValue() != 1)
         {
            hpDown = hpDown * (1 + (hitX.who as Player).use_gongji2.getValue() / 100) / (1 + this.魔防力.getValue() / 100);
         }
         if(GameData.gameLV == 5 && hpDown > hitX.gongJi_hp_MAX)
         {
            hpDown = hitX.gongJi_hp_MAX;
         }
         if(Main.tiaoShiYN)
         {
            hpDown += gongJiPOWER.getValue();
         }
         if(Boolean(NewPetPanel.XueMai) && (Main.player_1.playerCW || Main.P1P2 && Main.player_2.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(3))
            {
               hpDown *= 1.05;
            }
         }
         if(CaiYaoPanel.saveArr[1] > 0)
         {
            CYjianshang = 1 + (CaiYaoPanel.saveArr[1] * CaiYaoPanel.addArr[1] + CaiYaoPanel.saveArr[4] * CaiYaoPanel.addArr[4] + CaiYaoPanel.saveArr[7] * CaiYaoPanel.addArr[7] + CaiYaoPanel.saveArr[10] * CaiYaoPanel.addArr[10] + CaiYaoPanel.saveArr[13] * CaiYaoPanel.addArr[13]) * 0.01;
            hpDown *= CYjianshang;
         }
         hpDown *= Skill_bingzui.HpDownUp(hitX.who,this);
         hpDown *= this.hitHpDownUP;
         if(hitX.who is Player)
         {
            hpDown *= (hitX.who as Player).gongji_UP;
         }
         hit_x = this.x;
         hit_y = this.y;
         if(hitX.who is Player && !(hitX.who as Player).playerCW2)
         {
            if((hitX.who as Player).data.skinNum == 0 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(2).getHuanHua() == 4 || (hitX.who as Player).data.skinNum == 1 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 4)
            {
               if(Math.random() * 100 < 40 && Player.szCD > 160)
               {
                  Player.szCD = 0;
                  class_JG = NewLoad.OtherData.getClass("时装技能激光") as Class;
                  (hitX.who as Player).skin_W.addChild(new class_JG());
               }
            }
            if((hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 372 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               if(Math.random() * 100 < 5 && Player.szCD > 54)
               {
                  Player.szCD = 0;
                  class_1 = NewLoad.XiaoGuoData.getClass("时装陨石") as Class;
                  (hitX.who as Player).skin_W.addChild(new class_1());
               }
            }
            if((hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 377 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               cishu = 0;
               for(i = 0; i < Fly.All.length; i++)
               {
                  if(Fly.All[i]._name == "时装龙卷风")
                  {
                     cishu++;
                  }
               }
               if(Math.random() * 100 < 5 && cishu < 3 && Player.szCD > 54)
               {
                  Player.szCD = 0;
                  class_2 = NewLoad.XiaoGuoData.getClass("时装龙卷风") as Class;
                  (hitX.who as Player).skin_W.addChild(new class_2());
               }
            }
            if((hitX.who as Player).data.skinNum == 0 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(2).getHuanHua() == 1 || (hitX.who as Player).data.skinNum == 1 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 1)
            {
               if(Math.random() * 100 < 10)
               {
                  classRef2 = NewLoad.OtherData.getClass("zhuoshang") as Class;
                  this.skin.addChild(new classRef2());
                  this.HpXX2((hitX.who as Player).use_gongji.getValue() * 0.5);
                  ++this.zhuoshaoTimes;
               }
               if(this.zhuoshaoTimes >= 10)
               {
                  classRef3 = NewLoad.OtherData.getClass("huolong") as Class;
                  (hitX.who as Player).skin_W.addChild(new classRef3());
                  this.zhuoshaoTimes = 0;
               }
            }
            if((hitX.who as Player).data.skinNum == 0 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(2).getHuanHua() == 3 || (hitX.who as Player).data.skinNum == 1 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 3)
            {
               if(Math.random() * 100 < 20 && Player.szCD > 54)
               {
                  Player.szCD = 0;
                  classDark = NewLoad.XiaoGuoData.getClass("幻化黑暗爆炸") as Class;
                  fff = new classDark();
                  (hitX.who as Player).skin_W.addChild(fff);
                  fff.nameXX = "幻化黑暗爆炸";
               }
            }
            if((hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 404 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               if(Player.szCD > 71 && (hitX.who as Player).isDarkState == false)
               {
                  Player.szCD = 0;
                  if(Math.random() * 100 < 20)
                  {
                     (hitX.who as Player).darkTime = 0;
                     (hitX.who as Player).isDarkState = true;
                  }
               }
            }
            if((hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 446 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
            {
               if(Player.szCD > 40)
               {
                  if(Math.random() * 100 < 35)
                  {
                     if((hitX.who as Player).jianCDnum < 11)
                     {
                        if((hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6) && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getFrame() == 446 && (hitX.who as Player).data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
                        {
                           new GDpic2(this.x + Main.world.x,this.y - 100,Play_Interface.interfaceX,hitX.who);
                           ++(hitX.who as Player).jianCDnum;
                           if(hitX.who == Main.player_1)
                           {
                              if((hitX.who as Player).jianCDnum < 11)
                              {
                                 Play_Interface.interfaceX.xiari1.gotoAndStop((hitX.who as Player).jianCDnum);
                              }
                              else
                              {
                                 Play_Interface.interfaceX.xiari1.gotoAndPlay((hitX.who as Player).jianCDnum);
                              }
                           }
                           else if((hitX.who as Player).jianCDnum < 11)
                           {
                              Play_Interface.interfaceX.xiari2.gotoAndStop((hitX.who as Player).jianCDnum);
                           }
                           else
                           {
                              Play_Interface.interfaceX.xiari2.gotoAndPlay((hitX.who as Player).jianCDnum);
                           }
                           if((hitX.who as Player).jianCDnum == 11)
                           {
                              (hitX.who as Player).GetAllSkillCD();
                           }
                        }
                     }
                     (hitX.who as Player).jianCD = true;
                  }
                  else
                  {
                     Player.szCD = 0;
                  }
               }
            }
         }
         if(Main.gameNum.getValue() == 28)
         {
            for(i in Fly.All)
            {
               if((Fly.All[i] as Fly)._name == "天使护盾")
               {
                  hpDown *= 0.5;
               }
            }
         }
         if(this.className == "摩羯座")
         {
            for(i in Fly.All)
            {
               if((Fly.All[i] as Fly)._name == "黑茫光环")
               {
                  hpDown = 1;
                  if(EnemyBoss29.BOSSENG < 5)
                  {
                     ++EnemyBoss29.BOSSENG;
                  }
               }
            }
         }
         if(Main.gameNum.getValue() == 57)
         {
            for(i in Enemy.All)
            {
               if((Enemy.All[i] as Enemy).id == 79 && this.id != 79)
               {
                  if(Math.abs(Enemy.All[i].x - this.x) < 350)
                  {
                     hpDown *= 0.1;
                  }
               }
            }
         }
         if(this.className == "阿卡利" || this.className == "阿卡利召唤")
         {
            ran = Math.random() * 100;
            if(ran < 3)
            {
               this.skin.otherGoTo("攻击5");
               return;
            }
         }
         if(this.id == 7002)
         {
            this.skin.GoTo("攻击1");
            return;
         }
         if(this.id == 1056)
         {
            ran = Math.random() * 100;
            if(ran < 3)
            {
               if(this.attack_TimeNum[1] == 27 * 4)
               {
                  this.skin.GoTo("攻击2");
                  return;
               }
            }
         }
         if(this.id == 1056)
         {
            hpDown -= hpDown * this.jianShangPER;
         }
         if(this.className == "悬赏8")
         {
            ran = Math.random() * 100;
            if(ran < 3)
            {
               this.skin.otherGoTo("攻击1");
               return;
            }
         }
         if(this.id == 9001)
         {
            if(hitX.who is Player)
            {
               if((hitX.who as Player).skin.currentLabel != "攻击1" && (hitX.who as Player).skin.currentLabel != "攻击2" && (hitX.who as Player).skin.currentLabel != "攻击3" && (hitX.who as Player).skin.currentLabel != "攻击4" && hitX.parent._name != "法师魔法弹")
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"请按J普通攻击");
                  return;
               }
            }
         }
         if(this.id == 9002)
         {
            if(hitX.who is Player)
            {
               if((hitX.who as Player).skin.currentLabel != "上挑")
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"请按W+J特殊攻击");
                  return;
               }
            }
         }
         if(this.id == 9003)
         {
            if(hitX.who is Player)
            {
               if((hitX.who as Player).skin.currentLabel != "下斩")
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"请按S+J特殊攻击");
                  return;
               }
            }
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= hpDown;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(hpDown < EnemyBossXS225.xishou_Value)
            {
               hpDown = 0;
            }
            if(skillDown < EnemyBossXS225.xishou_Value)
            {
               skillDown = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            hpDown = Math.ceil(Math.random() * 2);
            skillDown = Math.ceil(Math.random() * 2);
         }
         if(this.id == 7007 && this.skin.redTime > 0)
         {
            hpDown *= 0.5;
         }
         if(this.id == 7008 && this.skin.baTiTime > 0)
         {
            hpDown *= 0.3;
         }
         if(Boolean(Main.tiaoShiYN) && this.id == 2015)
         {
            hpDown = 9999999;
         }
         if(hitX.who is Player)
         {
            whoX2 = hitX.who;
            if(whoX2.playerJL)
            {
               hpDown = whoX2.playerJL.BD1020(hpDown);
            }
         }
         hpDown = this.GongHuiBoos_HpXX(hpDown);
         skillDown = this.GongHuiBoos_HpXX(skillDown);
         hpDown = this.YouLing_HpXX(hpDown,hitX.who,hitX.times);
         skillDown = this.YouLing_HpXX(skillDown,hitX.who,hitX.times,true);
         if(skillDown > 0)
         {
            NewMC.Open("_特殊数字",Main.world.moveChild_Other,xxx,yyy - 50,20,skillDown,true);
         }
         GongHuiTiaoZan.BossHpXX(this);
         if(hitX.who is Player)
         {
            pXX = hitX.who;
            if(pXX.tsXiaoGuo_time > 0)
            {
               hpDown *= 1.2;
            }
         }
         hpDown *= 1 + Panel_youling.lvArr[4] * 0.002;
         if(hitX.who is Player && hitX.who.hpXX2num != 0)
         {
            if(Main.gameNum.getValue() == 3000)
            {
               hpDown = Math.random() * 2 + 1;
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpDown,true);
            }
            else if(this.id == 2015 || Main.gameNum.getValue() == 3000)
            {
               hpDown = Math.ceil(Math.random() * 2);
               skillDown = Math.ceil(Math.random() * 2);
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpDown,true);
            }
            else if(this.className == "悬赏9")
            {
               EnemyBossXS225.xishou_Value -= hpDown;
               if(EnemyBossXS225.xishou_Value < 0)
               {
                  EnemyBossXS225.xishou_Value = 0;
               }
               if(hpDown < EnemyBossXS225.xishou_Value)
               {
                  hpDown = 0;
               }
            }
            else if((hitX.who as Player).hpXX2Str == hitX.jiNengStr)
            {
               hpDown = this.GongHuiBoos_HpXX(hitX.who.hpXX2num);
               hpDown = this.YouLing_HpXX(hitX.who.hpXX2num,hitX.who);
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpDown,true);
            }
         }
         else if(bj)
         {
            if(Main.gameNum.getValue() == 3000)
            {
               hpDown = Math.random() * 2 + 1;
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpDown,true);
            }
            else if(this.id == 31)
            {
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy - 200,20,hpDown,true);
            }
            else if(this.id != 5003)
            {
               NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpDown,true);
            }
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            hpDown = Math.random() * 2 + 1;
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,xxx,yyy,20,hpDown,true);
         }
         else if(this.id == 31)
         {
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,xxx,yyy - 200,15,hpDown,true);
         }
         else if(this.id != 5003)
         {
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,xxx,yyy,15,hpDown,true);
         }
         if(this.buffTime_jl2016 > 0)
         {
            if(xArr[0] == 1)
            {
               p = xArr[1];
               num = hpDown * xArr[2];
               p.HpUp(num);
               NewMC.Open("回血效果",p,0,0,0,num);
            }
         }
         if(hitX.who is Player)
         {
            whoX = hitX.who;
            if(whoX.playerJL)
            {
               whoX.playerJL.BD1001(hpDown);
            }
         }
         if(hpDown > 0 && this.life.getValue() > 0)
         {
            this.life.setValue(this.life.getValue() - hpDown);
            hpNUMXXX(hpDown);
            hpNUMXXX2(hitX.parent,hitX.who,hpDown);
            this.life.setValue(this.life.getValue() - skillDown);
            hpNUMXXX(skillDown);
            hpNUMXXX2(hitX.parent,hitX.who,skillDown);
            WinShow.txt_4 += hpDown;
            lifeX = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
            this.lifeMC.gotoAndStop(lifeX);
            if(this.life.getValue() < this.lifeMAX.getValue() / 2)
            {
               if(Boolean(JingLingCatch.only) && GameData.BOSSid.getValue() == this.id)
               {
                  JingLingCatch.addSkin();
                  JingLingCatch.only = false;
               }
            }
            GongHuiTiaoZan.BossHpXX(this);
            if(this.life.getValue() <= 0)
            {
               if(hitX.name == "金箍棒")
               {
                  (hitX.who as ChongWu).goJiCD_now[2] = 0;
                  (hitX.who as ChongWu).goJiCD_now[1] += 2;
                  (hitX.who as ChongWu).goJiCD_now[3] += 2;
                  (hitX.who as ChongWu).goJiCD_now[4] += 2;
                  (hitX.who as ChongWu).nullTime = 0;
                  TiaoShi.txtShow("金箍棒XXX");
               }
               if(this.rebornTimes > 0)
               {
                  this.reborn();
                  return;
               }
               this.life.setValue(0);
               if(this.金钱.getValue() > 0)
               {
                  NewMC.Open("掉钱",Main.world.moveChild_Other,xxx2,yyy2,15,this.金钱.getValue(),true);
               }
               if(Boolean(p) && p.data.getElvesSlot().backElvesSkill2() > 0)
               {
                  p.HpUp(p.data.getElvesSlot().backElvesSkill2(),2);
               }
               expXXX = this.经验.getValue() * InitData.GetEXPxN().getValue();
               expXXX = int(GongHui_jiTan.expUP(expXXX));
               Main.player_1.ExpUP(expXXX);
               Main.player_1.MoneyUP(this.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(expXXX);
                  Main.player_2.MoneyUP(this.金钱.getValue());
               }
               this.任务品掉落();
               this.装备掉落();
               PaiHang_Data.EnemyOver();
               AchData.addEnemyNum(this.id);
               TaskData.setEnemyNum(this.id);
               TaskData.isEnemyTaskOk();
               NPC_YouLing.KillEnemy();
               ChongWu2.EnenyKillDiaoluo(this.id);
               this.newYearDiaoLuo();
               this.fiveOneDiaoLuo();
               CardPanel.monsterSlot.addMonsterCard(this.id);
               GongHuiRenWu.enemyOK(this.id);
            }
            GongHuiTiaoZan.BossHpXX(this);
         }
         var rdm:Number = Math.random() * this.temp3.getValue() / this.temp10.getValue();
         this.浮动硬直 += rdm;
         var XX:Number = hitX.硬直 - this.skin.被攻击硬直 - this.浮动硬直;
         if(XX >= this.skin.continuousTime)
         {
            XX = hitX.硬直 - this.硬直.getValue() - this.浮动硬直;
         }
         if(XX >= 0)
         {
            this.skin.GoTo("被打",int(XX));
            this.jump_power = 0;
         }
         if(this.震退值.getValue() != 0)
         {
            this.runArr = new Array();
            xxx = hitX.runArr[0] * this.震退值.getValue();
            yyy = hitX.runArr[1] * this.震退值.getValue();
            numXXXX = hitX.runArr[2] * this.震退值.getValue();
            xxx2 = Math.abs(xxx);
            if(this.RLXX == "R")
            {
               this.runPower(-3,yyy,numXXXX);
            }
            else if(this.RLXX == "L")
            {
               this.runPower(3,yyy,numXXXX);
            }
            else if(hitX.RL)
            {
               this.runPower(-xxx,yyy,numXXXX);
            }
            else
            {
               this.runPower(xxx,yyy,numXXXX);
            }
         }
      }
      
      public function YouLing_HpXX(hpNum:int, who:Object = null, numX:int = 1, yn:Boolean = false) : int
      {
         var up:int = 0;
         var bjUP:int = 0;
         var runType:String = null;
         var hpNumX:int = hpNum;
         if(Main.gameNum.getValue() >= 7000 && Main.gameNum.getValue() <= 7200 && hpNum > 0)
         {
            if(hpNum > 100)
            {
               hpNum = 100;
            }
            if(Boolean(who) && who is Player)
            {
               up = (who as Player).use_gongji.getValue() / 1000;
               hpNum += up;
               bjUP = Math.pow(Number(who.use_baoji.getValue() / 70),0.8) / 20;
               hpNum += bjUP;
               runType = (who as Player).skin.runType;
               if(!yn && (runType == "攻击1" || runType == "攻击2" || runType == "攻击3" || runType == "攻击4"))
               {
                  hpNum = 20;
               }
            }
            hpNum /= numX;
         }
         return hpNum;
      }
      
      public function GongHuiBoos_HpXX(hpNum:int) : int
      {
         if(GameData.gameLV == 6)
         {
            if(hpNum <= 5000)
            {
               return 1;
            }
            if(hpNum <= 10000)
            {
               return 2;
            }
            if(hpNum <= 20000)
            {
               return 3;
            }
            if(hpNum <= 40000)
            {
               return 4;
            }
            if(hpNum <= 80000)
            {
               return 5;
            }
            return 6;
         }
         return hpNum;
      }
      
      public function HpXX2(hpDown:int, xArr:Array = null) : *
      {
         var lifeX:int = 0;
         var expXXX:int = 0;
         if(this.id == 7002)
         {
            return;
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= hpDown;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(hpDown < EnemyBossXS225.xishou_Value)
            {
               hpDown = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            hpDown = Math.ceil(Math.random() * 2);
         }
         if(Main.gameNum.getValue() == 3000)
         {
            hpDown = Math.random() * 2 + 1;
         }
         hpDown = this.GongHuiBoos_HpXX(hpDown);
         hpDown = this.YouLing_HpXX(hpDown);
         if(hpDown > 0 && this.life.getValue() > 0)
         {
            this.life.setValue(this.life.getValue() - hpDown);
            NewMC.Open("_攻击数字",Main.world.moveChild_Other,this.x,this.y,15,hpDown,true);
            WinShow.txt_4 += hpDown;
            lifeX = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
            this.lifeMC.gotoAndStop(lifeX);
            if(this.life.getValue() <= 0)
            {
               if(this.rebornTimes > 0)
               {
                  this.reborn();
                  return;
               }
               this.life.setValue(0);
               expXXX = this.经验.getValue() * InitData.GetEXPxN().getValue();
               expXXX = int(GongHui_jiTan.expUP(expXXX));
               Main.player_1.ExpUP(expXXX);
               Main.player_1.MoneyUP(this.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(expXXX);
                  Main.player_2.MoneyUP(this.金钱.getValue());
               }
               this.任务品掉落();
               this.装备掉落();
               PaiHang_Data.EnemyOver();
               AchData.addEnemyNum(this.id);
               TaskData.setEnemyNum(this.id);
               TaskData.isEnemyTaskOk();
               CardPanel.monsterSlot.addMonsterCard(this.id);
               GongHuiRenWu.enemyOK(this.id);
               NPC_YouLing.KillEnemy();
            }
         }
         GongHuiTiaoZan.BossHpXX(this);
      }
      
      public function reborn() : *
      {
         --this.rebornTimes;
         this.hpUpEnemy(this.lifeMAX.getValue());
         var lifeX:int = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
         this.lifeMC.gotoAndStop(lifeX);
         if(this.id == 3022)
         {
            this.skin.runOver = true;
            this.skin.GoTo("攻击3");
         }
      }
      
      public function hpUpEnemy(xxx:int) : *
      {
         if(this.life.getValue <= 0)
         {
            return;
         }
         this.life.setValue(this.life.getValue() + xxx);
         NewMC.Open("回血效果",this,0,0,0,xxx);
      }
      
      public function hpCount(hpDown:int) : *
      {
         var lifeX:int = 0;
         var expXXX:int = 0;
         if(this.id == 7002)
         {
            return;
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= hpDown;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(hpDown < EnemyBossXS225.xishou_Value)
            {
               hpDown = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            hpDown = Math.ceil(Math.random() * 2);
         }
         hpDown = this.GongHuiBoos_HpXX(hpDown);
         hpDown = this.YouLing_HpXX(hpDown);
         GongHuiTiaoZan.BossHpXX(this);
         if(hpDown > 0 && this.life.getValue() > 0)
         {
            this.life.setValue(this.life.getValue() - hpDown);
            hpNUMXXX(hpDown);
            lifeX = 100 - this.life.getValue() / this.lifeMAX.getValue() * 100;
            this.lifeMC.gotoAndStop(lifeX);
            if(this.life.getValue() <= 0)
            {
               if(this.rebornTimes > 0)
               {
                  this.reborn();
                  return;
               }
               this.life.setValue(0);
               expXXX = this.经验.getValue() * InitData.GetEXPxN().getValue();
               expXXX = int(GongHui_jiTan.expUP(expXXX));
               Main.player_1.ExpUP(expXXX);
               Main.player_1.MoneyUP(this.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(expXXX);
                  Main.player_2.MoneyUP(this.金钱.getValue());
               }
               this.任务品掉落();
               this.装备掉落();
               PaiHang_Data.EnemyOver();
               AchData.addEnemyNum(this.id);
               TaskData.setEnemyNum(this.id);
               TaskData.isEnemyTaskOk();
               NPC_YouLing.KillEnemy();
               if(Boolean(Main.tiaoShiYN) || Main.serverTime.getValue() >= 20161222 && Main.serverTime.getValue() <= 20170101)
               {
                  ShengDan_Interface.addRenWuWuPin(this);
               }
               ChongWu2.EnenyKillDiaoluo(this.id);
               this.newYearDiaoLuo();
               this.fiveOneDiaoLuo();
               CardPanel.monsterSlot.addMonsterCard(this.id);
               GongHuiRenWu.enemyOK(this.id);
            }
         }
         GongHuiTiaoZan.BossHpXX(this);
      }
      
      public function HpSXX(e:Enemy, hpNum:Number) : *
      {
         var lifeX:int = 0;
         var expXXX:int = 0;
         if(this.id == 7002)
         {
            return;
         }
         if(this.className == "悬赏9")
         {
            EnemyBossXS225.xishou_Value -= hpDown;
            if(EnemyBossXS225.xishou_Value < 0)
            {
               EnemyBossXS225.xishou_Value = 0;
            }
            if(hpDown < EnemyBossXS225.xishou_Value)
            {
               hpDown = 0;
            }
         }
         if(this.id == 2015 || Main.gameNum.getValue() == 3000)
         {
            hpNum = Math.ceil(Math.random() * 2);
         }
         if(hpNum > 650000)
         {
            hpNum = 650000;
         }
         if(hpNum > 80000 && GameData.gameLV == 5)
         {
            hpNum = 80000;
         }
         var xxxx:int = e.x + Math.random() * 100 - 50;
         var yyyy:int = e.y + Math.random() * 100 - 50;
         hpNum = this.GongHuiBoos_HpXX(hpNum);
         GongHuiTiaoZan.BossHpXX(this);
         hpNum = this.YouLing_HpXX(hpNum);
         NewMC.Open("_特殊数字",Main.world.moveChild_Other,xxxx,yyyy - 50,20,hpNum,true);
         if(hpNum > 0 && e.life.getValue() > 0)
         {
            e.life.setValue(e.life.getValue() - hpNum);
            hpNUMXXX(hpNum);
            lifeX = 100 - e.life.getValue() / e.lifeMAX.getValue() * 100;
            e.lifeMC.gotoAndStop(lifeX);
            if(e.life.getValue() <= 0)
            {
               e.life.setValue(0);
               NewMC.Open("掉钱",Main.world.moveChild_Other,xxxx,yyyy,15,this.金钱.getValue(),true);
               expXXX = this.经验.getValue() * InitData.GetEXPxN().getValue();
               expXXX = int(GongHui_jiTan.expUP(expXXX));
               Main.player_1.ExpUP(expXXX);
               Main.player_1.MoneyUP(e.金钱.getValue());
               if(Main.P1P2)
               {
                  Main.player_2.ExpUP(expXXX);
                  Main.player_2.MoneyUP(e.金钱.getValue());
               }
               e.任务品掉落();
               e.装备掉落();
               AchData.addEnemyNum(e.id);
               TaskData.setEnemyNum(e.id);
               TaskData.isEnemyTaskOk();
               GongHuiRenWu.enemyOK(this.id);
               NPC_YouLing.KillEnemy();
            }
         }
         GongHuiTiaoZan.BossHpXX(this);
      }
      
      public function shengdandiaoluo(id:int) : *
      {
         var rdm:int = Math.random() * 100;
         for(var i:int = 0; i < InitData.shengdanArr.length; i++)
         {
            if(InitData.shengdanArr[i][0] == id)
            {
               if(InitData.shengdanArr[i][1] > rdm)
               {
                  Main.addShengDan();
               }
            }
         }
      }
      
      public function fiveOneDiaoLuo() : *
      {
         if(Main.serverTime.getValue() > FiveOne_Interface.overTime)
         {
            return;
         }
         if(Boolean(GameData.BossIS) && this == GameData.BossIS)
         {
            if(this.id == 29 || this.id == 38 || this.id == 102 || this.id == 104 || this.id == 105 || this.id == 107 || this.id == 2015 || this.id == 108)
            {
               return;
            }
            if(Math.random() * 1000 < 100)
            {
               ++FiveOne_Interface.arrAll[2];
               NewMC.Open("文字提示",Main._stage,480,400,45,0,true,2,"获得生命之核！");
            }
            if(Math.random() * 1000 < 150)
            {
               ++FiveOne_Interface.arrAll[3];
               NewMC.Open("文字提示",Main._stage,480,450,45,0,true,2,"获得生命之光！");
            }
            return;
         }
         if(Math.random() * 1000 < 40)
         {
            ++FiveOne_Interface.arrAll[4];
            NewMC.Open("文字提示",Main._stage,480,400,45,0,true,2,"获得生命之水！");
            return;
         }
      }
      
      public function newYearDiaoLuo() : *
      {
         var r:int = 0;
         if(Main.serverTime.getValue() <= NewYear_Interface.overTime)
         {
            if(Boolean(GameData.BossIS) && this == GameData.BossIS)
            {
               if(this.id == 2015)
               {
                  r = Math.random() * 100;
                  if(r < 10)
                  {
                     NewYear_Interface.addKey();
                     NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"获得宝箱钥匙！");
                     return;
                  }
               }
            }
         }
      }
      
      public function 装备掉落() : *
      {
         var i:int = 0;
         var moneyNum:int = 0;
         var ii3:int = 0;
         var killPointNum:int = 0;
         var ii2:int = 0;
         var xx2:* = undefined;
         var yy2:* = undefined;
         var nullObj:Object = null;
         var ob:obj = null;
         var xx3:* = undefined;
         var yy3:* = undefined;
         var nullObj2:Object = null;
         var id:int = 0;
         var type:int = 0;
         var typeOther:int = 0;
         var xx:* = undefined;
         var yy:* = undefined;
         var arr:Array = null;
         var XXX:Equip = null;
         var XXX2:Gem = null;
         var XXX7:PetEquip = null;
         var strXX:String = null;
         var XXX3:Otherobj = null;
         if(this.id == 4010)
         {
            moneyNum = Math.random() * 6 + 5;
            for(ii3 = 0; ii3 < moneyNum; ii3++)
            {
               xx2 = this.x + Math.random() * 300 - 150;
               yy2 = this.y - Math.random() * 100 - 100;
               nullObj = new Object();
               ob = new obj(nullObj,xx2,yy2,360);
            }
            killPointNum = Math.random() * 5 + 1;
            for(ii2 = 0; ii2 < killPointNum; ii2++)
            {
               xx3 = this.x + Math.random() * 300 - 150;
               yy3 = this.y - Math.random() * 100 - 100;
               nullObj2 = new Object();
               ob = new obj(nullObj2,xx3,yy3,361);
            }
         }
         if(Main.serverTime.getValue() <= ChunJiePanel.overTime && ChunJiePanel.saveArr2_2022[2] < 35)
         {
            for(i in this.cj_arr)
            {
               if(this.cj_arr[i][0] == this.id)
               {
                  if(Math.random() * 10000 < this.cj_arr[i][1])
                  {
                     ChunJiePanel.addShaGuaiJiFen();
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜获得 魔法马刷");
                  }
               }
            }
         }
         var ii:uint = this.掉落数量.getValue() * InitData.GetDOWNxN().getValue();
         for(var j:int = 0; j < Main.player1.getTitleSlot().getListLength(); j++)
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(j).getId() == 25)
            {
               ii++;
            }
         }
         if(this.className == "悬赏7" && Main.wts.getBoss() >= 222)
         {
            if(getEnemyNum("阿卡利") > 0 || getEnemyNum("阿卡利召唤") > 0)
            {
               return;
            }
         }
         if((this.className == "阿卡利" || this.className == "阿卡利召唤") && Main.wts.getBoss() >= 222)
         {
            if(getEnemyNum("悬赏7") > 0)
            {
               return;
            }
         }
         for(i = 0; i < ii; i++)
         {
            id = this.掉落物品计算();
            type = int(String(id).substr(1,1));
            typeOther = int(String(id).substr(0,1));
            xx = this.x + Math.random() * 200 - 100;
            yy = this.y - 100;
            if(this.id == "5015")
            {
               xx -= 300;
            }
            if(this.id == 8001)
            {
               yy = 450 - Math.random() * 30 + 15;
               xx = 580 + Math.random() * 80 - 40;
            }
            xx = this.ObjWhere(xx);
            if(id != 0)
            {
               if(type == 1)
               {
                  arr = [id];
                  XXX = EquipFactory.createEquipByColorAndLevel(arr);
                  ob = new obj(XXX,xx,yy);
               }
               else if(type == 2)
               {
                  arr = [id];
                  XXX2 = GemFactory.createGemByColorAndLevel(arr);
                  ob = new obj(XXX2,xx,yy);
               }
               else if(type == 7)
               {
                  XXX7 = PetEquip.creatPetEquip(id);
                  NewPetPanel.bag.addPetBag(XXX7);
                  strXX = "获得:" + PetEquip.creatPetEquip(id).getName();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,strXX);
               }
               if(typeOther == 6)
               {
                  XXX3 = OtherFactory.creatOther(id);
                  ob = new obj(XXX3,xx,yy);
               }
            }
         }
      }
      
      public function ObjWhere(x:int) : int
      {
         var xx:int = Math.random() * 100;
         if(x < 0)
         {
            return x + xx;
         }
         if(x > Main.world._width)
         {
            return int(Main.world._width - xx);
         }
         return x;
      }
      
      public function 掉落物品计算() : int
      {
         var xx:int = Math.random() * 10000 + 1;
         var num:int = 0;
         for(j = 0; j < 20; ++j)
         {
            num += this.掉落概率[j];
            if(num >= xx)
            {
               return this.掉落物品[j];
            }
         }
         return 0;
      }
      
      public function onADDED_TO_STAGE(e:*) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         Main.world.moveChild_Enemy.addChild(this);
         this.cengjitiaozheng();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function cengjitiaozheng() : *
      {
         if(this.id == 2018)
         {
            Main.world.moveChild_Other.addChild(this);
         }
      }
      
      public function onREMOVED_FROM_STAGE(e:*) : *
      {
         if(this.skin)
         {
            this.skin.Over();
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Dead() : *
      {
         if(this.parent)
         {
            EnemyBossXS242.BeiDong_2(this.x,this.y);
            this.parent.removeChild(this);
            if(GameData.gameLV == 5 && Play_Interface.bossIS && Play_Interface.bossIS == this)
            {
               Panel_XianHua.xianHuaUp();
            }
            if(this.id == 3020 || this.id == 4003 || this.id == 2010)
            {
               if(Main.player_1)
               {
                  Main.player_1.speedTemp.setValue(0);
                  Main.player_1.debuff = 0;
               }
               if(Boolean(Main.P1P2) && Boolean(Main.player_2))
               {
                  Main.player_2.speedTemp.setValue(0);
                  Main.player_2.debuff = 0;
               }
            }
            if(this.id == 4008 || this.id == 1056)
            {
               while(Main.world.moveChild_Enemy.numChildren > 0)
               {
                  Main.world.moveChild_Enemy.removeChildAt(0);
               }
            }
            if(this.id == 7001)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(1);
               }
            }
            if(this.id == 7003)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(2);
               }
            }
            if(this.id == 7006)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(3);
               }
            }
            if(this.id == 7007)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(4);
               }
            }
            if(this.id == 7008)
            {
               if(Math.random() * 100 < 4)
               {
                  LingHunShi_Interface.Add_LHS(5);
               }
            }
            this.life.setValue(0);
         }
         if(FanPaiPanel.saveArr2[2] < 500)
         {
            if(this.id == 100 || this.id == 10 || this.id == 103 || this.id == 37 || this.id == 51 || this.id == 52 || this.id == 36 || this.id == 219 || this.id == 220 || this.id == 221 || this.id == 5005 || this.id == 5006)
            {
               return;
            }
            ++FanPaiPanel.saveArr2[2];
            ++FanPaiPanel.saveArr2[3];
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         ++wscd;
         if(this.lastTime > -1)
         {
            --this.lastTime;
            if(this.lastTime == 0)
            {
               this.life.setValue(0);
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
         }
         if(this.buffTime_jl2016 > 0)
         {
            --this.buffTime_jl2016;
         }
         if(this.life.getValue() > 0)
         {
            if(this.isIce == true && this.life.getValue() > 0)
            {
               this.iceHPXX();
            }
            this.MoveData();
            if(!this.noMove && !this.isIce)
            {
               this.MoveRun();
            }
         }
         else
         {
            if(this.skin)
            {
               this.skin.GoTo("死亡");
            }
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      public function iceHPXX() : *
      {
         var hpX:* = undefined;
         var hpHP:int = 0;
         if(Main.player1.getEquipSlot().getEquipFromSlot(7) && Main.player1.getEquipSlot().getEquipFromSlot(7).getFrame() == 385 && Main.player1.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0 || Main.P1P2 && (Main.player2.getEquipSlot().getEquipFromSlot(7) && Main.player2.getEquipSlot().getEquipFromSlot(7).getFrame() == 385 && Main.player2.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0))
         {
            ++this.iceTime;
            if(this.iceTime == 27)
            {
               hpX = int(Main.player_1.use_gongji.getValue() * Math.pow(Main.player1.getLevel(),0.2));
               hpHP = this.life.getValue() - hpX;
               if(GameData.gameLV == 5)
               {
                  if(hpX > 16200)
                  {
                     hpX = 16200;
                  }
               }
               this.hpCount(hpX);
               HPdown.Open(hpX,this.x,this.y - this.height);
               this.iceTime = 0;
            }
         }
      }
      
      internal function test() : *
      {
         if(Main.player_1.skin.runType == "技能1" || Main.player_1.skin.runType == "技能2" || Main.player_1.skin.runType == "技能3" || Main.player_1.skin.runType == "技能4" || Main.player_1.skin.runType == "转职技能1" || Main.player_1.skin.runType == "转职技能2" || Main.player_1.skin.runType == "转职技能3" || Main.player_1.skin.runType == "转职技能4")
         {
            this.temp = Main.player_1.skin.runType;
            if(this.temp != this.tempXX && this.tempTimeXX > 5)
            {
               this.tempXX = this.temp;
               this.tempTimeXX = 0;
               this.skin.GoTo("攻击5");
            }
            return;
         }
      }
      
      public function MoveData() : *
      {
         var i:int = 0;
         var randomNum:int = 0;
         var randomRun:String = null;
         var jump:int = 0;
         var num:int = 0;
         this.WhereAreYou();
         this.Attack_TimeXX();
         var distance_XX:* = Math.abs(this.distance_X);
         var distance_YY:* = Math.abs(this.distance_Y);
         if(this.skin.runType == "站立" || this.skin.runType == "移动" || this.skin.runType == "跳")
         {
            randomNum = Math.random() * this.attack_XY.length;
            randomRun = "攻击" + (randomNum + 1);
            if(this.skin.continuousTime <= 0 && this.gjcd_Time <= 0)
            {
               this.jump_power = this.jumptemp;
               if(this.attack_XY[randomNum] && this.attack_Time[randomNum] == this.attack_TimeNum[randomNum] && distance_XX < this.attack_XY[randomNum][0] && distance_YY < this.attack_XY[randomNum][1])
               {
                  if(this.distance_X > 0)
                  {
                     this.getRL(false);
                  }
                  else
                  {
                     this.getRL(true);
                  }
                  this.attack_TimeNum[randomNum] = 0;
                  if(this.className == "金牛座")
                  {
                     if(randomNum == 4 && bool_5)
                     {
                        this.skin.GoTo(randomRun);
                        bool_6 = false;
                        bool_5 = false;
                     }
                     else if(randomNum == 5 && bool_6)
                     {
                        this.skin.GoTo(randomRun);
                        bool_5 = false;
                        bool_6 = false;
                     }
                     else if(randomNum < 4)
                     {
                        this.skin.GoTo(randomRun);
                     }
                  }
                  else if(this.className == "BOSS_54")
                  {
                     if(randomNum == 3)
                     {
                        return;
                     }
                     if(boss54 < 5 && randomNum == 5)
                     {
                        return;
                     }
                     if(boss54 == 5)
                     {
                        this.skin.GoTo("攻击6");
                        ++boss54;
                     }
                     else if(boss54 > 5)
                     {
                        if(Math.random() * 10 > 5)
                        {
                           this.skin.GoTo("攻击3");
                        }
                        else
                        {
                           this.skin.GoTo("攻击5");
                        }
                     }
                     else
                     {
                        this.skin.GoTo(randomRun);
                     }
                  }
                  else if(this.className == "天秤座")
                  {
                     if(randomNum == 0 || randomNum == 1)
                     {
                        for(i = 0; i < Fly.All.length; i++)
                        {
                           if((Fly.All[i] as Fly)._name == "火焰之路" || (Fly.All[i] as Fly)._name == "寒冰之路")
                           {
                              return;
                           }
                        }
                     }
                     this.skin.GoTo(randomRun);
                  }
                  else if(this.className == "摩羯座")
                  {
                     if(randomNum == 1)
                     {
                        if(All.length > 1)
                        {
                           return;
                        }
                     }
                     if(EnemyBoss29.BOSSENG > 0)
                     {
                        --EnemyBoss29.BOSSENG;
                     }
                     this.skin.GoTo(randomRun);
                  }
                  else if(this.className == "双子座")
                  {
                     if(randomNum == 1 || randomNum == 2)
                     {
                        num = 0;
                        for(i = 0; i < Fly.All.length; i++)
                        {
                           if((Fly.All[i] as Fly)._name == "恶魔球")
                           {
                              num++;
                           }
                        }
                        if(num < 2)
                        {
                           this.skin.GoTo("攻击1");
                        }
                     }
                     this.skin.GoTo(randomRun);
                  }
                  else if(this.id == 3022)
                  {
                     if(randomNum == 2)
                     {
                        return;
                     }
                     this.skin.GoTo(randomRun);
                  }
                  else if(this.className == "悬赏7")
                  {
                     if(randomNum == 0)
                     {
                        for(i in Enemy.All)
                        {
                           if((Enemy.All[i] as Enemy).className == "阿卡利" || (Enemy.All[i] as Enemy).className == "阿卡利召唤")
                           {
                              return;
                           }
                        }
                     }
                     if(randomNum == 2)
                     {
                        for(i in Enemy.All)
                        {
                           if((Enemy.All[i] as Enemy).className == "罗生门")
                           {
                              return;
                           }
                        }
                     }
                     this.skin.GoTo(randomRun);
                  }
                  else
                  {
                     if(this.className == "BOSS_53")
                     {
                        ++this.tempTimeXX;
                        this.test();
                        if(randomNum == 4)
                        {
                           return;
                        }
                     }
                     if(this.className == "悬赏5")
                     {
                        if(randomNum == 3 && xuanshang5 == false || randomNum == 4 && xuanshang5 == false)
                        {
                           return;
                        }
                        if(randomNum == 3 && xuanshang5 == true)
                        {
                           xuanshang5 = false;
                        }
                        if(randomNum == 4 && xuanshang5 == true)
                        {
                           xuanshang5 = false;
                        }
                     }
                     this.skin.GoTo(randomRun);
                  }
                  this.浮动硬直 = 0;
               }
               else
               {
                  if(this.className == "火鸟王" || this.id == 5005 || this.id == 5006 || this.id == 5014 || this.id == 95)
                  {
                     this.skin.GoTo("移动");
                     return;
                  }
                  if(distance_XX < 50 && distance_YY < 500)
                  {
                     this.skin.GoTo("站立");
                  }
                  else if(this.distance_X > this.guard_XY[0])
                  {
                     this.runPower(this.walk_power,0,1);
                     this.getRL(false);
                     this.skin.GoTo("移动");
                  }
                  else if(this.distance_X < -this.guard_XY[0])
                  {
                     this.runPower(-this.walk_power,0,1);
                     this.getRL(true);
                     this.skin.GoTo("移动");
                  }
                  else
                  {
                     this.skin.GoTo("站立");
                  }
               }
            }
            jump = Math.random() * 60;
            if(jump == 0 && !this.jumping)
            {
               if(this.RL)
               {
                  this.runPower(-this.walk_power * 0.8,this.jump_power,this.jump_time);
               }
               else
               {
                  this.runPower(this.walk_power * 0.8,this.jump_power,this.jump_time);
               }
            }
         }
         this.runX = this.runY = 0;
         for(i = int(this.runArr.length - 1); i >= 0; i--)
         {
            if(this.runArr[i][2] > 0)
            {
               this.runX += this.runArr[i][0] + this.runArr[i][3] * this.runArr[i][2];
               this.runY -= this.runArr[i][1] + this.runArr[i][4] * this.runArr[i][2];
               --this.runArr[i][2];
            }
            else
            {
               this.runArr.splice(i,1);
            }
         }
         if(this.runY < 0)
         {
            this.jumpType = 1;
            this.gravityNum = 0;
            this.jumping = true;
         }
         else
         {
            this.jumpType = 2;
            if(this.gravity != 0)
            {
               ++this.gravityNum;
            }
            this.runY += this.gravity * this.gravityNum;
         }
      }
      
      public function WhereAreYou() : *
      {
         var i:int = 0;
         var dx2:* = undefined;
         if(Main.gameNum.getValue() == 3000 && Boolean(NpcYY._this))
         {
            this.distance_X = this.x - NpcYY._this.x;
            this.distance_Y = this.y - NpcYY._this.y;
         }
         else if(Player.All.length > 0)
         {
            this.distance_X = this.x - Player.All[0].x;
            this.distance_Y = this.y - Player.All[0].y;
            for(i = 1; i < Player.All.length; i++)
            {
               dx2 = this.x - Player.All[i].x;
               if(Math.abs(dx2) < Math.abs(this.distance_X))
               {
                  this.distance_X = dx2;
                  this.distance_Y = this.y - Player.All[i].y;
               }
            }
         }
         else
         {
            this.distance_X = 0;
         }
      }
      
      public function MoveRun() : *
      {
         var xxYN:Boolean = false;
         var i:int = 0;
         var tempX:int = 0;
         var AA:Boolean = false;
         var i2:uint = 0;
         var map:* = undefined;
         var BB:Boolean = false;
         var CC:Boolean = false;
         if(this.noHitMap)
         {
            return;
         }
         if(this.runX > 0)
         {
            xxYN = true;
         }
         var xx:int = this.runX;
         var yy:int = this.runY;
         var forecast_x:int = this.x + Main.world.x;
         var forecast_y:int = this.y;
         var heightX:int = this.height;
         if(this.heightXXX != 0)
         {
            heightX = this.heightXXX;
         }
         for(i = Math.abs(yy); i > 0; i--)
         {
            if(this.jumpType == 1)
            {
               AA = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - heightX,true));
               if(!AA)
               {
                  for(i2 = 0; i2 < Main.world.numChildren; i2++)
                  {
                     map = Main.world.getChildAt(i2);
                     if(map is Map && Boolean(map.MapData1.hitTestPoint(forecast_x,forecast_y - heightX,true)))
                     {
                        AA = true;
                        break;
                     }
                  }
               }
               if(!AA)
               {
                  forecast_y--;
               }
            }
            else if(this.jumpType == 2)
            {
               BB = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 3,true));
               AA = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 1,true));
               CC = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y + 6,true));
               for(i2 = 0; i2 < Main.world.numChildren; i2++)
               {
                  map = Main.world.getChildAt(i2);
                  if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x,forecast_y - 3,true)))
                  {
                     BB = true;
                  }
                  else if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x,forecast_y - 1,true)))
                  {
                     AA = true;
                  }
                  else if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x,forecast_y + 6,true)))
                  {
                     CC = true;
                  }
               }
               this.jumping = false;
               if(BB)
               {
                  forecast_y -= 2;
               }
               else if(AA)
               {
                  this.runY = 0;
                  this.gravityNum = 0;
               }
               else if(CC)
               {
                  forecast_y += 3;
               }
               else
               {
                  forecast_y++;
                  this.jumping = true;
               }
            }
            else if(this.jumpType == 3)
            {
               AA = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 1,true));
               for(i2 = 0; i2 < Main.world.numChildren; i2++)
               {
                  map = Main.world.getChildAt(i2);
                  if(map is Map && Boolean(map.MapData1.hitTestPoint(forecast_x,forecast_y - this.height,true)))
                  {
                     AA = true;
                  }
               }
               if(!AA)
               {
                  this.jumpType = 2;
                  break;
               }
               forecast_y++;
            }
         }
         this.y = forecast_y;
         if(xx > 0)
         {
            tempX = forecast_x - 20;
         }
         else
         {
            tempX = forecast_x + 20;
         }
         for(i = Math.abs(xx); i > 0; i--)
         {
            AA = Boolean(Main.world.MapData1.hitTestPoint(tempX,forecast_y - 50,true));
            if(!AA)
            {
               for(i2 = 0; i2 < Main.world.numChildren; i2++)
               {
                  map = Main.world.getChildAt(i2);
                  if(map is Map && Boolean(map.MapData.hitTestPoint(tempX,forecast_y - 50,true)))
                  {
                     AA = true;
                     break;
                  }
               }
            }
            if(!AA)
            {
               if(xx > 0)
               {
                  forecast_x--;
               }
               else
               {
                  forecast_x++;
               }
            }
         }
         var xxx:int = forecast_x - Main.world.x;
         if(xxx > Main.world._width + 200)
         {
            this.x = Main.world._width + 200;
         }
         else if(xxx < -200)
         {
            this.x = -200;
         }
         else
         {
            this.x = xxx;
         }
      }
      
      public function runPower(numX:Number = 0, numY:Number = 0, time:int = 0) : *
      {
         if(time <= 3)
         {
            this.runArr[this.runArr.length] = [numX,numY,1,0,0,0];
            return;
         }
         var numXX:* = numX / time * this.parabola;
         var numYY:* = numY / time * this.parabola;
         var gravityX:Number = numX * (1 - this.parabola) / (time * time - time * (time - 1) / 2);
         var gravityY:Number = numY * (1 - this.parabola) / (time * time - time * (time - 1) / 2);
         this.runArr[this.runArr.length] = [numXX,numYY,time,gravityX,gravityY];
      }
      
      public function getRL(_RL:Boolean) : *
      {
         this.RL = _RL;
         if(this.className == "左墙" || this.className == "监牢" || this.className == "金牛座石像" || this.className == "金牛座石像2" || this.id == 303 || this.id == 2014 || this.id == 88 || this.id == 5001 || this.id == 5002 || this.id == 5003 || this.id == 5015 || this.id == 88 || this.id == 5016 || this.id == 5017 || this.id == 5018 || this.id == 5019 || this.id == 5020 || this.id == 5021 || this.id == 5022)
         {
            return;
         }
         if(_RL)
         {
            this.skin.scaleX = -1;
         }
         else if(!_RL)
         {
            this.skin.scaleX = 1;
         }
         if(this.id == 9001 || this.id == 9002 || this.id == 9003 || this.id == 9004)
         {
            if(this.skin.xx_mc)
            {
               this.skin.xx_mc.scaleX = this.skin.scaleX;
            }
         }
      }
      
      public function set_attack_Time() : *
      {
         var i:* = undefined;
         var idX:int = 0;
         var str:String = null;
         var rTime:int = 0;
         this.EnemySkinXml = EnemySkin.EnemySkinXmlArr[GameData.gameLV][this.关卡];
         if(!this.EnemySkinXml)
         {
            TiaoShi.txtShow("!!!找不到怪物攻击数据:" + this.关卡);
            return;
         }
         for(var x:int = 0; x < this.attack_XY.length; x++)
         {
            for(i in this.EnemySkinXml.怪物攻击)
            {
               idX = int(this.EnemySkinXml.怪物攻击[i].ID);
               str = String(this.EnemySkinXml.怪物攻击[i].帧标签);
               if(this.id == idX && str == "攻击" + (x + 1))
               {
                  rTime = int(this.EnemySkinXml.怪物攻击[i].间隔);
                  this.attack_Time[x] = rTime;
                  this.attack_TimeNum[x] = int(rTime * Math.random());
                  break;
               }
            }
         }
      }
      
      public function Attack_TimeXX() : *
      {
         this.NoGongJiMC();
         if(this.noGongJiTime > 0)
         {
            --this.noGongJiTime;
            return;
         }
         for(var i:int = 0; i < this.attack_TimeNum.length; i++)
         {
            if(this.attack_TimeNum[i] < this.attack_Time[i])
            {
               ++this.attack_TimeNum[i];
            }
         }
      }
      
      public function 任务品掉落() : *
      {
         var XXX3:Quest = null;
         var xx:* = undefined;
         var yy:* = undefined;
         var ob:obj = null;
         var xID:uint = 0;
         var id:int = this.任务概率掉落计算();
         if(id != 0)
         {
            if(id >= 84117 && id <= 84120)
            {
               xID = uint(id - 84117);
               if(Boolean(Main.player1) && (Main.player1.getBag().fallQusetBag(id) > 0 || Main.LuoPanArr[xID] == 1))
               {
                  return;
               }
               if(Main.P1P2 && Main.player2 && (Main.player2.getBag().fallQusetBag(id) > 0 || Main.LuoPanArr[xID] == 1))
               {
                  return;
               }
            }
            XXX3 = QuestFactory.creatQust(id);
            xx = this.x;
            yy = this.y;
            if(this.id == 8001)
            {
               yy = 450 - Math.random() * 30 + 15;
               xx = 580 + Math.random() * 80 - 40;
            }
            ob = new obj(XXX3,xx,yy);
            trace("任务品掉落??",XXX3,xx,yy);
         }
      }
      
      public function 任务概率掉落计算() : int
      {
         var fallLevel:Number = NaN;
         var type:Number = NaN;
         var questId:Number = NaN;
         var xxx:int = 0;
         if(!this.mainQuest())
         {
            if(!this.getbagQuestNum())
            {
               xxx = Math.random() * 10000 + 1;
            }
            else
            {
               xxx = 99999;
            }
         }
         else
         {
            xxx = 99999;
         }
         for(j = 0; j < 3; ++j)
         {
            fallLevel = Number(this.特殊掉落物品[j]);
            if(fallLevel != 0)
            {
               type = Number(QuestFactory.getType(fallLevel));
               if(type == 0)
               {
                  questId = Number(QuestFactory.getTaskId(fallLevel));
                  if(TaskData.isTaskById(questId) != null)
                  {
                     if(!TaskData.getAddBag(questId))
                     {
                        if(xxx <= this.特殊掉落概率[j])
                        {
                           return fallLevel;
                        }
                     }
                  }
               }
               else
               {
                  questId = Number(QuestFactory.getTaskId(fallLevel));
                  if(xxx <= this.特殊掉落概率[j])
                  {
                     return fallLevel;
                  }
               }
            }
         }
         return 0;
      }
      
      public function getbagQuestNum() : Boolean
      {
         var fallLevel:Number = NaN;
         var bagNum:Number = NaN;
         var type:Number = NaN;
         for(var i:uint = 0; i < 3; i++)
         {
            if(this.特殊掉落物品[i] != 0)
            {
               fallLevel = Number(this.特殊掉落物品[i]);
               type = Number(QuestFactory.getType(fallLevel));
               if(type == 1)
               {
                  if(Main.player2 == null)
                  {
                     return this.p1bagFall(fallLevel);
                  }
                  if(this.p1bagFall(fallLevel) || this.p2bagFall(fallLevel))
                  {
                     return true;
                  }
               }
               else if(type == 2)
               {
                  if(Main.player2 == null)
                  {
                     if(this.p1bagFall(fallLevel))
                     {
                        return this.p1bagFall(fallLevel);
                     }
                  }
                  else
                  {
                     if(this.p1bagFall(fallLevel) && this.p2bagFall(fallLevel))
                     {
                        return true;
                     }
                     if(Boolean(SetTransferPanel.tbo1()) && this.p2bagFall(fallLevel))
                     {
                        return true;
                     }
                     if(Boolean(SetTransferPanel.tbo2()) && this.p1bagFall(fallLevel))
                     {
                        return true;
                     }
                  }
               }
               else if(type == 0)
               {
                  return false;
               }
            }
         }
         return false;
      }
      
      public function p1bagFall(fallLevel:Number) : Boolean
      {
         var bagNum:Number = Number(Main.player1.getBag().fallQusetBag(fallLevel));
         var fallMax:Number = Number(QuestFactory.getFallMax(fallLevel));
         if(bagNum < fallMax)
         {
            return false;
         }
         return true;
      }
      
      public function p2bagFall(fallLevel:Number) : Boolean
      {
         var bagNum:Number = Number(Main.player2.getBag().fallQusetBag(fallLevel));
         var fallMax:Number = Number(QuestFactory.getFallMax(fallLevel));
         if(bagNum < fallMax)
         {
            return false;
         }
         return true;
      }
      
      public function mainQuest() : Boolean
      {
         var fallLevel:Number = NaN;
         var type:Number = NaN;
         var p1:Boolean = false;
         var p2:Boolean = false;
         for(var i:uint = 0; i < 3; i++)
         {
            if(this.特殊掉落物品[i] != 0)
            {
               fallLevel = Number(this.特殊掉落物品[i]);
               type = Number(QuestFactory.getType(fallLevel));
               if(type == 1)
               {
                  return Main.getQuest(fallLevel);
               }
               if(type == 2)
               {
                  if(Main.player2 == null)
                  {
                     return SetTransferPanel.tbo1();
                  }
                  if(Boolean(SetTransferPanel.tbo1()) && Boolean(SetTransferPanel.tbo2()))
                  {
                     return true;
                  }
               }
               else if(type == 0)
               {
                  return false;
               }
            }
         }
         return false;
      }
      
      public function NoGongJiMC() : *
      {
         var classRef:Class = null;
         if(this.noGongJiTime > 0 && !this.ZhenShe_Mc && Boolean(ChongWu.chongWu_Data[34]))
         {
            classRef = ChongWu.chongWu_Data[34].getClass("ZhenShe_Mc") as Class;
            this.ZhenShe_Mc = new classRef();
            addChild(this.ZhenShe_Mc);
            this.ZhenShe_Mc.y = -this.skin.height;
         }
         if(this.ZhenShe_Mc)
         {
            if(this.noGongJiTime > 0)
            {
               this.ZhenShe_Mc.visible = true;
            }
            else
            {
               this.ZhenShe_Mc.visible = false;
            }
         }
      }
   }
}

