package src
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.skill.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.Skin.*;
   import src.other.*;
   
   public class Skin extends MovieClip
   {
      
      private static var FindFrameObj:Object;
      
      public static var XmlLoaded:Boolean = false;
      
      public static var PlayerXml:Array = new Array();
      
      public static var xishouWuDi:Boolean = false;
      
      public static var xishouHP:int = 0;
      
      private var myTimeTemp:int = 0;
      
      private var isDef:Boolean = true;
      
      private var fyValue:int;
      
      public var playX:MovieClip;
      
      public var skinNum:int;
      
      public var Xml:XML;
      
      public var frame:int = 1;
      
      public var runOver:Boolean;
      
      public var continuous:Boolean;
      
      public var stopRun:Boolean;
      
      public var gravity:int;
      
      public var moveYN:Boolean;
      
      public var 硬直:int;
      
      public var 被攻击硬直:int;
      
      public var 魔法消耗:int;
      
      public var 能量消耗:int;
      
      public var 技能冷却:int;
      
      public var 持续时间:int;
      
      public var 作用类型:int;
      
      public var 数值1:Number;
      
      public var 数值2:Number;
      
      public var 数值3:Number;
      
      public var 数值4:Number;
      
      public var 数值5:Number;
      
      public var hpX:Number;
      
      public var hpMax:Number;
      
      public var hpXX:VT = VT.createVT();
      
      public var runX:int;
      
      public var runY:int;
      
      public var runTime:int;
      
      public var timeNum:int = 0;
      
      public var continuousTime:int;
      
      public var chiXu:int;
      
      public var yingZhi:int;
      
      public var run_X:int;
      
      public var run_Y:int;
      
      public var attTimes:int;
      
      public var type:int = 0;
      
      public var space:int = 0;
      
      public var totalTime:int = 0;
      
      public var numValue:int = 0;
      
      public var showYN:Boolean = false;
      
      public var runType:String = "站立";
      
      public var runArr:Array = [];
      
      public var moveArr:Array = [];
      
      public var skillArr:Array = ["攻击1","攻击2","攻击3","攻击4","跑攻","上挑","下斩","技能1","技能2","技能3","技能4","转职技能1","转职技能2","转职技能3","转职技能4"];
      
      public var skill_Id_Arr:Array = [["a1","a2","a3","a4","a5","a6","a7","a8","a9","a10","a11","a12","a13","a14","a15"],["b1","b2","b3","b4","b5","b6","b7","b8","b9","b10","b11","b12","b13","b14","b15"],["c1","c2","c3","c4","c5","c6","c7","c8","c9","c10","c11","c12","c13","c14","c15"],["k1","k2","k3","k4","k5","k6","k7","k8","k9","k10","k11","k12","k13","k14","k15"]];
      
      private var runNum:int = 1;
      
      internal var skill_ID:int;
      
      internal var skill_Value:int;
      
      internal var nodead_C:Class = NewLoad.XiaoGuoData.getClass("undeath") as Class;
      
      internal var nodead:MovieClip = new this.nodead_C();
      
      internal var time_Temp_2:int = 0;
      
      internal var hp_5s:int = 0;
      
      internal var time_SGHL:int = 0;
      
      public var time_mc:MovieClip;
      
      internal var kk:HitXX = null;
      
      internal var time_Temp:int = 0;
      
      internal var fanBool:Boolean = false;
      
      internal var beiShu:Number;
      
      public function Skin()
      {
         super();
         mouseEnabled = mouseChildren = false;
         this.GoTo("站");
         addEventListener(Event.REMOVED_FROM_STAGE,this.Over);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Stop() : *
      {
         gotoAndStop("站");
         removeEventListener(Event.REMOVED_FROM_STAGE,this.Over);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Over(e:* = null) : *
      {
         gotoAndStop(1);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         if(Boolean(this.playX) && !this.playX is Player2)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
         if(this.showYN)
         {
            this.Over();
            return;
         }
         if(this.timeNum > 0)
         {
            --this.timeNum;
         }
         if(this.continuousTime > 0)
         {
            --this.continuousTime;
         }
         else if(this.runType == "被打")
         {
            this.stopRun = true;
            this.GoTo("站");
         }
         this.GoToPlay();
         this.GoToPlay2();
      }
      
      public function GoToPlay2() : *
      {
         if(this.playX.skin_Z)
         {
            this.playX.skin_Z.gotoAndStop(this.currentFrame);
         }
         if(this.playX.skin_W)
         {
            this.playX.skin_W.gotoAndStop(this.currentFrame);
         }
         if(!(this.runType == "跳" || this.skinNum != 3 && this.runType == "站"))
         {
            if(this.playX.skin_Z2)
            {
               this.playX.skin_Z2.gotoAndStop(this.currentFrame);
            }
            if(this.playX.skin_Z3)
            {
               this.playX.skin_Z3.gotoAndStop(this.currentFrame);
            }
         }
      }
      
      private function GoToPlay() : *
      {
         this.isRunOver();
         if(this.runOver)
         {
            if(this.continuous || this.continuousTime > 0)
            {
               gotoAndPlay(this.runType);
               if(this.playX.skin_Z3)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z3.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z3.gotoAndPlay(this.runType);
                  }
               }
               if(this.playX.skin_Z2)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z2.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z2.gotoAndPlay(this.runType);
                  }
               }
               this.runOver = false;
               this.frame = 1;
            }
            else
            {
               this.GoTo("站");
            }
         }
         this.FindFrame();
         ++this.frame;
         if(this.playX.data.skinArr[this.playX.data.skinNum] == 2 && this.currentFrame == 295)
         {
            (this.playX as Player).noYingZhiTime = 216;
         }
      }
      
      public function GoTo(tempRun:String, timeX:int = 0, isStop:Boolean = false) : *
      {
         var strXX:* = undefined;
         var num2:* = undefined;
         var p:Player = null;
         var strX:* = undefined;
         var num:int = 0;
         if(tempRun == "被打" && this.playX && this.playX is Player && Boolean((this.playX as Player).playerCW2))
         {
            return;
         }
         if(isStop == true)
         {
            this.stopRun = true;
         }
         if(tempRun == "怪物技能")
         {
            this.enemySkill();
         }
         if(tempRun == "被打")
         {
            this.runType = tempRun;
            this.runOver = false;
            this.stopRun = false;
            gotoAndPlay(this.runType);
         }
         if((this.runOver || this.stopRun) && this.runType != tempRun)
         {
            tempRun = this.OtherGoTo(tempRun);
            if(this.CanGoTo(tempRun))
            {
               this.runType = tempRun;
               this.otherGoTo(this.runType);
               if(this.runType == "攻击1" || this.runType == "攻击2" || this.runType == "攻击3" || this.runType == "攻击4")
               {
                  Skill_bingzui.Add_Skill_bingzui(this.playX);
               }
               if(this.runType == "技能1" || this.runType == "技能2" || this.runType == "技能3" || this.runType == "技能4" || this.runType == "转职技能1" || this.runType == "转职技能2" || this.runType == "转职技能3" || this.runType == "转职技能4")
               {
                  if(this.playX is Player)
                  {
                     p = this.playX;
                     Skill_All.Add_Skill_All(p);
                     Skill_lei.Add_Skill_lei(p);
                     HuanHuaBuff.Add_HuanHuaBuff(p);
                     if(p.guangQiu)
                     {
                        p.guangQiu.GongJiType();
                     }
                     Skill_guangDun.addToPlayer(p);
                     p.huiFuYinZangXX();
                     p.fly4004_3_YN = true;
                     p.jnGoYn = true;
                  }
               }
               if(this.runType == "转职技能1" && this.playX is Player)
               {
                  p = this.playX;
                  if(p.data.skinArr[p.data.skinNum] == 3)
                  {
                     p.RZJN();
                  }
               }
               strXX = tempRun.substr(0,2);
               num2 = tempRun.substr(2,1);
               if(strXX == "技能" && (num2 >= 1 && num2 <= 4))
               {
                  p.HeiAnJiNengOk(num2);
               }
               this.runOver = false;
               this.frame = 1;
               gotoAndPlay(this.runType);
               if(this.playX.skin_Z3)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z3.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z3.gotoAndPlay(this.runType);
                  }
               }
               if(this.playX.skin_Z2)
               {
                  if(this.runType == "跳" || this.skinNum != 3 && this.runType == "站")
                  {
                     this.playX.skin_Z2.gotoAndStop(this.runType);
                  }
                  else
                  {
                     this.playX.skin_Z2.gotoAndPlay(this.runType);
                  }
               }
               this.FindFrame();
               MusicBox.ActMusicPlay(this.playX.data.skinArr[this.playX.data.skinNum],this.runType);
            }
            else
            {
               strX = tempRun.substr(0,2);
               if(strX == "技能")
               {
                  num = int(tempRun.substr(2,1));
                  if(num >= 1 && num <= 4)
                  {
                     (this.parent as Player).HeiAnJiNeng(num);
                  }
               }
            }
         }
         if(timeX >= 0)
         {
            this.continuousTime = timeX;
         }
      }
      
      public function CanGoTo(tempRun:String) : Boolean
      {
         var arr:Array = this.get_Skill_Id_and_LV(tempRun);
         if(arr[1] == 0)
         {
            return false;
         }
         if(Boolean(this.parent) && Boolean(this.parent.CanSkill(arr[0],arr[1])))
         {
            if(this.playX is Player)
            {
               JingLing.ACT_Stop(this.playX,tempRun);
            }
            return true;
         }
         return false;
      }
      
      public function Get_StopRun() : Boolean
      {
         return this.stopRun;
      }
      
      private function FindFrame() : *
      {
         var i:* = undefined;
         var 帧标签:String = null;
         var 当前帧:int = 0;
         var nowHPx:Number = NaN;
         this.moveArr = null;
         this.hpXX.setValue(this.NowHPxx());
         for(i in this.Xml.技能)
         {
            帧标签 = String(this.Xml.技能[i].帧标签);
            当前帧 = int(this.Xml.技能[i].当前帧);
            if(this.runType == 帧标签 && this.frame == 当前帧)
            {
               this.continuous = String(this.Xml.技能[i].循环).toString() == "true" || String(this.Xml.技能[i].循环).toString() == "TRUE" ? true : false;
               this.stopRun = String(this.Xml.技能[i].中断).toString() == "true" || String(this.Xml.技能[i].中断).toString() == "TRUE" ? true : false;
               this.moveYN = String(this.Xml.技能[i].移动).toString() == "true" || String(this.Xml.技能[i].移动).toString() == "TRUE" ? true : false;
               this.gravity = int(this.Xml.技能[i].重力调整);
               this.moveArr = [int(this.Xml.技能[i].移动参数.X),int(this.Xml.技能[i].移动参数.Y),int(this.Xml.技能[i].移动参数.持续)];
               nowHPx = Number(this.NowHPxx());
               this.hpX = Number(this.Xml.技能[i].伤害.hp) * nowHPx;
               this.hpXX.setValue(this.hpX);
               this.被攻击硬直 = int(this.Xml.技能[i].被攻击硬直);
               this.硬直 = int(this.Xml.技能[i].硬直) + this.yingZhi;
               this.runX = int(this.Xml.技能[i].伤害.震退) + this.run_X;
               this.runY = int(this.Xml.技能[i].伤害.挑高) + this.run_Y;
               this.runTime = int(this.Xml.技能[i].伤害.持续) + this.chiXu;
               return;
            }
         }
      }
      
      public function get_Skill_Id_and_LV(str:String = "") : Array
      {
         var i:* = undefined;
         var sId:String = null;
         var slv:int = 0;
         var arr:Array = null;
         if(str == "")
         {
            str = this.runType;
         }
         for(i in this.skillArr)
         {
            if(str == this.skillArr[i])
            {
               sId = this.skill_Id_Arr[this.skinNum][i];
               slv = int(this.parent.data.getSkillLevel(sId));
               return [sId,slv];
            }
         }
         return [-1,-1];
      }
      
      private function NowHPxx() : Number
      {
         var sArr:* = undefined;
         var hpxx:Number = NaN;
         var gameNumX:int = 0;
         var akillArr:Array = this.get_Skill_Id_and_LV();
         if(akillArr == null || akillArr[1] <= 0)
         {
            return 1;
         }
         if(Boolean(SkillFactory.skillAllDataArr2[akillArr[0]]) && Boolean(SkillFactory.skillAllDataArr2[akillArr[0]][akillArr[1]]))
         {
            sArr = (SkillFactory.skillAllDataArr2[akillArr[0]][akillArr[1]] as Skill).getSkillValueArray();
            hpxx = Number(sArr[0].getValue());
            this.attTimes = sArr[4].getValue();
            this.yingZhi = sArr[5].getValue();
            this.run_Y = sArr[6].getValue();
            this.run_X = sArr[7].getValue();
            this.chiXu = sArr[8].getValue();
            gameNumX = int(Main.gameNum.getValue());
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = sArr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = sArr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = sArr[11].getValue();
            }
            else if(gameNumX == 999)
            {
               this.hpMax = sArr[12].getValue();
            }
            else
            {
               this.hpMax = sArr[9].getValue();
            }
            return hpxx;
         }
         trace("找不到伤害系数");
         return 0;
      }
      
      private function isRunOver() : *
      {
         if(this.currentLabel != this.runType)
         {
            this.runOver = true;
            if(Boolean(this.playX) && this.playX is Player)
            {
               (this.playX as Player).jnGoYn = false;
            }
         }
         else
         {
            this.runOver = false;
         }
      }
      
      public function OtherGoTo(str:String) : String
      {
         var tempStr:String = null;
         if(this.timeNum <= 0)
         {
            this.runNum = 1;
         }
         if(this.timeNum > 0 && str == "攻击")
         {
            tempStr = str + this.runNum;
            if(this.runNum == 4)
            {
               this.runNum = 1;
            }
            else
            {
               ++this.runNum;
            }
            return tempStr;
         }
         return str;
      }
      
      public function otherGoTo(str:String) : *
      {
      }
      
      public function enemySkill() : *
      {
         var esName:String = null;
         var classRef:Class = null;
         var percent1:Number = NaN;
         var hpMaxxx:int = 0;
         var hpReal:int = 0;
         var percent2:Number = NaN;
         var mpMax:int = 0;
         var mpReal:int = 0;
         var classRef2:Class = null;
         var time_class:Class = null;
         if(this.parent.energySlot.getEnergyPer(this.parent) != 100)
         {
            return;
         }
         if(this.playX is Player)
         {
            (this.playX as Player).tsXiaoGuoFun();
         }
         if(this.parent.data.skinNum == 0)
         {
            this.parent.energySlot.energyLeftNum.setValue(0);
            if(this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(0) != null)
            {
               this.skill_ID = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(0).getGemSkill();
               this.skill_Value = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(0).getClassName();
            }
         }
         else
         {
            this.parent.energySlot.energyRightNum.setValue(0);
            if(this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(1) != null)
            {
               this.skill_ID = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(1).getGemSkill();
               this.skill_Value = this.parent.data.getEquipSkillSlot().getGemFromSkillSlot(1).getClassName();
            }
         }
         var sk:Skill = SkillFactory.getSkillById(this.skill_ID);
         esName = sk.getSkillName();
         var arr:Array = sk.getSkillValueArray();
         var typeTemp:Number = sk.getSkillActOn();
         var gameNumX:int = int(Main.gameNum.getValue());
         if(esName == "眼镜蛇冲击")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("眼镜蛇冲击") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "双头狗冲击")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("双头狗冲击") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "双头狗冲击波")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("双头狗冲击波") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "蝎王地刺")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("蝎王地刺") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "雪女落冰")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[6].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[6].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("雪女落冰") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "金太郎斧劈")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("金太郎斧劈") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "生命复苏")
         {
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("生命复苏") as Class;
            this.parent.skin_W.addChild(new classRef());
            percent1 = Number(arr[0].getValue());
            hpMaxxx = int((this.parent as Player).use_hp_Max.getValue());
            hpReal = int(hpMaxxx * percent1);
            (this.parent as Player).HpUp(hpReal);
         }
         else if(esName == "法力再生")
         {
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("法力再生") as Class;
            this.parent.skin_W.addChild(new classRef());
            percent2 = Number(arr[0].getValue());
            mpMax = int((this.parent as Player).use_mp_Max.getValue());
            mpReal = int(mpMax * percent2);
            (this.parent as Player).MpUp(mpReal);
         }
         else if(esName == "熔岩魔天火")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("熔岩魔天火") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "死灵爆裂")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("死灵爆裂") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "鳄鱼旋风斩")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("鳄鱼旋风斩") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "狮王裂地斩")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("狮王裂地斩") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "杀戮狂魔波")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("狂魔波技能") as Class;
            classRef2 = NewLoad.enemySkill[this.skill_Value].getClass("杀戮狂魔波") as Class;
            this.parent.skin_W.addChild(new classRef());
            this.parent.skin_W.addChild(new classRef2());
            trace("怪物技能 杀戮狂魔波 >>>>>>>>>>>>>>>>",this.hpX,this.attTimes);
         }
         else if(esName == "白狼滚石")
         {
            setTimeout(this.gunshi,1000);
            setTimeout(this.gunshi,1300);
            classRef2 = NewLoad.enemySkill[this.skill_Value].getClass("白狼滚石") as Class;
            this.parent.skin_W.addChild(new classRef2());
         }
         else if(esName == "白狼怒吼")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("白狼怒吼") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "暗黑风暴")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("暗黑风暴") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "真·暗黑风暴")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("暗黑风暴") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "黑洞")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("黑洞") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "魔轮转")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("魔轮转") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "真·魔轮转")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("魔轮转") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "缩小术")
         {
            this.type = typeTemp;
            this.hpX = arr[0].getValue();
            this.numValue = arr[1].getValue();
            this.space = arr[2].getValue();
            this.totalTime = arr[3].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("缩小术") as Class;
            classRef2 = NewLoad.enemySkill[this.skill_Value].getClass("缩小吸引") as Class;
            this.parent.skin_W.addChild(new classRef());
            this.parent.skin_W.addChild(new classRef2());
         }
         else if(esName == "雪女风雪")
         {
            this.type = typeTemp;
            this.hpX = arr[0].getValue();
            this.numValue = arr[1].getValue();
            this.space = arr[2].getValue();
            this.totalTime = arr[3].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            this.hpMax = arr[9].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("雪女风雪") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "机械反弹")
         {
            this.beiShu = arr[0].getValue();
            TweenMax.to(this,3,{"dropShadowFilter":{
               "color":16711680,
               "alpha":1,
               "blurX":18,
               "blurY":18
            }});
            this.addEventListener(Event.ENTER_FRAME,this.fanshang);
         }
         else if(esName == "无敌罩")
         {
            this.hpX = arr[0].getValue();
            this.attTimes = arr[4].getValue();
            this.硬直 = arr[5].getValue();
            this.runY = arr[6].getValue();
            this.runX = arr[7].getValue();
            this.runTime = arr[8].getValue();
            if(gameNumX >= 1 && gameNumX <= 9)
            {
               this.hpMax = arr[9].getValue();
            }
            else if(gameNumX >= 10 && gameNumX <= 16)
            {
               this.hpMax = arr[10].getValue();
            }
            else if(gameNumX >= 51 && gameNumX <= 62)
            {
               this.hpMax = arr[11].getValue();
            }
            else
            {
               this.hpMax = arr[9].getValue();
            }
            classRef = NewLoad.enemySkill[this.skill_Value].getClass("无敌罩") as Class;
            this.parent.skin_W.addChild(new classRef());
         }
         else if(esName == "吸收护罩")
         {
            xishouHP = (this.parent as Player).use_hp_Max.getValue() * arr[0].getValue();
            xishouWuDi = true;
            this.parent.addChild(this.nodead);
            this.time_Temp_2 = 0;
            this.addEventListener(Event.ENTER_FRAME,this.xishou);
            this.addEventListener(Event.REMOVED_FROM_STAGE,this.shanchu);
         }
         else if(esName == "时光回流")
         {
            time_class = NewLoad.XiaoGuoData.getClass("时光回流") as Class;
            this.time_mc = new time_class();
            this.hp_5s = (this.parent as Player).hp.getValue();
            Main.world.moveChild_Other.addChild(this.time_mc);
            this.addEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
            this.addEventListener(Event.REMOVED_FROM_STAGE,this.shanchu2);
         }
      }
      
      public function shanchu(e:*) : *
      {
         if(this.parent.contains(this.nodead))
         {
            this.parent.removeChild(this.nodead);
         }
         xishouWuDi = false;
         EnergySlot.energyBool = true;
         this.removeEventListener(Event.ENTER_FRAME,this.xishou);
      }
      
      public function xishou(e:*) : *
      {
         EnergySlot.energyBool = false;
         ++this.time_Temp_2;
         if(xishouWuDi == false)
         {
            if(this.parent.contains(this.nodead))
            {
               this.parent.removeChild(this.nodead);
            }
            EnergySlot.energyBool = true;
            this.time_Temp_2 = 0;
            this.removeEventListener(Event.ENTER_FRAME,this.xishou);
            return;
         }
         if(this.time_Temp_2 > 216)
         {
            xishouWuDi = false;
            this.time_Temp_2 = 0;
            EnergySlot.energyBool = true;
            if(this.parent.contains(this.nodead))
            {
               this.parent.removeChild(this.nodead);
            }
            this.removeEventListener(Event.ENTER_FRAME,this.xishou);
            return;
         }
      }
      
      public function shanchu2(e:*) : *
      {
         if(Main.world.moveChild_Other.contains(this.time_mc))
         {
            Main.world.moveChild_Other.removeChild(this.time_mc);
         }
         EnergySlot.energyBool = true;
         this.removeEventListener(Event.ENTER_FRAME,this.shanchu2);
      }
      
      private function shiguanghuiliu(e:*) : *
      {
         var hpx:int = 0;
         ++this.time_SGHL;
         if(this.parent)
         {
            EnergySlot.energyBool = false;
            this.time_mc.x = (this.parent as Player).x;
            this.time_mc.y = (this.parent as Player).y;
            if((this.parent as Player).hp.getValue() <= 0)
            {
               if(Main.world.moveChild_Other.contains(this.time_mc))
               {
                  Main.world.moveChild_Other.removeChild(this.time_mc);
               }
               EnergySlot.energyBool = true;
               this.removeEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
            }
            if(this.time_SGHL > 147)
            {
               if((this.parent as Player).hp.getValue() > 0)
               {
                  hpx = this.hp_5s - (this.parent as Player).hp.getValue();
                  (this.parent as Player).hp.setValue(this.hp_5s);
                  if(hpx > 0)
                  {
                     NewMC.Open("回血效果",this.parent,0,0,0,hpx);
                  }
                  if(Main.world.moveChild_Other.contains(this.time_mc))
                  {
                     Main.world.moveChild_Other.removeChild(this.time_mc);
                  }
               }
               EnergySlot.energyBool = true;
               this.removeEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
               this.time_SGHL = 0;
            }
         }
         else
         {
            if(Main.world.moveChild_Other.contains(this.time_mc))
            {
               Main.world.moveChild_Other.removeChild(this.time_mc);
            }
            EnergySlot.energyBool = true;
            this.removeEventListener(Event.ENTER_FRAME,this.shiguanghuiliu);
            this.time_SGHL = 0;
         }
      }
      
      internal function fanshang(e:*) : *
      {
         var i:* = undefined;
         this.fanBool = true;
         ++this.time_Temp;
         if(this.time_Temp > 400)
         {
            this.time_Temp = 0;
            this.fanBool = false;
            TweenMax.to(this,1,{"dropShadowFilter":{
               "color":16711680,
               "alpha":0
            }});
            this.removeEventListener(Event.ENTER_FRAME,this.fanshang);
         }
         if(this.fanBool == true)
         {
            for(i in HitXX.AllHitXX)
            {
               if(Boolean(HitXX.AllHitXX[i]) && this.parent.hitXX == HitXX.AllHitXX[i])
               {
                  this.parent.hitXX.runArr = [0,0,0];
                  this.parent.hitXX.硬直 = 0;
                  this.parent.hitXX.times = 200;
                  this.parent.hitXX.gongJi_hp *= this.beiShu;
                  if(this.parent.hitXX != this.kk)
                  {
                     this.kk = this.parent.hitXX;
                     if((this.parent.hitXX.who as Enemy).life.getValue() > 0)
                     {
                        (this.parent.hitXX.who as Enemy).HpXX(this.parent.hitXX);
                     }
                  }
               }
            }
         }
      }
      
      public function gunshi() : *
      {
         var gameNumX:int = int(Main.gameNum.getValue());
         var sk:Skill = SkillFactory.getSkillById(this.skill_ID);
         esName = sk.getSkillName();
         var arr:Array = sk.getSkillValueArray();
         var typeTemp:Number = sk.getSkillActOn();
         this.hpX = arr[0].getValue();
         this.attTimes = arr[4].getValue();
         this.硬直 = arr[5].getValue();
         this.runY = arr[6].getValue();
         this.runX = arr[7].getValue();
         this.runTime = arr[8].getValue();
         if(gameNumX >= 1 && gameNumX <= 9)
         {
            this.hpMax = arr[6].getValue();
         }
         else if(gameNumX >= 10 && gameNumX <= 16)
         {
            this.hpMax = arr[10].getValue();
         }
         else if(gameNumX >= 51 && gameNumX <= 62)
         {
            this.hpMax = arr[11].getValue();
         }
         else
         {
            this.hpMax = arr[12].getValue();
         }
         var classRef:Class = NewLoad.enemySkill[this.skill_Value].getClass("滚石") as Class;
         this.parent.skin_W.addChild(new classRef());
      }
      
      public function 震动(num:int = 4) : *
      {
         if(Main.world)
         {
            Main.world.Quake(num);
         }
      }
   }
}

