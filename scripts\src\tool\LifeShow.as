package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.Enemy;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol930")]
   public class LifeShow extends MovieClip
   {
      
      public static var _this:LifeShow;
      
      public var t2_txt:TextField;
      
      public var x1:MovieClip;
      
      public var x2:MovieClip;
      
      private var who:Enemy;
      
      private var maxHP:Number = 50000;
      
      private var oneHP:Number = 5000;
      
      private var enemyHP_show:int = 50000;
      
      private var enemyHP_new:Number = 50000;
      
      private var enemyHP_old:Number = 50000;
      
      private var col_1:uint;
      
      private var col_2:uint;
      
      private var m1:MovieClip;
      
      private var m2:MovieClip;
      
      public var xxHp:int = 0;
      
      private var colArr:Array = ["0x3399FF","0x00CC33","0x9966FF","0xFF9900"];
      
      private var hpDownNumMax:int = 20;
      
      private var hpDownNum:int = 20;
      
      public function LifeShow()
      {
         super();
         _this = this;
         this.m1 = this["x1"] as MovieClip;
         this.m2 = this["x2"] as MovieClip;
      }
      
      public function Start(_who:Enemy) : *
      {
         var xx2:int = 0;
         if(!this.who || this.who != _who)
         {
            this.who = _who;
            this.maxHP = this.who.lifeMAX.getValue();
            this.enemyHP_show = this.enemyHP_old = this.who.life.getValue();
            if(this.maxHP == this.enemyHP_old)
            {
               this.who.GongHuiHp_Num();
               this.enemyHP_old = this.maxHP;
            }
            this.m2.visible = true;
            this.m1.scaleX = 1;
            if(this.maxHP > 100000)
            {
               xx2 = this.maxHP / 100000 + 1;
               this.oneHP = this.maxHP / xx2;
            }
            else
            {
               this.oneHP = this.maxHP / 2;
            }
            this.col_1 = 3381759;
            if(this.enemyHP_show < 200000)
            {
               this.col_2 = 13369344;
            }
            else
            {
               this.col_2 = 52275;
            }
            this.MC_color(this.m1,this.col_1);
            this.MC_color(this.m2,this.col_2);
            this.t2_txt.text = "X" + int(this.maxHP / this.oneHP);
            addEventListener(Event.ENTER_FRAME,this.HpDown);
         }
      }
      
      public function Stop() : *
      {
         this.who = null;
         removeEventListener(Event.ENTER_FRAME,this.HpDown);
      }
      
      private function HpDown(e:* = null) : *
      {
         var xLife:int = 0;
         var oldTiaoShu:int = 0;
         var thisDown:int = 0;
         var newTiaoShu:int = 0;
         if(!this.who)
         {
            return;
         }
         this.enemyHP_new = this.who.life.getValue();
         if(this.enemyHP_new != this.enemyHP_old)
         {
            this.hpDownNum = this.hpDownNumMax;
            xLife = this.enemyHP_old - this.enemyHP_new;
            this.xxHp += xLife;
            this.enemyHP_old = this.enemyHP_new;
         }
         if(this.xxHp == 0)
         {
            this.hpDownNum = this.hpDownNumMax;
            return;
         }
         if(this.hpDownNum > 0)
         {
            if(this.hpDownNum == this.hpDownNumMax)
            {
               this.MC_color(this.m1,16777215);
            }
            else if(this.hpDownNum == this.hpDownNumMax - 3)
            {
               this.MC_color(this.m1,this.col_1);
            }
            --this.hpDownNum;
            oldTiaoShu = Math.ceil(this.enemyHP_show / this.oneHP);
            thisDown = this.xxHp / this.hpDownNum;
            this.enemyHP_show -= thisDown;
            this.xxHp -= thisDown;
            newTiaoShu = Math.ceil(this.enemyHP_show / this.oneHP);
            this.m1.scaleX = this.enemyHP_show % this.oneHP / this.oneHP;
            if(this.m1.scaleX < 0)
            {
               this.m1.scaleX = 0;
            }
            if(oldTiaoShu != newTiaoShu)
            {
               this.JiaoHuanMC(newTiaoShu);
            }
            if(newTiaoShu > 1)
            {
               this.m2.visible = true;
            }
            else
            {
               this.m2.visible = false;
            }
         }
      }
      
      private function JiaoHuanMC(num:uint) : *
      {
         var x:int = 0;
         this.col_1 = this.col_2;
         if(num <= 1)
         {
            this.col_1 = 13369344;
            this.t2_txt.text = "";
         }
         else
         {
            this.t2_txt.text = "X" + num;
            x = int(this.systemChange(this.colArr[num % this.colArr.length],16,10));
            this.col_2 = x;
         }
         this.MC_color(this.m1,this.col_1);
         this.MC_color(this.m2,this.col_2);
         this.hpDownNum == this.hpDownNumMax;
      }
      
      private function MC_color(mc:MovieClip, num:uint) : *
      {
         var col:uint = num;
         var Boxcolor:ColorTransform = new ColorTransform();
         Boxcolor.color = col;
         mc.transform.colorTransform = Boxcolor;
      }
      
      public function randomColor() : String
      {
         var col:int = 0;
         var str:String = "0x";
         for(var i:int = 0; i < 3; i++)
         {
            col = Math.random() * 223 + 16;
            str += this.systemChange(String(col),10,16);
         }
         return str;
      }
      
      public function systemChange(txt:String, radix:uint, target:uint) : String
      {
         var num:Number = Number(parseInt(txt,radix));
         return num.toString(target);
      }
   }
}

