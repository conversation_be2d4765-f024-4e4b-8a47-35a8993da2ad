package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol3488")]
   public dynamic class 购买物品提示栏 extends MovieClip
   {
      
      public var P1_buy_btn:SimpleButton;
      
      public var P2_buy_btn:SimpleButton;
      
      public var close_btn:SimpleButton;
      
      public var pic_mc:Shop_picNEW;
      
      public var 名字:TextField;
      
      public var 数量:TextField;
      
      public var 点券:TextField;
      
      public var 说明:TextField;
      
      public function 购买物品提示栏()
      {
         super();
      }
   }
}

