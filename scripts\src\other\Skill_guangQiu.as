package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_guangQiu extends Fly
   {
      
      public var typeX:int = 1;
      
      public var gjPoint:Point;
      
      public var gjTime:int;
      
      public function Skill_guangQiu()
      {
         super();
      }
      
      public static function addToPlayer(p:Player) : *
      {
         var classX:Class = null;
         if(EquipYN(p))
         {
            classX = NewLoad.OtherData.getClass("圣域光球") as Class;
            p.guangQiu = new classX();
            p.guangQiu.who = p;
            p.guangQiu.init();
            Main.world.moveChild_Other.addChild(p.guangQiu);
         }
      }
      
      public static function EquipYN(p:Player) : Boolean
      {
         var equipX:Equip = null;
         if(Boolean(p) && Boolean(p.guangQiu))
         {
            p.guangQiu.onREMOVED_FROM_STAGE();
         }
         if(<PERSON><PERSON><PERSON>(p) && Boolean(p.data.getEquipSlot().getEquipFromSlot(7)))
         {
            equipX = p.data.getEquipSlot().getEquipFromSlot(7);
            if(equipX && equipX.getFrame() == 486 && equipX.getRemainingTime() > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function init() : *
      {
         RL = who.RL;
         gongJi_hp_MAX = 6000;
         硬直 = 0;
         gongJi_hp = 2;
         this.x = who.x;
         this.y = who.y - 80;
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function onREMOVED_FROM_STAGE(e:* = null) : *
      {
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
         this.who.guangQiu = null;
         this.Dead();
      }
      
      override public function onADDED_TO_STAGE(e:* = null) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
      }
      
      override public function onENTER_FRAME(e:*) : *
      {
         if(this.typeX != 2)
         {
            if(who.flyTime != 0)
            {
               if(this.typeX < 3)
               {
                  this.typeX = 3;
               }
            }
            else
            {
               this.typeX = 1;
            }
         }
         this.mcPlay();
         this.Move();
      }
      
      override public function Move() : *
      {
         var xx:int = 0;
         var speedXX:int = 0;
         var speedYY:int = 0;
         this.visible = true;
         if(who && who is Player && Boolean((who as Player).playerCW2))
         {
            this.visible = false;
         }
         this.scaleX = 1;
         if(this.typeX == 2)
         {
            if(this.gjTime <= 0)
            {
               this.typeX = 1;
               this.rotation = 0;
            }
            else
            {
               this.x += this.gjPoint.x;
               this.y += this.gjPoint.y;
            }
            --this.gjTime;
         }
         else if(this.typeX == 1)
         {
            this.rotation = 0;
            xx = who.RL ? int(who.x - 50) : int(who.x + 50);
            speedXX = Math.abs(who.x - this.x) / 12;
            speedYY = Math.abs(who.y - this.y) / 12;
            if(this.x < xx - speedXX - 20)
            {
               this.x += speedXX;
            }
            else if(this.x > xx + speedXX + 20)
            {
               this.x -= speedXX;
            }
            if(this.y < who.y - 45 - speedYY - 5)
            {
               this.y += speedYY;
            }
            else if(this.y > who.y - 45 + speedYY + 5)
            {
               this.y -= speedYY;
            }
         }
         else if(this.typeX == 4)
         {
            this.rotation = 0;
            this.x = who.x;
            this.y = who.y;
            this.scaleX = who.skin.scaleX;
         }
      }
      
      public function GongJiType() : *
      {
         var num:int = 0;
         var en:Enemy = null;
         var xx:Number = NaN;
         var yy:Number = NaN;
         var p1:Point = null;
         var p2:Point = null;
         var angle:Number = NaN;
         var theta:Number = NaN;
         if(Enemy.All.length > 0)
         {
            num = Math.random() * Enemy.All.length;
            en = Enemy.All[num];
            xx = en.x - this.x;
            yy = en.y - this.y;
            p1 = new Point(this.x,this.y);
            p2 = new Point(en.x,en.y - 70);
            angle = Math.atan2(p1.x - p2.x,p1.y - p2.y);
            theta = angle * (180 / Math.PI);
            this.rotation = 180 - theta;
            this.gjTime = 10;
            this.gjPoint = new Point(xx / this.gjTime,yy / this.gjTime);
            this.typeX = 2;
         }
      }
      
      public function mcPlay() : *
      {
         if(this.typeX == 2)
         {
            if(this.currentLabel != "飞")
            {
               this.gotoAndPlay("飞");
            }
         }
         else if(this.typeX == 1)
         {
            if(this.currentLabel != "跟随")
            {
               this.gotoAndPlay("跟随");
            }
         }
         else if(this.typeX == 3)
         {
            if(this.currentLabel != "消失" && this.currentLabel != "护罩生成" && this.currentLabel != "护罩")
            {
               this.gotoAndPlay("消失");
            }
            if(this.currentLabel != "消失")
            {
               this.gotoAndPlay("护罩生成");
               this.typeX = 4;
            }
         }
         else if(this.typeX == 4)
         {
            if(this.currentLabel != "护罩" && this.currentLabel != "护罩生成")
            {
               this.gotoAndPlay("护罩");
            }
         }
      }
   }
}

