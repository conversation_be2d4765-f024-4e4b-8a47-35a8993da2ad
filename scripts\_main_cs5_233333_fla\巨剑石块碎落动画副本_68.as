package _main_cs5_233333_fla
{
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol6783")]
   public dynamic class 巨剑石块碎落动画副本_68 extends MovieClip
   {
      
      public function 巨剑石块碎落动画副本_68()
      {
         super();
         addFrameScript(74,this.frame75);
      }
      
      internal function frame75() : *
      {
         stop();
      }
   }
}

