package com.hotpoint.braveManIII.views.wantedPanel
{
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class NewWantedPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var xsrwPanel:MovieClip;
      
      public static var xsrw:NewWantedPanel;
      
      private static var loadData:ClassLoader;
      
      public static var CZXSOK:Boolean = false;
      
      public static var clickNum:Number = 0;
      
      public static var czNum:Number = 0;
      
      public static var click:Number = 0;
      
      public static var yeshu:Number = 1;
      
      public static var overNum:Number = 0;
      
      public static var JLOK:Boolean = false;
      
      private static var loadName:String = "NewWanted_v1100.swf";
      
      private static var OpenYN:Boolean = false;
      
      public function NewWantedPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!xsrwPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("WantedShow") as Class;
         xsrwPanel = new classRef();
         xsrw.addChild(xsrwPanel);
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         xsrw = new NewWantedPanel();
         LoadSkin();
         Main._stage.addChild(xsrw);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         xsrw = new NewWantedPanel();
         Main._stage.addChild(xsrw);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         if(!Main.wts)
         {
            Main.wts = WantedTaskSlot.creatWantedTaskSlot();
         }
         Main.allClosePanel();
         if(xsrwPanel)
         {
            if(Main.wts.getAccess() > -1)
            {
               clickNum = Main.wts.getAccess();
               yeshu = Math.ceil((Main.wts.getAccess() + 1) / 3);
            }
            Main.stopXX = true;
            xsrw.x = 0;
            xsrw.y = 0;
            addListener();
            xsrw.visible = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(!Main.wts)
         {
            Main.wts = WantedTaskSlot.creatWantedTaskSlot();
         }
         if(xsrwPanel)
         {
            Main.stopXX = false;
            removeListener();
            xsrw.visible = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListener() : *
      {
         xsrwPanel["touming"].visible = false;
         xsrwPanel["msg_mc"].stop();
         xsrwPanel["msg_mc"].mouseEnabled = false;
         xsrwPanel["msg_mc"].visible = false;
         xsrwPanel["NoMoney_mc"].visible = false;
         xsrwPanel["lingqu_btn"].visible = false;
         xsrwPanel["NoMoney_mc"]["yes_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         xsrwPanel["NoMoney_mc"]["addMoney_btn"].addEventListener(MouseEvent.CLICK,addMoney_btn);
         xsrwPanel["shang_btn"].addEventListener(MouseEvent.CLICK,shang_btn);
         xsrwPanel["xia_btn"].addEventListener(MouseEvent.CLICK,xia_btn);
         xsrwPanel["lingqu_btn"].addEventListener(MouseEvent.CLICK,lingqu);
         xsrwPanel["closeP"].addEventListener(MouseEvent.CLICK,closeXS);
         for(var i:int = 0; i < 3; i++)
         {
            xsrwPanel["kk" + i].addEventListener(MouseEvent.MOUSE_OVER,overDO);
            xsrwPanel["kk" + i].addEventListener(MouseEvent.MOUSE_OUT,outDO);
            xsrwPanel["cd" + i].addEventListener(MouseEvent.CLICK,lqReset);
            xsrwPanel["jq" + i].addEventListener(MouseEvent.CLICK,jiequ);
            xsrwPanel["fq" + i].addEventListener(MouseEvent.CLICK,fangqiRW);
            xsrwPanel["wc" + i].addEventListener(MouseEvent.CLICK,wanchengRW);
            xsrwPanel["number" + i].stop();
            xsrwPanel["time" + i].visible = false;
            xsrwPanel["yjq" + i].visible = false;
         }
         panelShow();
      }
      
      public static function removeListener() : *
      {
         xsrwPanel["NoMoney_mc"]["yes_btn"].removeEventListener(MouseEvent.CLICK,closeNORMB);
         xsrwPanel["NoMoney_mc"]["addMoney_btn"].removeEventListener(MouseEvent.CLICK,addMoney_btn);
         xsrwPanel["shang_btn"].removeEventListener(MouseEvent.CLICK,shang_btn);
         xsrwPanel["xia_btn"].removeEventListener(MouseEvent.CLICK,xia_btn);
         xsrwPanel["lingqu_btn"].removeEventListener(MouseEvent.CLICK,lingqu);
         xsrwPanel["closeP"].removeEventListener(MouseEvent.CLICK,closeXS);
         for(var i:int = 0; i < 3; i++)
         {
            xsrwPanel["kk" + i].removeEventListener(MouseEvent.MOUSE_OVER,overDO);
            xsrwPanel["kk" + i].removeEventListener(MouseEvent.MOUSE_OUT,outDO);
            xsrwPanel["cd" + i].removeEventListener(MouseEvent.CLICK,lqReset);
            xsrwPanel["jq" + i].removeEventListener(MouseEvent.CLICK,jiequ);
            xsrwPanel["fq" + i].removeEventListener(MouseEvent.CLICK,fangqiRW);
            xsrwPanel["wc" + i].removeEventListener(MouseEvent.CLICK,wanchengRW);
         }
      }
      
      public static function outDO(e:*) : *
      {
         xsrwPanel["msg_mc"].visible = false;
      }
      
      public static function overDO(e:*) : *
      {
         click = e.target.name.substr(2,1);
         xsrwPanel["msg_mc"].visible = true;
         xsrwPanel["msg_mc"].x = xsrwPanel.mouseX + 10;
         xsrwPanel["msg_mc"].y = 200;
         if(xsrwPanel.mouseX > 600)
         {
            xsrwPanel["msg_mc"].x -= 400;
         }
         click = Main.wts.getWantedTaskFromSlot(click + 3 * (yeshu - 1)).getFrame();
         xsrwPanel["msg_mc"].gotoAndStop(click);
      }
      
      public static function lqReset(e:*) : *
      {
         czNum = e.target.name.substr(2,1);
         czNum += (yeshu - 1) * 3;
         if(Main.player1.getBag().getOtherobjNum(63289) >= 3)
         {
            Main.player1.getBag().delOtherById(63289,3);
            Main.wts.delCD(czNum);
            panelShow();
         }
         else if(Boolean(Main.P1P2) && Main.player2.getBag().getOtherobjNum(63289) >= 3)
         {
            Main.player2.getBag().delOtherById(63289,3);
            Main.wts.delCD(czNum);
            panelShow();
         }
         else if(Shop4399.moneyAll.getValue() >= 5)
         {
            Api_4399_All.BuyObj(InitData.chongzhirenwu.getValue());
            CZXSOK = true;
            xsrwPanel["touming"].visible = true;
         }
         else
         {
            xsrwPanel["NoMoney_mc"].visible = true;
         }
      }
      
      public static function lqczOK() : *
      {
         if(CZXSOK)
         {
            xsrwPanel["touming"].visible = false;
            Main.wts.delCD(czNum);
            CZXSOK = false;
            panelShow();
         }
      }
      
      private static function closeNORMB(e:*) : void
      {
         xsrwPanel["NoMoney_mc"].visible = false;
      }
      
      private static function addMoney_btn(e:*) : void
      {
         Main.ChongZhi();
      }
      
      public static function closeXS(e:*) : *
      {
         close();
      }
      
      public static function shang_btn(e:*) : *
      {
         if(yeshu > 1)
         {
            --yeshu;
         }
         panelShow();
      }
      
      public static function xia_btn(e:*) : *
      {
         if(yeshu == 1 && Main.wts.wantedTaskTimesLV1() >= 10)
         {
            yeshu = 2;
         }
         else if(yeshu == 2 && Main.wts.wantedTaskTimesLV2() >= 10)
         {
            yeshu = 3;
         }
         else if(yeshu == 3 && Main.wts.wantedTaskTimesLV3() >= 10)
         {
            yeshu = 4;
         }
         else if(yeshu == 4 && Main.wts.wantedTaskTimesLV4() >= 10)
         {
            yeshu = 5;
         }
         else if(yeshu == 5 && Main.wts.wantedTaskTimesLV5() >= 10)
         {
            yeshu = 6;
         }
         else if(yeshu == 6 && Main.wts.wantedTaskTimesLV6() >= 10)
         {
            yeshu = 7;
         }
         else if(yeshu == 7 && Main.wts.wantedTaskTimesLV7() >= 10)
         {
            yeshu = 8;
         }
         else if(yeshu == 8 && Main.wts.wantedTaskTimesLV8() >= 10)
         {
            yeshu = 9;
         }
         else if(yeshu == 9 && Main.wts.wantedTaskTimesLV9() >= 10)
         {
            yeshu = 10;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"该等级完成次数不足，无法进入下一等级悬赏");
         }
         panelShow();
      }
      
      public static function wanchengRW(e:*) : *
      {
         clickNum = e.target.name.substr(2,1);
         clickNum += 3 * (yeshu - 1);
         if(Main.P1P2)
         {
            if(Main.player1.getBag().backSuppliesBagNum() > 1 && Main.player2.getBag().backSuppliesBagNum() > 1)
            {
               Main.wts.getWantedTaskFromSlot(clickNum).setState(3);
               panelShow();
               JLOK = true;
               xsrwPanel["touming"].visible = true;
               Main.Save();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,奖励无法发放");
            }
         }
         else if(Main.player1.getBag().backSuppliesBagNum() > 1)
         {
            Main.wts.getWantedTaskFromSlot(clickNum).setState(3);
            panelShow();
            JLOK = true;
            xsrwPanel["touming"].visible = true;
            Main.Save();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,奖励无法发放");
         }
      }
      
      public static function getJiangLi() : *
      {
         if(JLOK)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backSuppliesBagNum() > 1 && Main.player2.getBag().backSuppliesBagNum() > 1)
               {
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21219));
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21220));
                  Main.player2.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21219));
                  Main.player2.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21220));
                  Main.wts.getWantedTaskFromSlot(clickNum).setState(3);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"奖励领取成功，请在背包查看");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
               if(Math.random() * 10 > 5)
               {
                  if(Main.player1.getBag().backGemBagNum() > 0)
                  {
                     Main.player1.getBag().addGemBag(GemFactory.creatGemById(Main.wts.getWantedTaskFromSlot(clickNum).getReward_1()));
                  }
               }
            }
            else
            {
               if(Main.player1.getBag().backSuppliesBagNum() > 1)
               {
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21219));
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21220));
                  Main.wts.getWantedTaskFromSlot(clickNum).setState(3);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"奖励领取成功，请在背包查看");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
               if(Math.random() * 10 > 5)
               {
                  if(Main.player1.getBag().backGemBagNum() > 0)
                  {
                     Main.player1.getBag().addGemBag(GemFactory.creatGemById(Main.wts.getWantedTaskFromSlot(clickNum).getReward_1()));
                  }
               }
            }
            JLOK = false;
            xsrwPanel["touming"].visible = false;
         }
      }
      
      public static function fangqiRW(e:*) : *
      {
         clickNum = e.target.name.substr(2,1);
         clickNum += 3 * (yeshu - 1);
         Main.wts.getWantedTaskFromSlot(clickNum).setState(0);
         panelShow();
      }
      
      public static function jiequ(e:*) : *
      {
         overNum = e.target.name.substr(2,1);
         overNum += 3 * (yeshu - 1);
         if(Main.P1P2)
         {
            if(Main.player1.getGold() >= 12000 && Main.player2.getGold() >= 12000)
            {
               if(Main.player1.getKillPoint() > 30 && Main.player2.getKillPoint() > 30)
               {
                  Main.player2.AddKillPoint(-30);
                  Main.player1.payGold(12000);
                  Main.player1.AddKillPoint(-30);
                  Main.player2.payGold(12000);
                  Main.wts.setWantedTaskSlot(overNum);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
            }
         }
         else if(Main.player1.getGold() >= 12000)
         {
            if(Main.player1.getKillPoint() > 30)
            {
               Main.player1.payGold(12000);
               Main.player1.AddKillPoint(-30);
               Main.wts.setWantedTaskSlot(overNum);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点不足");
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
         }
         panelShow();
      }
      
      public static function lingqu(e:*) : *
      {
         if(Main.wts.getJLTimes() < 1 && yeshu == 1)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63176));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63176));
                  Main.wts.addJLTimes();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"称号领取成功，已放入背包其他栏");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63176));
               Main.wts.addJLTimes();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"称号领取成功，已放入背包其他栏");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes2() < 1 && yeshu == 2)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63201));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63201));
                  Main.wts.addJLTimes2();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"称号领取成功，已放入背包其他栏");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63201));
               Main.wts.addJLTimes2();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"称号领取成功，已放入背包其他栏");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes3() < 1 && yeshu == 3)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63213));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63213));
                  Main.wts.addJLTimes3();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63213));
               Main.wts.addJLTimes3();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes4() < 1 && yeshu == 4)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63217));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63217));
                  Main.wts.addJLTimes4();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63217));
               Main.wts.addJLTimes4();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes5() < 1 && yeshu == 5)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63233));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63233));
                  Main.wts.addJLTimes5();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63233));
               Main.wts.addJLTimes5();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes6() < 1 && yeshu == 6)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63240));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63240));
                  Main.wts.addJLTimes6();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63240));
               Main.wts.addJLTimes6();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes7() < 1 && yeshu == 7)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63276));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63276));
                  Main.wts.addJLTimes7();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63276));
               Main.wts.addJLTimes7();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes8() < 2 && yeshu == 8)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63283));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63283));
                  Main.wts.addJLTimes8();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63283));
               Main.wts.addJLTimes8();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes9() < 2 && yeshu == 9)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63330));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63330));
                  Main.wts.addJLTimes9();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63330));
               Main.wts.addJLTimes9();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            }
         }
         if(Main.wts.getJLTimes10() < 2 && yeshu == 10)
         {
            if(Main.P1P2)
            {
               if(Main.player1.getBag().backOtherBagNum() >= 1 && Main.player2.getBag().backOtherBagNum() >= 1)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63393));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63393));
                  Main.wts.addJLTimes10();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63393));
               Main.wts.addJLTimes10();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            }
         }
         panelShow();
      }
      
      private static function xxShow(count:int) : *
      {
         for(var i:int = 0; i < 10; i++)
         {
            if(i < count)
            {
               xsrwPanel["xx" + i].visible = true;
            }
            else
            {
               xsrwPanel["xx" + i].visible = false;
            }
         }
      }
      
      private static function panelShow() : *
      {
         var i:int = 0;
         for(i = 0; i < 3; i++)
         {
            xsrwPanel["fq" + i].visible = false;
            xsrwPanel["wc" + i].visible = false;
            xsrwPanel["cd" + i].visible = false;
            xsrwPanel["yjq" + i].visible = false;
            xsrwPanel["jq" + i].visible = true;
            xsrwPanel["number" + i].gotoAndStop(yeshu);
            xsrwPanel["tq" + i].gotoAndStop(Main.wts.getWantedTaskFromSlot(i + 3 * (yeshu - 1)).getFrame());
         }
         xsrwPanel["lingqu_btn"].visible = false;
         xsrwPanel._money_txt.text = Main.player1.getGold();
         xsrwPanel._kill_txt.text = Main.player1.getKillPoint();
         xsrwPanel._rmb_txt.text = Shop4399.moneyAll.getValue();
         if(yeshu == 1)
         {
            xxShow(Main.wts.wantedTaskTimesLV1());
         }
         else if(yeshu == 2)
         {
            xxShow(Main.wts.wantedTaskTimesLV2());
         }
         else if(yeshu == 3)
         {
            xxShow(Main.wts.wantedTaskTimesLV3());
         }
         else if(yeshu == 4)
         {
            xxShow(Main.wts.wantedTaskTimesLV4());
         }
         else if(yeshu == 5)
         {
            xxShow(Main.wts.wantedTaskTimesLV5());
         }
         else if(yeshu == 6)
         {
            xxShow(Main.wts.wantedTaskTimesLV6());
         }
         else if(yeshu == 7)
         {
            xxShow(Main.wts.wantedTaskTimesLV7());
         }
         else if(yeshu == 8)
         {
            xxShow(Main.wts.wantedTaskTimesLV8());
         }
         else if(yeshu == 9)
         {
            xxShow(Main.wts.wantedTaskTimesLV9());
         }
         else if(yeshu == 10)
         {
            xxShow(Main.wts.wantedTaskTimesLV10());
         }
         if(yeshu == 10 && Main.wts.wantedTaskTimesLV10() >= 10 && Main.wts.getJLTimes10() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 9 && Main.wts.wantedTaskTimesLV9() >= 10 && Main.wts.getJLTimes9() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 8 && Main.wts.wantedTaskTimesLV8() >= 10 && Main.wts.getJLTimes8() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 7 && Main.wts.wantedTaskTimesLV7() >= 10 && Main.wts.getJLTimes7() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 6 && Main.wts.wantedTaskTimesLV6() >= 10 && Main.wts.getJLTimes6() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 5 && Main.wts.wantedTaskTimesLV5() >= 10 && Main.wts.getJLTimes5() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 4 && Main.wts.wantedTaskTimesLV4() >= 10 && Main.wts.getJLTimes4() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 3 && Main.wts.wantedTaskTimesLV3() >= 10 && Main.wts.getJLTimes3() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 2 && Main.wts.wantedTaskTimesLV2() >= 10 && Main.wts.getJLTimes2() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(yeshu == 1 && Main.wts.wantedTaskTimesLV1() >= 10 && Main.wts.getJLTimes() < 1)
         {
            xsrwPanel["lingqu_btn"].visible = true;
         }
         if(Main.wts.getAccess() > -1)
         {
            if(yeshu * 3 - Main.wts.getAccess() > 0 && yeshu * 3 - Main.wts.getAccess() < 4)
            {
               xsrwPanel["yjq" + (Main.wts.getAccess() - 3 * (yeshu - 1))].visible = true;
               if(Main.wts.getWantedTaskFromSlot(Main.wts.getAccess()).getState() == 1)
               {
                  xsrwPanel["fq" + (Main.wts.getAccess() - 3 * (yeshu - 1))].visible = true;
               }
               if(Main.wts.getWantedTaskFromSlot(Main.wts.getAccess()).getState() == 2)
               {
                  xsrwPanel["wc" + (Main.wts.getAccess() - 3 * (yeshu - 1))].visible = true;
               }
            }
            for(i = 0; i < 3; i++)
            {
               xsrwPanel["jq" + i].visible = false;
            }
         }
         if(Main.wts.getISCD() == -1)
         {
            xsrwPanel.removeEventListener(Event.ENTER_FRAME,cdTime);
         }
         else
         {
            xsrwPanel.addEventListener(Event.ENTER_FRAME,cdTime);
         }
      }
      
      public static function cdTime(e:*) : *
      {
         var i:int = 0;
         for(i = 0; i < 3; i++)
         {
            if(Main.wts.getWantedTaskFromSlot(i + 3 * (yeshu - 1)).getState() == 3)
            {
               if(Main.wts.getWantedTaskFromSlot(i + 3 * (yeshu - 1)).nowCD() <= 0)
               {
                  panelShow();
               }
               else
               {
                  xsrwPanel["yjq" + i].visible = false;
                  xsrwPanel["cd" + i].visible = true;
                  xsrwPanel["jq" + i].visible = false;
                  xsrwPanel["time" + i].text = "冷却时间" + int(Main.wts.getWantedTaskFromSlot(i + 3 * (yeshu - 1)).nowCD() / 60) + "分" + Main.wts.getWantedTaskFromSlot(i + 3 * (yeshu - 1)).nowCD() % 60 + "秒";
                  xsrwPanel["time" + i].visible = true;
               }
            }
            else
            {
               xsrwPanel["cd" + i].visible = false;
               xsrwPanel["time" + i].visible = false;
            }
         }
         if(Main.wts.getISCD() == -1)
         {
            for(i = 0; i < 3; i++)
            {
               xsrwPanel["time" + i].visible = false;
            }
            xsrwPanel.removeEventListener(Event.ENTER_FRAME,cdTime);
            panelShow();
         }
      }
   }
}

