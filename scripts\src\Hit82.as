package src
{
   import flash.display.*;
   import flash.events.*;
   import src.other.*;
   import src.tool.*;
   
   public class Hit82 extends MovieClip
   {
      
      private static var fff:Fly;
      
      public static var AllHit82:Array = [];
      
      public var objArr:Array = [];
      
      public function Hit82()
      {
         super();
         _stage = Main._this;
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public static function HitFly() : *
      {
         var hitF:Hit82 = null;
         var i2:int = 0;
         for(var i:int = 0; i < AllHit82.length; i++)
         {
            hitF = AllHit82[i];
            if(hitF.parent is BossSX82)
            {
               for(i2 = 0; i2 < Fly.All.length; i2++)
               {
                  if(fff != Fly.All[i2] && Fly.All[i2]._name == "黑幕球" && (hitF.parent as BossSX82).hp > 0 && Fly.All[i2].life != 0 && Fly.All[i2].aaaaa && hitF.hitTestObject(Fly.All[i2].aaaaa))
                  {
                     fff = Fly.All[i2];
                     (Fly.All[i2] as Fly).life = 0;
                     (hitF.parent as BossSX82).hpxx();
                  }
               }
            }
         }
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         var i:int = 0;
         if(AllHit82)
         {
            for(i = 0; i < AllHit82.length; i++)
            {
               if(AllHit82[i] == this)
               {
                  return;
               }
            }
         }
         AllHit82[AllHit82.length] = this;
      }
      
      private function onREMOVED_FROM_STAGE(e:*) : *
      {
         var i:int = 0;
         if(AllHit82)
         {
            for(i = 0; i < AllHit82.length; i++)
            {
               if(AllHit82[i] == this)
               {
                  AllHit82.splice(i,1);
               }
            }
         }
         this.objArr = new Array();
      }
   }
}

