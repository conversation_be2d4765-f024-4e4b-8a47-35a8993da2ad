package src
{
   import com.*;
   import com.ByteArrayXX.*;
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.elves.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.make.*;
   import com.hotpoint.braveManIII.models.monsterCard.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.pet.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.petGem.*;
   import com.hotpoint.braveManIII.models.plan.*;
   import com.hotpoint.braveManIII.models.player.*;
   import com.hotpoint.braveManIII.models.quest.*;
   import com.hotpoint.braveManIII.models.skill.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.models.task.*;
   import com.hotpoint.braveManIII.models.title.*;
   import com.hotpoint.braveManIII.models.wantedTask.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.composePanel.*;
   import com.hotpoint.braveManIII.views.elvesPanel.*;
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.jiangliPanel.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.setProfession.*;
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.stampPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.strPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import com.hotpoint.braveManIII.views.titelPanel.*;
   import com.hotpoint.braveManIII.views.tuijianPanel.*;
   import com.hotpoint.braveManIII.views.wantedPanel.*;
   import com.junkbyte.console.Cc;
   import flash.display.*;
   import flash.events.*;
   import flash.external.*;
   import flash.net.*;
   import flash.system.*;
   import flash.text.TextField;
   import flash.utils.*;
   import src._data.*;
   import src.npc.*;
   import src.other.*;
   import src.tool.*;
   import unit4399.events.*;
   
   public class Main extends MovieClip
   {
      
      public static var wts:WantedTaskSlot;
      
      public static var _stage:Stage;
      
      public static var _this:Main;
      
      public static var _stageWidth:int;
      
      public static var _stageHeight:int;
      
      public static var world:Map;
      
      public static var player_1:Player;
      
      public static var player_2:Player;
      
      public static var P1P2:Boolean;
      
      public static var player1:PlayerData;
      
      public static var player2:PlayerData;
      
      public static var varX:int;
      
      public static var verify:int;
      
      public static var initTime:VT;
      
      public static var saveTimeX:int;
      
      public static var getServerTime:Boolean;
      
      public static var JiNeng_mc:MovieClip;
      
      public static var sdysbtn:MovieClip;
      
      public static var debugMode:Boolean = true;
      
      public static var serviceHold:* = null;
      
      public static var HuiTie8Num:int = 0;
      
      public static var HuiTie8Arr:Array = [0,0,0,0,0,0,0,0,0,0];
      
      public static var LuoPanArr:Array = [0,0,0,0];
      
      public static var shengdan:Array = [VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0),VT.createVT(0)];
      
      public static var shengdanTimes:VT = VT.createVT(0);
      
      public static var lingQueArr:Array = [0,0,0,0,0,0,0,0,0,0,0];
      
      public static var lingQueArr2:Array = [0,0,0,0,0,0,0,0,0,0,0];
      
      public static var fps:int = 30;
      
      public static var gameNum:VT = VT.createVT();
      
      public static var gameNum2:VT = VT.createVT();
      
      public static var varX_old:int = 0;
      
      public static var guanKa:Array = [];
      
      public static var saveNum:int = -1;
      
      public static var stopXX:Boolean = false;
      
      public static var loadSaveOver:Boolean = false;
      
      public static var serverTime:VT = VT.createVT();
      
      public static var serverTimeStr:String = "";
      
      public static var serverDayNum:Number = 0;
      
      public static var userId:uint = 0;
      
      public static var logName:String = "";
      
      public static var logName2:String = "";
      
      public static var logNameSave:String = "";
      
      public static var logYN:Boolean = false;
      
      public static var NoLog:int = 0;
      
      public static var NoLogInfo:Array = new Array();
      
      public static var tiaoShiYN2:Boolean = false;
      
      public static var tiaoShiYN:Boolean = false;
      
      public static var noSave:int = -43994399;
      
      public static var Map0_YN:Boolean = false;
      
      public static var Map0_YN2:Boolean = false;
      
      public static var water:VT = VT.createVT(1);
      
      public static var questArr:Array = [[false,84110],[false,84111],[false,84112],[false,84113],[false,84114]];
      
      public static var newPlay:int = 0;
      
      public static var yinCangP1P2:int = 0;
      
      public static var senDanArr:* = ["彩球","圣诞帽","圣诞靴","驯鹿","糖果","雪花","礼物","铃铛","糖果棒"];
      
      public static var tempDataEnd:Boolean = false;
      
      private static var buy_DuoKai:Boolean = false;
      
      public static var saveMc_YN:Boolean = true;
      
      public static var saveStop:Array = [false,true];
      
      public static var saveID:VT = VT.createVT();
      
      public static var ServerTimeNum:int = 0;
      
      public static var ysbtn:SimpleButton = new YSbtn();
      
      public var _stage_txt:TextField;
      
      private var _4399_function_store_id:String = "3885799f65acec467d97b4923caebaae";
      
      internal var _4399_function_payMoney_id:String = "10f73c09b41d9f41e761232f5f322f38";
      
      internal var _4399_function_shop_id:String = "30ea6b51a23275df624b781c3eb43ac6";
      
      internal var _4399_function_rankList_id:String = "69f52ab6eb1061853a761ee8c26324ae";
      
      internal var _4399_function_union_id:String = "7c7a741b186b91e2975006321918345f";
      
      public var blackXXXX:遮罩 = new 遮罩();
      
      private var timeLog:uint = 30;
      
      private var duoKai_num:int = 0;
      
      public var EmenyDataMD5_YN:Boolean = false;
      
      internal var chongZhiFanLi_YN:Boolean = true;
      
      public function Main()
      {
         super();
         if(stage)
         {
            this.InitXX();
         }
         else
         {
            addEventListener(Event.ADDED_TO_STAGE,this.InitXX);
         }
      }
      
      public static function addShengDan() : void
      {
         var value:int = Math.random() * 9;
         NewMC.Open("圣诞挂件",Main._this,480,290,30,0,true,2,senDanArr[value]);
         if((shengdan[value] as VT).getValue() == 0)
         {
            (shengdan[value] as VT).setValue(1);
         }
      }
      
      public static function LoadTempDataArr() : *
      {
         TempData.Init();
         tempDataEnd = true;
      }
      
      private static function NologFun() : Boolean
      {
         var jsid:String = ExternalInterface.call("UniLogin.getUid");
         TiaoShi.txtShow("退出帐号检测:" + jsid + "," + userId);
         if(tiaoShiYN == true)
         {
            return true;
         }
         if(jsid != "" && Number(jsid) == userId)
         {
            return true;
         }
         DuoKai_Info.Open(2,jsid);
         return false;
      }
      
      public static function ChongZhi() : Boolean
      {
         var _info:String = null;
         var info:String = null;
         var rNum:int = int(Math.random() * 79461312 + 10000000);
         var objX:Object = {
            "pay_union":10014,
            "username":Main.logName,
            "pay_money":100,
            "random_num":rNum,
            "role":Main.logName,
            "is_wpay":2
         };
         var strXX:String = JSONs.encode(objX);
         trace("微端?",strXX);
         if(ExternalInterface.available)
         {
            _info = ExternalInterface.call("eval","navigator.userAgent");
            info = _info;
            if(info.indexOf("4399.air.wd") >= 0 || info.indexOf("4399.yzzr.air") >= 0)
            {
               ExternalInterface.call("openPay",strXX);
               return true;
            }
         }
         if(Main.serviceHold)
         {
            PayMoneyVar.instance.money = 100;
            Main.serviceHold.payMoney_As3(PayMoneyVar.instance);
         }
         return false;
      }
      
      public static function DuoKai_Fun(bool:Boolean = false) : *
      {
         if(serviceHold)
         {
            buy_DuoKai = bool;
            serviceHold.getStoreState();
         }
      }
      
      public static function Save(bool:Boolean = true, id:int = 0) : *
      {
         if(Main.tiaoShiYN2)
         {
            SaveFun();
            return;
         }
         if(!Main.tiaoShiYN && NologFun() == false)
         {
            return;
         }
         if(id == 1)
         {
            JiHua_Interface.ppp4_9 = true;
         }
         if(!JiHua_Interface._this)
         {
            JiHua_Interface.initArr();
         }
         JiHua_Interface.planGO();
         saveStop[0] = true;
         saveStop[1] = bool;
         saveID.setValue(id);
         DuoKai_Fun();
      }
      
      public static function SaveFun() : *
      {
         if(saveID.getValue() > 0)
         {
            if(saveID.getValue() == 1)
            {
               saveID.setValue(2);
            }
         }
         EquipShopPanel.equipShopNoRMBOK();
         MenuTooltip.getChongWu();
         MenuTooltip.getChongWu2();
         MenuTooltip.getDuanWu();
         MenuTooltip.getXingLing();
         MenuTooltip.getQiXi();
         MenuTooltip.getShiZhuang();
         MenuTooltip.getChiBang();
         Play_Interface.getBuChang();
         NewWantedPanel.getJiangLi();
         NewPetPanel.xianshiJN();
         xilianPanel.xlOK();
         ChunJiePanel.lingQuOK();
         XingLing_Interface.saveOK = true;
         StrPanel.QiangHuaOK();
         TiaoZhanPaiHang_Interface.GeiObjMC_Play();
         TiaoZhanPaiHang_Interface.GeiObjMC_Play2();
         NewPetPanel.saveShowRL();
         ZhuanPan.saveYN = true;
         upstarPanel.UpOK_save();
         JinHuaPanel2.saveOK = true;
         Panel_youling.SaveOk();
      }
      
      public static function Save2(bool:Boolean = true) : *
      {
         saveMc_YN = bool;
         if(noSave > InitData.BuyNum_0.getValue() || Boolean(DuoKai_Info._noSave))
         {
            return;
         }
         var titleX:String = "";
         titleX += "P1:Lv" + player1.getLevel();
         if(Main.P1P2)
         {
            titleX += " / P2:Lv" + player2.getLevel();
         }
         var objX:Object = new Object();
         objX["var"] = Main.varX;
         if(Main.logNameSave == "" && Main.varX_old < 182)
         {
            objX["logNameSave"] = MD5contrast.SaveStr(logName.toLocaleLowerCase(),saveNum);
         }
         else
         {
            objX["logNameSave"] = Main.logNameSave;
         }
         objX["NoLog"] = Main.NoLog;
         objX["NoLogInfo"] = Main.NoLogInfo;
         objX["saveTimeX"] = saveTimeX;
         var x:int = 1;
         if(Main.P1P2)
         {
            x = 2;
         }
         else
         {
            x = 1;
         }
         var i:int = 1;
         while(i <= x)
         {
            objX["p" + i] = new Object();
            objX["p" + i] = Main["player" + i];
            i++;
         }
         objX["P1P2"] = Main.P1P2;
         objX["changKu"] = StoragePanel.storage;
         objX["guanKa"] = Main.guanKa;
         objX["questArr"] = Main.questArr;
         objX["verify"] = Math.random() * 9999;
         objX["InitDataX"] = InitData.SaveInitData();
         objX["RenWu"] = TaskData.saveTask();
         objX["buyTime"] = Data_KillShop.buyTime;
         objX["buyArr"] = Data_KillShop.buyArr;
         objX["KaiQiShopArr2"] = ShopKillPoint.KaiQiShopArr2;
         objX["MakeData"] = MakeData.saveMake();
         objX["lingQueArr"] = lingQueArr;
         objX["lingQueArr2"] = lingQueArr2;
         objX["TeShuHuoDongArr"] = TeShuHuoDong.TeShuHuoDongArr;
         objX["chengjiu"] = AchData.save();
         objX["xuansang"] = Main.wts;
         objX["kaPian"] = CardPanel.monsterSlot;
         objX["petB"] = NewPetPanel.bag;
         objX["xgKEY"] = NewPetPanel.XGkey;
         objX["lvKEY"] = NewPetPanel.LVkey;
         objX["lqTIMES"] = JiangLiPanel.LQtimes;
         objX["XueMai"] = NewPetPanel.XueMai;
         objX["caiYao0"] = CaiYaoPanel.saveArr;
         objX["caiYao1"] = CaiYaoPanel.saveArr2;
         objX["yaoArr"] = YaoYuan.yaoArr;
         objX["huaZhi"] = Play_Interface.huaZhi;
         objX["Map0_YN"] = Main.Map0_YN;
         objX["Map0_YN2"] = Main.Map0_YN2;
         objX["water"] = Main.water;
         objX["LuoPanSuiPian"] = Main.LuoPanArr;
         objX["JiHua"] = PlanFactory.JiHuaData;
         objX["JiHua2"] = PlanFactory.JiHuaData2;
         objX["XinglingUp"] = XingLingFactory.xingLingData;
         objX["vipUserArr"] = Vip_Interface.vipUserArr;
         objX["TaskDataBoXX5"] = TaskData.BoXX;
         objX["banben_save"] = SixOne_Interface.banben_save;
         objX["dayTimes2021"] = SixOne_Interface.dayTimes2021;
         objX["nowdate2021"] = SixOne_Interface.nowdate2021;
         objX["okTimes2021"] = SixOne_Interface.okTimes2021;
         objX["state2021"] = SixOne_Interface.state2021;
         objX["buChangHeCeng2"] = Play_Interface.buChangHeCeng2;
         objX["huaArr"] = Panel_XianHua.huaArr;
         objX["chunjie_saveNew"] = ChunJiePanel.saveNew;
         objX["chunjie1_2022"] = ChunJiePanel.saveArr_2022;
         objX["chunjie2_2022"] = ChunJiePanel.saveArr2_2022;
         objX["varSave"] = NewYear_Interface.varSave;
         objX["newyearKey2022"] = NewYear_Interface.key2022;
         objX["newyearJuan2022"] = NewYear_Interface.juan2022;
         objX["newyearJL2022"] = NewYear_Interface.allJiangLi2022;
         objX["newyearYN2022"] = NewYear_Interface.openYesNo2022;
         objX["arrAll"] = FiveOne_Interface.arrAll;
         objX["ZhuanPan"] = ZhuanPan.saveTime;
         objX["ZhuanPanYN"] = ZhuanPan.Num1YN2;
         objX["qdArr"] = QianDao.qdArr;
         objX["tuijian"] = TuiJianPanel.arrSave;
         objX["initTime"] = Main.initTime;
         objX["lingQuTime"] = ChongZhi_Interface.lingQuTime;
         objX["gameTime"] = JiFenLingQue3.gameTime;
         objX["TiaoZan_newTime"] = PaiHang_Data.newTime;
         objX["inGameNum"] = PaiHang_Data.inGameNum;
         objX["sArr2"] = PaiHang_Data.sArr2;
         objX["jiFenArr"] = PaiHang_Data.jiFenArr;
         objX["PK_jiFenArr"] = PK_UI.jiFenArr;
         objX["saveTime"] = JiFenLingQue3.saveTime;
         objX["saveTime2"] = GengXin.saveTime;
         objX["saveVar"] = GengXin.saveVar;
         objX["yinCangP1P2"] = Main.yinCangP1P2;
         objX["HuiTie8Arr"] = HuiTie8Arr;
         objX["lhs_Data"] = LingHunShi_Interface.lhs_Data;
         objX["noMovPlayArr"] = LingHunShi_Interface.noMovPlayArr;
         objX["rwArr"] = GongHuiRenWu.rwArr;
         objX["rwArr2"] = GongHuiRenWu.rwArr2;
         objX["tzData"] = GongHuiTiaoZan.tzData;
         objX["yueKaTime"] = YueKa_Interface.yueKaTime;
         objX["yueKaTime2"] = YueKa_Interface.yueKaTime2;
         objX["lingQu_new"] = ChongZhi_Interface4.lingQu_new;
         objX["youHun1"] = Panel_youling.lvArr;
         objX["youHun2"] = Panel_youling.bzNumArr;
         objX["youHun3"] = NPC_YouLing.gameNumX;
         objX["cw2save"] = ChongWu2.cw2save;
         var byte:ByteArray = new ByteArray();
         byte.writeObject(objX);
         byte.position = 0;
         var tmpStr:String = Base64.encodeByteArray(byte);
         Cc.info(tmpStr);
         if(Main.saveNum == -1)
         {
            Main.serviceHold.saveData(titleX,tmpStr);
         }
         else
         {
            Main.serviceHold.saveData(titleX,tmpStr,false,Main.saveNum);
         }
      }
      
      public static function GetServerTime(yn:Boolean = false) : *
      {
         if(yn)
         {
            serviceHold.getServerTime();
            return;
         }
         if(serviceHold && ServerTimeNum <= 0)
         {
            serviceHold.getServerTime();
            ServerTimeNum = 1620;
         }
         else
         {
            --ServerTimeNum;
         }
      }
      
      public static function createServerDateA(value:String) : Number
      {
         var darr:Array = value.split(" ");
         var darry:Array = (darr[0] as String).split("-");
         var darryHours:Array = (darr[1] as String).split(":");
         var temp:String = "" + darry[1] + "/" + darry[2] + "/" + darry[0] + " " + darr[1];
         var serverDate:Date = new Date(temp + " GMT+0800");
         var serverDateTime:* = serverDate.time;
         return Math.floor(serverDateTime / 86400000);
      }
      
      public static function allClosePanel() : *
      {
         SuppliesShopPanel.close();
         SkillPanel.close();
         SetTransferPanel.close();
         SetKeyPanel.close();
         ItemsPanel.close();
         EquipShopPanel.close();
         StoragePanel.close();
         村长任务界面.Close();
         装备商人界面.Close();
         装备商人界面2.Close();
         药水商人界面.Close();
         药水商人界面2.Close();
         技能导师界面.Close();
         技能导师界面2.Close();
         探险家界面.Close();
         探险家界面2.Close();
         伽妮雅界面.Close();
         塔丽莎界面.Close();
         星冰界面.Close();
         船长界面.Close();
         InWaterDoor.Close();
         InWaterKey.Close();
         Shop4399.Close();
         ShopKillPoint.Close();
         xilianPanel.close();
         NewPetPanel.close();
         StrPanel.close();
         ComPosePanel.close();
         MakePanel.close();
         Shop_LiBao.Close();
         Shop_LiBao2.Close();
         AchPanel.close();
         StampPanel.close();
         ItemsPanel.szcbTime();
         TaskPanel.close();
         TitelPanel.CengHaoShow();
         CardPanel.close();
         FanPaiPanel.close();
         ChunJiePanel.close();
         ElvesPanel.close();
         JiangLiPanel.close();
         PK_UI.Close();
         PK_UI_Sel.Close();
         NewWantedPanel.close();
         AchData.upPoint();
         YueKa_Interface.Close();
         Panel_youling.CloseX();
         if(Shop_LiBao.shopX)
         {
            Shop_LiBao.JianCe();
            Shop_LiBao2.JianCe();
         }
         XingLing_Interface.CloseX();
      }
      
      public static function isVip() : Boolean
      {
         var j:int = 0;
         while(j < Main.player1.getTitleSlot().getListLength())
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(j).getId() == 25)
            {
               return true;
            }
            j++;
         }
         return false;
      }
      
      public static function getQuest(fallLevel:Number) : Boolean
      {
         var i:Number = 0;
         while(i < questArr.length)
         {
            if(questArr[i][1] == fallLevel)
            {
               return questArr[i][0];
            }
            i++;
         }
         return false;
      }
      
      public static function setQuest(fallLevel:Number) : void
      {
         var i:Number = 0;
         while(i < questArr.length)
         {
            if(questArr[i][1] == fallLevel)
            {
               questArr[i][0] = true;
               break;
            }
            i++;
         }
      }
      
      public static function getAllQuest() : Boolean
      {
         var i:Number = 0;
         while(i < questArr.length)
         {
            if(questArr[i][0])
            {
               return true;
            }
            i++;
         }
         return false;
      }
      
      public static function questBag() : Boolean
      {
         var i:Number = 0;
         while(i < questArr.length)
         {
            if(Boolean(Main.player1) && Main.player1.getBag().fallQusetBag(questArr[i][1]) > 0)
            {
               return true;
            }
            if(Main.P1P2 && Main.player2 && Main.player2.getBag().fallQusetBag(questArr[i][1]) > 0)
            {
               return true;
            }
            i++;
         }
         return false;
      }
      
      public static function JiaoYan() : Boolean
      {
         Cc.info("校验触发1");
         if(tiaoShiYN == true)
         {
            return true;
         }
         if(Main.varX < Main.varX_old)
         {
            Cc.info("校验触发1--游戏盒");
            Strat.stratX.游戏盒信息();
            return false;
         }
         return true;
      }
      
      public static function JiaoYan2() : Boolean
      {
         Cc.info("校验触发2");
         if(tiaoShiYN == true)
         {
            return true;
         }
         if(NoLog < 2 && (Main.logNameSave == "" && Main.varX_old < 182 || Boolean(MD5contrast.contrast(Main.logName,Main.logNameSave,Main.saveNum))))
         {
            Cc.info("校验触发2-md5验证通过");
            return true;
         }
         if(XiuZheng())
         {
            return true;
         }
         Cc.info("校验触发2-验证失败");
         Strat.stratX.复制存档信息();
         return false;
      }
      
      private static function XiuZheng() : Boolean
      {
         Cc.info("存档修正函数执行");
         if(Main.NoLogInfo[13])
         {
            Main.NoLog = 0;
            Main.NoLogInfo[13] = null;
            return true;
         }
         if(Boolean(Main.NoLogInfo[15]) && serverTime.getValue() < 20131118)
         {
            return true;
         }
         if(Main.NoLogInfo[5] == 888 || Main.NoLogInfo[5] == 111 || Main.NoLogInfo[5] == 122 || Main.NoLogInfo[5] == 333 || Main.NoLogInfo[5] == 444)
         {
            Main.NoLog = 0;
            Main.NoLogInfo[5] = null;
            return true;
         }
         if(Main.NoLogInfo[4] < 1000)
         {
            Main.NoLogInfo[4] = null;
            return true;
         }
         Cc.info("存档修正失败-已经修改为成功");
         return true;
      }
      
      public static function NoGame(str:String = "未知") : *
      {
         if(!Main.tiaoShiYN)
         {
            DuoKai_Info._noSave = true;
            while(true)
            {
               _this.visible = false;
            }
            return;
         }
         NewMC.Open("文字提示",Main._stage,480,250,540,0,false,0,"游戏异常:" + str);
      }
      
      public static function addBtn() : *
      {
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            Main.world.moveChild_Back.addChild(ysbtn);
            ysbtn.x = 2430;
            ysbtn.y = 495;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 3)
         {
            Main.world.moveChild_Back.addChild(ysbtn);
            ysbtn.x = 2430;
            ysbtn.y = 495;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 4)
         {
            Main.world.moveChild_Back.addChild(ysbtn);
            ysbtn.x = 1265;
            ysbtn.y = 467;
         }
         ysbtn.visible = true;
         ysbtn.addEventListener(MouseEvent.CLICK,huodong61);
      }
      
      public static function addBtn_20161225() : *
      {
         Main.sdysbtn = new SDYSbtn();
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            Main.world.moveChild_Back.addChild(sdysbtn);
            sdysbtn.x = 2200;
            sdysbtn.y = 510;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 3)
         {
            Main.world.moveChild_Back.addChild(sdysbtn);
            sdysbtn.x = 2140;
            sdysbtn.y = 510;
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 4)
         {
            Main.world.moveChild_Back.addChild(sdysbtn);
            sdysbtn.x = 1720;
            sdysbtn.y = 470;
         }
         RefreshBtn1225();
         sdysbtn.buttonMode = true;
         sdysbtn.visible = true;
         sdysbtn.addEventListener(MouseEvent.CLICK,huodong1225);
         sdysbtn.mouseChildren = true;
         sdysbtn.mouseEnabled = false;
      }
      
      public static function RefreshBtn1225() : *
      {
         if(ShengDan_Interface.moment_2021.getValue() == 1)
         {
            sdysbtn.gotoAndStop(1);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 2)
         {
            sdysbtn.gotoAndStop(2);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 3)
         {
            sdysbtn.gotoAndStop(3);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 4)
         {
            sdysbtn.gotoAndStop(4);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 5)
         {
            sdysbtn.gotoAndStop(5);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 6)
         {
            sdysbtn.gotoAndStop(6);
         }
         else if(ShengDan_Interface.moment_2021.getValue() == 7)
         {
            sdysbtn.gotoAndStop(7);
         }
         else
         {
            sdysbtn.gotoAndStop(8);
         }
      }
      
      public static function huodong61(e:*) : *
      {
         SixOne_Interface.Open();
      }
      
      public static function huodong1225(e:*) : *
      {
         ShengDan_Interface.Open();
      }
      
      public static function getGameInfo() : Object
      {
         if(!debugMode)
         {
            return {"basic":true};
         }
         try
         {
            return {
               "version":varX,
               "map":gameNum.getValue(),
               "fps":fps,
               "player1":(player1 ? {
                  "level":player1.getLevel(),
                  "hp":player1.getHp() + "/" + player1.getMaxHp(),
                  "x":(player_1 ? player_1.x : 0),
                  "y":(player_1 ? player_1.y : 0)
               } : null),
               "player2":(P1P2 && player2 ? {
                  "level":player2.getLevel(),
                  "hp":player2.getHp() + "/" + player2.getMaxHp(),
                  "x":(player_2 ? player_2.x : 0),
                  "y":(player_2 ? player_2.y : 0)
               } : null)
            };
         }
         catch(e:Error)
         {
            return {"error":e.message};
         }
      }
      
      public static function logToConsole(message:String) : void
      {
         if(_stage && _stage.getChildByName("Console") != null)
         {
            Cc.instance.log("[游戏日志] " + message);
         }
      }
      
      public function setHold(hold:*) : void
      {
         serviceHold = hold;
      }
      
      public function InitXX(e:* = null) : *
      {
         var date:Date = new Date();
         var dateXX:int = date.getFullYear() * 10000 + (date.getMonth() + 1) * 100 + date.getDate();
         serverTime.setValue(dateXX);
         System.useCodePage = false;
         Main.GetServerTime();
         this.onADDED_TO_STAGE();
         noSave = -(Math.random() * 999999 + 999);
         removeEventListener(Event.ADDED_TO_STAGE,this.InitXX);
         InitData.Init();
         Data2.Init();
         Data_KillShop.Init();
         Data_qinMiDu.Init();
         Data_mosen.Init();
         ChongWu2.Init();
         PetFactory.AddLVDataInit();
         MusicBox.MusicPlay("封面声音");
         this.regAllClass();
         Request.RequestX();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_Game);
         if(!stage.getChildByName("Console"))
         {
            var console:Object = {};
         }
         console.name = "Console";
         stage.addChild(console);
      }
      
      public function onENTER_FRAME_Game(e:*) : *
      {
         ChunJiePanel.addZaiXianJiFen();
      }
      
      public function regAllClass() : void
      {
         Cc.info("序列化注册开始");
         registerClassAlias("PlayerData",PlayerData);
         registerClassAlias("Attribute",Attribute);
         registerClassAlias("Bag",Bag);
         registerClassAlias("Equip",Equip);
         registerClassAlias("EquipBaseAttrib",EquipBaseAttrib);
         registerClassAlias("EquipBaseAttribTypeConst",EquipBaseAttribTypeConst);
         registerClassAlias("EquipReinforce",EquipReinforce);
         registerClassAlias("EquipSkillSlot",EquipSkillSlot);
         registerClassAlias("EquipSlot",EquipSlot);
         registerClassAlias("Gem",Gem);
         registerClassAlias("GemTypeConst",GemTypeConst);
         registerClassAlias("Otherobj",Otherobj);
         registerClassAlias("PlayerData",PlayerData);
         registerClassAlias("promptSlot",promptSlot);
         registerClassAlias("Quest",Quest);
         registerClassAlias("Skill",Skill);
         registerClassAlias("SkillSlot",SkillSlot);
         registerClassAlias("Storage",Storage);
         registerClassAlias("StrengthenDeleteSlot",StrengthenDeleteSlot);
         registerClassAlias("StrengthenMosaicSlot",StrengthenMosaicSlot);
         registerClassAlias("StrengthenSlot",StrengthenSlot);
         registerClassAlias("StrengthenSynthesisSlot",StrengthenSynthesisSlot);
         registerClassAlias("SuitEquipAttrib",SuitEquipAttrib);
         registerClassAlias("Supplies",Supplies);
         registerClassAlias("SuppliesAffect",SuppliesAffect);
         registerClassAlias("SuppliesSlot",SuppliesSlot);
         registerClassAlias("Task",Task);
         registerClassAlias("Make",Make);
         registerClassAlias("VT",VT);
         registerClassAlias("Pet",Pet);
         registerClassAlias("PetSlot",PetSlot);
         registerClassAlias("BadgeSlot",BadgeSlot);
         registerClassAlias("TitleSlot",TitleSlot);
         registerClassAlias("Title",Title);
         registerClassAlias("WantedTaskSlot",WantedTaskSlot);
         registerClassAlias("WantedTask",WantedTask);
         registerClassAlias("StampSlot",StampSlot);
         registerClassAlias("Elves",Elves);
         registerClassAlias("ElvesSlot",ElvesSlot);
         registerClassAlias("MonsterSlot",MonsterSlot);
         registerClassAlias("MonsterCard",MonsterCard);
         registerClassAlias("Plan",Plan);
         registerClassAlias("PetEquip",PetEquip);
         registerClassAlias("PetBag",PetBag);
         registerClassAlias("PetGem",PetGem);
      }
      
      private function onADDED_TO_STAGE() : *
      {
         stage.addEventListener(SaveEvent.SAVE_GET,this.saveProcess);
         stage.addEventListener(SaveEvent.SAVE_SET,this.saveProcess);
         stage.addEventListener(SaveEvent.SAVE_LIST,this.saveProcess);
         stage.addEventListener("netSaveError",this.netSaveErrorHandler,false,0,true);
         stage.addEventListener("MVC_CLOSE_PANEL",this.closePanelHandler);
         stage.addEventListener("userLoginOut",this.onUserLogOutHandler,false,0,true);
         stage.addEventListener("logreturn",this.saveProcess);
         stage.addEventListener("serverTimeEvent",this.onGetServerTimeHandler);
         Main.fps = stage.frameRate;
         trace("游戏帧数设定:",Main.fps);
         stage.addEventListener("multipleError",this.multipleErrorHandler,false,0,true);
         stage.addEventListener("StoreStateEvent",this.getStoreStateHandler,false,0,true);
         stage.align = StageAlign.TOP_LEFT;
         stage.scaleMode = StageScaleMode.NO_SCALE;
         Main._stage = stage;
         Main._this = this;
         Main._stageWidth = stage.width;
         Main._stageHeight = stage.height;
         Strat.Open();
         BasicKey.Start(Main._stage);
         LoadTempDataArr();
      }
      
      private function closePanelHandler(e:Event) : void
      {
         var logInfo:Object = serviceHold.isLog;
         if(logInfo == null)
         {
            addEventListener(Event.ENTER_FRAME,this.OnTimeShowLogUi);
         }
      }
      
      private function OnTimeShowLogUi(e:*) : *
      {
         --this.timeLog;
         if(this.timeLog <= 0)
         {
            this.showLogUi();
            this.timeLog = 60;
            removeEventListener(Event.ENTER_FRAME,this.OnTimeShowLogUi);
         }
      }
      
      public function showLogUi() : void
      {
         var logInfo:Object = null;
         if(serviceHold)
         {
            logInfo = serviceHold.isLog;
            if(logInfo == null)
            {
               serviceHold.showLogPanel();
            }
         }
      }
      
      public function onUserLogOutHandler(evt:Event = null) : void
      {
         this.blackXXXX.Black_mc.x = this.blackXXXX.Black_mc.y = 0;
         navigateToURL(new URLRequest("javascript:location.reload(); "),"_self");
         Main.player1 = Main.player2 = null;
      }
      
      private function netSaveErrorHandler(evt:Event) : void
      {
         trace("网络存档失败了!");
         NewMC.Open("文字提示",this,400,400,50,0,true,2,"存档失败");
      }
      
      private function saveProcess(e:SaveEvent) : void
      {
         Cc.info("解析存档程序启动~");
         var tmpStr:String = null;
         var newByteArray:ByteArray = null;
         var objX:Object = null;
         var numServerTime:int = 0;
         var tempStrX:String = null;
         var tempSaveTime:String = null;
         var tempSaveTimeNum:int = 0;
         var data:Array = null;
         var i:int = 0;
         var obj:Object = null;
         switch(e.type)
         {
            case SaveEvent.SAVE_GET:
               if(e.ret == null || e.ret.data == null)
               {
                  return;
               }
               Cc.info("解析存档程序-获取data信息完成");
               tmpStr = String(e.ret.data);
               newByteArray = Base64.decodeToByteArray(tmpStr) as ByteArray;
               Cc.info("解析存档程序-获取到解析的byteArray");
               newByteArray.position = 0;
               Main.saveNum = e.ret.index;
               Cc.info("解析存档程序-获取保存索引—开始反序列化");
               objX = newByteArray.readObject();
               Cc.info("解析存档程序-反序列化完成");
               Doc.myObjX = objX;
               Cc.info("解析存档程序-获取存档完成");
               numServerTime = serverTime.getValue();
               Cc.info("解析存档程序-获取服务器时间");
               tempStrX = e.ret.datetime;
               Cc.info("解析存档程序-获取存档时间");
               tempSaveTime = tempStrX.substr(0,4) + tempStrX.substr(5,2) + tempStrX.substr(8,2);
               Cc.info("解析存档程序-获取保存时间" + tempSaveTime);
               tempSaveTimeNum = int(tempSaveTime);
               TiaoShi.tempVarTime = tempSaveTimeNum;
               if(tempSaveTimeNum > 20151020 && objX["var"] < 1020)
               {
                  Cc.info("解析存档程序-进入旧版本调试");
                  NoGame("使用旧版本进行修改!");
                  if(!Main.tiaoShiYN)
                  {
                     return;
                  }
               }
               Cc.info("解析存档程序-开始拷贝存档");
               Main.varX_old = objX["var"];
               if(objX["logNameSave"])
               {
                  logNameSave = objX["logNameSave"];
               }
               Cc.info("解析存档程序-进程1");
               NoLog = 0;
               if(objX["NoLog"])
               {
                  NoLog = objX["NoLog"];
               }
               Cc.info("解析存档程序-进程2");
               if(Boolean(objX["NoLogInfo"]) && !(objX["NoLogInfo"] is Array))
               {
                  objX["NoLogInfo"] = null;
               }
               Cc.info("解析存档程序-进程3");
               if(objX["NoLogInfo"])
               {
                  Main.NoLogInfo = objX["NoLogInfo"];
               }
               Cc.info("解析存档程序-进程4");
               XiuZheng();
               if(JiaoYan() != true || JiaoYan2() != true)
               {
                  return;
               }
               Cc.info("解析存档程序-进程5");
               Main.P1P2 = objX["P1P2"];
               if(objX["changKu"])
               {
                  StoragePanel.storage = objX["changKu"];
               }
               Cc.info("解析存档程序-进程6");
               if(objX["chengjiu"])
               {
                  if(Main.varX_old > 275)
                  {
                     AchData.xxx = objX["chengjiu"];
                  }
               }
               Cc.info("解析存档程序-进程7");
               i = 0;
               while(i <= 150)
               {
                  if(!objX["guanKa"][i])
                  {
                     Main.guanKa[i] = 0;
                  }
                  else
                  {
                     Main.guanKa[i] = objX["guanKa"][i];
                  }
                  if(Main.guanKa[i] is Boolean)
                  {
                     if(Main.guanKa[i] == true)
                     {
                        Main.guanKa[i] = 1;
                     }
                     else
                     {
                        Main.guanKa[i] = 0;
                     }
                  }
                  i++;
               }
               Cc.info("解析存档程序-进程8");
               if(Main.guanKa[1] != 0 && Main.guanKa[1] != 1 && Main.guanKa[1] != 2 && Main.guanKa[1] != 3)
               {
                  Main.guanKa[1] = 1;
               }
               Cc.info("解析存档程序-进程9");
               Main.questArr = objX["questArr"];
               Main.verify = objX["verify"];
               if(objX["InitDataX"])
               {
                  InitData.LoadInitData(objX["InitDataX"]);
               }
               Cc.info("解析存档程序-进程10");
               if(objX["buyTime"])
               {
                  Data_KillShop.buyTime = objX["buyTime"];
                  Data_KillShop.buyArr = objX["buyArr"];
                  ShopKillPoint.KaiQiShopArr2 = objX["KaiQiShopArr2"];
               }
               Cc.info("解析存档程序-进程11");
               if(objX["RenWu"])
               {
                  TaskData.saveArr = objX["RenWu"];
               }
               Cc.info("解析存档程序-进程12");
               if(objX["MakeData"])
               {
                  MakeData.duMake(objX["MakeData"]);
               }
               Cc.info("解析存档程序-进程13");
               if(Main.P1P2)
               {
                  x = 2;
               }
               else
               {
                  x = 1;
               }
               Cc.info("解析存档程序-进程14");
               i = 1;
               while(i <= x)
               {
                  Main["player" + i] = objX["p" + i] as PlayerData;
                  i++;
               }
               Cc.info("解析存档程序-进程15");
               if(Boolean(objX["lingQueArr"]) && objX["lingQueArr"].length == 11)
               {
                  lingQueArr = objX["lingQueArr"];
               }
               Cc.info("解析存档程序-进程16");
               if(Boolean(objX["lingQueArr2"]) && objX["lingQueArr2"].length == 11)
               {
                  lingQueArr2 = objX["lingQueArr2"];
               }
               Cc.info("解析存档程序-进程17");
               if(objX["TeShuHuoDongArr"])
               {
                  TeShuHuoDong.TeShuHuoDongArr = objX["TeShuHuoDongArr"];
               }
               Cc.info("解析存档程序-进程18");
               TeShuHuoDong.Init();
               if(objX["xuansang"])
               {
                  Main.wts = objX["xuansang"];
                  if(Main.varX_old == 1100)
                  {
                     WantedTaskSlot.JLTimes10XX();
                  }
               }
               Cc.info("解析存档程序-进程19");
               if(objX["kaPian"])
               {
                  CardPanel.monsterSlot = objX["kaPian"];
               }
               Cc.info("解析存档程序-进程20");
               if(objX["petB"])
               {
                  NewPetPanel.bag = objX["petB"];
               }
               Cc.info("解析存档程序-进程21");
               if(objX["xgKEY"])
               {
                  NewPetPanel.XGkey = objX["xgKEY"];
               }
               Cc.info("解析存档程序-进程22");
               if(objX["lvKEY"])
               {
                  NewPetPanel.LVkey = objX["lvKEY"];
               }
               Cc.info("解析存档程序-进程23");
               if(objX["lqTIMES"])
               {
                  JiangLiPanel.LQtimes = objX["lqTIMES"];
               }
               Cc.info("解析存档程序-进程24");
               if(objX["XueMai"])
               {
                  NewPetPanel.XueMai = objX["XueMai"];
               }
               Cc.info("解析存档程序-进程25");
               if(objX["lingQuTime"])
               {
                  ChongZhi_Interface.lingQuTime = objX["lingQuTime"];
               }
               Cc.info("解析存档程序-进程26");
               if(objX["caiYao0"])
               {
                  CaiYaoPanel.saveArr = objX["caiYao0"];
               }
               Cc.info("解析存档程序-进程27");
               if(objX["caiYao1"])
               {
                  CaiYaoPanel.saveArr2 = objX["caiYao1"];
               }
               Cc.info("解析存档程序-进程28");
               if(objX["yaoArr"])
               {
                  YaoYuan.yaoArr = objX["yaoArr"];
               }
               Cc.info("解析存档程序-进程29");
               if(objX["huaZhi"])
               {
                  Play_Interface.huaZhi = objX["huaZhi"];
               }
               Cc.info("解析存档程序-进程30");
               if(objX["Map0_YN"])
               {
                  Main.Map0_YN = objX["Map0_YN"];
               }
               Cc.info("解析存档程序-进程31");
               if(objX["Map0_YN2"])
               {
                  Main.Map0_YN2 = objX["Map0_YN2"];
               }
               Cc.info("解析存档程序-进程32");
               if(objX["water"])
               {
                  Main.water = objX["water"];
               }
               Cc.info("解析存档程序-进程33");
               if(objX["LuoPanSuiPian"])
               {
                  Main.LuoPanArr = objX["LuoPanSuiPian"];
               }
               Cc.info("解析存档程序-进程34");
               if(objX["XinglingUp"])
               {
                  XingLingFactory.xingLingData = objX["XinglingUp"];
                  XingLingFactory.Init_xingLingData();
               }
               Cc.info("解析存档程序-进程35");
               if(objX["JiHua"])
               {
                  PlanFactory.JiHuaData = objX["JiHua"];
               }
               Cc.info("解析存档程序-进程36");
               if(objX["JiHua2"])
               {
                  PlanFactory.JiHuaData2 = objX["JiHua2"];
               }
               Cc.info("解析存档程序-进程37");
               if(objX["vipUserArr"])
               {
                  Vip_Interface.vipUserArr = objX["vipUserArr"];
               }
               Cc.info("解析存档程序-进程38");
               if(objX["TaskDataBoXX5"])
               {
                  TaskData.BoXX = objX["TaskDataBoXX5"];
               }
               Cc.info("解析存档程序-进程39");
               if(objX["TaskDataBoXX6"])
               {
                  TaskData.BoXX2 = objX["TaskDataBoXX6"];
               }
               Cc.info("解析存档程序-进程40");
               if(objX["banben_save"])
               {
                  SixOne_Interface.banben_save = objX["banben_save"];
               }
               Cc.info("解析存档程序-进程41");
               if(objX["dayTimes2021"])
               {
                  SixOne_Interface.dayTimes2021 = objX["dayTimes2021"];
               }
               Cc.info("解析存档程序-进程42");
               if(objX["nowdate2021"])
               {
                  SixOne_Interface.nowdate2021 = objX["nowdate2021"];
               }
               Cc.info("解析存档程序-进程43");
               if(objX["okTimes2021"])
               {
                  SixOne_Interface.okTimes2021 = objX["okTimes2021"];
               }
               Cc.info("解析存档程序-进程44");
               if(objX["state2021"])
               {
                  SixOne_Interface.state2021 = objX["state2021"];
               }
               Cc.info("解析存档程序-进程45");
               if(objX["buChangHeCeng2"])
               {
                  Play_Interface.buChangHeCeng2 = objX["buChangHeCeng2"];
               }
               Panel_XianHua.huaArr = objX["huaArr"];
               Cc.info("解析存档程序-进程46");
               if(objX["chunjie_saveNew"])
               {
                  ChunJiePanel.saveNew = objX["chunjie_saveNew"];
               }
               Cc.info("解析存档程序-进程47");
               if(objX["chunjie1_2022"])
               {
                  ChunJiePanel.saveArr_2022 = objX["chunjie1_2022"];
               }
               Cc.info("解析存档程序-进程48");
               if(objX["chunjie2_2022"])
               {
                  ChunJiePanel.saveArr2_2022 = objX["chunjie2_2022"];
               }
               Cc.info("解析存档程序-进程49");
               if(objX["varSave"])
               {
                  NewYear_Interface.varSave = objX["varSave"];
               }
               Cc.info("解析存档程序-进程50");
               if(objX["newyearKey2022"])
               {
                  NewYear_Interface.key2022 = objX["newyearKey2022"];
               }
               Cc.info("解析存档程序-进程51");
               if(objX["newyearJuan2022"])
               {
                  NewYear_Interface.juan2022 = objX["newyearJuan2022"];
               }
               Cc.info("解析存档程序-进程52");
               if(objX["newyearJL2022"])
               {
                  NewYear_Interface.allJiangLi2022 = objX["newyearJL2022"];
               }
               Cc.info("解析存档程序-进程53");
               if(objX["newyearYN2022"])
               {
                  NewYear_Interface.openYesNo2022 = objX["newyearYN2022"];
               }
               Cc.info("解析存档程序-进程54");
               if(objX["arrAll"])
               {
                  FiveOne_Interface.arrAll = objX["arrAll"];
               }
               Cc.info("解析存档程序-进程55");
               if(objX["ZhuanPan"])
               {
                  ZhuanPan.saveTime = objX["ZhuanPan"];
               }
               Cc.info("解析存档程序-进程56");
               if(objX["ZhuanPanYN"])
               {
                  ZhuanPan.Num1YN2 = objX["ZhuanPanYN"];
               }
               Cc.info("解析存档程序-进程57");
               if(objX["qdArr"])
               {
                  QianDao.qdArr = objX["qdArr"];
               }
               Cc.info("解析存档程序-进程58");
               if(objX["tuijian"])
               {
                  TuiJianPanel.arrSave = objX["tuijian"];
               }
               Vip_Interface.SetData();
               SaveXX.TestGoldMax();
               Cc.info("解析存档程序-进程59");
               if(objX["TiaoZan_newTime"])
               {
                  PaiHang_Data.newTime = objX["TiaoZan_newTime"];
               }
               Cc.info("解析存档程序-进程60");
               if(objX["inGameNum"])
               {
                  PaiHang_Data.inGameNum = objX["inGameNum"];
               }
               Cc.info("解析存档程序-进程61");
               if(objX["sArr2"])
               {
                  PaiHang_Data.sArr2 = objX["sArr2"];
               }
               Cc.info("解析存档程序-进程62");
               if(objX["jiFenArr"])
               {
                  PaiHang_Data.jiFenArr = objX["jiFenArr"];
               }
               Cc.info("解析存档程序-进程63");
               if(objX["PK_jiFenArr"])
               {
                  PK_UI.jiFenArr = objX["PK_jiFenArr"];
               }
               Cc.info("解析存档程序-进程64");
               if(objX["saveTime"])
               {
                  JiFenLingQue3.saveTime = objX["saveTime"];
               }
               Cc.info("解析存档程序-进程65");
               if(objX["yinCangP1P2"])
               {
                  Main.yinCangP1P2 = objX["yinCangP1P2"];
               }
               Cc.info("解析存档程序-进程66");
               if(objX["saveTime2"])
               {
                  GengXin.saveTime = objX["saveTime2"];
               }
               Cc.info("解析存档程序-进程67");
               if(objX["saveVar"])
               {
                  GengXin.saveVar = objX["saveVar"];
               }
               Cc.info("解析存档程序-进程68");
               if(objX["gameTime"])
               {
                  JiFenLingQue3.gameTime = objX["gameTime"];
               }
               Cc.info("解析存档程序-进程69");
               if(objX["HuiTie8Arr"])
               {
                  HuiTie8Arr = objX["HuiTie8Arr"];
               }
               PaiHang_Data.InitSave();
               Cc.info("解析存档程序-进程70");
               if(Main.initTime)
               {
                  objX["initTime"] = Main.initTime;
               }
               Cc.info("解析存档程序-进程71");
               Strat.startGame();
               JiFenLingQue3.Post();
               if(Main.player1.level.getValue() >= 50)
               {
                  PK_UI.TiaoJiao_1();
               }
               if(objX["lhs_Data"])
               {
                  LingHunShi_Interface.lhs_Data = objX["lhs_Data"];
               }
               if(objX["noMovPlayArr"])
               {
                  LingHunShi_Interface.noMovPlayArr = objX["noMovPlayArr"];
               }
               if(objX["rwArr"])
               {
                  GongHuiRenWu.rwArr = objX["rwArr"];
               }
               if(objX["rwArr2"])
               {
                  GongHuiRenWu.rwArr2 = objX["rwArr2"];
               }
               if(objX["tzData"])
               {
                  GongHuiTiaoZan.tzData = objX["tzData"];
               }
               if(objX["yueKaTime"])
               {
                  YueKa_Interface.yueKaTime = objX["yueKaTime"];
               }
               if(objX["yueKaTime2"])
               {
                  YueKa_Interface.yueKaTime2 = objX["yueKaTime2"];
               }
               if(objX["lingQu_new"])
               {
                  ChongZhi_Interface4.lingQu_new = objX["lingQu_new"];
               }
               if(objX["youHun1"])
               {
                  Panel_youling.lvArr = objX["youHun1"];
               }
               if(objX["youHun2"])
               {
                  Panel_youling.bzNumArr = objX["youHun2"];
               }
               Cc.info("解析存档程序-进程34");
               if(objX["youHun3"])
               {
                  NPC_YouLing.gameNumX = objX["youHun3"];
               }
               if(objX["cw2save"])
               {
                  ChongWu2.cw2save = objX["cw2save"];
               }
               Cc.info("解析存档程序-拷贝存档完成");
               trace("读档");
               if(objX["saveTimeX"])
               {
                  if(numServerTime != 0 && objX["saveTimeX"] != numServerTime)
                  {
                     this.DayInit();
                  }
               }
               saveTimeX = numServerTime;
               break;
            case SaveEvent.SAVE_SET:
               if(e.ret as Boolean == true)
               {
                  if(Main.saveMc_YN)
                  {
                     NewMC.Open("保存成功",this);
                     Main.SaveFun();
                  }
               }
               break;
            case SaveEvent.SAVE_LIST:
               Cc.info("读档程序-读档开始");
               data = e.ret as Array;
               Cc.info("读档程序-获取所有存档列表");
               loadSaveOver = true;
               trace("读取存档列表------->" + data);
               if(data == null)
               {
                  trace("存档列表 null");
                  i = 0;
                  while(i < 8)
                  {
                     Strat.SAVE_LIST[i] = "空存档";
                     i++;
                  }
               }
               else
               {
                  Cc.info("读档程序-存档列表获取成功");
                  for(i in data)
                  {
                     obj = data[i];
                     if(obj == null)
                     {
                        Strat.SAVE_LIST[i] = "空存档";
                     }
                     else
                     {
                        tmpStr = "存档的位置:" + obj.index + "存档时间:" + obj.datetime + "存档标题:" + obj.title;
                        Cc.info("读档程序-存档信息初步读取");
                        trace(tmpStr);
                        Strat.SAVE_LIST[int(obj.index)] = obj.title + "\n" + obj.datetime;
                     }
                  }
               }
               Shop_LiBao.subString();
               break;
            case "logreturn":
               userId = e.ret.uid;
               Cc.info("读档程序-uid获取");
               logName = e.ret.name;
               if(e.ret.nickName)
               {
                  logName2 = e.ret.nickName;
                  Cc.info("读档程序-角色名获取");
               }
               else
               {
                  logName2 = "?" + e.ret.name;
                  Cc.info("读档程序-未知角色名");
               }
               logYN = true;
               if(!loadSaveOver)
               {
                  Main.serviceHold.getList();
               }
         }
      }
      
      private function multipleErrorHandler(evt:Event) : void
      {
         TiaoShi.txtShow("游戏多开了!");
         DuoKai_Info.Open();
      }
      
      private function getStoreStateHandler(evt:Event) : void
      {
         switch(evt.data)
         {
            case "1":
               this.duoKai_num = 0;
               TiaoShi.txtShow("多开检测正常!");
               if(buy_DuoKai)
               {
                  buy_DuoKai = false;
                  Api_4399_All.BuyObj_GO();
               }
               if(saveStop[0])
               {
                  saveStop[0] = false;
                  Save2(saveStop[1]);
               }
               break;
            case "0":
               DuoKai_Info.Open(1);
               break;
            case "-1":
               if(this.duoKai_num > 3)
               {
                  DuoKai_Info.Open(2);
                  break;
               }
               DuoKai_Fun();
               ++this.duoKai_num;
               break;
            case "-2":
               DuoKai_Info.Open(2);
               break;
            case "-3":
               DuoKai_Info.Open(2);
         }
      }
      
      private function DayInit() : *
      {
         TeShuHuoDong.DayInit();
         this.chongZhiFanLi_YN = true;
         NewYear_Interface.juan2022.setValue(5);
         NPC_YouLing.gameNumX = 1;
      }
      
      private function onGetServerTimeHandler(evt:DataEvent) : void
      {
         var tempStr:String = null;
         var tempArr:Array = null;
         var xx2:String = null;
         if(evt.data == "")
         {
            ServerTimeNum = 0;
            GetServerTime();
            return;
         }
         trace("当前服务器时间为:" + evt.data);
         Main.serverDayNum = Main.createServerDateA(evt.data);
         var xx:String = evt.data.substr(0,4) + evt.data.substr(5,2) + evt.data.substr(8,2);
         if(!getServerTime)
         {
            serverTimeStr = evt.data.substr(0,4) + "/" + evt.data.substr(5,2) + "/" + evt.data.substr(8,2) + " " + evt.data.substr(10);
            serverTime.setValue(int(xx));
            TiaoShi.txtShow("系统时间:" + Main.serverTime.getValue());
            PaiHang_Data.InitSave();
            if(!Main.initTime)
            {
               Main.initTime = serverTime;
            }
            QianDao.year = evt.data.substr(0,4);
            QianDao.month = evt.data.substr(5,2);
            QianDao.day = evt.data.substr(8,2);
            getServerTime = true;
         }
         else if(PK_UI.getTimeYn)
         {
            PK_UI.getTimeArr[0] = evt.data.substr(0,4) + "" + evt.data.substr(5,2) + "" + evt.data.substr(8,2);
            tempStr = evt.data.substr(11);
            tempArr = tempStr.split(":");
            PK_UI.getTimeArr[1] = tempArr[0];
            PK_UI.getTimeArr[2] = tempArr[1];
            PK_UI.getTimeArr[3] = tempArr[2];
            PK_UI.xxxTime();
            if(TiaoShi._this)
            {
               TiaoShi.txtShow("查询时间:" + xx);
               xx2 = evt.data.substr(11);
               tempArr = xx2.split(":");
            }
         }
         if(Main.serverTime.getValue() > Panel_XianHua.overTime)
         {
            Panel_XianHua.huaArr = null;
         }
      }
      
      public function Loading() : *
      {
         Load.Open();
         addEventListener(Event.ENTER_FRAME,this.onLoading);
      }
      
      private function onLoading(e:*) : *
      {
         if(Boolean(Load.All_Loaded) && Load.loadingX.visible == true)
         {
            Load.loadOk = false;
            Load.All_Loaded = false;
            GameData.GetData();
            this.GameStart();
            removeEventListener(Event.ENTER_FRAME,this.onLoading);
            Load.Close();
            NewPetPanel.openLoad();
            this.shanbiGAIfangyu();
            TitelPanel.open(true);
            TitelPanel.close();
         }
      }
      
      public function GameStart() : *
      {
         var tempArr:Array = null;
         var NpcY:NpcYY = null;
         Request.RequestX();
         Cc.info("开始游戏-准备工作完成，即将开始游戏");
         trace("GameStart >>>>>>>>>>>>>>>>>>>>>>>>>",gameNum.getValue(),gameNum2.getValue());
         if(varX_old <= 1902)
         {
            XiuZeng.XiuZengAll();
         }
         if(Main.gameNum.getValue() >= 7001 && Main.gameNum.getValue() <= 7200)
         {
            NPC_YouLing.timeX_YN = true;
         }
         if(gameNum.getValue() != 0 && gameNum2.getValue() == 1 && !this.EmenyDataMD5_YN)
         {
            Data2.EmenyDataMD5();
            this.EmenyDataMD5_YN = true;
         }
         GongHuiRenWu.initData();
         ChongWu2.InitSave();
         if(Main.gameNum.getValue() == 888)
         {
            Load.Loading(14,false);
         }
         NewLoad2.Loading();
         NPC_YouLing.timeGo();
         this.selHuiTie8();
         TiaoZhan_Interface.LoadSkin();
         EnergySlot.energyBool = true;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            if(Main.water.getValue() != 1)
            {
               Main.gameNum2.setValue(4);
            }
            else if(Main.Map0_YN)
            {
               Main.gameNum2.setValue(3);
            }
            NewDoor.bool_1 = true;
            NewDoor.bool_2 = true;
            NewDoor.bool_3 = true;
            NewDoor.bool_4 = true;
            NewDoor.setRdm();
            Play_Interface.jihuaTiShi();
            Player.skillYAO = 0;
            if(Main.player_1)
            {
               Main.player_1.speedTemp.setValue(0);
               Main.player_1.debuff = 0;
            }
            if(Boolean(Main.P1P2) && Boolean(Main.player_2))
            {
               Main.player_2.speedTemp.setValue(0);
               Main.player_2.debuff = 0;
            }
            JingLingCatch.count = 0;
         }
         if(Main.gameNum.getValue() == 82 && Main.gameNum2.getValue() == 1)
         {
            if(NewDoor.bool_0)
            {
               NewDoor.bool_0 = false;
            }
            else
            {
               NewDoor.bool_1 = true;
               NewDoor.bool_2 = true;
               NewDoor.bool_3 = true;
               NewDoor.bool_4 = true;
               NewDoor.setRdm();
            }
         }
         Play_Interface.BossLifeYN = false;
         MakeData.initAllMake();
         if(varX_old == 995)
         {
            MakeData.AllMakeXX();
         }
         AchData.getAllAch();
         TaskData.initAllTask();
         MusicBox.MusicPlay();
         this.DelGame();
         this.AddMap();
         this.AddPlayer(1);
         if(P1P2)
         {
            this.AddPlayer(2);
         }
         this.XiuZengZhuangZhi();
         this.XiuZengSkill();
         if(Main.gameNum.getValue() == 0)
         {
            tempArr = [157,158,159,160,161,162,163];
            Api_4399_GongHui.getNum(tempArr);
         }
         ItemsPanel.open();
         ItemsPanel.close();
         GameData.GetData();
         Play_Interface.Open();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         CheckTest.startTest();
         SaveXX.TestGoldMax();
         if(Main.gameNum2.getValue() < 1 || Main.gameNum.getValue() == 999)
         {
            Player.一起信春哥();
            WinShow.All_0();
         }
         PaiHang_Data.All_0();
         Main.player_1.noMove = false;
         Main.player_1.noYingZhi = false;
         if(Boolean(Main.player1.playerCW_Data) && !Main.player_1.playerCW2)
         {
            Main.player_1.NewCW(Main.player1.playerCW_Data);
         }
         if(Main.P1P2)
         {
            Main.player_2.noMove = false;
            Main.player_2.noYingZhi = false;
            if(Boolean(Main.player2.playerCW_Data) && !Main.player_2.playerCW2)
            {
               Main.player_2.NewCW(Main.player2.playerCW_Data);
            }
         }
         if(Main.gameNum.getValue() != 999)
         {
            PK_UI.PK_ing = false;
            PK_UI.getTimeYn = false;
         }
         else
         {
            PK_UI.PK_ing = true;
         }
         if(Main.gameNum.getValue() > 17 && Main.gameNum.getValue() < 29)
         {
            JiHua_Interface.ppp5_4 = true;
         }
         if(Main.serverTime.getValue() <= 20221016)
         {
            addBtn();
            if(SixOne_Interface.state2021.getValue() == 1 && Main.gameNum.getValue() == SixOne_Interface.guankaNum)
            {
               SixOne_Interface.state2021.setValue(2);
               SixOne_Interface.dayTimes2021.setValue(SixOne_Interface.dayTimes2021.getValue() + 1);
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.zhongqiuState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_1.zhongqiuState = 0;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.zhongqiuState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_2.zhongqiuState = 0;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.zhongqiuState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_1.zhongqiuState = 2;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.zhongqiuState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_2.zhongqiuState = 2;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.jianrenState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_1.jianrenState = 0;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.jianrenState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_2.jianrenState = 0;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.jianrenState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_1.jianrenState = 2;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.jianrenState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_2.jianrenState = 2;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.shengmingState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_1.shengmingState = 0;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.shengmingState == 2)
         {
            if(Main.gameNum.getValue() == 0 || Main.gameNum2.getValue() == 1)
            {
               Main.player_2.shengmingState = 0;
            }
         }
         if(Boolean(Main.player_1) && Main.player_1.shengmingState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_1.shengmingState = 2;
            }
         }
         if(Main.P1P2 && Main.player_2 && Main.player_2.shengmingState == 1)
         {
            if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
            {
               Main.player_2.shengmingState = 2;
            }
         }
         if(Main.gameNum.getValue() == 0)
         {
            TuiJianPanel.openLV();
         }
         JingLingCatch.only = true;
         _stage.addChild(this.blackXXXX);
         this.blackXXXX.Black_mc.y = 5000;
         this.blackXXXX.Black_mc.x = 5000;
         if(Player.upLevelInfo == true && Main.gameNum.getValue() == 0)
         {
            NewMC.Open("文字提示",Main._this,470,500,150,0,true,1,"恭喜你升到2级! 你可以在主城找【奥古斯汀】学习技能啦!");
            Player.upLevelInfo = false;
         }
         GengXin.CaXun();
         YinCang.Init();
         if(NpcYY._this)
         {
            NpcYY._this.over();
         }
         if(gameNum.getValue() == 3000)
         {
            NpcY = new NpcYY();
         }
         YaoYuan.Init();
      }
      
      public function ChongZhiFanLi() : *
      {
         if(Boolean(this.chongZhiFanLi_YN) && Main.gameNum.getValue() == 0 && serverTime.getValue() <= 20170213)
         {
            this.chongZhiFanLi_YN = false;
            Czfl.Open();
         }
      }
      
      public function selHuiTie8() : *
      {
         if(!tiaoShiYN && Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() != 1)
         {
            Api_4399_GongHui.getNum([137]);
         }
      }
      
      public function DelGame() : *
      {
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         Enemy.All = new Array();
         Player.All = new Array();
         Player2.All = new Array();
         HitXX.AllHitXX = new Array();
         Hit82.AllHit82 = new Array();
         HitF.AllHitF = new Array();
         HitP.AllHitP = new Array();
         for(i in Fly.All)
         {
            (Fly.All[i] as Fly).Dead2();
         }
         Fly.All = new Array();
         if(Main.world)
         {
            Main.world.parent.removeChild(Main.world);
            Main.world.y = 5000;
            Main.world = null;
         }
      }
      
      public function shanbiGAIfangyu() : *
      {
         var i:int = 0;
         i = 0;
         while(i < 13)
         {
            if(Boolean(Main.player1.getEquipSlot().getEquipFromSlot(i)) && Main.player1.getEquipSlot().getEquipFromSlot(i).getPosition() == 3)
            {
               Main.player1.getEquipSlot().getEquipFromSlot(i).changeReinforceTest();
            }
            if(Boolean(Main.player1.getEquipSlot().getEquipFromSlot(i)) && Main.player1.getEquipSlot().getEquipFromSlot(i).getNewSkill() == 57232)
            {
               Main.player1.getEquipSlot().getEquipFromSlot(i).setNewSkill(57230);
            }
            i++;
         }
         i = 0;
         while(i < 48)
         {
            if(Boolean(Main.player1.getBag().getEquipFromBag(i)) && Main.player1.getBag().getEquipFromBag(i).getPosition() == 3)
            {
               Main.player1.getBag().getEquipFromBag(i).changeReinforceTest();
            }
            if(Boolean(Main.player1.getBag().getEquipFromBag(i)) && Main.player1.getBag().getEquipFromBag(i).getNewSkill() == 57232)
            {
               Main.player1.getBag().getEquipFromBag(i).setNewSkill(57230);
            }
            i++;
         }
         if(Main.P1P2)
         {
            i = 0;
            while(i < 13)
            {
               if(Boolean(Main.player2.getEquipSlot().getEquipFromSlot(i)) && Main.player2.getEquipSlot().getEquipFromSlot(i).getPosition() == 3)
               {
                  Main.player2.getEquipSlot().getEquipFromSlot(i).changeReinforceTest();
               }
               if(Boolean(Main.player2.getEquipSlot().getEquipFromSlot(i)) && Main.player2.getEquipSlot().getEquipFromSlot(i).getNewSkill() == 57232)
               {
                  Main.player2.getEquipSlot().getEquipFromSlot(i).setNewSkill(57230);
               }
               i++;
            }
            i = 0;
            while(i < 48)
            {
               if(Boolean(Main.player2.getBag().getEquipFromBag(i)) && Main.player2.getBag().getEquipFromBag(i).getPosition() == 3)
               {
                  Main.player2.getBag().getEquipFromBag(i).changeReinforceTest();
               }
               if(Boolean(Main.player2.getBag().getEquipFromBag(i)) && Main.player2.getBag().getEquipFromBag(i).getNewSkill() == 57232)
               {
                  Main.player2.getBag().getEquipFromBag(i).setNewSkill(57230);
               }
               i++;
            }
         }
      }
      
      private function AddMap() : *
      {
         var className:String = GameData.SelMapName();
         var gameNumXX:uint = gameNum.getValue();
         trace("AddMap ---------------------->",gameNumXX,className);
         var classRef:Class = Map.MapArr[gameNumXX].getClass(className as String) as Class;
         world = new classRef();
         addChild(Main.world);
         Main._stage.stageFocusRect = false;
         Main._stage.focus = Main.world;
         if(Main.gameNum.getValue() == 81)
         {
            if(Main.world["_back"])
            {
               Main.world["_back"].addChild(new MapJG());
            }
         }
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0 && Main.player1.level.getValue() < 30 && Main.player1.getPoints() >= 4)
         {
            if(!JiNeng_mc)
            {
               JiNeng_mc = new JiNengTiShi();
               JiNeng_mc.x = 263;
               JiNeng_mc.y = 135;
               Main.world.addChild(JiNeng_mc);
            }
         }
      }
      
      private function AddPlayer(num:int = 1) : *
      {
         if(num == 1)
         {
            if(!player_1)
            {
               player_1 = new Player();
               if(Main.player1)
               {
                  player_1.data = Main.player1;
               }
               else
               {
                  player_1.data = Main.player1 = PlayerData.creatPlayerData(1);
                  player_1.data.skinArr = SetProfession.skinArr1;
               }
               player_1.Load_All_Player_Data();
            }
            Main.world.moveChild_Player.addChild(player_1);
            if(player_1.playerCW2)
            {
               Main.world.moveChild_Player.addChild(player_1.playerCW2);
            }
            player_1.x = 280;
            if(player_1.hp.getValue() > 0)
            {
               player_1.y = 450;
            }
            else
            {
               player_1.y = -50;
            }
            if(player_1.CW2gameNum != Main.gameNum.getValue())
            {
               player_1.CW2gameNum = 0;
               if(player_1.playerCW2)
               {
                  player_1.playerCW2.DeadX();
               }
            }
            player_1.newSkin();
            player_1.KeyControl_YN = true;
            if(Main.gameNum.getValue() == 4001)
            {
               player_1.x = -200;
               player_1.KeyControl_YN = false;
            }
            if(Main.gameNum2.getValue() == 1 && player_1.playerJL && Boolean(player_1.playerJL.jiNengZD_ID))
            {
               player_1.playerJL.jiNengZD_num = 100;
            }
         }
         else if(num == 2)
         {
            if(!player_2)
            {
               player_2 = new Player();
               if(Main.player2)
               {
                  player_2.data = Main.player2;
               }
               else
               {
                  player_2.data = Main.player2 = PlayerData.creatPlayerData(2);
                  player_2.data.skinArr = SetProfession.skinArr2;
               }
               player_2.Load_All_Player_Data();
            }
            Main.world.moveChild_Player.addChild(player_2);
            if(player_2.playerCW2)
            {
               Main.world.moveChild_Player.addChild(player_2.playerCW2);
            }
            player_2.x = 240;
            if(player_1.hp.getValue() > 0)
            {
               player_1.y = 450;
            }
            else
            {
               player_1.y = -50;
            }
            player_2.y = 450;
            if(player_2.CW2gameNum != Main.gameNum.getValue())
            {
               player_2.CW2gameNum = 0;
               if(player_2.playerCW2)
               {
                  player_2.playerCW2.DeadX();
               }
            }
            player_2.newSkin();
            player_2.KeyControl_YN = true;
            if(Main.gameNum.getValue() == 4001)
            {
               player_2.x = -220;
               player_2.KeyControl_YN = false;
            }
            if(Main.gameNum2.getValue() == 1 && player_2.playerJL && Boolean(player_2.playerJL.jiNengZD_ID))
            {
               player_2.playerJL.jiNengZD_num = 100;
            }
         }
         Main.world.开启Show0();
      }
      
      private function onENTER_FRAME(e:Event) : *
      {
         ++JiFenLingQue3.gameTime;
         --Skill_guangDun.noTime;
         if(Main.gameNum.getValue() != 0 && Main.gameNum.getValue() != 7000)
         {
            Hit82.HitFly();
            HitF.HitFly();
            HitP.HitPlayer();
            GameData.MonsterGo();
            ++WinShow.txt_1;
            if(Main.gameNum.getValue() == 999)
            {
               PK_UI.AddOtherPlayer();
            }
         }
         HitXX.HitEnemy();
         Load.md5_Start();
         Main.GetServerTime();
         PaiHang_Data.TimeStart();
      }
      
      public function XiuZengSkill() : *
      {
         var id:int = 0;
         var i2:int = 0;
         var id2:int = 0;
         var arr:Array = [7,22,37,45,68];
         var p:PlayerData = Main.player1;
         var i:int = 0;
         while(i < arr.length)
         {
            id = int(arr[i]);
            if(p._skillArr[id][1] == 0)
            {
               p._skillArr[id][1] = 1;
            }
            i++;
         }
         if(Main.P1P2)
         {
            p = Main.player2;
            i2 = 0;
            while(i2 < arr.length)
            {
               id2 = int(arr[i2]);
               if(p._skillArr[id2][1] == 0)
               {
                  p._skillArr[id2][1] = 1;
               }
               i2++;
            }
         }
      }
      
      public function skillArrXXX(num:int = 1) : *
      {
         var p:PlayerData = Main.player1;
         if(num == 2)
         {
            p = Main.player2;
         }
         p._skillArr[61] = ["k1",1];
         p._skillArr[62] = ["k2",1];
         p._skillArr[63] = ["k3",1];
         p._skillArr[64] = ["k4",1];
         p._skillArr[65] = ["k5",1];
         p._skillArr[66] = ["k6",1];
         p._skillArr[67] = ["k7",1];
         p._skillArr[68] = ["k8",1];
         p._skillArr[69] = ["k9",0];
         p._skillArr[70] = ["k10",0];
         p._skillArr[71] = ["k11",0];
         p._skillArr[72] = ["k12",0];
         p._skillArr[73] = ["k13",0];
         p._skillArr[74] = ["k14",0];
         p._skillArr[75] = ["k15",0];
         p._skillArr[76] = ["k16",0];
      }
      
      private function XiuZengZhuangZhi() : *
      {
         var i:int = 0;
         if(Main.player1._transferArr.length < 4)
         {
            Main.player1._transferArr[3] = false;
            this.skillArrXXX();
         }
         if(Main.P1P2)
         {
            if(Main.player2._transferArr.length < 4)
            {
               Main.player2._transferArr[3] = false;
               this.skillArrXXX(2);
            }
         }
         var num:int = 0;
         i = 0;
         while(i < 3)
         {
            if(Main.player1._transferArr[i])
            {
               num++;
            }
            i++;
         }
         if(num > 1)
         {
            SkillPanel.open();
            SkillPanel.close();
            if(SkillPanel._instance)
            {
               SkillPanel._instance.aginP1();
               i = 0;
               while(i < 3)
               {
                  if(Main.player1._transferArr[i])
                  {
                     Main.player1._transferArr[i] = false;
                  }
                  i++;
               }
               Main.player1.AddKillPoint(180);
               Main.player1.addGold(20000);
               TiaoShi.txtShow("清除转职---------> 1P");
            }
         }
         num = 0;
         if(Main.P1P2)
         {
            i = 0;
            while(i < 3)
            {
               if(Main.player2._transferArr[i])
               {
                  num++;
               }
               i++;
            }
            if(num > 1)
            {
               SkillPanel.open();
               SkillPanel.close();
               if(SkillPanel._instance)
               {
                  SkillPanel._instance.aginP2();
                  i = 0;
                  while(i < 3)
                  {
                     if(Main.player2._transferArr[i])
                     {
                        Main.player2._transferArr[i] = false;
                     }
                     i++;
                  }
                  Main.player2.AddKillPoint(180);
                  Main.player2.addGold(20000);
                  TiaoShi.txtShow("清除转职---------> 2P");
               }
            }
         }
      }
   }
}

