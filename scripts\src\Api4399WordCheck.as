package src
{
   import com.adobe.serialization.json.JSONs;
   import open4399Tools.*;
   import open4399Tools.events.*;
   
   public class Api4399WordCheck
   {
      
      private var open4399ToolsApi:Open4399ToolsApi;
      
      private var initOk:int = 0;
      
      private var checkCall:Function;
      
      public function Api4399WordCheck()
      {
         super();
         this.open4399ToolsApi = Open4399ToolsApi.getInstance();
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.SERVICE_INIT,this.onServiceInitComplete);
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS_ERROR,this.onCheckBadWordsError);
         this.open4399ToolsApi.addEventListener(Open4399ToolsEvent.CHECK_BAD_WORDS,this.onCheckBadWords);
         this.open4399ToolsApi.init();
      }
      
      private function onServiceInitComplete(event:Open4399ToolsEvent) : void
      {
         this.initOk = 1;
      }
      
      private function onCheckBadWordsError(e:Open4399ToolsEvent) : void
      {
         trace("接口调用出问题了！");
      }
      
      public function checkWord(word:String, resultCall:Function) : Boolean
      {
         if(this.initOk == 1)
         {
            this.checkCall = resultCall;
            this.open4399ToolsApi.checkBadWords(word);
            return true;
         }
         return false;
      }
      
      private function onCheckBadWords(e:Open4399ToolsEvent) : void
      {
         var tempf:Function = null;
         var obj:Object = JSONs.decode(String(e.data));
         if(this.checkCall != null)
         {
            tempf = this.checkCall;
            this.checkCall = null;
            if(obj.code == "10000")
            {
               tempf(0);
            }
            else
            {
               tempf(1);
            }
         }
      }
   }
}

