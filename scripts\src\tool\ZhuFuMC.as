package src.tool
{
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol6680")]
   public class ZhuFuMC extends MovieClip
   {
      
      public var go_btn:SimpleButton;
      
      public var go_mc:MovieClip;
      
      public function ZhuFuMC()
      {
         super();
         addFrameScript(29,this.frame30);
         this.go_btn.addEventListener(MouseEvent.CLICK,this.onCLICK);
         this.go_mc.close_btn.addEventListener(MouseEvent.CLICK,this.onClose);
      }
      
      private function onCLICK(e:*) : *
      {
         var xx:int = 0;
         if(Main.gameNum.getValue() == 18)
         {
            xx = 4;
         }
         else if(Main.gameNum.getValue() == 19)
         {
            xx = 3;
         }
         else if(Main.gameNum.getValue() == 20)
         {
            xx = 1;
         }
         else if(Main.gameNum.getValue() == 21)
         {
            xx = 2;
         }
         else if(Main.gameNum.getValue() == 22)
         {
            xx = 5;
         }
         else if(Main.gameNum.getValue() == 23)
         {
            xx = 8;
         }
         else if(Main.gameNum.getValue() == 24)
         {
            xx = 4;
         }
         else if(Main.gameNum.getValue() == 25)
         {
            xx = 3;
         }
         else if(Main.gameNum.getValue() == 26)
         {
            xx = 1;
         }
         else if(Main.gameNum.getValue() == 27)
         {
            xx = 2;
         }
         else if(Main.gameNum.getValue() == 28)
         {
            xx = 5;
         }
         else if(Main.gameNum.getValue() == 29)
         {
            xx = 8;
         }
         JinHuaPanel.open(true,Main.gameNum.getValue() - 17,xx);
      }
      
      private function onClose(e:*) : *
      {
      }
      
      internal function frame30() : *
      {
         stop();
      }
   }
}

