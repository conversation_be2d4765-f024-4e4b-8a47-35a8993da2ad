package src
{
   import com.hotpoint.braveManIII.models.common.*;
   
   public class XMLtool
   {
      
      public function XMLtool()
      {
         super();
      }
      
      public static function StrToIntArr(str:String) : Array
      {
         var arr:Array = null;
         var i:int = 0;
         if(str != "")
         {
            arr = str.split(",");
            for(i in arr)
            {
               arr[i] = int(arr[i]);
            }
         }
         return arr;
      }
      
      public static function StrToNum(str:String, vtYN:Boolean = true) : Array
      {
         var arr:Array = null;
         var i:int = 0;
         if(str != "")
         {
            arr = str.split(",");
            for(i in arr)
            {
               arr[i] = Number(arr[i]);
               if(vtYN)
               {
                  arr[i] = VT.createVT(arr[i]);
               }
            }
         }
         return arr;
      }
      
      public static function StrToArr(str:String) : Array
      {
         var i:int = 0;
         var arr:Array = [];
         if(Boolean(str) && str != "")
         {
            arr = str.split(",");
            for(i in arr)
            {
               arr[i] = int(arr[i]);
               arr[i] = VT.createVT(arr[i]);
            }
         }
         return arr;
      }
      
      public static function StrToStrArr(str:String) : Array
      {
         var arr:Array = null;
         var i:int = 0;
         if(str != "")
         {
            arr = str.split(",");
            for(i in arr)
            {
               arr[i] = arr[i];
            }
         }
         return arr;
      }
      
      public static function StrToArr2(str:String) : Array
      {
         var arr:Array = null;
         var i:int = 0;
         var arr2:Array = null;
         var i2:int = 0;
         if(str != "")
         {
            arr = str.split("*");
            for(i in arr)
            {
               arr2 = arr[i].split(",");
               arr[i] = arr2;
               for(i2 in arr[i])
               {
                  arr[i][i2] = Number(arr[i][i2]);
                  arr[i][i2] = VT.createVT(arr[i][i2]);
               }
            }
         }
         return arr;
      }
   }
}

