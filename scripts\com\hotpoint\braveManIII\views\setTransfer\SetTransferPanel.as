package com.hotpoint.braveManIII.views.setTransfer
{
   import flash.display.MovieClip;
   import flash.events.*;
   import src.*;
   
   public class SetTransferPanel extends MovieClip
   {
      
      private static var _instance:SetTransferPanel;
      
      private static var skin:MovieClip;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      private static var loadName:String = "Panel_ZZ_v1200.swf";
      
      public var kk_mc:*;
      
      public var zhezhao1:*;
      
      public var zhezhao2:*;
      
      public var b_0:*;
      
      public var b_1:*;
      
      public var p1_btn:*;
      
      public var p2_btn:*;
      
      public var tishi_close:*;
      
      public var tishi_close2:*;
      
      public var close_btn:*;
      
      public var sel_0:*;
      
      public var sel_1:*;
      
      public var ok_btn:*;
      
      private var stata:Number = 1;
      
      private var xxx:Number = 0;
      
      public function SetTransferPanel()
      {
         super();
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         Main.stopXX = true;
         if(SetTransferPanel._instance == null)
         {
            open_yn = true;
            Loading();
            return;
         }
         SetTransferPanel._instance.x = 0;
         SetTransferPanel._instance.y = 0;
         SetTransferPanel._instance.init();
         SetTransferPanel._instance.visible = true;
         Main._stage.addChild(SetTransferPanel._instance);
         SetTransferPanel._instance.kk_mc.tishi_close.addEventListener(MouseEvent.CLICK,onTishi_close);
         SetTransferPanel._instance.kk_mc.tishi_close2.addEventListener(MouseEvent.CLICK,onTishi_close);
         SetTransferPanel._instance.kk_mc.ok_btn.addEventListener(MouseEvent.CLICK,SetTransferPanel._instance.clickHandle);
         SetTransferPanel._instance.p1_btn.addEventListener(MouseEvent.CLICK,SetTransferPanel._instance.clickHandle);
         SetTransferPanel._instance.p2_btn.addEventListener(MouseEvent.CLICK,SetTransferPanel._instance.clickHandle);
         SetTransferPanel._instance.sel_0.addEventListener(MouseEvent.CLICK,SetTransferPanel._instance.clickHandle);
         SetTransferPanel._instance.sel_1.addEventListener(MouseEvent.CLICK,SetTransferPanel._instance.clickHandle);
         SetTransferPanel._instance.close_btn.addEventListener(MouseEvent.CLICK,close);
         trace("专职界面 打开");
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("com.hotpoint.braveManIII.views.setTransfer.SetTransferPanel") as Class;
         SetTransferPanel._instance = new classRef();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      public static function close(e:* = null) : void
      {
         Main.stopXX = false;
         if(SetTransferPanel._instance == null)
         {
            return;
         }
         SetTransferPanel._instance.visible = false;
         Play_Interface.Open();
         open_yn = false;
      }
      
      public static function tbo1() : Boolean
      {
         for(var i:uint = 0; i <= 3; i++)
         {
            if(Boolean(Main.player1) && Boolean(Main.player1.isTransfer(i)))
            {
               return true;
            }
         }
         return false;
      }
      
      public static function tbo2() : Boolean
      {
         for(var i:uint = 0; i <= 3; i++)
         {
            if(Boolean(Main.player2) && Boolean(Main.player2.isTransfer(i)))
            {
               return true;
            }
         }
         return false;
      }
      
      public static function onTishi_close(e:*) : *
      {
         SetTransferPanel._instance.kk_mc.visible = false;
      }
      
      public function init() : void
      {
         this.updataStata();
         this.p1orP2();
         this.initData();
         this.kk_mc.visible = false;
      }
      
      private function initData() : void
      {
         var arr:Array = [];
         if(Main.player2 == null)
         {
            arr = this.pickSkill1();
         }
         else if(Main.player2 != null)
         {
            if(tbo1())
            {
               arr = this.pickSkill2();
            }
            else if(tbo2())
            {
               arr = this.pickSkill1();
            }
            else if(!tbo1() && !tbo2())
            {
               if(Main.player1.getLevel() < 25)
               {
                  arr = this.pickSkill2();
               }
               else
               {
                  arr = this.pickSkill1();
               }
            }
         }
         this.kkk(arr);
      }
      
      private function clickHandle(e:MouseEvent) : void
      {
         var btn_name:* = e.target.name;
         trace("btn_name = " + btn_name);
         var arr:Array = [];
         switch(btn_name)
         {
            case "p1_btn":
               this.stata = 1;
               arr = this.pickSkill1();
               this.kkk(arr);
               break;
            case "p2_btn":
               this.stata = 2;
               arr = this.pickSkill2();
               this.kkk(arr);
               break;
            case "sel_0":
               this.xxx = this.b_0.currentFrame;
               this.kk_mc.visible = true;
               break;
            case "sel_1":
               this.xxx = this.b_1.currentFrame;
               this.kk_mc.visible = true;
               break;
            case "ok_btn":
               this.okPanel();
         }
      }
      
      private function okPanel() : void
      {
         if(this.xxx != 0)
         {
            if(this.stata == 1)
            {
               if(Main.player1.getKillPoint() >= 180)
               {
                  if(Main.player1.getGold() >= 20000)
                  {
                     TransferOkPanel.open();
                     Main.player1.payGold(20000);
                     Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - 180);
                     Main.player1.setTransfer(this.xxx - 1);
                     trace("p1转职成功:",tbo1(),this.xxx);
                     this.gg();
                     this.visible = false;
                     Play_Interface.Open();
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
                     trace("p1金币不足");
                  }
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点不足");
                  trace("物品不足");
               }
            }
            else if(this.stata == 2)
            {
               if(Main.player2.getKillPoint() >= 180)
               {
                  if(Main.player2.getGold() >= 20000)
                  {
                     TransferOkPanel.open();
                     Main.player2.payGold(20000);
                     Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - 180);
                     Main.player2.setTransfer(this.xxx - 1);
                     trace("p2转职成功:",tbo2(),this.xxx);
                     this.gg();
                     this.visible = false;
                     Play_Interface.Open();
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
                     trace("p2金币不足");
                  }
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点不足");
                  trace("p2物品不足");
               }
            }
         }
      }
      
      private function updataStata() : void
      {
         if(Main.player_2 != null)
         {
            if(tbo1() || Main.player1.getLevel() < 25)
            {
               this.stata = 2;
            }
            else if(tbo2() || Main.player2.getLevel() < 25)
            {
               this.stata = 1;
            }
            else if(!tbo1() || !tbo2())
            {
               this.stata = 1;
            }
         }
         else
         {
            this.stata = 1;
         }
         trace("stata:" + this.stata);
      }
      
      private function gg() : void
      {
         if(Main.player2 == null)
         {
            if(tbo1())
            {
               trace("p1转职");
               Main.world["转职"].visible = false;
            }
         }
         else if(Main.player2 != null)
         {
            if(tbo1() && tbo2())
            {
               Main.world["转职"].visible = false;
            }
            else if(tbo1() && !tbo2())
            {
               if(Main.player2.getLevel() < 25)
               {
                  Main.world["转职"].visible = false;
               }
            }
            else if(tbo2() && !tbo1())
            {
               if(Main.player1.getLevel() < 25)
               {
                  Main.world["转职"].visible = false;
               }
            }
         }
      }
      
      private function kkk(arr:Array) : void
      {
         for(i = 0; i < arr.length; ++i)
         {
            this["b_" + i].gotoAndStop(arr[i] + 1);
         }
      }
      
      private function pickSkill1() : Array
      {
         var arr:Array = [];
         var zy1:Array = Main.player1.getPickSkill();
         for(var i:uint = 0; i < zy1.length; i++)
         {
            if(zy1[i])
            {
               arr.push(i);
            }
         }
         return arr;
      }
      
      private function pickSkill2() : Array
      {
         var arr:Array = [];
         var zy2:Array = Main.player2.getPickSkill();
         for(var i:uint = 0; i < zy2.length; i++)
         {
            if(zy2[i])
            {
               arr.push(i);
            }
         }
         return arr;
      }
      
      private function p1orP2() : void
      {
         this.zhezhao1.visible = true;
         this.zhezhao2.visible = true;
         this.p1_btn.gotoAndStop(1);
         this.p2_btn.gotoAndStop(1);
         if(Main.player2 == null)
         {
            this.p2_btn.gotoAndStop(4);
         }
         if(!tbo1())
         {
            if(Main.player1.getLevel() >= 25)
            {
               this.zhezhao1.visible = false;
            }
            else
            {
               this.p1_btn.gotoAndStop(4);
            }
         }
         else
         {
            this.p1_btn.gotoAndStop(4);
         }
         if(Main.player2 != null)
         {
            if(!tbo2())
            {
               if(Main.player2.getLevel() >= 25)
               {
                  this.zhezhao2.visible = false;
               }
               else
               {
                  this.p2_btn.gotoAndStop(4);
               }
            }
            else
            {
               this.p2_btn.gotoAndStop(4);
            }
         }
      }
   }
}

