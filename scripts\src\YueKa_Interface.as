package src
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   
   public class YueKa_Interface extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      public static var _this:YueKa_Interface;
      
      private static var loadName:String = "yueKa.swf";
      
      private static var OpenYN:Boolean = false;
      
      public static var yueKaTime:Number = 0;
      
      public static var yueKaTime2:Number = 0;
      
      private var tooltip:ItemsTooltip;
      
      private var skin:MovieClip;
      
      public var objIdArr:Array = [0,63181,21221,33213,63107,63203,63147,63100,63210];
      
      public var objTypeArr:Array = [0,3,1,2,3,3,3,3,3];
      
      public var objNumArr:Array = [0,1,2,1,2,1,1,1,5];
      
      private var buyok:Boolean = false;
      
      public function YueKa_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Close(e:* = null) : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
         }
      }
      
      private static function InitOpen() : *
      {
         var temp:YueKa_Interface = new YueKa_Interface();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function GetBuy() : *
      {
         if(Boolean(_this) && Boolean(_this.buyok))
         {
            _this.buyok = false;
            NewMC.Open("文字提示",_this,400,400,30,0,true,2,"月卡购买成功");
            YueKa_Interface.yueKaTime = Main.serverDayNum + 30;
            _this.skin._BLACK_mc.visible = false;
            Main.Save2();
            _this.Show();
            _this.skin.buy_mc.visible = false;
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("Skin") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         this.skin.Close_btn.addEventListener(MouseEvent.CLICK,Close);
         this.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.skin.buy_btn.addEventListener(MouseEvent.CLICK,this.BuyGo);
         this.skin.lingqu_btn.addEventListener(MouseEvent.CLICK,this.LingQu);
         this.skin.buy_mc.visible = false;
         this.skin.buy_mc.buyOk_btn.addEventListener(MouseEvent.CLICK,this.BuyGo2);
         this.skin.buy_mc.buyClose_btn.addEventListener(MouseEvent.CLICK,this.BuyClose);
         this.tooltip = new ItemsTooltip();
         this.addChild(this.tooltip);
         this.tooltip.visible = false;
         if(OpenYN)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
      
      public function Show() : *
      {
         var dayNum:* = undefined;
         var mm:MovieClip = null;
         var num:int = 0;
         if(Main.serverDayNum == 0)
         {
            this.skin.buy_btn.visible = false;
            this.skin.day_txt.text = "数据读取中...";
         }
         else if(Main.serverDayNum < YueKa_Interface.yueKaTime)
         {
            this.skin.buy_btn.visible = false;
            dayNum = YueKa_Interface.yueKaTime - Main.serverDayNum;
            this.skin.day_txt.text = "剩余" + dayNum + "天";
         }
         else
         {
            this.skin.buy_btn.visible = true;
         }
         this.skin.lingqu_btn.visible = false;
         if(YueKa_Interface.yueKaTime == 0 || YueKa_Interface.yueKaTime < Main.serverDayNum || YueKa_Interface.yueKaTime2 < Main.serverDayNum)
         {
            this.skin.lingqu_btn.visible = true;
         }
         for(var i:uint = 1; i <= 8; i++)
         {
            mm = new Shop_picNEW();
            if(this.skin["n_" + i])
            {
               num = int(this.skin["n_" + i].getChildIndex(this.skin["n_" + i].pic_xx));
               mm.x = this.skin["n_" + i].pic_xx.x;
               mm.y = this.skin["n_" + i].pic_xx.y;
               mm.name = "pic_xx";
               mm.gotoAndStop(this.getFrame(this.objTypeArr[i],this.objIdArr[i]));
               this.skin["n_" + i].removeChild(this.skin["n_" + i].pic_xx);
               this.skin["n_" + i].pic_xx = mm;
               this.skin["n_" + i].addChild(mm);
               this.skin["n_" + i].setChildIndex(mm,num);
               this.skin["n_" + i].howNum.text = this.objNumArr[i];
            }
            else
            {
               trace("找不到图标:" + i);
            }
         }
         this.skin._BLACK_mc.visible = false;
      }
      
      public function LingQu(e:*) : *
      {
         if(Main.serverDayNum == 0)
         {
            NewMC.Open("文字提示",_this,480,450,45,0,true,2,"数据读取中,请稍后再试...");
            return;
         }
         if(YueKa_Interface.yueKaTime >= Main.serverDayNum && YueKa_Interface.yueKaTime2 < Main.serverDayNum)
         {
            this.lingQueOk();
         }
         else if(YueKa_Interface.yueKaTime == 0 || YueKa_Interface.yueKaTime > Main.serverDayNum)
         {
            NewMC.Open("文字提示",_this,480,450,45,0,true,2,"请先购买月卡");
         }
      }
      
      public function lingQueOk() : *
      {
         if(Main.player1.getBag().backOtherBagNum() < 6)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
            return;
         }
         if(Main.player1.getBag().backGemBagNum < 1)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"宝石类背包空间不足!");
            return;
         }
         if(Main.player1.getBag().backSuppliesBagNum < 1)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"消耗品背包空间不足!");
            return;
         }
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63107));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63107));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
         Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
         Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
         Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
         YueKa_Interface.yueKaTime2 = Main.serverDayNum;
         this.skin.lingqu_btn.visible = false;
         NewMC.Open("文字提示",_this,480,450,45,0,true,2,"今日物品已领取");
         Main.Save2();
      }
      
      private function BuyGo(e:*) : *
      {
         var money:uint = uint(InitData.yueKa30.getValue());
         if(Shop4399.moneyAll.getValue() >= money)
         {
            this.skin.buy_mc.visible = true;
            return;
         }
         NewMC.Open("文字提示",this,480,450,45,0,true,2,"点券不足");
      }
      
      private function BuyGo2(e:*) : *
      {
         this.buyok = true;
         this.skin._BLACK_mc.visible = true;
         Api_4399_All.BuyObj(InitData.yueKaID.getValue());
      }
      
      private function BuyClose(e:*) : *
      {
         this.skin.buy_mc.visible = false;
      }
      
      public function getFrame(type:Number, id:Number) : Number
      {
         var ob:Object = null;
         if(type == 0 || type == 4 || type == 5 || type == 6)
         {
            ob = EquipFactory.createEquipByID(id);
            return (ob as Equip).getFrame();
         }
         if(type == 1)
         {
            ob = SuppliesFactory.getSuppliesById(id);
            return (ob as Supplies).getFrame();
         }
         if(type == 2)
         {
            ob = GemFactory.creatGemById(id);
            return (ob as Gem).getFrame();
         }
         if(type == 3)
         {
            ob = OtherFactory.creatOther(id);
            return (ob as Otherobj).getFrame();
         }
         return 1;
      }
      
      private function addobj(type:Number, id:Number) : Object
      {
         var ob:Object = null;
         if(type == 0 || type == 4 || type == 5 || type == 6)
         {
            ob = EquipFactory.createEquipByID(id);
         }
         else if(type == 1)
         {
            ob = SuppliesFactory.getSuppliesById(id);
         }
         else if(type == 2)
         {
            ob = GemFactory.creatGemById(id);
         }
         else if(type == 3)
         {
            ob = OtherFactory.creatOther(id);
         }
         return ob;
      }
      
      private function daoJuOut(e:DaoJuEvent) : void
      {
         this.tooltip.visible = false;
      }
      
      private function daoJuOver(e:DaoJuEvent) : void
      {
         var str:String = e.target.name.substr(0,2);
         var id:Number = Number(e.target.name.substr(2,1));
         if(str != "n_")
         {
            return;
         }
         this.tooltip.visible = true;
         this.tooltip.x = Main._stage.mouseX;
         this.tooltip.y = Main._stage.mouseY;
         var ob:* = this.addobj(this.objTypeArr[id],this.objIdArr[id]);
         if(ob is Equip)
         {
            this.tooltip.equipTooltip(ob);
         }
         else if(ob is Supplies)
         {
            this.tooltip.suppliesTooltip(ob);
         }
         else if(ob is Gem)
         {
            this.tooltip.gemTooltip(ob);
         }
         else if(ob is Otherobj)
         {
            this.tooltip.otherTooltip(ob);
         }
      }
   }
}

