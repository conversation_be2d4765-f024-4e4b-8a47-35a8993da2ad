package
{
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1616")]
   public dynamic class 关卡过渡黑幕 extends MovieClip
   {
      
      public function 关卡过渡黑幕()
      {
         super();
         addFrameScript(21,this.frame22);
      }
      
      internal function frame22() : *
      {
         stop();
      }
   }
}

