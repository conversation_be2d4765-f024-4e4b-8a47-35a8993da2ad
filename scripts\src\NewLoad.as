package src
{
   import com.hotpoint.braveManIII.models.player.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4245")]
   public class NewLoad extends MovieClip
   {
      
      public static var OtherData:ClassLoader;
      
      public static var XiaoGuoData:ClassLoader;
      
      public static var chongZhiData:ClassLoader;
      
      public static var zhuangBeiSkin:Array = [new Array(),new Array(),new Array(),new Array()];
      
      public static var loadArr:Array = new Array();
      
      public static var loadTypeArr:Array = new Array();
      
      public static var loadType:uint = 1;
      
      public static var who:uint = 1;
      
      public static var loadingYN:Boolean = true;
      
      public static var strXXXX:String = "";
      
      public static var enemySkill:Array = [new Array()];
      
      public static var loadLR:Array = new Array();
      
      private static var boolYN:Boolean = true;
      
      public function NewLoad()
      {
         super();
      }
      
      public static function Loading(type:uint = 1, _who:uint = 1) : *
      {
         LoadingX(type,_who);
         LoadEquipGo();
         LoadSkillGo();
      }
      
      public static function LoadingX(type:uint = 1, _who:uint = 1) : *
      {
         var ii:uint = 0;
         loadType = type;
         who = _who;
         var playNum:uint = 1;
         if(Main.P1P2)
         {
            playNum = 2;
         }
         if(loadType == 1)
         {
            for(ii = 1; ii <= playNum; ii++)
            {
               if(!Main["player" + ii])
               {
                  Main["player" + ii] = PlayerData.creatPlayerData(ii);
               }
               Loading_Equip(Main["player" + ii]);
               Loading_Skill(Main["player" + ii]);
            }
         }
         else if(loadType == 2 || loadType == 3)
         {
            Loading_Equip(Main["player" + _who]);
            Loading_Skill(Main["player" + _who]);
         }
         else if(loadType == 4)
         {
            Loading_Equip(PK_UI.whoData);
         }
      }
      
      private static function Loading_Equip(whoData:PlayerData) : *
      {
         var name3:int = 0;
         var tempNum:uint = 0;
         var tempData:* = undefined;
         var loadStr:* = null;
         var xArr:Array = null;
         var data:PlayerData = whoData;
         var zhiYe:uint = uint(data.skinArr[data.skinNum]);
         var name4:String = "0";
         var tempArr:Array = [1,6,7,9];
         for(var i:uint = 0; i < tempArr.length; i++)
         {
            tempNum = uint(tempArr[i]);
            if(data.getEquipSlot().getEquipFromSlot(tempNum) != null)
            {
               name3 = data.getEquipSlot().getEquipFromSlot(tempNum).getClassName3();
               name4 = data.getEquipSlot().getEquipFromSlot(tempNum).getClassName4();
            }
            else if(i == 0 || i == 3)
            {
               name3 = 1;
               name4 = "1_v2";
            }
            tempData = zhuangBeiSkin[zhiYe][name3];
            if(!tempData && name4 != "0")
            {
               loadStr = "equip_" + zhiYe + "_" + name4 + ".swf";
               xArr = [zhiYe,name3,loadStr];
               loadArr.push(xArr);
            }
         }
      }
      
      public static function PaiHangLoad(tempArr:Array) : *
      {
         var zhiYe:int = 0;
         var id:int = 0;
         var name:String = null;
         var loadName:String = null;
         var tempData:* = undefined;
         var loadStr:* = null;
         var xArr:Array = null;
         for(var i:int = 3; i <= 4; i++)
         {
            if(tempArr[i][0] != 0)
            {
               zhiYe = int(tempArr[0]);
               id = int(tempArr[i][0]);
               name = tempArr[i][1];
               loadName = tempArr[i][2];
               tempData = zhuangBeiSkin[zhiYe][id];
               if(!tempData && name != "0")
               {
                  if(name == 53)
                  {
                     name = 12;
                  }
                  loadStr = "equip_" + zhiYe + "_" + name + ".swf";
                  xArr = [zhiYe,id,loadStr,loadName];
                  loadArr.push(xArr);
               }
            }
         }
         loadType = 5;
         LoadEquipGo();
      }
      
      private static function LoadEquipGo() : *
      {
         Main._this._stage_txt.text += "loadArr.length = " + loadArr.length + "\n";
         Main._this._stage_txt.text += "loadArr = " + loadArr + "\n";
         if(loadArr.length > 0)
         {
            if(loadingYN)
            {
               zhuangBeiSkin[loadArr[0][0]][loadArr[0][1]] = new ClassLoader(loadArr[0][2]);
               zhuangBeiSkin[loadArr[0][0]][loadArr[0][1]].addEventListener(Event.COMPLETE,LoadEquipEnd);
               loadingYN = false;
            }
            if(Boolean(Play_Interface.interfaceX) && loadType != 5)
            {
               Play_Interface.interfaceX["load_mc"].visible = true;
            }
         }
         else
         {
            if(loadType == 1)
            {
               Main._this._stage_txt.text += "Load.newLoad_Ok()\n";
               Load.newLoad_Ok();
            }
            else if(loadType == 2)
            {
               Main.player_1.newSkin_LoadEnd();
               Main._this._stage_txt.text += "loadType 2\n";
            }
            else if(loadType == 3)
            {
               Main.player_2.newSkin_LoadEnd();
               Main._this._stage_txt.text += "loadType 3\n";
            }
            else if(loadType == 4)
            {
               PK_UI.LoadEnd_And_AddPlayer2();
            }
            else if(loadType == 5)
            {
               TiaoZhanPaiHang_Interface.ShowPlayer();
            }
            if(Play_Interface.interfaceX)
            {
               Play_Interface.interfaceX["load_mc"].visible = false;
            }
         }
      }
      
      private static function LoadEquipEnd(e:*) : *
      {
         ++Load.loadName;
         loadArr.splice(0,1);
         loadingYN = true;
         LoadEquipGo();
      }
      
      private static function Loading_Skill(whoData:PlayerData) : *
      {
         var xxxx:int = 0;
         var loadStr:* = null;
         var data:PlayerData = whoData;
         for(var i:uint = 0; i < 2; i++)
         {
            xxxx = -1;
            if(data.getEquipSkillSlot().getGemFromSkillSlot(i))
            {
               if(int(data.getEquipSkillSlot().getGemFromSkillSlot(i).getClassName()) > 0)
               {
                  xxxx = int(data.getEquipSkillSlot().getGemFromSkillSlot(i).getClassName());
               }
            }
            if(xxxx != -1)
            {
               loadStr = "skill_" + xxxx + ".swf";
               loadLR.push([xxxx,loadStr]);
            }
         }
      }
      
      private static function LoadSkillGo() : *
      {
         if(loadLR.length > 0)
         {
            if(boolYN)
            {
               enemySkill[loadLR[0][0]] = new ClassLoader(loadLR[0][1]);
               enemySkill[loadLR[0][0]].addEventListener(Event.COMPLETE,LoadSkillEnd);
               boolYN = false;
            }
         }
      }
      
      private static function LoadSkillEnd(e:*) : *
      {
         ++Load.loadName;
         boolYN = true;
         loadLR.splice(0,1);
         LoadSkillGo();
      }
   }
}

