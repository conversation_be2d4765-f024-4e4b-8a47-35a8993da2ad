package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.strengthenPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class 船长界面 extends MovieClip
   {
      
      private static var only:船长界面;
      
      public var close2:SimpleButton;
      
      public var in_btn:SimpleButton;
      
      public var _btn:SimpleButton;
      
      public var youLing_btn:SimpleButton;
      
      public function 船长界面()
      {
         super();
         this.close2.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this._btn.addEventListener(MouseEvent.CLICK,this.GoGo);
         this.youLing_btn.addEventListener(MouseEvent.CLICK,this.GoYouLing);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         Main.allClosePanel();
         if(!only)
         {
            classRef = All_Npc.loadData.getClass("src.npc.船长界面") as Class;
            xxMov = new classRef();
            only = xxMov;
         }
         Main._stage.addChild(only);
         only.x = xx;
         only.y = yy;
         only.visible = true;
      }
      
      public static function Close() : *
      {
         if(only)
         {
            only.x = 5000;
            only.y = 5000;
            only.visible = false;
         }
      }
      
      private function CloseXX(e:*) : *
      {
         Close();
      }
      
      private function GoGo(e:*) : *
      {
         if(Main.Map0_YN == false)
         {
            Main.Map0_YN = true;
            Main.Save();
         }
         Main.water.setValue(1);
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.GameStart();
         Close();
      }
      
      private function GoYouLing(e:*) : *
      {
         Main.allClosePanel();
         Main.gameNum.setValue(7000);
         Main.gameNum2.setValue(1);
         GameData.gameLV = 1;
         Close();
         Main._this.Loading();
      }
   }
}

