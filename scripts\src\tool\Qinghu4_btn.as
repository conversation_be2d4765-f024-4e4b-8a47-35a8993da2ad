package src.tool
{
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class Qinghu4_btn extends SimpleButton
   {
      
      public function Qinghu4_btn()
      {
         super();
         this.Show();
      }
      
      private function Show() : *
      {
         if(TeShuHuoDong.TeShuHuoDongArr[10] != 0)
         {
            this.x = 5000;
            this.y = 5000;
            this.visible = false;
            removeEventListener(MouseEvent.CLICK,this.onCLICK);
         }
         else
         {
            this.x = 465;
            this.y = 150;
            this.visible = true;
            addEventListener(MouseEvent.CLICK,this.onCLICK);
         }
      }
      
      private function onCLICK(e:*) : *
      {
         var x:int = 0;
         var x2:int = 0;
         var num1:int = int(StoragePanel.storage.backOtherEmptyNum());
         var num2:int = int(StoragePanel.storage.backGemEmptyNum());
         if(num1 >= 3 && num2 >= 2)
         {
            StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63140));
            for(x = 0; x < 5; x++)
            {
               StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63155));
               StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63147));
            }
            for(x2 = 0; x2 < 2; x2++)
            {
               StoragePanel.storage.addGemStorage(GemFactory.creatGemById(33314));
            }
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功 物品已放入仓库");
            ++TeShuHuoDong.TeShuHuoDongArr[10];
            this.Show();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
         }
      }
   }
}

