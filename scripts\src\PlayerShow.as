package src
{
   import com.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import src.other.*;
   import src.tool.*;
   
   public class PlayerShow extends MovieClip
   {
      
      public var dataArr:Array = [1,1,1,[0],[0]];
      
      public var skin:Skin;
      
      public var skin_W:Skin_WuQi;
      
      public var skin_Z:Skin_ZhuangBei;
      
      public var skin_Z2:Skin_ZhuangBei;
      
      public var skin_Z3:Skin_ZhuangBei;
      
      public var skin_Z2_V:Boolean = true;
      
      public var skin_Z3_V:Boolean = true;
      
      public var headFrame:int = 1;
      
      public function PlayerShow()
      {
         super();
         mouseEnabled = mouseChildren = false;
      }
      
      public function Loading(_dataArr:Array = null) : Boolean
      {
         if(TiaoZhanPaiHang_Interface.selTypeX != 0)
         {
            return true;
         }
         this.dataArr = _dataArr;
         if(!this.dataArr || this.dataArr.length < 5)
         {
            return true;
         }
         var zhiYe:int = int(this.dataArr[0]);
         var chiBangID:int = int(this.dataArr[4][0]);
         var zhuangBeiID:int = int(this.dataArr[3][0]);
         if(zhuangBeiID != 0 && this.dataArr[3][2] == 53)
         {
            this.dataArr[3][2] = 12;
         }
         if(zhuangBeiID != 0 && !NewLoad.zhuangBeiSkin[zhiYe][zhuangBeiID] || chiBangID != 0 && !NewLoad.zhuangBeiSkin[zhiYe][chiBangID])
         {
            NewLoad.PaiHangLoad(this.dataArr);
            return false;
         }
         return true;
      }
      
      public function AddSkin() : *
      {
         var classRef:Class = null;
         var className:String = null;
         if(this.skin_Z3)
         {
            this.skin_Z3.parent.removeChild(this.skin_Z3);
            this.skin_Z3 = null;
         }
         if(this.skin)
         {
            this.skin.parent.removeChild(this.skin);
            this.skin = null;
         }
         if(this.skin_Z)
         {
            this.skin_Z.parent.removeChild(this.skin_Z);
            this.skin_Z = null;
         }
         if(this.skin_W)
         {
            this.skin_W.parent.removeChild(this.skin_W);
            this.skin_W = null;
         }
         if(!this.dataArr || this.dataArr.length < 5)
         {
            return;
         }
         this.visible = true;
         var zhiYe:int = int(this.dataArr[0]);
         var chiBangID:int = int(this.dataArr[4][0]);
         this.headFrame = this.dataArr[1];
         var zhuangBeiID:int = int(this.dataArr[3][0]);
         if(chiBangID != 0)
         {
            className = this.dataArr[4][2];
            classRef = NewLoad.zhuangBeiSkin[zhiYe][chiBangID].getClass(className) as Class;
            this.skin_Z3 = new classRef();
            this.skin_Z3.gotoAndStop("站");
            this.skin_Z3.mouseChildren = this.skin_Z3.mouseEnabled = false;
            addChild(this.skin_Z3);
         }
         var skinNumX:int = int(this.dataArr[0]);
         classRef = Player.PlayerMcArr[skinNumX].getClass("src.Skin.Skin_player" + skinNumX) as Class;
         this.skin = new classRef();
         this.skin.skinNum = skinNumX;
         this.skin.Xml = Skin.PlayerXml[skinNumX];
         this.skin.playX = this;
         this.skin.Stop();
         this.skin.mouseChildren = this.skin.mouseEnabled = false;
         addChild(this.skin);
         if(zhuangBeiID != 0)
         {
            className = this.dataArr[3][2];
            classRef = NewLoad.zhuangBeiSkin[zhiYe][zhuangBeiID].getClass(className) as Class;
            this.skin_Z = new classRef();
            this.skin_Z.gotoAndStop("站");
            this.skin_Z.mouseChildren = this.skin_Z.mouseEnabled = false;
            addChild(this.skin_Z);
         }
         classRef = Skin_WuQi.PlayerMcArr[zhiYe].getClass(this.dataArr[2]) as Class;
         this.skin_W = new classRef();
         this.skin_W.gotoAndStop("站");
         this.skin_W.mouseChildren = this.skin_W.mouseEnabled = false;
         addChild(this.skin_W);
      }
   }
}

