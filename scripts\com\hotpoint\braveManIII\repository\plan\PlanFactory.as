package com.hotpoint.braveManIII.repository.plan
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.plan.Plan;
   import src.*;
   
   public class PlanFactory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public static var JiHuaData:Array = [];
      
      public static var JiHuaData2:Array = [];
      
      public function PlanFactory()
      {
         super();
      }
      
      public static function Init_JiHuaData() : *
      {
         var i:int = 0;
         if(JiHuaData.length <= 0)
         {
            for(i = 0; i < allData.length; i++)
            {
               if((allData[i] as PlanBasicData).getGroup() <= 7)
               {
                  JiHuaData[i] = (allData[i] as PlanBasicData).creatPlan();
               }
            }
         }
      }
      
      public static function Init_JiHuaData2() : *
      {
         var i:int = 0;
         if(JiHuaData2.length <= 0)
         {
            for(i = 0; i < allData.length; i++)
            {
               if((allData[i] as PlanBasicData).getGroup() == 8)
               {
                  JiHuaData2[i] = (allData[i] as PlanBasicData).creatPlan();
               }
            }
         }
      }
      
      public static function creatPlanFactory() : *
      {
         var plan:PlanFactory = new PlanFactory();
         myXml = XMLAsset.createXML(Data2.jihua);
         plan.creatPlanFactory();
         Init_JiHuaData();
         Init_JiHuaData2();
      }
      
      public static function getPlanById(id:Number) : PlanBasicData
      {
         var pData:PlanBasicData = null;
         var data:PlanBasicData = null;
         for each(data in allData)
         {
            if(data.getId() == id)
            {
               pData = data;
            }
         }
         if(pData == null)
         {
            trace("找不到");
         }
         return pData;
      }
      
      public static function getPlanByGroup(groupid:Number, group:Number) : PlanBasicData
      {
         var pData:PlanBasicData = null;
         var data:PlanBasicData = null;
         for each(data in allData)
         {
            if(data.getGroupId() == groupid && data.getGroup() == group)
            {
               pData = data;
            }
         }
         if(pData == null)
         {
            trace("找不到");
         }
         return pData;
      }
      
      public static function findGroupId(id:Number) : Number
      {
         return getPlanById(id).getGroupId();
      }
      
      public static function findXiaoZu(id:Number) : Number
      {
         return getPlanById(id).getGroup();
      }
      
      public static function findIntroduction(id:Number) : String
      {
         return getPlanById(id).getIntroduction();
      }
      
      public static function findRewardType_1(id:Number) : Number
      {
         return getPlanById(id).getRewardType_1();
      }
      
      public static function findRewardType_2(id:Number) : Number
      {
         return getPlanById(id).getRewardType_2();
      }
      
      public static function findReward_1(id:Number) : Number
      {
         return getPlanById(id).getReward_1();
      }
      
      public static function findReward_2(id:Number) : Number
      {
         return getPlanById(id).getReward_2();
      }
      
      public static function findFrame_1(id:Number) : Number
      {
         return getPlanById(id).getFrame_1();
      }
      
      public static function findFrame_2(id:Number) : Number
      {
         return getPlanById(id).getFrame_2();
      }
      
      public static function findCount_1(id:Number) : Number
      {
         return getPlanById(id).getCount_1();
      }
      
      public static function findCount_2(id:Number) : Number
      {
         return getPlanById(id).getCount_2();
      }
      
      public static function creatPlan(id:Number) : Plan
      {
         return getPlanById(id).creatPlan();
      }
      
      private function creatPlanFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var groupid:Number = NaN;
         var group:String = null;
         var introduction:String = null;
         var rewardtype_1:Number = NaN;
         var rewardtype_2:Number = NaN;
         var reward_1:Number = NaN;
         var reward_2:Number = NaN;
         var frame_1:Number = NaN;
         var frame_2:Number = NaN;
         var count_1:Number = NaN;
         var count_2:Number = NaN;
         var data:PlanBasicData = null;
         for each(property in myXml.计划)
         {
            id = Number(property.编号);
            groupid = Number(property.小组编号);
            group = String(property.小组);
            introduction = String(property.描述);
            rewardtype_1 = Number(property.奖励类型1);
            rewardtype_2 = Number(property.奖励类型2);
            reward_1 = Number(property.奖励1);
            reward_2 = Number(property.奖励2);
            frame_1 = Number(property.帧数1);
            frame_2 = Number(property.帧数2);
            count_1 = Number(property.数量1);
            count_2 = Number(property.数量2);
            data = PlanBasicData.creatPlanBasicData(id,groupid,group,introduction,rewardtype_1,rewardtype_2,reward_1,reward_2,frame_1,frame_2,count_1,count_2);
            allData.push(data);
         }
      }
   }
}

