package com.hotpoint.braveManIII.views.suppliesShopPanel
{
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.ui.*;
   import src.*;
   
   public class SuppliesShopPanel extends MovieClip
   {
      
      private static var yaopin:Supplies;
      
      public static var suppliesShowPanel:MovieClip;
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var dragObj:MovieClip;
      
      public static var clickObj:MovieClip;
      
      private static var pointx:Number;
      
      private static var pointy:Number;
      
      private static var oldNum:Number;
      
      private static var chosen:Supplies;
      
      public static var ssp:SuppliesShopPanel;
      
      public static var myPlayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      public static var isDown:Boolean = false;
      
      public static var returnx:Number = 0;
      
      public static var returny:Number = 0;
      
      public static var yaoOK:Boolean = false;
      
      public static var shopSuppliesList:Array = SuppliesFactory.createSuppliesByShop();
      
      private static var yeshu:int = 1;
      
      private static var clickTime:int = 0;
      
      private static var myMenu:ContextMenu = new ContextMenu();
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "NewYaoShop_v2.swf";
      
      public function SuppliesShopPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!suppliesShowPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var mm:MovieClip = null;
         var num:int = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = suppliesShowPanel.getChildIndex(suppliesShowPanel["s1_" + i]);
            mm.x = suppliesShowPanel["s1_" + i].x;
            mm.y = suppliesShowPanel["s1_" + i].y;
            mm.name = "s1_" + i;
            suppliesShowPanel.removeChild(suppliesShowPanel["s1_" + i]);
            suppliesShowPanel["s1_" + i] = mm;
            suppliesShowPanel.addChild(mm);
            suppliesShowPanel.setChildIndex(mm,num);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("NewYaoShow") as Class;
         suppliesShowPanel = new classRef();
         ssp.addChild(suppliesShowPanel);
         InitIcon();
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         ssp = new SuppliesShopPanel();
         LoadSkin();
         Main._stage.addChild(ssp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         ssp = new SuppliesShopPanel();
         Main._stage.addChild(ssp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(suppliesShowPanel)
         {
            Main.stopXX = true;
            ssp.x = 0;
            ssp.y = 0;
            myPlayer = Main.player1;
            addListenerP1();
            ssp.visible = true;
            JiHua_Interface.ppp1_12 = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(suppliesShowPanel)
         {
            ssp.visible = false;
            Main.stopXX = false;
            removeListenerP1();
         }
         else
         {
            InitClose();
         }
      }
      
      private static function CloseYP(e:MouseEvent) : void
      {
         close();
      }
      
      public static function addListenerP1() : *
      {
         var j:uint = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            suppliesShowPanel["s1_" + i].mouseChildren = false;
            suppliesShowPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            suppliesShowPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            suppliesShowPanel["s1_" + i].addEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(j = 0; j < 6; j++)
         {
            suppliesShowPanel["se_" + j]["buy_btn"].addEventListener(MouseEvent.CLICK,BuySuppies);
            suppliesShowPanel["se_" + j]["buy_btn"].addEventListener(MouseEvent.MOUSE_OVER,BuyOVER);
            suppliesShowPanel["se_" + j]["buy_btn"].addEventListener(MouseEvent.MOUSE_OUT,BuyOUT);
            suppliesShowPanel["se_" + j].mouseEnabled = false;
         }
         suppliesShowPanel["mySell"]["sell_btn"].addEventListener(MouseEvent.CLICK,sellSupplies);
         suppliesShowPanel["mySell"]["key_btn1"].addEventListener(MouseEvent.CLICK,setKey_1);
         suppliesShowPanel["mySell"]["key_btn2"].addEventListener(MouseEvent.CLICK,setKey_2);
         suppliesShowPanel["mySell"]["key_btn3"].addEventListener(MouseEvent.CLICK,setKey_3);
         for(j = 0; j < 6; j++)
         {
            suppliesShowPanel["se_" + j].stop();
         }
         if(Main.P1P2)
         {
            suppliesShowPanel["bag1"].visible = true;
            suppliesShowPanel["bag2"].visible = true;
            suppliesShowPanel["xbag1"].visible = true;
            suppliesShowPanel["xbag2"].visible = true;
            suppliesShowPanel["bag1"].addEventListener(MouseEvent.CLICK,changeToOne);
            suppliesShowPanel["bag2"].addEventListener(MouseEvent.CLICK,changeToTwo);
         }
         else
         {
            suppliesShowPanel["bag1"].visible = false;
            suppliesShowPanel["bag2"].visible = false;
            suppliesShowPanel["xbag1"].visible = false;
            suppliesShowPanel["xbag2"].visible = false;
         }
         suppliesShowPanel["NoMoney_mc"]["no_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         suppliesShowPanel["NoMoney_mc"]["yes_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         suppliesShowPanel["NoMoney_mc"]["addMoney_btn"].addEventListener(MouseEvent.CLICK,addMoney_btn);
         suppliesShowPanel["leftPage"].addEventListener(MouseEvent.CLICK,upPage);
         suppliesShowPanel["rightPage"].addEventListener(MouseEvent.CLICK,downPage);
         suppliesShowPanel["closeYP"].addEventListener(MouseEvent.CLICK,CloseYP);
         suppliesShowPanel["mySell"].visible = false;
         suppliesShowPanel["NoMoney_mc"].visible = false;
         suppliesShowPanel["touming"].visible = false;
         suppliesShowPanel["goumaitishi"].mouseEnabled = false;
         suppliesShowPanel["npc_mc"].stop();
         allShow();
      }
      
      public static function removeListenerP1() : *
      {
         var j:uint = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            suppliesShowPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            suppliesShowPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            suppliesShowPanel["s1_" + i].removeEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(j = 0; j < 6; j++)
         {
            suppliesShowPanel["se_" + j]["buy_btn"].removeEventListener(MouseEvent.CLICK,BuySuppies);
            suppliesShowPanel["se_" + j]["buy_btn"].removeEventListener(MouseEvent.MOUSE_OVER,BuyOVER);
            suppliesShowPanel["se_" + j]["buy_btn"].removeEventListener(MouseEvent.MOUSE_OUT,BuyOUT);
         }
         suppliesShowPanel["mySell"]["sell_btn"].removeEventListener(MouseEvent.CLICK,sellSupplies);
         suppliesShowPanel["mySell"]["key_btn1"].removeEventListener(MouseEvent.CLICK,setKey_1);
         suppliesShowPanel["mySell"]["key_btn2"].removeEventListener(MouseEvent.CLICK,setKey_2);
         suppliesShowPanel["mySell"]["key_btn3"].removeEventListener(MouseEvent.CLICK,setKey_3);
         for(j = 0; j < 6; j++)
         {
            suppliesShowPanel["se_" + j].stop();
         }
         if(Main.P1P2)
         {
            suppliesShowPanel["bag1"].removeEventListener(MouseEvent.CLICK,changeToOne);
            suppliesShowPanel["bag2"].removeEventListener(MouseEvent.CLICK,changeToTwo);
         }
         suppliesShowPanel["leftPage"].removeEventListener(MouseEvent.CLICK,upPage);
         suppliesShowPanel["rightPage"].removeEventListener(MouseEvent.CLICK,downPage);
         suppliesShowPanel["closeYP"].removeEventListener(MouseEvent.CLICK,CloseYP);
      }
      
      private static function closeNORMB(e:*) : void
      {
         suppliesShowPanel["NoMoney_mc"].visible = false;
      }
      
      private static function addMoney_btn(e:*) : void
      {
         Main.ChongZhi();
      }
      
      private static function changeToOne(e:*) : *
      {
         myPlayer = Main.player1;
         allShow();
      }
      
      private static function changeToTwo(e:*) : *
      {
         myPlayer = Main.player2;
         allShow();
      }
      
      private static function upPage(e:*) : *
      {
         if(yeshu > 1)
         {
            --yeshu;
         }
         allShow();
      }
      
      private static function downPage(e:*) : *
      {
         var arr:Array = shopSuppliesList;
         if(yeshu < Math.ceil(arr.length / 6))
         {
            ++yeshu;
         }
         allShow();
      }
      
      public static function allShow() : void
      {
         var i:uint = 0;
         var temp:int = 0;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 0)
         {
            suppliesShowPanel["npc_mc"].gotoAndStop(1);
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 3)
         {
            suppliesShowPanel["npc_mc"].gotoAndStop(2);
         }
         else if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() == 4)
         {
            suppliesShowPanel["npc_mc"].gotoAndStop(3);
         }
         if(Main.P1P2)
         {
            if(myPlayer == Main.player1)
            {
               suppliesShowPanel["bag1"].visible = false;
               suppliesShowPanel["bag2"].visible = true;
            }
            else
            {
               suppliesShowPanel["bag1"].visible = true;
               suppliesShowPanel["bag2"].visible = false;
            }
         }
         suppliesShowPanel["gold_txt"].text = myPlayer.getGold();
         suppliesShowPanel["kill_txt"].text = myPlayer.getKillPoint();
         suppliesShowPanel["rmb_txt"].text = Shop4399.moneyAll.getValue();
         myPlayer.getBag().zhengliBagS();
         for(i = 0; i < 24; i++)
         {
            suppliesShowPanel["s1_" + i]["t_txt"].visible = false;
            if(myPlayer.getBag().getSuppliesFromBag(i) != null)
            {
               suppliesShowPanel["s1_" + i].gotoAndStop(myPlayer.getBag().getSuppliesFromBag(i).getFrame());
               suppliesShowPanel["s1_" + i].visible = true;
               if(myPlayer.getBag().getSuppliesFromBag(i).getTimes() > 1)
               {
                  suppliesShowPanel["s1_" + i]["t_txt"].text = myPlayer.getBag().getSuppliesFromBag(i).getTimes();
                  suppliesShowPanel["s1_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               suppliesShowPanel["s1_" + i].visible = false;
            }
         }
         var showNum:int = 6;
         var arr:Array = shopSuppliesList;
         suppliesShowPanel["pageNum"].text = yeshu + "/" + Math.ceil(arr.length / 6);
         for(i = 0; i < 6; i++)
         {
            temp = i + (yeshu - 1) * 6;
            if(arr[temp] != null)
            {
               suppliesShowPanel["se_" + i].visible = true;
               suppliesShowPanel["se_" + i].gotoAndStop(arr[temp].getFrame());
            }
            else
            {
               suppliesShowPanel["se_" + i].visible = false;
            }
         }
      }
      
      private static function menuOpen(e:MouseEvent) : void
      {
         itemsTooltip.visible = false;
         clickObj = e.target as MovieClip;
         suppliesShowPanel.addChild(suppliesShowPanel["mySell"]);
         suppliesShowPanel["mySell"].visible = true;
         suppliesShowPanel["mySell"].x = clickObj.x + 50;
         suppliesShowPanel["mySell"].y = clickObj.y + 60;
      }
      
      private static function sellSupplies(e:MouseEvent) : void
      {
         var num:int = int(clickObj.name.substr(3,2));
         var str:String = clickObj.name.substr(0,2);
         if(str == "s1")
         {
            myPlayer.addGold(myPlayer.getBag().getSuppliesFromBag(num).getPrice());
            myPlayer.getBag().delSupplies(num);
         }
         suppliesShowPanel["mySell"].visible = false;
         allShow();
      }
      
      public static function setKey_1(e:MouseEvent) : *
      {
         var su:Supplies = null;
         var num:int = int(clickObj.name.substr(3,2));
         var str:String = clickObj.name.substr(0,2);
         if(str == "s1")
         {
            su = myPlayer.getBag().getSuppliesFromBag(num);
            myPlayer.getSuppliesSlot().setToSuppliesSlot(su,0);
         }
         suppliesShowPanel["mySell"].visible = false;
      }
      
      public static function setKey_2(e:MouseEvent) : *
      {
         var su:Supplies = null;
         var num:int = int(clickObj.name.substr(3,2));
         var str:String = clickObj.name.substr(0,2);
         if(str == "s1")
         {
            su = myPlayer.getBag().getSuppliesFromBag(num);
            myPlayer.getSuppliesSlot().setToSuppliesSlot(su,1);
         }
         suppliesShowPanel["mySell"].visible = false;
      }
      
      public static function setKey_3(e:MouseEvent) : *
      {
         var su:Supplies = null;
         var num:int = int(clickObj.name.substr(3,2));
         var str:String = clickObj.name.substr(0,2);
         if(str == "s1")
         {
            su = myPlayer.getBag().getSuppliesFromBag(num);
            myPlayer.getSuppliesSlot().setToSuppliesSlot(su,2);
         }
         suppliesShowPanel["mySell"].visible = false;
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         suppliesShowPanel["mySell"].visible = false;
         suppliesShowPanel.addChild(itemsTooltip);
         var overNum:uint = uint(e.target.name.substr(3,2));
         var str:String = e.target.name.substr(0,2);
         itemsTooltip.x = suppliesShowPanel.mouseX;
         itemsTooltip.y = suppliesShowPanel.mouseY;
         if(str == "s1")
         {
            if(myPlayer.getBag().getSuppliesFromBag(overNum) != null)
            {
               itemsTooltip.suppliesTooltip(myPlayer.getBag().getSuppliesFromBag(overNum),1);
            }
            itemsTooltip.visible = true;
         }
      }
      
      private static function tooltipClose(e:MouseEvent) : void
      {
         itemsTooltip.visible = false;
      }
      
      private static function BuyOVER(e:MouseEvent) : void
      {
         var num:int = int(e.target.parent.name.substr(3,2));
         var arr:Array = shopSuppliesList;
         var temp:int = num + (yeshu - 1) * 6;
         suppliesShowPanel["goumaitishi"].visible = true;
         suppliesShowPanel["goumaitishi"].x = suppliesShowPanel.mouseX + 10;
         suppliesShowPanel["goumaitishi"].y = suppliesShowPanel.mouseY;
         suppliesShowPanel["goumaitishi"]["rmb_txt"].text = "消耗" + arr[temp].getPrice() * 2 + "金币，金币不足时可用" + (arr[temp] as Supplies).getRmbPrice() + "点卷购买";
      }
      
      private static function BuyOUT(e:MouseEvent) : void
      {
         suppliesShowPanel["goumaitishi"].visible = false;
      }
      
      private static function BuySuppies(e:MouseEvent) : void
      {
         var num:int = int(e.target.parent.name.substr(3,2));
         var arr:Array = shopSuppliesList;
         var temp:int = num + (yeshu - 1) * 6;
         yaopin = (arr[temp] as Supplies).getClone();
         if(myPlayer.getBag().backSuppliesBagNum() > 0)
         {
            if(myPlayer.getGold() >= arr[temp].getPrice() * 2)
            {
               myPlayer.getBag().addSuppliesBag((arr[temp] as Supplies).getClone());
               myPlayer.payGold(arr[temp].getPrice() * 2);
            }
            else if(Shop4399.moneyAll.getValue() >= (arr[temp] as Supplies).getRmbPrice())
            {
               Api_4399_All.BuyObj((arr[temp] as Supplies).getRmbId());
               yaoOK = true;
               suppliesShowPanel["touming"].visible = true;
            }
            else
            {
               suppliesShowPanel["NoMoney_mc"].visible = true;
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
         }
         allShow();
      }
      
      public static function yaoRMBOK() : *
      {
         if(yaoOK)
         {
            myPlayer.getBag().addSuppliesBag(yaopin);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功");
            yaoOK = false;
            allShow();
            suppliesShowPanel["touming"].visible = false;
         }
      }
   }
}

