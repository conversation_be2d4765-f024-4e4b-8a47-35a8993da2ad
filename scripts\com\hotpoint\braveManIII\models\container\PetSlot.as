package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.pet.*;
   
   public class PetSlot
   {
      
      private var _mode:Boolean = false;
      
      private var _jilu:int = -1;
      
      private var _slot:Array = new Array();
      
      private var _slotLimit:VT = VT.createVT(5);
      
      public function PetSlot()
      {
         super();
      }
      
      public static function createPetSlot() : PetSlot
      {
         var ps:PetSlot = new PetSlot();
         for(var i:int = 0; i < 50; i++)
         {
            ps._slot[i] = null;
         }
         return ps;
      }
      
      public function get slotLimit() : VT
      {
         return this._slotLimit;
      }
      
      public function set slotLimit(value:VT) : void
      {
         this._slotLimit = value;
      }
      
      public function get slot() : Array
      {
         return this._slot;
      }
      
      public function set slot(value:Array) : void
      {
         this._slot = value;
      }
      
      public function get jilu() : int
      {
         return this._jilu;
      }
      
      public function set jilu(value:int) : void
      {
         this._jilu = value;
      }
      
      public function get mode() : Boolean
      {
         return this._mode;
      }
      
      public function set mode(value:Boolean) : void
      {
         this._mode = value;
      }
      
      public function getJilu() : int
      {
         return this._jilu;
      }
      
      public function setJilu(value:int) : void
      {
         this._jilu = value;
      }
      
      public function getMode() : Boolean
      {
         return this._mode;
      }
      
      public function setMode(value:Boolean) : void
      {
         this._mode = value;
      }
      
      public function getPetFromSlot(num:Number) : Pet
      {
         if(this._slot[num] != null)
         {
            return this._slot[num];
         }
         return null;
      }
      
      public function addPetSlot(value:Pet) : Boolean
      {
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] == null)
            {
               this._slot[i] = value;
               return true;
            }
         }
         return false;
      }
      
      public function delPetSlot(num:int) : Pet
      {
         var pet:Pet = null;
         if(this._slot[num] != null)
         {
            pet = this._slot[num];
            this._slot[num] = null;
         }
         return pet;
      }
      
      public function getPetSlotNum() : int
      {
         return this._slotLimit.getValue();
      }
      
      public function backPetSlotNum() : int
      {
         if(this._slotLimit.getValue() == 3)
         {
            this._slotLimit.setValue(5);
         }
         var num:int = 0;
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function backPetNum() : int
      {
         var num:int = 0;
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] != null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function addPetSlotNum(num:int) : *
      {
         this._slotLimit.setValue(this._slotLimit.getValue() + num);
      }
      
      public function plan6_3() : Boolean
      {
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Pet).getWX() > 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_3() : Boolean
      {
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Pet).id.getValue() == 73101 || (this._slot[i] as Pet).id.getValue() == 73501 || (this._slot[i] as Pet).id.getValue() == 73103 || (this._slot[i] as Pet).id.getValue() == 73104)
               {
                  return true;
               }
            }
         }
         return false;
      }
   }
}

