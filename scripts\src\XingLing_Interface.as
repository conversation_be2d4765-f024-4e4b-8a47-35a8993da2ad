package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src._data.*;
   import src.tool.*;
   
   public class XingLing_Interface extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      private static var _this:XingLing_Interface;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "XLUP_v972.swf";
      
      public static var selNumMax:uint = 12;
      
      public static var sel_LV_NumMax:uint = 10;
      
      public static var UP_Fun_YES_go:Boolean = false;
      
      public static var UP_Fun_YES_go2:Boolean = false;
      
      public static var UP_Fun_YES_go3:Boolean = false;
      
      public static var upOK:Boolean = false;
      
      public static var saveOK:Boolean = false;
      
      private var skin:MovieClip;
      
      private var xuQiuObjArr:Array = [0,63162,63169,63171,63183,63197,63207,63246,63257,63268,63273,63337,63340];
      
      private var xArr:Array = ["","巨蟹座能源: ","双鱼座能源: ","水瓶座能源: ","射手座能源: ","白羊座能源: ","金牛座能源: ","天蝎座能源: ","狮子座能源: ","处女座能源: ","天秤座能源: ","双子座能源: ","魔羯座能源: "];
      
      private var selNum:VT = VT.createVT(1);
      
      private var sel_LV_Num:VT = VT.createVT(1);
      
      private var up_Money_1:Boolean = false;
      
      private var up_Money_2:Boolean = false;
      
      internal var time:uint = 0;
      
      public function XingLing_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var temp:XingLing_Interface = new XingLing_Interface();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
         }
      }
      
      public static function UP_Fun_YES() : *
      {
         var i:uint = 0;
         if(UP_Fun_YES_go)
         {
            UP_Fun_YES_go = false;
            i = uint(XingLingFactory.UP(_this.selNum.getValue(),_this.sel_LV_Num.getValue()));
            _this.skin._BLACK_mc.visible = false;
            _this.Show();
            _this.ShowSanSuo(i);
         }
      }
      
      public static function UP2_Fun_YES() : *
      {
         var tempArr:Array = null;
         var lv:uint = 0;
         var num:uint = 0;
         var next:uint = 0;
         if(UP_Fun_YES_go3)
         {
            UP_Fun_YES_go3 = false;
            XingLingFactory.UP2(_this.selNum.getValue(),_this.sel_LV_Num.getValue());
            tempArr = XingLingFactory.Get_LV(_this.selNum.getValue(),_this.sel_LV_Num.getValue());
            lv = uint(tempArr[0]);
            num = uint(tempArr[1]);
            next = _this.sel_LV_Num.getValue() + 1;
            if(num == 10 && next <= sel_LV_NumMax)
            {
               _this.sel_LV_Num.setValue(next);
               _this.up_Money_1 = _this.up_Money_2 = false;
            }
            _this.skin._BLACK_mc.visible = false;
            _this.Show();
         }
      }
      
      public static function UpPinZhi_Fun_YES() : *
      {
         if(UP_Fun_YES_go2)
         {
            upOK = XingLingFactory.QiangHua(_this.selNum.getValue(),_this.sel_LV_Num.getValue());
            UP_Fun_YES_go2 = false;
            _this.ShowSanSuo(0);
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("Skin") as Class;
         this.skin = new classRef();
         this.skin.addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addChild(this.skin);
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(e:* = null) : *
      {
         this.visible = false;
         this.x = this.y = 5000;
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         var i:uint = 0;
         this.skin._BLACK_mc.visible = false;
         this.skin._jinJieXuQiu_mc.visible = false;
         this.skin._jinJieXuQiu_mc.close_btn.addEventListener(MouseEvent.CLICK,this.JinJieClose);
         this.skin._jinJieXuQiu_mc.ok_btn.addEventListener(MouseEvent.CLICK,this.JinJieOK);
         this.skin._jinJieXuQiu_mc.dianQuan_btn.addEventListener(MouseEvent.CLICK,this.JinJieOK2);
         this.skin._jinJieXuQiu_mc.dianQuan_btn.addEventListener(MouseEvent.MOUSE_MOVE,this.JinJieOK2_MOUSE_MOVE);
         this.skin._jinJieXuQiu_mc.dianQuan_btn.addEventListener(MouseEvent.MOUSE_OUT,this.JinJieOK2_MOUSE_OUT);
         this.skin.close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         for(i = 1; i <= selNumMax; i++)
         {
            this.skin["sel_btn_" + i].addEventListener(MouseEvent.CLICK,this.SEL_XingZuo_FUN);
         }
         for(i = 1; i <= sel_LV_NumMax; i++)
         {
            this.skin["sel2_" + i].visible = false;
            this.skin["sel2_" + i].mouseChildren = false;
            this.skin["sel2_" + i].addEventListener(MouseEvent.CLICK,this.SEL_LV_FUN);
            this.skin["sel2_" + i].addEventListener(MouseEvent.MOUSE_MOVE,this.SEL_LV_FUN_MOUSE_MOVE);
            this.skin["sel2_" + i].addEventListener(MouseEvent.MOUSE_OUT,this.SEL_LV_FUN_MOUSE_OUT);
         }
         this.skin["up_btn"].addEventListener(MouseEvent.CLICK,this.UP_Fun);
         this.skin["up2_btn"].addEventListener(MouseEvent.CLICK,this.UP_Fun2);
         this.skin["A_mc"].addEventListener(MouseEvent.CLICK,this.Sel_Money_A);
         this.skin["B_mc"].addEventListener(MouseEvent.CLICK,this.Sel_Money_B);
         this.skin["C_mc"].addEventListener(MouseEvent.CLICK,this.Sel_Money_C);
         this.skin["qiangHua_btn"].addEventListener(MouseEvent.CLICK,this.QiangHua_Fun);
      }
      
      private function Sel_Money_A(e:* = null) : *
      {
         this.up_Money_1 = this.up_Money_2 = true;
         this.skin["A_mc"]["yes_mc"].visible = true;
         this.skin["B_mc"]["yes_mc"].visible = false;
         this.skin["C_mc"]["yes_mc"].visible = false;
      }
      
      private function Sel_Money_B(e:* = null) : *
      {
         this.up_Money_1 = this.up_Money_2 = false;
         this.skin["A_mc"]["yes_mc"].visible = false;
         this.skin["B_mc"]["yes_mc"].visible = true;
         this.skin["C_mc"]["yes_mc"].visible = true;
      }
      
      private function Sel_Money_C(e:* = null) : *
      {
         this.up_Money_1 = this.up_Money_2 = false;
         this.skin["A_mc"]["yes_mc"].visible = false;
         this.skin["B_mc"]["yes_mc"].visible = true;
         this.skin["C_mc"]["yes_mc"].visible = true;
      }
      
      private function SelObj(lv:uint) : *
      {
         if(lv == 10)
         {
            this.skin["B_mc"].visible = false;
            this.skin["C_mc"].visible = true;
         }
         else
         {
            this.skin["B_mc"].visible = true;
            this.skin["C_mc"].visible = false;
         }
         if(Boolean(this.up_Money_1) || Boolean(this.up_Money_2))
         {
            this.Sel_Money_A();
         }
         else
         {
            this.Sel_Money_B();
            this.Sel_Money_C();
         }
      }
      
      private function Show() : *
      {
         var i:uint = 0;
         var lvNum:uint = 0;
         if(Main.guanKa[this.selNum.getValue() + 17] > 0)
         {
            this.skin["back_mc"].visible = false;
         }
         else
         {
            this.skin["back_mc"].visible = true;
         }
         for(i = 1; i <= selNumMax; i++)
         {
            this.skin["sel_" + i].visible = false;
            if(i == this.selNum.getValue())
            {
               this.skin["sel_" + i].visible = true;
            }
            if(Main.guanKa[i + 17] > 0)
            {
               this.skin["no_Sel_" + i].visible = false;
            }
         }
         var textArr:Array = XingLingFactory.Get_LV_Data_Str(this.selNum.getValue(),this.sel_LV_Num.getValue());
         var tempArr:Array = XingLingFactory.Get_LV(this.selNum.getValue(),this.sel_LV_Num.getValue());
         var frame:uint = tempArr[0] + 1;
         var lv:uint = uint(tempArr[1]);
         this.skin["qiangHua_btn"].visible = false;
         if(lv == 10)
         {
            this.skin["up_btn"].visible = false;
            lvNum = uint(XingLingFactory.Get_one_color(this.selNum.getValue(),this.sel_LV_Num.getValue()));
            if(lvNum < 5 || Boolean(Main.isVip()) && lvNum < 6)
            {
               this.skin["qiangHua_btn"].visible = true;
            }
         }
         else
         {
            this.skin["up_btn"].visible = true;
         }
         for(i = 1; i <= 10; i++)
         {
            this.skin["shuXing_" + i].text = textArr[i];
            this.TextColor(this.skin["shuXing_" + i],frame);
            if(lv >= i)
            {
               this.skin["shuXing_mc_" + i].gotoAndStop(frame);
            }
            else
            {
               this.skin["shuXing_mc_" + i].gotoAndStop(1);
            }
         }
         var XXXX:uint = uint(XingLingFactory.Get_LV2(this.selNum.getValue()));
         var color:Array = XingLingFactory.Get_LV_color(this.selNum.getValue());
         for(i = 1; i <= sel_LV_NumMax; i++)
         {
            this.skin["sel2_" + i]["sel_this"].visible = false;
            if(i <= XXXX)
            {
               this.skin["sel2_" + i].visible = true;
            }
            else
            {
               this.skin["sel2_" + i].visible = false;
            }
            this.skin["sel2_" + i].gotoAndStop(color[i] - 1);
         }
         this.skin["sel2_" + this.sel_LV_Num.getValue()]["sel_this"].visible = true;
         this.skin["up2_btn"].visible = false;
         if(this.sel_LV_Num.getValue() + 1 <= sel_LV_NumMax && color[this.sel_LV_Num.getValue() + 1] <= 1)
         {
            this.skin["up2_btn"].visible = true;
         }
         this.XuQiuShow(lv);
         this.SelObj(lv);
         this.ShowTolal_Data();
      }
      
      private function XuQiuShow(lv:uint) : *
      {
         var str:String = null;
         var arr2:Array = null;
         var arr:Array = null;
         var id:* = undefined;
         var killNum:uint = 0;
         str = this.xArr[this.selNum.getValue()];
         if(lv == 10)
         {
            arr2 = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
            this.skin["C_mc"]._txt.text = "星源方块:1" + "/" + this.getXuQiuNum_Other(63235);
            this.skin["A_mc"]._txt.text = "点券:9/" + Shop4399.moneyAll.getValue();
         }
         else
         {
            arr = XingLingFactory.Sel_XuQiu_up1(this.selNum.getValue());
            id = this.xuQiuObjArr[this.selNum.getValue()];
            killNum = uint(arr[1]);
            if(Main.isVip())
            {
               killNum /= 2;
            }
            this.skin["B_mc"]._txt.text = str + arr[0] + "/" + this.getXuQiuNum_Other(id) + "  击杀点:" + killNum + "/" + this.getXuQiuNumKill_min();
            this.skin["A_mc"]._txt.text = "点券:" + arr[2] + "/" + Shop4399.moneyAll.getValue();
         }
      }
      
      private function TextColor(t:*, col:uint) : *
      {
         if(col == 3)
         {
            TextTool.ColorX(t,"0x66FF00");
         }
         else if(col == 4)
         {
            TextTool.ColorX(t,"0x3399FF");
         }
         else if(col == 5)
         {
            TextTool.ColorX(t,"0xFF3399");
         }
         else if(col == 6)
         {
            TextTool.ColorX(t,"0xFFCC00");
         }
         else if(col == 7)
         {
            TextTool.ColorX(t,"0xFF0000");
         }
      }
      
      private function ShowTolal_Data() : *
      {
         var arr:Array = XingLingFactory.GetTolal_DataStr();
         for(var i:uint = 1; i <= 8; i++)
         {
            this.skin["shuXingNum_" + i].text = arr[i];
            this.skin["shuXingNum_2" + i].text = arr[i + 8];
            this.skin["jinSe_txt"].text = "已点亮" + XingLingFactory.dianLiangNum * 10 + "颗";
            this.skin["jindu"]["_mc"].scaleX = XingLingFactory.dianLiangNumX;
         }
      }
      
      private function ShowSanSuo(num:uint = 1) : *
      {
         var i:uint = 0;
         if(num == -1)
         {
            for(i = 1; i <= 10; i++)
            {
               this.skin["shuXing_mc_" + i]["show_mc"].gotoAndStop(1);
            }
         }
         else if(num == 0)
         {
            for(i = 1; i <= 10; i++)
            {
               this.skin["shuXing_mc_" + i]["show_mc"].gotoAndPlay(2);
            }
         }
         else
         {
            this.skin["shuXing_mc_" + num]["show_mc"].gotoAndPlay(2);
         }
      }
      
      private function SEL_XingZuo_FUN(e:MouseEvent) : *
      {
         var i:uint = uint((e.target.name as String).substr(8,2));
         this.selNum.setValue(i);
         this.sel_LV_Num.setValue(1);
         _this.up_Money_1 = _this.up_Money_2 = false;
         this.Show();
      }
      
      private function SEL_LV_FUN(e:MouseEvent) : *
      {
         for(var i:uint = 1; i <= sel_LV_NumMax; i++)
         {
            this.skin["sel2_" + i]["sel_this"].visible = false;
         }
         var i2:uint = uint((e.target.name as String).substr(5,2));
         this.sel_LV_Num.setValue(i2);
         this.skin["sel2_" + i2]["sel_this"].visible = true;
         this.Show();
      }
      
      private function SEL_LV_FUN_MOUSE_OUT(e:MouseEvent) : *
      {
         var i2:uint = uint((e.target.name as String).substr(5,2));
         if(this.sel_LV_Num.getValue() != i2)
         {
            this.skin["sel2_" + i2]["sel_this"].visible = false;
         }
      }
      
      private function SEL_LV_FUN_MOUSE_MOVE(e:MouseEvent) : *
      {
         var i2:uint = uint((e.target.name as String).substr(5,2));
         this.skin["sel2_" + i2]["sel_this"].visible = true;
      }
      
      private function UP_Fun(e:MouseEvent) : *
      {
         var id:* = undefined;
         var arr1:Array = null;
         var killNum:uint = 0;
         var arr:Array = XingLingFactory.Sel_XuQiu_up1(this.selNum.getValue());
         if(this.up_Money_1)
         {
            if(Shop4399.moneyAll.getValue() >= arr[2])
            {
               this.skin._BLACK_mc.visible = true;
               Api_4399_All.BuyObj(arr[3]);
               UP_Fun_YES_go = true;
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
            }
         }
         else
         {
            id = this.xuQiuObjArr[this.selNum.getValue()];
            arr1 = this.XuQiuNum_Other(id,arr[0]);
            killNum = uint(arr[1]);
            if(Main.isVip())
            {
               killNum /= 2;
            }
            if(arr1[0] != 0 && Boolean(this.XuQiuNum_kill(killNum)))
            {
               this.KouChuObj(arr1);
               this.KouChukill(killNum);
               UP_Fun_YES_go = true;
               UP_Fun_YES();
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"材料不足,可选择点券升级");
            }
         }
      }
      
      private function UP_Fun2(e:MouseEvent) : *
      {
         var str:String = null;
         this.skin._jinJieXuQiu_mc.visible = true;
         this.skin._jinJieXuQiu_mc.dianQuan_mc.visible = false;
         var arr:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         str = this.xArr[this.selNum.getValue()];
         var id:* = this.xuQiuObjArr[this.selNum.getValue()];
         this.skin._jinJieXuQiu_mc.xuQiu_num1.text = str + arr[0] + "/" + this.getXuQiuNum_Other(id);
         this.skin._jinJieXuQiu_mc.xuQiu_num2.text = "圣光水晶: " + arr[1] + "/" + this.getXuQiuNum_Other(63138);
      }
      
      private function JinJieClose(e:MouseEvent) : *
      {
         this.skin._jinJieXuQiu_mc.visible = false;
      }
      
      private function JinJieOK(e:MouseEvent) : *
      {
         this.skin._jinJieXuQiu_mc.visible = false;
         var id:* = this.xuQiuObjArr[this.selNum.getValue()];
         var arr:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         var arr1:Array = this.XuQiuNum_Other(id,arr[0]);
         var arr2:Array = this.XuQiuNum_Other(63138,arr[1]);
         if(arr1[0] != 0 && arr2[0] != 0)
         {
            this.KouChuObj(arr1);
            this.KouChuObj(arr2);
            UP_Fun_YES_go3 = true;
            UP2_Fun_YES();
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"材料不足");
         }
      }
      
      private function JinJieOK2(e:MouseEvent) : *
      {
         this.skin._jinJieXuQiu_mc.visible = false;
         var id:* = this.xuQiuObjArr[this.selNum.getValue()];
         var arr:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         var num:uint = uint(arr[2]);
         if(Shop4399.moneyAll.getValue() >= arr[2])
         {
            this.skin._BLACK_mc.visible = true;
            Api_4399_All.BuyObj(arr[3]);
            UP_Fun_YES_go3 = true;
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,90,0,true,2,"点券不足");
         }
      }
      
      private function JinJieOK2_MOUSE_MOVE(e:*) : *
      {
         this.skin._jinJieXuQiu_mc.dianQuan_mc.visible = true;
         var id:* = this.xuQiuObjArr[this.selNum.getValue()];
         var arr:Array = XingLingFactory.Sel_XuQiu_up2(this.selNum.getValue());
         var num:uint = uint(arr[2]);
         this.skin._jinJieXuQiu_mc.dianQuan_mc.dianQuan_txt.text = "需消耗" + num + "点券";
      }
      
      private function JinJieOK2_MOUSE_OUT(e:*) : *
      {
         this.skin._jinJieXuQiu_mc.dianQuan_mc.visible = false;
      }
      
      private function QiangHua_Fun(e:MouseEvent) : *
      {
         var money:uint = 0;
         var moneyID:uint = 0;
         var arr1:Array = null;
         this.skin["qiangHua_btn"].visible = false;
         this.skin["_BLACK_mc"].visible = true;
         if(this.up_Money_1)
         {
            money = uint(InitData.xingLing_num9.getValue());
            moneyID = uint(InitData.xingLing_ID55.getValue());
            if(Shop4399.moneyAll.getValue() >= money)
            {
               this.skin._BLACK_mc.visible = true;
               Api_4399_All.BuyObj(moneyID);
               UP_Fun_YES_go2 = true;
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
               this.skin["qiangHua_btn"].visible = true;
               this.skin._BLACK_mc.visible = false;
            }
         }
         else
         {
            arr1 = this.XuQiuNum_Other(63235,InitData.BuyNum_1.getValue());
            if(arr1[0] != 0)
            {
               this.KouChuObj(arr1);
               UP_Fun_YES_go2 = true;
               UpPinZhi_Fun_YES();
               Main.Save();
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"材料不足,可选择点券升级");
               this.skin["qiangHua_btn"].visible = true;
               this.skin._BLACK_mc.visible = false;
            }
         }
         addEventListener(Event.ENTER_FRAME,this.onTime);
      }
      
      private function getXuQiuNumKill_min() : uint
      {
         var xx:uint = 0;
         xx = uint(Main.player1.killPoint.getValue());
         if(Boolean(Main.P1P2) && xx > Main.player2.killPoint.getValue())
         {
            xx = uint(Main.player2.killPoint.getValue());
         }
         return xx;
      }
      
      private function getXuQiuNum_Other(id:uint) : uint
      {
         var xx:uint = 0;
         xx += Main.player1.getBag().getOtherobjNum(id);
         if(Main.P1P2)
         {
            xx += Main.player2.getBag().getOtherobjNum(id);
         }
         return xx;
      }
      
      private function XuQiuNum_Other(id:uint, num:uint) : Array
      {
         var p1Num:uint = 0;
         var p2Num:uint = 0;
         var xx:uint = 0;
         if(Main.player1.getBag().getOtherobjNum(id) >= num)
         {
            return [id,num,0];
         }
         if(Main.P1P2)
         {
            p1Num = uint(Main.player1.getBag().getOtherobjNum(id));
            p2Num = num - Main.player1.getBag().getOtherobjNum(id);
            if(Main.player2.getBag().getOtherobjNum(id) >= p2Num)
            {
               return [id,p1Num,p2Num];
            }
            return [0];
         }
         return [0];
      }
      
      private function KouChuObj(arr:Array) : *
      {
         if(arr[1] > 0)
         {
            Main.player1.getBag().delOtherById(arr[0],arr[1]);
         }
         if(arr[2] > 0)
         {
            Main.player2.getBag().delOtherById(arr[0],arr[2]);
         }
      }
      
      private function XuQiuNum_kill(num:uint) : Boolean
      {
         if(!Main.P1P2 && Main.player1.killPoint.getValue() >= num)
         {
            return true;
         }
         if(Boolean(Main.P1P2) && Main.player1.killPoint.getValue() >= num && Main.player2.killPoint.getValue() >= num)
         {
            return true;
         }
         return false;
      }
      
      private function KouChukill(num:uint) : *
      {
         Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - num);
         if(Main.P1P2)
         {
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - num);
         }
      }
      
      private function onTime(e:*) : *
      {
         var lvNum:uint = 0;
         ++this.time;
         if(this.time > 50 && saveOK)
         {
            this.skin._BLACK_mc.visible = false;
            saveOK = false;
            this.time = 0;
            removeEventListener(Event.ENTER_FRAME,this.onTime);
            lvNum = uint(XingLingFactory.Get_one_color(this.selNum.getValue(),this.sel_LV_Num.getValue()));
            if(upOK)
            {
               NewMC.Open("文字提示",_this,400,400,30,0,true,2,"强化成功!");
            }
            else
            {
               NewMC.Open("文字提示",_this,400,400,30,0,true,2,"强化失败!");
            }
            this.Show();
         }
      }
   }
}

