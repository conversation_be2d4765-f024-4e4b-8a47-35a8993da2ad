package com.hotpoint.braveManIII.models.basicBtn
{
   import com.hotpoint.braveManIII.events.*;
   import flash.events.*;
   
   public class SideChangeBtnOver extends BasicBtn
   {
      
      private var _isClick:Boolean = false;
      
      public function SideChangeBtnOver()
      {
         super();
      }
      
      override protected function mouseUp(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.MOUSE_UP)
         {
            this.gotoAndStop(3);
         }
      }
      
      override protected function mouseDown(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.MOUSE_DOWN)
         {
            this.gotoAndStop(3);
            this._isClick = true;
         }
      }
      
      override protected function rollOut(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OUT && this._isClick == false)
         {
            this.gotoAndStop(1);
         }
         else
         {
            this.gotoAndStop(3);
         }
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_OUT,this.id));
      }
      
      override protected function clickFn(event:MouseEvent) : void
      {
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_CHANGE,this.id));
      }
      
      override protected function rollOver(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OVER)
         {
            this.gotoAndStop(2);
         }
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_OVER,this.id));
      }
      
      public function set isClick(isClick:Boolean) : void
      {
         this._isClick = isClick;
         if(!this._isClick)
         {
            if(this.currentFrame != 4)
            {
               return this.gotoAndStop(1);
            }
            return this.gotoAndStop(4);
         }
         if(this._isClick)
         {
            return this.gotoAndStop(3);
         }
      }
   }
}

