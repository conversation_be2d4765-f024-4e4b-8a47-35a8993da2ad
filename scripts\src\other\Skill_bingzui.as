package src.other
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_bingzui extends Fly
   {
      
      public function Skill_bingzui()
      {
         super();
      }
      
      public static function Add_Skill_bingzui(player:Player) : *
      {
         if(player.data.getEquipSlot().getEquipFromSlot(6) && player.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 498 && player.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            if(Player.szCD > 10)
            {
               Xiao<PERSON><PERSON>(player);
               Player.szCD = 0;
            }
         }
      }
      
      public static function HpDownUp(whoX:Object, enX:Enemy) : Number
      {
         var player:* = undefined;
         var xx:Number = 1;
         if((enX.isIce || enX.walk_power != enX.walk_temp) && whoX is Player)
         {
            player = whoX;
            if(player.data.getEquipSlot().getEquipFromSlot(7) && player.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 499 && player.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
            {
               xx = 1.1;
            }
         }
         return xx;
      }
      
      public static function XiaoGuo(player:Player) : *
      {
         Player.szCD = 0;
         var class_JG:Class = NewLoad.OtherData.getClass("冰锥XX") as Class;
         var flyX:* = new class_JG();
         player.skin_W.addChild(flyX);
      }
      
      override public function onADDED_TO_STAGE(e:* = null) : *
      {
         var player:Player = null;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         var parentMC:MovieClip = this;
         while(parentMC != _stage)
         {
            if(parentMC is Skin_WuQi && parentMC.parent is Player)
            {
               who = parentMC.parent;
               this.RL = (who as Player).RL;
               if(RL)
               {
                  scaleX *= -1;
               }
               gongJi_hp_MAX = 6000;
               硬直 = 0;
               gongJi_hp = 1;
               player = parentMC.parent;
               if(player.data.getEquipSlot().getEquipFromSlot(7) && player.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 499 && player.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  gongJi_hp = 2;
               }
               attTimes = who.skin.attTimes;
               runX = who.skin.runX;
               runTime = who.skin.runTime;
               break;
            }
            parentMC = parentMC.parent as MovieClip;
         }
         this.x = who.x;
         this.y = who.y;
         Main.world.moveChild_Other.addChild(this);
         this.BingDong();
      }
      
      public function BingDong() : *
      {
         var pX:int = 0;
         var pY:int = 0;
         var i:int = 0;
         var eX:int = 0;
         var eY:int = 0;
         var hxx:HitXX = null;
         var numXX:int = Math.random() * 100;
         if(numXX < 10)
         {
            pX = int(who.x);
            pY = int(who.y);
            for(i = 0; i < Enemy.All.length; i++)
            {
               eX = int(Enemy.All[i].x);
               eY = int(Enemy.All[i].y);
               if(RL && pX < eX && pX > eX - 420 || !RL && pX > eX && pX < eX + 420 && pY < eY + 20 && pY > eY - Enemy.All[i].skin.height)
               {
                  hxx = new HitXX();
                  hxx.type = 603;
                  hxx.totalTime = 55;
                  hxx.space = 55;
                  hxx.numValue = 0;
                  new BuffEnemy(hxx,Enemy.All[i]);
               }
            }
         }
      }
      
      override public function onENTER_FRAME(e:*) : *
      {
         if(over && this.currentLabel != "结束")
         {
            Dead();
            return;
         }
         if(!over && life == 0)
         {
            life = -1;
            gotoAndPlay("结束");
            continuous = false;
            over = true;
         }
         if(!over && time != -1)
         {
            --time;
            if(time == -1)
            {
               time = -1;
               gotoAndPlay("结束");
               continuous = false;
               over = true;
            }
         }
         if(continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         else if(!continuous && this.currentLabel != "运行")
         {
            over = true;
         }
         if(!over)
         {
            Move();
         }
         otherXX();
      }
   }
}

