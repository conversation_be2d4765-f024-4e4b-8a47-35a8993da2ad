package com.adobe.serialization.json
{
   public class JSONs
   {
      
      public function JSONs()
      {
         super();
      }
      
      public static function encode(o:Object) : String
      {
         return new JSONEncoder(o).getString();
      }
      
      public static function decode(s:String, strict:Boolean = true) : *
      {
         return new JSONDecoders(s,strict).getValue();
      }
   }
}

