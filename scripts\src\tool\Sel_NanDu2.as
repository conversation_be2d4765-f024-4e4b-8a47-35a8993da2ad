package src.tool
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class Sel_NanDu2 extends MovieClip
   {
      
      public static var _this:Sel_NanDu2;
      
      private static var gameNumX:int = 0;
      
      private static var dianQuanYN:Boolean = false;
      
      public var NanDu_mc_1:MovieClip;
      
      public var NanDu_mc_2:MovieClip;
      
      public var NanDu_mc_3:MovieClip;
      
      public var NanDu_mc_4:MovieClip;
      
      public var Close_btn:SimpleButton;
      
      public var next_btn:SimpleButton;
      
      public var back_btn:SimpleButton;
      
      public var x1_txt:*;
      
      public var x2_txt:*;
      
      public var x3_txt:*;
      
      public var x4_txt:*;
      
      public var dianQuan_mc:MovieClip;
      
      public var NoMoney_mc:MovieClip;
      
      public var yes_btn:SimpleButton;
      
      public var addMoney_btn:SimpleButton;
      
      public var _txt:*;
      
      public var dianQuan_Yes:SimpleButton;
      
      public var dianQuan_No:SimpleButton;
      
      public var tiaozan_mc:MovieClip;
      
      public var xxx_mc:MovieClip;
      
      public var dianQuan2_mc:MovieClip;
      
      private var chenJiuArr:Array = [0,35,35,35,35,35,35,35,35,35,35,35,35];
      
      private var chenJiuXXXArr:Array = [0,19,49,59,79,89,99,109,119,119,119,119,119];
      
      private var txtArr1:Array = ["?????????????????","解锁条件:游戏成就达到20点","解锁条件:游戏成就达到50点","解锁条件:游戏成就达到60点","解锁条件:游戏成就达到80点","解锁条件:游戏成就达到90点","解锁条件:游戏成就达到100点","解锁条件:游戏成就达到110点","解锁条件:游戏成就达到120点","解锁条件:游戏成就达到120点","解锁条件:游戏成就达到120点","解锁条件:游戏成就达到120点","解锁条件:游戏成就达到120点"];
      
      private var txtArr2:Array = ["?????????????????","星座功能:史诗指环祝福","星座功能:史诗项链祝福","星座功能:史诗头饰祝福","星座功能:史诗战甲祝福","星座功能:史诗武器祝福","星座功能:时装翅膀祝福","星座功能:史诗指环进阶祝福","星座功能:史诗项链进阶祝福","星座功能:史诗头饰进阶祝福","星座功能:史诗战甲进阶祝福","星座功能:史诗武器进阶祝福","星座功能:史诗时装进阶祝福"];
      
      private var numX:int = 1;
      
      private var numMax:int = 12;
      
      internal var gameX:uint = 18;
      
      public function Sel_NanDu2()
      {
         super();
         _this = this;
         this.dianQuan_mc.addEventListener(Event.ADDED_TO_STAGE,this.DianQuanShow);
         this.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.next_btn.addEventListener(MouseEvent.CLICK,this.Next_Fun);
         this.back_btn.addEventListener(MouseEvent.CLICK,this.Back_Fun);
         this.Show();
      }
      
      public static function DianQuanGoGame() : *
      {
         if(gameNumX == 0)
         {
            return;
         }
         Main.gameNum.setValue(gameNumX);
         GameData.gameLV = 4;
         if(SelMap.tiaoZanType)
         {
            GameData.gameLV = 5;
         }
         _this.GameGo();
         gameNumX = 0;
      }
      
      private static function DianQuan_Fun() : *
      {
         _this.dianQuan2_mc.visible = true;
         _this.dianQuan2_mc.ok_btn.addEventListener(MouseEvent.CLICK,DianQuan_ok);
         _this.dianQuan2_mc.close_btn.addEventListener(MouseEvent.CLICK,DianQuan_close);
      }
      
      private static function DianQuan_ok(e:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 5)
         {
            Api_4399_All.BuyObj(127);
            _this.xxx_mc.visible = true;
            dianQuanYN = true;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
            _this.dianQuan2_mc.visible = false;
         }
      }
      
      private static function DianQuan_close(e:*) : *
      {
         _this.dianQuan2_mc.visible = false;
      }
      
      public static function DianQuan_GO() : *
      {
         if(dianQuanYN)
         {
            Main.serviceHold.getServerTime();
            dianQuanYN = false;
            _this.xxx_mc.visible = true;
            GameData.gameLV = 5;
            _this.Close();
            Main.gameNum2.setValue(1);
            Main._this.Loading();
         }
      }
      
      public static function TxtShow1(dataAry:Array, paiHangId:int) : *
      {
         var tmpObj:Object = null;
         if(!_this)
         {
            return;
         }
         if(PaiHang_Data.paiHangArr[paiHangId][1])
         {
            tmpObj = PaiHang_Data.paiHangArr[paiHangId][1];
            _this.tiaozan_mc.x1_txt.text = tmpObj.userName;
            _this.tiaozan_mc.x2_txt.text = tmpObj.score;
         }
         else
         {
            _this.tiaozan_mc.x1_txt.text = "暂无";
            _this.tiaozan_mc.x2_txt.text = "暂无";
         }
      }
      
      public static function TxtShow2(dataAry:Array, paiHangId:int) : *
      {
         var i:int = 0;
         var tmpObj:Object = null;
         if(!_this)
         {
            return;
         }
         if(dataAry != null && dataAry.length != 0)
         {
            for(i in dataAry)
            {
               tmpObj = dataAry[i];
               if(int(tmpObj.index) == Main.saveNum)
               {
                  _this.tiaozan_mc.x3_txt.text = tmpObj.score;
                  return;
               }
            }
            _this.tiaozan_mc.x3_txt.text = "未上榜";
         }
         else
         {
            _this.tiaozan_mc.x3_txt.text = "未上榜";
         }
      }
      
      private function Next_Fun(e:*) : *
      {
         var TiaoZhan_numMax:int = (PaiHang_Data.gameNum_x1_4.length - 2) / 4 + 1;
         var guanKa_numMax:int = (this.numMax - 1) / 4 + 1;
         if(!SelMap.tiaoZanType)
         {
            if(this.numX < guanKa_numMax)
            {
               ++this.numX;
               this.Show();
            }
         }
         else if(this.numX < TiaoZhan_numMax)
         {
            ++this.numX;
            this.Show();
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他星灵挑战模式暂未开启");
         }
      }
      
      private function Back_Fun(e:*) : *
      {
         if(this.numX > 1)
         {
            --this.numX;
            this.Show();
         }
      }
      
      private function DianQuanShow(e:*) : *
      {
         this.dianQuan_mc.dianQuan_Yes.addEventListener(MouseEvent.CLICK,this.DianQuan);
         this.dianQuan_mc.dianQuan_No.addEventListener(MouseEvent.CLICK,this.DianQuanClose);
      }
      
      public function Open() : *
      {
         this.x = this.y = 0;
         this.visible = true;
         this.dianQuan_mc.visible = false;
         this.dianQuan_mc.x = this.dianQuan_mc.y = 0;
         this.Show();
      }
      
      public function Close(e:* = null) : *
      {
         this.x = this.y = 5000;
         this.visible = false;
         this.dianQuan_mc.visible = false;
      }
      
      public function DianQuan(e:* = null) : *
      {
         if(Shop4399.moneyAll.getValue() < 3)
         {
            (this.NoMoney_mc as NoMoneyInfo).Open(3);
            return;
         }
         Api_4399_All.BuyObj(InitData.xingLing.getValue());
         this.dianQuan_mc.visible = false;
      }
      
      public function DianQuanClose(e:* = null) : *
      {
         this.dianQuan_mc.visible = false;
      }
      
      private function SelNanDu_MOUSE_MOVE(e:MouseEvent) : *
      {
         this.gameX = (e.target.parent.name as String).substr(9,1);
         this.gameX = (this.numX - 1) * 4 + 17 + this.gameX;
         _this.tiaozan_mc.mouseChildren = _this.tiaozan_mc.mouseEnabled = false;
         _this.tiaozan_mc.x = mouseX + 15;
         _this.tiaozan_mc.y = mouseY - 25;
         if(SelMap.tiaoZanType)
         {
            this.XXX(this.gameX,e.target.parent);
         }
      }
      
      private function SelNanDu_MOUSE_OUT(e:MouseEvent) : *
      {
         _this.tiaozan_mc.x = _this.tiaozan_mc.y = -5000;
      }
      
      private function SelNanDu(e:MouseEvent) : *
      {
         this.gameX = (e.target.parent.name as String).substr(9,1);
         this.gameX = (this.numX - 1) * 4 + 17 + this.gameX;
         var numXX:int = 35;
         if(Boolean(Main.isVip()) && AchData.cjPoint_1.getValue() >= 18)
         {
            Main.gameNum.setValue(this.gameX);
            GameData.gameLV = 4;
            if(SelMap.tiaoZanType)
            {
               GameData.gameLV = 5;
            }
            this.GameGo();
            if(!SelMap.tiaoZanType)
            {
               numXX = int(GongHui_jiTan.killPointXX(18));
               AchData.cjPoint_1.setValue(AchData.cjPoint_1.getValue() - numXX);
            }
         }
         else if(AchData.cjPoint_1.getValue() >= this.chenJiuArr[this.gameX - 17])
         {
            Main.gameNum.setValue(this.gameX);
            GameData.gameLV = 4;
            if(SelMap.tiaoZanType)
            {
               GameData.gameLV = 5;
            }
            this.GameGo();
            if(!SelMap.tiaoZanType)
            {
               numXX = int(GongHui_jiTan.killPointXX(this.chenJiuArr[this.gameX - 17]));
               AchData.cjPoint_1.setValue(AchData.cjPoint_1.getValue() - numXX);
            }
         }
         else
         {
            this.dianQuan_mc.visible = true;
            gameNumX = this.gameX;
         }
      }
      
      private function XXX(gameX:int, objX:MovieClip) : *
      {
         var p1p2:int = 0;
         var xxNum:int = 0;
         var selTypeX:int = 0;
         var tmpObj1:Object = null;
         var tmpObj2:Object = null;
         if(Main.P1P2)
         {
            p1p2 = 2;
         }
         else
         {
            p1p2 = 1;
         }
         var gameNumX:int = gameX;
         if(gameNumX >= 1 && gameNumX <= 9)
         {
            xxNum = 1;
            selTypeX = gameNumX;
         }
         else if(gameNumX >= 10 && gameNumX <= 16)
         {
            xxNum = 2;
            selTypeX = gameNumX - 9;
         }
         else if(gameNumX >= 51 && gameNumX <= 62)
         {
            xxNum = 3;
            selTypeX = gameNumX - 50;
         }
         else if(gameNumX >= 18 && gameNumX <= 29)
         {
            xxNum = 4;
            selTypeX = gameNumX - 17;
         }
         var pId:int = int(PaiHang_Data["gameNum_x" + p1p2 + "_" + xxNum][selTypeX]);
         if(!PaiHang_Data.paiHangArr[pId])
         {
            Api_4399_All.GetRankListsData(1,50,pId);
            Api_4399_All.GetOneRankInfo(Main.logName,pId,1);
         }
         if(Boolean(PaiHang_Data.paiHangArr[pId]) && Boolean(PaiHang_Data.paiHangArr[pId][0]))
         {
            tmpObj1 = PaiHang_Data.paiHangArr[pId][0];
            this.tiaozan_mc.x3_txt.text = tmpObj1.score;
         }
         else
         {
            this.tiaozan_mc.x3_txt.text = "暂无";
         }
         if(Boolean(PaiHang_Data.paiHangArr[pId]) && Boolean(PaiHang_Data.paiHangArr[pId][1]))
         {
            tmpObj2 = PaiHang_Data.paiHangArr[pId][1];
            this.tiaozan_mc.x1_txt.text = tmpObj2.userName;
            this.tiaozan_mc.x2_txt.text = tmpObj2.score;
         }
         else
         {
            this.tiaozan_mc.x1_txt.text = "暂无";
            this.tiaozan_mc.x2_txt.text = "暂无";
         }
         var money:int = int((PaiHang_Data.dataArr[gameNumX][0] as VT).getValue());
         var inGameNum:int = int(PaiHang_Data.inGameNum[gameNumX]);
         objX["txt1"].text = "消耗击杀点:10\n消耗金币:" + money;
         objX["txt2"].text = "本关剩余挑战次数:" + inGameNum;
      }
      
      private function GameGo() : *
      {
         var money:int = 0;
         var inGameNum:int = 0;
         if(SelMap.tiaoZanType)
         {
            money = int((PaiHang_Data.dataArr[Main.gameNum.getValue()][0] as VT).getValue());
            if(Main.P1P2)
            {
               if(Main.player1.getGold() < money || Main.player2.getGold() < money)
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"金币不足");
                  return;
               }
               if(Main.player1.killPoint.getValue() < 10 || Main.player2.killPoint.getValue() < 10)
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"击杀点不足");
                  return;
               }
               if(Main.player1.level.getValue() < 30 || Main.player2.level.getValue() < 30)
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"等级30级后方可进入");
                  return;
               }
            }
            else
            {
               if(Main.player1.level.getValue() < 30)
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"等级30级后方可进入");
                  return;
               }
               if(Main.player1.getGold() < money)
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"金币不足");
                  return;
               }
               if(Main.player1.killPoint.getValue() < 10)
               {
                  NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"击杀点不足");
                  return;
               }
            }
            inGameNum = int(PaiHang_Data.inGameNum[Main.gameNum.getValue()]);
            if(inGameNum <= 0)
            {
               DianQuan_Fun();
               return;
            }
            if(Main.P1P2)
            {
               Main.player1.payGold(money);
               Main.player2.payGold(money);
               Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - 10);
               Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - 10);
            }
            else
            {
               Main.player1.payGold(money);
               Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - 10);
            }
            --PaiHang_Data.inGameNum[Main.gameNum.getValue()];
         }
         if(GameData.gameLV == 5)
         {
            Main.serviceHold.getServerTime();
         }
         SelMap.Close();
         Main.gameNum2.setValue(1);
         Main._this.Loading();
         this.Close();
         Main.Save();
      }
      
      private function Show() : *
      {
         var xx:uint = 0;
         this.xxx_mc.visible = false;
         this.dianQuan2_mc.visible = false;
         for(var i:uint = 1; i < 5; i++)
         {
            xx = (this.numX - 1) * 4 + i;
            this["NanDu_mc_" + i].gotoAndStop(xx);
            if(this.numMax < (this.numX - 1) * 4 + i)
            {
               this["NanDu_mc_" + i].visible = false;
            }
            else
            {
               this["NanDu_mc_" + i].visible = true;
               if(AchData.cjPoint_2.getValue() > this.chenJiuXXXArr[xx])
               {
                  this["NanDu_mc_" + i].shou_mc.visible = false;
                  this["NanDu_mc_" + i]._txt.text = this.txtArr2[xx];
                  if(!SelMap.tiaoZanType)
                  {
                     this["NanDu_mc_" + i]._txt2.text = "进入条件:消耗每日成就35点";
                  }
                  else
                  {
                     this["NanDu_mc_" + i]._txt2.text = "进入条件:无";
                  }
                  this["NanDu_mc_" + i]._btn.addEventListener(MouseEvent.CLICK,this.SelNanDu);
                  this["NanDu_mc_" + i]._btn.addEventListener(MouseEvent.MOUSE_MOVE,this.SelNanDu_MOUSE_MOVE);
                  this["NanDu_mc_" + i]._btn.addEventListener(MouseEvent.MOUSE_OUT,this.SelNanDu_MOUSE_OUT);
               }
               else
               {
                  this["NanDu_mc_" + i].shou_mc.visible = true;
                  this["NanDu_mc_" + i]._txt.text = this.txtArr1[xx];
                  this["NanDu_mc_" + i]._btn.removeEventListener(MouseEvent.CLICK,this.SelNanDu);
                  this["NanDu_mc_" + i]._btn.removeEventListener(MouseEvent.MOUSE_MOVE,this.SelNanDu_MOUSE_MOVE);
                  this["NanDu_mc_" + i]._btn.removeEventListener(MouseEvent.MOUSE_OUT,this.SelNanDu_MOUSE_OUT);
               }
            }
            this["NanDu_mc_" + i].txt1.visible = this["NanDu_mc_" + i].txt2.visible = this.tiaozan_mc.visible = false;
            if(SelMap.tiaoZanType)
            {
               this["NanDu_mc_" + i].txt1.visible = this["NanDu_mc_" + i].txt2.visible = this.tiaozan_mc.visible = true;
            }
         }
         this.x1_txt.text = AchData.cjPoint_1.getValue();
         this.x2_txt.text = AchData.cjPoint_2.getValue();
         this.x3_txt.text = this.numX + "/3";
         this.x4_txt.text = Shop4399.moneyAll.getValue();
      }
   }
}

