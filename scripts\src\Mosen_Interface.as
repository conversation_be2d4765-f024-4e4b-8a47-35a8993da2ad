package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.filters.*;
   import flash.text.*;
   import flash.utils.*;
   
   public class Mosen_Interface extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      public static var _this:Mosen_Interface;
      
      private static var loadName:String = "moShen_v1920.swf";
      
      private static var OpenYN:Boolean = false;
      
      public static var dianQuanYn:Boolean = false;
      
      private var buyOk:Boolean;
      
      private var buyOk2:Boolean;
      
      private var skin:MovieClip;
      
      public var ysNum:int = 1;
      
      public var mcMove:int = -1;
      
      public var mcSel:int = 1;
      
      public function Mosen_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Close(e:* = null) : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
         }
      }
      
      private static function InitOpen() : *
      {
         var temp:Mosen_Interface = new Mosen_Interface();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function BuyGo() : *
      {
         var id:int = 0;
         var arr2:Array = null;
         if(Boolean(_this) && Boolean(_this.buyOk))
         {
            _this.skin.zeZao_mc.visible = false;
            _this.buyOk = false;
            id = int(Data_mosen.paiXu[_this.mcSel - 1]);
            arr2 = Data_mosen.data[id];
            ++ChongWu2.cw2save[arr2[0]][0];
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"突破成功");
            Main.Save();
            _this.Show();
         }
         if(Boolean(_this) && Boolean(_this.buyOk2))
         {
            _this.skin.zeZao_mc.visible = false;
            _this.buyOk2 = false;
            id = int(Data_mosen.paiXu[_this.mcSel - 1]);
            arr2 = Data_mosen.data[id];
            ++ChongWu2.cw2save[arr2[0]][0];
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"觉醒成功");
            Main.Save();
            _this.Show();
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("Skin") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         this.skin.zeZao_mc.visible = false;
         this.skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
         this.skin.qieye1.addEventListener(MouseEvent.CLICK,this.Qieye1);
         this.skin.qieye2.addEventListener(MouseEvent.CLICK,this.Qieye2);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            Close();
         }
         for(var i:int = 0; i < 10; i++)
         {
            this.skin["mc_" + i].addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
            this.skin["mc_" + i].addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
            this.skin["mc_" + i].addEventListener(MouseEvent.CLICK,this.onCLICK);
         }
         this.skin.jueXing_mc.show_mc.visible = false;
         for(var j:int = 1; j <= 3; j++)
         {
            this.skin["jueXing_mc"]["xMC_" + j].addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE2);
            this.skin["jueXing_mc"]["xMC_" + j].addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT2);
         }
         this.skin.jueXing_mc.dianQuan_mc.yes_mc.visible = false;
         this.skin.jueXing_mc.dianQuan_mc.yes_mc.mouseChildren = this.skin.jueXing_mc.dianQuan_mc.yes_mc.mouseEnabled = false;
         this.skin.jueXing_mc.dianQuan_mc._btn.addEventListener(MouseEvent.CLICK,this.onDianQuan);
         this.skin.jueXing_mc.juexing_btn.addEventListener(MouseEvent.CLICK,this.JueXing);
         this.skin.jueXing_mc.tepo_btn.addEventListener(MouseEvent.CLICK,this.JueXing);
      }
      
      public function JueXing(e:*) : *
      {
         var id:int = int(Data_mosen.paiXu[this.mcSel - 1]);
         var arr2:Array = Data_mosen.data[id];
         var arr:Array = ChongWu2.cw2save[arr2[0]];
         var lv:int = int(arr[0]);
         this.skin.jueXing_mc.dianQuan_mc.yes_mc.visible = dianQuanYn;
         if(lv >= 10)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已满级");
            return;
         }
         if(dianQuanYn)
         {
            trace("使用点券购买",Shop4399.moneyAll.getValue());
            if(lv == 0)
            {
               if(Shop4399.moneyAll.getValue() >= 598)
               {
                  this.buyOk2 = true;
                  Api_4399_All.BuyObj(308);
                  this.skin.zeZao_mc.visible = true;
               }
               else
               {
                  Shop4399.NoMoney_info_Open();
               }
            }
            else if(Shop4399.moneyAll.getValue() >= 299)
            {
               this.buyOk = true;
               Api_4399_All.BuyObj(307);
               this.skin.zeZao_mc.visible = true;
            }
            else
            {
               Shop4399.NoMoney_info_Open();
            }
            return;
         }
         if(lv == 0 && (arr[1] < 10 || arr[2] < 10 || arr[3] < 10))
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"激活物品不足");
            return;
         }
         if(arr[1] < 5 || arr[2] < 5 || arr[3] < 5)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"突破物品不足");
            return;
         }
         ++ChongWu2.cw2save[arr2[0]][0];
         ChongWu2.cw2save[arr2[0]][1] -= arr2[9];
         ChongWu2.cw2save[arr2[0]][2] -= arr2[10];
         ChongWu2.cw2save[arr2[0]][3] -= arr2[11];
         if(ChongWu2.cw2save[arr2[0]][0] == 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"觉醒成功");
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"突破成功");
         }
         Main.Save();
         this.Show();
      }
      
      public function onDianQuan(e:MouseEvent) : *
      {
         dianQuanYn = !dianQuanYn;
         this.Show();
      }
      
      public function onMOUSE_MOVE2(e:MouseEvent) : *
      {
         var arr:Array = null;
         var num:int = int(e.target.name.substr(4,1));
         if((e.target.name as String).length == 5)
         {
            this.skin.jueXing_mc.show_mc.visible = true;
            this.skin.jueXing_mc.show_mc.x = e.target.x - 54;
            arr = [0,"黑暗炼狱","极限炼狱","轮回炼狱"];
            this.skin.jueXing_mc.show_mc.t1_txt.text = arr[num] + "秘文-冥王龙";
            this.skin.jueXing_mc.show_mc.t2_txt.text = "记载着【冥王龙魔神】觉醒的古老秘文之一，打败" + arr[num] + "之主有小几率获得（不在背包中显示）";
         }
      }
      
      public function onMOUSE_OUT2(e:MouseEvent) : *
      {
         this.skin.jueXing_mc.show_mc.visible = false;
      }
      
      public function onCLICK(e:MouseEvent) : *
      {
         var num:int = int(e.target.parent.name.substr(3,1));
         var numX:int = (this.ysNum - 1) * 10 + num + 1;
         if(this.JiHuoYn(numX))
         {
            this.mcSel = numX;
            this.Show();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"未开放");
         }
      }
      
      public function onMOUSE_MOVE(e:MouseEvent) : *
      {
         var num:int = int(e.target.parent.name.substr(3,1));
         this.mcMove = num;
         this.Show();
      }
      
      public function onMOUSE_OUT(e:MouseEvent) : *
      {
         this.mcMove = -1;
         this.Show();
      }
      
      public function Qieye1(e:*) : *
      {
         if(this.ysNum > 1)
         {
            --this.ysNum;
            this.Show();
         }
      }
      
      public function Qieye2(e:*) : *
      {
         if(this.ysNum < 2)
         {
            ++this.ysNum;
            this.Show();
         }
      }
      
      public function Show() : *
      {
         var num:int = 0;
         var arr:Array = null;
         for(var i:int = 0; i < 10; i++)
         {
            num = (this.ysNum - 1) * 10 + i + 1;
            arr = Data_mosen.data[Data_mosen.paiXu[num - 1]];
            if(arr)
            {
               this.skin["mc_" + i]["_mc"].gotoAndStop(arr[2]);
               this.skin["mc_" + i]["x_mc"].gotoAndStop(1);
               if(num == this.mcSel)
               {
                  this.skin["mc_" + i]["x_mc"].gotoAndStop(3);
               }
               this.HuiHua(this.skin["mc_" + i]["_mc"],!this.JiHuoYn(num));
               this.skin["mc_" + i].visible = true;
            }
            else
            {
               this.skin["mc_" + i].visible = false;
            }
         }
         if(this.mcMove != -1)
         {
            this.skin["mc_" + this.mcMove]["x_mc"].gotoAndStop(3);
         }
         this.skin.yueshu_txt.text = this.ysNum + "/2";
         var id:int = int(Data_mosen.paiXu[this.mcSel - 1]);
         var arr2:Array = Data_mosen.data[id];
         var lv:int = int(ChongWu2.cw2save[arr2[0]][0]);
         var numXX:int = lv == 0 ? 10 : 5;
         this.skin.jueXing_mc.dianQuan_mc.yes_mc.visible = dianQuanYn;
         this.skin.jueXing_mc._txt1.text = ChongWu2.cw2save[arr2[0]][1] + "/" + numXX;
         this.skin.jueXing_mc._txt2.text = ChongWu2.cw2save[arr2[0]][2] + "/" + numXX;
         this.skin.jueXing_mc._txt3.text = ChongWu2.cw2save[arr2[0]][3] + "/" + numXX;
         if(dianQuanYn)
         {
            this.skin.jueXing_mc._txt1.text = ChongWu2.cw2save[arr2[0]][1] + "/-";
            this.skin.jueXing_mc._txt2.text = ChongWu2.cw2save[arr2[0]][2] + "/-";
            this.skin.jueXing_mc._txt3.text = ChongWu2.cw2save[arr2[0]][3] + "/-";
         }
         this.skin.jueXing_mc._txt.text = lv == 0 ? "使用598点券觉醒" : "使用299点券突破";
         this.skin.name_txt.text = "Lv." + lv + "冥王龙魔神";
         this.skin.x1_txt.text = "能量:" + (200 + lv * arr2[7]) + "% (主人生命)";
         this.skin.x2_txt.text = "增伤:" + lv * arr2[8] + "%";
         this.skin.jueXing_txt.text = "觉醒奖励:攻击+" + lv * arr2[6];
         if(lv == 0)
         {
            this.skin.name_txt.text = "冥王龙魔神 (未觉醒)";
            this.skin.x1_txt.text = "能量:(未觉醒)";
            this.skin.x2_txt.text = "增伤:(未觉醒)";
            this.skin.jueXing_txt.text = "觉醒奖励:(未觉醒)";
         }
         this.skin.jueXing_mc.tepo_btn.visible = false;
         this.skin.jueXing_mc.juexing_btn.visible = true;
         if(lv > 0)
         {
            this.skin.jueXing_mc.tepo_btn.visible = true;
            this.skin.jueXing_mc.juexing_btn.visible = false;
         }
      }
      
      public function JiHuoYn(num:*) : Boolean
      {
         if(num != 1)
         {
            return false;
         }
         return true;
      }
      
      public function HuiHua(mc:MovieClip, yn:Boolean = false) : *
      {
         var filter:ColorMatrixFilter = null;
         if(yn)
         {
            filter = new ColorMatrixFilter([0.3,0.6,0,0,0,0.3,0.6,0,0,0,0.3,0.6,0,0,0,0,0,0,1,0]);
            mc.filters = [filter];
         }
         else
         {
            mc.filters = [];
         }
      }
   }
}

