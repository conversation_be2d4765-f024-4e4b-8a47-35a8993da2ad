package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol6463")]
   public class NoMoneyInfo extends MovieClip
   {
      
      public var _txt:TextField;
      
      public var addMoney_btn:SimpleButton;
      
      public var yes_btn:SimpleButton;
      
      public function NoMoneyInfo()
      {
         super();
         this.yes_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.addMoney_btn.addEventListener(MouseEvent.CLICK,this.Open_AddMoney2);
      }
      
      public function Open(money:uint = 0) : *
      {
         this.visible = true;
         this.x = this.y = 0;
         this._txt.text = "购买需要" + money + "点券";
      }
      
      public function Close(e:* = null) : *
      {
         this.visible = false;
         this.x = this.y = 5000;
      }
      
      private function Open_AddMoney2(e:* = null) : *
      {
         Main.ChongZhi();
         this.Close();
      }
   }
}

