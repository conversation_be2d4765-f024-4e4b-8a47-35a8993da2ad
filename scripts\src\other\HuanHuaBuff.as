package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class HuanHuaBuff extends MovieClip
   {
      
      public static var h1:<PERSON>an<PERSON>uaBuff;
      
      public static var h2:<PERSON><PERSON><PERSON>uaBuff;
      
      public var gjXX_num:Number = 0;
      
      public var buffTime:int = 0;
      
      public var who:Player;
      
      public function HuanHuaBuff()
      {
         super();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public static function Add_HuanHuaBuff(player:Player) : *
      {
         var e2:Equip = player.data.getEquipSlot().getEquipFromSlot(2);
         var e5:Equip = player.data.getEquipSlot().getEquipFromSlot(5);
         if(e2 && e2.getHuanHua() == 5 || e5 && e5.getHuanHua() == 5)
         {
            if(player == Main.player_1)
            {
               if(!h1)
               {
                  h1 = new HuanHuaBuff();
                  h1.who = player;
               }
               ++h1.gjXX_num;
            }
            else if(player == Main.player_2)
            {
               if(!h2)
               {
                  h2 = new HuanHuaBuff();
                  h2.who = player;
               }
               ++h2.gjXX_num;
            }
         }
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         if(this.gjXX_num >= 8)
         {
            this.gjXX_num = 0;
            this.buffTime = 27 * 5;
         }
         if(this.buffTime > 0)
         {
            --this.buffTime;
            if(this.buffTime % 27 == 0)
            {
               if(this.who.hp.getValue() > 0)
               {
                  this.who.HpUp(8 / 5,2);
               }
            }
         }
      }
   }
}

