package src.other
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class SkillDarkBoom extends Fly
   {
      
      public function SkillDarkBoom()
      {
         super();
      }
      
      override public function onADDED_TO_STAGE(e:* = null) : *
      {
         var parentMC:MovieClip = null;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         for(var i:int = 0; i < All.length; i++)
         {
            if(All[i] == this)
            {
               return;
            }
         }
         All[All.length] = this;
         parentMC = this;
         while(parentMC != _stage)
         {
            if(parentMC is Skin_WuQi && parentMC.parent is Player)
            {
               who = parentMC.parent;
               this.RL = (who as Player).RL;
               硬直 = who.skin.硬直;
               gongJi_hp_MAX = 12000;
               gongJi_hp = 3.2;
               attTimes = who.skin.attTimes;
               runX = who.skin.runX;
               runTime = who.skin.runTime;
               break;
            }
            if(parentMC is EnemySkin && parentMC.parent is Enemy)
            {
               who = parentMC.parent;
               this.RL = (who as Enemy).RL;
               硬直 = who.skin.硬直;
               attTimes = who.skin.attTimes;
               runX = (parentMC as EnemySkin).runX;
               runTime = (parentMC as EnemySkin).runTime;
               type = (parentMC as EnemySkin).type;
               space = (parentMC as EnemySkin).space;
               totalTime = (parentMC as EnemySkin).totalTime;
               numValue = (parentMC as EnemySkin).numValue;
               break;
            }
            parentMC = parentMC.parent as MovieClip;
         }
         Main.world.moveChild_Other2.addChild(this);
         this.y = Enemy.hit_y;
         this.x = Enemy.hit_x;
      }
   }
}

