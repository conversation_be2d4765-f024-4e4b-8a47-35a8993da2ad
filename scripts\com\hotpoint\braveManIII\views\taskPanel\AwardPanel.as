package com.hotpoint.braveManIII.views.taskPanel
{
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.utils.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4144")]
   public class AwardPanel extends MovieClip
   {
      
      private static var _instance:AwardPanel;
      
      public var d_0:MovieClip;
      
      public var d_1:MovieClip;
      
      public var d_2:MovieClip;
      
      public var d_3:MovieClip;
      
      public var d_4:MovieClip;
      
      public var jl_0:Str_强化;
      
      public var jl_1:Str_强化;
      
      public var jl_2:Str_强化;
      
      public var jl_3:Str_强化;
      
      public var jl_4:Str_强化;
      
      private var timer:Timer;
      
      private var timerNum:Number;
      
      private var bo:Boolean;
      
      public function AwardPanel()
      {
         super();
      }
      
      public static function open(arr1:Array, arr2:Array, time:Number = 100) : void
      {
         if(AwardPanel._instance == null)
         {
            _instance = new AwardPanel();
            _instance.InitIcon();
         }
         Main._stage.addChild(AwardPanel._instance);
         _instance.visible = true;
         _instance.alpha = 0;
         _instance.init(arr1,arr2);
         _instance.addEvent(time);
      }
      
      private function addEvent(time:Number) : void
      {
         this.bo = false;
         this.timerNum = time;
         this.timer = new Timer(0);
         this.timer.addEventListener(TimerEvent.TIMER,this.onTimer);
         this.timer.start();
      }
      
      private function init(arr1:Array, arr2:Array) : void
      {
         trace(arr1,"arr1_______________________");
         for(var i:uint = 0; i < 5; i++)
         {
            this["jl_" + i].visible = false;
            this["d_" + i].visible = false;
         }
         for(i = 0; i < arr1.length; i++)
         {
            this["jl_" + i].visible = true;
            this["d_" + i].visible = true;
            this["jl_" + i].pic_xx.gotoAndStop(arr1[i].getFrame());
            this["jl_" + i].howNum.text = String(arr2[i].getValue());
         }
      }
      
      public function InitIcon() : *
      {
         var mm:MovieClip = null;
         var num:int = 0;
         for(var i:uint = 0; i < 5; i++)
         {
            mm = new Shop_picNEW();
            num = int(_instance["jl_" + i].getChildIndex(_instance["jl_" + i].pic_xx));
            mm.x = _instance["jl_" + i].pic_xx.x;
            mm.y = _instance["jl_" + i].pic_xx.y;
            mm.name = "pic_xx";
            _instance["jl_" + i].removeChild(_instance["jl_" + i].pic_xx);
            _instance["jl_" + i].pic_xx = mm;
            _instance["jl_" + i].addChild(mm);
            _instance["jl_" + i].setChildIndex(mm,num);
         }
      }
      
      private function onTimer(e:TimerEvent) : void
      {
         if(!this.bo)
         {
            if(this.alpha < 1)
            {
               this.alpha += 0.1;
            }
            else
            {
               this.bo = true;
            }
         }
         else if(this.timerNum > 0)
         {
            --this.timerNum;
         }
         else if(this.alpha > 0)
         {
            this.alpha -= 0.1;
         }
         else
         {
            this.bo = false;
            this.visible = false;
            this.timer.stop();
            this.timer.removeEventListener(TimerEvent.TIMER,this.onTimer);
         }
      }
   }
}

