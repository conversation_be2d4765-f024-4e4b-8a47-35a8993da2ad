package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class JinHuaPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var jhPanel:MovieClip;
      
      public static var jhp:JinHuaPanel;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var starNum:Number;
      
      public static var mapNum:Number;
      
      public static var partNum:Number;
      
      public static var count:int;
      
      public static var myplayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      public static var arr:Array = [[1,63162,5],[1,63138,3],[1,63100,3],[1,63137,35],[2,63169,5],[2,63138,3],[2,63100,3],[2,63137,35],[3,63171,5],[3,63138,3],[3,63100,3],[3,63137,35],[4,63183,5],[4,63138,3],[4,63100,3],[4,63137,35],[5,63197,5],[5,63138,3],[5,63100,3],[5,63137,35],[6,63207,5],[6,63138,3],[6,63100,3],[6,63137,35],[7,63246,5],[7,63138,3],[7,63100,3],[7,63137,35],[8,63257,5],[8,63138,3],[8,63100,3],[8,63137,35],[9,63268,5],[9,63138,3],[9,63100,3],[9,63137,35],[10,63273,5],[10,63138,3],[10,63100,3],[10,63137,35],[11,63337,5],[11,63138,3],[11,63100,3],[11,63137,35],[12,63340,5],[12,63138,3],[12,63100,3],[12,63137,35]];
      
      public static var yeshu:Number = 0;
      
      public static var selbool:Boolean = false;
      
      public static var isPOne:Boolean = true;
      
      public static var arr_cl:Array = [];
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_XLZF_v1200.swf";
      
      public function JinHuaPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!jhPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = jhPanel.getChildIndex(jhPanel["e" + i]);
            mm.x = jhPanel["e" + i].x;
            mm.y = jhPanel["e" + i].y;
            mm.name = "e" + i;
            jhPanel.removeChild(jhPanel["e" + i]);
            jhPanel["e" + i] = mm;
            jhPanel.addChild(mm);
            jhPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 4; i++)
         {
            mm = new Shop_picNEW();
            num = jhPanel.getChildIndex(jhPanel["x" + i]);
            mm.x = jhPanel["x" + i].x;
            mm.y = jhPanel["x" + i].y;
            mm.name = "x" + i;
            jhPanel.removeChild(jhPanel["x" + i]);
            jhPanel["x" + i] = mm;
            jhPanel.addChild(mm);
            jhPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = jhPanel.getChildIndex(jhPanel["s" + i]);
            mm.x = jhPanel["s" + i].x;
            mm.y = jhPanel["s" + i].y;
            mm.name = "s" + i;
            jhPanel.removeChild(jhPanel["s" + i]);
            jhPanel["s" + i] = mm;
            jhPanel.addChild(mm);
            jhPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = jhPanel.getChildIndex(jhPanel["select"]);
         mm.x = jhPanel["select"].x;
         mm.y = jhPanel["select"].y;
         mm.name = "select";
         jhPanel.removeChild(jhPanel["select"]);
         jhPanel["select"] = mm;
         jhPanel.addChild(mm);
         jhPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("JHShow") as Class;
         jhPanel = new classRef();
         jhp.addChild(jhPanel);
         InitIcon();
         if(OpenYN)
         {
            open(isPOne,mapNum,partNum);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         jhp = new JinHuaPanel();
         LoadSkin();
         Main._stage.addChild(jhp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         jhp = new JinHuaPanel();
         Main._stage.addChild(jhp);
         OpenYN = false;
      }
      
      public static function open(pp:Boolean, map:int, part:int) : void
      {
         trace("祝福面板:",pp,"map:",map,"part:",part);
         Main.allClosePanel();
         if(jhPanel)
         {
            Main.stopXX = true;
            jhp.x = 0;
            jhp.y = 0;
            mapNum = map;
            partNum = part;
            isPOne = pp;
            if(isPOne)
            {
               myplayer = Main.player1;
            }
            addListenerP1();
            Main._stage.addChild(jhp);
            jhp.visible = true;
         }
         else
         {
            mapNum = map;
            partNum = part;
            isPOne = pp;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(jhPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            jhp.visible = false;
            selbool = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         if(Main.P1P2)
         {
            jhPanel["bagOne"].visible = false;
            jhPanel["bagTwo"].visible = true;
            jhPanel["back_mc"].visible = true;
            jhPanel["bagOne"].addEventListener(MouseEvent.CLICK,to1p);
            jhPanel["bagTwo"].addEventListener(MouseEvent.CLICK,to2p);
         }
         else
         {
            jhPanel["bagOne"].visible = false;
            jhPanel["bagTwo"].visible = false;
            jhPanel["back_mc"].visible = false;
         }
         jhPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         jhPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         jhPanel["jh_btn"].visible = false;
         jhPanel["jh_btn"].addEventListener(MouseEvent.CLICK,doJH);
         jhPanel["close"].addEventListener(MouseEvent.CLICK,closeJH);
         for(var i:uint = 0; i < 24; i++)
         {
            jhPanel["e" + i].mouseChildren = false;
            jhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            jhPanel["s" + i].mouseChildren = false;
            jhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         jhPanel["select"].gotoAndStop(1);
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function to1p(e:*) : *
      {
         myplayer = Main.player1;
         jhPanel["bagOne"].visible = false;
         jhPanel["bagTwo"].visible = true;
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         jhPanel["jh_btn"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function to2p(e:*) : *
      {
         myplayer = Main.player2;
         jhPanel["bagOne"].visible = true;
         jhPanel["bagTwo"].visible = false;
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         jhPanel["jh_btn"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function upGo(e:*) : *
      {
         yeshu = 0;
         playShow();
         otherShow();
      }
      
      public static function downGo(e:*) : *
      {
         yeshu = 1;
         playShow();
         otherShow();
      }
      
      public static function playShow() : *
      {
         var i:uint = 0;
         var e1:Equip = null;
         var buWei:* = undefined;
         var e2:Equip = null;
         var numXXX:* = undefined;
         var yeshuNum:int = yeshu + 1;
         jhPanel["yeshu_txt"].text = yeshuNum + "/2";
         for(i = 0; i < 24; i++)
         {
            jhPanel["e" + i].t_txt.text = "";
            e1 = myplayer.getBag().getEquipFromBag(i + 24 * yeshu);
            if(e1 != null)
            {
               if(Boolean(e1._blessAttrib) && e1._blessAttrib.getBeishu() >= 2)
               {
                  jhPanel["e" + i].visible = false;
               }
               else
               {
                  buWei = e1.getPosition();
                  if(mapNum > 6 && !e1.getBlessAttrib())
                  {
                     jhPanel["e" + i].visible = false;
                  }
                  else if(partNum == 5)
                  {
                     if((buWei == 0 || buWei == 5 || buWei == 6 || buWei == 7) && e1.getColor() >= 5)
                     {
                        jhPanel["e" + i].gotoAndStop(e1.getFrame());
                        jhPanel["e" + i].visible = true;
                     }
                     else
                     {
                        jhPanel["e" + i].visible = false;
                     }
                  }
                  else if(partNum == 8)
                  {
                     if(e1.getPosition() >= partNum && e1.getPosition() <= 9 && e1.getColor() >= 5)
                     {
                        jhPanel["e" + i].gotoAndStop(e1.getFrame());
                        jhPanel["e" + i].visible = true;
                     }
                     else
                     {
                        jhPanel["e" + i].visible = false;
                     }
                  }
                  else if(e1.getPosition() == partNum && e1.getColor() >= 5)
                  {
                     jhPanel["e" + i].gotoAndStop(e1.getFrame());
                     jhPanel["e" + i].visible = true;
                  }
                  else
                  {
                     jhPanel["e" + i].visible = false;
                  }
               }
            }
            else
            {
               jhPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            jhPanel["s" + i].t_txt.text = "";
            e2 = myplayer.getEquipSlot().getEquipFromSlot(i);
            if(e2 != null)
            {
               if(Boolean(e2._blessAttrib) && e2._blessAttrib.getBeishu() >= 2)
               {
                  jhPanel["s" + i].visible = false;
               }
               else if(mapNum > 6 && !e2.getBlessAttrib())
               {
                  jhPanel["s" + i].visible = false;
               }
               else if(partNum == 5)
               {
                  numXXX = e2.getPosition();
                  if((numXXX == 0 || numXXX == 5 || numXXX == 6 || numXXX == 7) && e2.getColor() >= 5)
                  {
                     jhPanel["s" + i].gotoAndStop(e2.getFrame());
                     jhPanel["s" + i].visible = true;
                  }
                  else
                  {
                     jhPanel["s" + i].visible = false;
                  }
               }
               else if(partNum == 8)
               {
                  if(e2.getPosition() >= partNum && e2.getPosition() <= 9 && e2.getColor() >= 5)
                  {
                     jhPanel["s" + i].gotoAndStop(e2.getFrame());
                     jhPanel["s" + i].visible = true;
                  }
                  else
                  {
                     jhPanel["s" + i].visible = false;
                  }
               }
               else if(e2.getPosition() == partNum && e2.getColor() == 5)
               {
                  jhPanel["s" + i].gotoAndStop(e2.getFrame());
                  jhPanel["s" + i].visible = true;
               }
               else
               {
                  jhPanel["s" + i].visible = false;
               }
            }
            else
            {
               jhPanel["s" + i].visible = false;
            }
         }
      }
      
      public static function otherShow() : *
      {
         var i:uint = 0;
         var otherID:Number = NaN;
         arr_cl = [];
         count = 0;
         for(i = 0; i < arr.length; i++)
         {
            if(arr[i][0] == mapNum)
            {
               arr_cl.push(arr[i]);
            }
         }
         for(i = 0; i < 4; i++)
         {
            otherID = Number(arr_cl[i][1]);
            jhPanel["x" + i].gotoAndStop(OtherFactory.getFrame(otherID));
            jhPanel["n" + i].text = arr_cl[i][2];
            if(myplayer.getBag().getOtherobjNum(otherID) >= arr_cl[i][2])
            {
               jhPanel["c" + i].text = arr_cl[i][2];
               ColorX(jhPanel["c" + i],"0xFFFF00");
               ++count;
            }
            else
            {
               jhPanel["c" + i].text = myplayer.getBag().getOtherobjNum(otherID);
               ColorX(jhPanel["c" + i],"0xFF0000");
            }
         }
      }
      
      private static function ColorX(t:*, col:String) : *
      {
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         t.setTextFormat(myTextFormat);
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         jhPanel["close"].removeEventListener(MouseEvent.CLICK,closeJH);
         jhPanel["jh_btn"].removeEventListener(MouseEvent.CLICK,doJH);
         for(i = 0; i < 24; i++)
         {
            jhPanel["e" + i].mouseChildren = false;
            jhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            jhPanel["s" + i].mouseChildren = false;
            jhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         jhPanel["up_btn"].removeEventListener(MouseEvent.CLICK,upGo);
         jhPanel["down_btn"].removeEventListener(MouseEvent.CLICK,downGo);
      }
      
      public static function closeJH(e:*) : *
      {
         close();
      }
      
      public static function doJH(e:*) : *
      {
         if(nameStr == "e")
         {
            if(mapNum <= 6)
            {
               myplayer.getBag().getEquipFromBag(clickNum).setBlessAttrib();
            }
            else
            {
               myplayer.getBag().getEquipFromBag(clickNum).setBlessAttribLV2();
            }
         }
         else if(mapNum <= 6)
         {
            myplayer.getEquipSlot().getEquipFromSlot(clickNum).setBlessAttrib();
         }
         else
         {
            myplayer.getEquipSlot().getEquipFromSlot(clickNum).setBlessAttribLV2();
         }
         for(var i:int = 0; i < 4; i++)
         {
            myplayer.getBag().delOtherById(arr_cl[i][1],arr_cl[i][2]);
         }
         trace("装备祝福成功 >>",mapNum);
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备祝福成功");
         close();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         jhPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(str == "e")
         {
            overNum += 24 * yeshu;
            if(myplayer.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(myplayer.getBag().getEquipFromBag(overNum),1);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(overNum,myplayer.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = jhPanel.mouseX + 10;
         itemsTooltip.y = jhPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         selbool = true;
         clickObj = e.target as MovieClip;
         jhPanel["chose"].visible = true;
         jhPanel["chose"].x = clickObj.x - 2;
         jhPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         jhPanel["select"].gotoAndStop(clickObj.currentFrame);
         jhPanel["select"].visible = true;
         if(nameStr == "e")
         {
            clickNum += 24 * yeshu;
         }
         if(count >= 4)
         {
            jhPanel["jh_btn"].visible = true;
         }
      }
   }
}

