package _main_cs5_233333_fla
{
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4793")]
   public dynamic class 仓库已满_667 extends MovieClip
   {
      
      public function 仓库已满_667()
      {
         super();
         addFrameScript(40,this.frame41);
      }
      
      internal function frame41() : *
      {
         this.parent.visible = false;
      }
   }
}

