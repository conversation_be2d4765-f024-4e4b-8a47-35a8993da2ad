package com.hotpoint.braveManIII.repository.probability
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   import src.tool.*;
   
   public class GemProbabilityFactory
   {
      
      public static var isProbabilityOk:Boolean;
      
      public static var probabilityArr:Array = [];
      
      public static var probabilityData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function GemProbabilityFactory()
      {
         super();
      }
      
      public static function creatProbabilltyData() : void
      {
         myXml = XMLAsset.createXML(InData.GemProbabiltyData);
         var probabilltyData:GemProbabilityFactory = new GemProbabilityFactory();
         probabilltyData.creatLoard();
      }
      
      public static function getProbabilltyByFallId(fallId:Number) : EquipBaseProbabilityData
      {
         var data:EquipBaseProbabilityData = null;
         var proData:EquipBaseProbabilityData = null;
         for each(data in probabilityData)
         {
            if(data.getfallLevel() == fallId)
            {
               TiaoShi.txtShow("data.getfallLevel() = ?? " + data.getfallLevel());
               TiaoShi.txtShow("data._goldArr = ?? " + data._goldArr);
               proData = data;
            }
         }
         if(proData == null)
         {
            TiaoShi.txtShow("找不到此掉落等级!!");
            trace("找不到此掉落等级！！");
         }
         return proData;
      }
      
      public static function getPorbabillty(fallId:Number, strengthenLevel:Number) : Number
      {
         return getProbabilltyByFallId(fallId).getProbabil(strengthenLevel);
      }
      
      public static function getGold(fallId:Number, strengthenLevel:Number) : Number
      {
         return getProbabilltyByFallId(fallId).getGold(strengthenLevel);
      }
      
      public function EquipProbabilityFactory() : *
      {
      }
      
      private function creatLoard() : *
      {
         this.xmlLoaded();
      }
      
      internal function xmlLoaded() : *
      {
         var property:XML = null;
         var fallLevel:Number = NaN;
         var probabilityArr:XMLList = null;
         var goldArr:XMLList = null;
         var myArr:Array = null;
         var myArr2:Array = null;
         var equipProData:EquipBaseProbabilityData = null;
         for each(property in myXml.强化成功率)
         {
            fallLevel = Number(property.掉落等级);
            probabilityArr = property.成功率;
            goldArr = property.金币;
            myArr = [];
            myArr2 = [];
            myArr.push(probabilityArr.强化等级一);
            myArr.push(probabilityArr.强化等级二);
            myArr.push(probabilityArr.强化等级三);
            myArr.push(probabilityArr.强化等级四);
            myArr.push(probabilityArr.强化等级五);
            myArr2.push(goldArr.强化等级一);
            myArr2.push(goldArr.强化等级二);
            myArr2.push(goldArr.强化等级三);
            myArr2.push(goldArr.强化等级四);
            myArr2.push(goldArr.强化等级五);
            equipProData = EquipBaseProbabilityData.creatProbabilityData(fallLevel,myArr,myArr2);
            probabilityData.push(equipProData);
         }
         isProbabilityOk = true;
      }
   }
}

