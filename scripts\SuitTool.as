package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1685")]
   public dynamic class SuitTool extends MovieClip
   {
      
      public var append_txt:TextField;
      
      public var name0:TextField;
      
      public var name1:TextField;
      
      public var name2:TextField;
      
      public var name3:TextField;
      
      public var name_txt:TextField;
      
      public function SuitTool()
      {
         super();
      }
   }
}

