package com.hotpoint.braveManIII.views.itemsTooltip
{
   import com.hotpoint.braveManIII.models.container.EquipSlot;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.quest.Quest;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.equip.*;
   import flash.display.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   import src.tool.*;
   
   public class ItemsTooltip extends MovieClip
   {
      
      private static var myPlayer:Player;
      
      private var tooltip:MovieClip = new TooltipShow();
      
      private var newItemsTool:NewItemsTool = new NewItemsTool();
      
      public function ItemsTooltip()
      {
         super();
         this.mouseEnabled = false;
         this.tooltip.x = this.x = 0;
         this.tooltip.y = this.y = 0;
         this.addChild(this.tooltip);
         this.addChild(this.newItemsTool);
         this.newItemsTool.visible = false;
      }
      
      private function getParts(num:Number) : String
      {
         switch(num)
         {
            case 0:
               return "忍刀";
            case 1:
               return "头部";
            case 2:
               return "战甲";
            case 3:
               return "项链";
            case 4:
               return "戒指";
            case 5:
               return "大剑";
            case 6:
               return "法杖";
            case 7:
               return "拳套";
            case 8:
               return "时装";
            case 9:
               return "翅膀";
            case 10:
               return "头部";
            case 11:
               return "战甲";
            case 12:
               return "项链";
            case 13:
               return "戒指";
            default:
               return "未知";
         }
      }
      
      public function setTooltipPoint() : *
      {
         var myPoint:Point = new Point(0,0);
         myPoint = this.localToGlobal(myPoint);
         if(myPoint.y + this.height > 580)
         {
            this.y = 580 - this.height;
         }
         if(myPoint.x + this.width > 1000 && this.tooltip["right_mc"].visible == true)
         {
            this.x = myPoint.x - this.width - 60;
         }
         if(myPoint.y < 0)
         {
            this.y = 0;
         }
      }
      
      private function setPosition() : *
      {
         this.tooltip["star_0"].y = 25;
         this.tooltip["star_1"].y = 25;
         this.tooltip["star_2"].y = 25;
         this.tooltip["star_3"].y = 25;
         this.tooltip["star_4"].y = 25;
         this.tooltip["star_5"].y = 25;
         this.tooltip["type_txt"].y = 54;
         this.tooltip["lv2_txt"].y = 72;
         this.tooltip["lv_txt"].y = 72;
         this.tooltip["txt_qhmax"].y = 90;
         this.tooltip["line0"].y = 113;
         this.tooltip["line1"].y = 304;
         this.tooltip["txt_1"].y = 118;
         this.tooltip["txt_2"].y = 136;
         this.tooltip["txt_3"].y = 154;
         this.tooltip["txt_4"].y = 172;
         this.tooltip["txt_5"].y = 190;
         this.tooltip["txt_6"].y = 190;
         this.tooltip["txt_7"].y = 208;
         this.tooltip["txt_88"].y = 226;
         this.tooltip["txt_8"].y = 244;
         this.tooltip["txt_9"].y = 262;
         this.tooltip["txt_10"].y = 280;
         this.tooltip["txt_11"].y = 314;
         this.tooltip["txt_12"].y = 334;
         this.tooltip["txt_13"].y = 334;
         this.tooltip["txt_14"].y = 352;
         this.tooltip["txt_15"].y = 352;
         this.tooltip["explain"].y = 371;
         this.tooltip["price"].y = 419;
         this.tooltip["down_mc"].y = 427;
         this.tooltip["middle_mc"].y = 20;
         this.tooltip["gemslot_mc"].y = 313;
         this.tooltip["down_mc"].visible = true;
         this.tooltip["middle_mc"].visible = true;
         this.tooltip["middle_mc"].height = 408;
         this.tooltip["price"].visible = true;
         this.tooltip["explain"].visible = true;
         this.tooltip["gemslot_mc"].visible = true;
         this.tooltip["line1"].visible = true;
         this.tooltip["line0"].visible = true;
         this.tooltip["lv_txt"].visible = true;
         this.tooltip["lv2_txt"].visible = true;
         this.tooltip["txt_qhmax"].visible = true;
         for(var i:int = 1; i < 16; i++)
         {
            this.tooltip["txt_" + i].visible = true;
         }
         for(i = 0; i < 6; i++)
         {
            this.tooltip["star_" + i].visible = false;
            this.tooltip["star_" + i].gotoAndStop(1);
         }
         this.tooltip["txt_" + 88].visible = false;
         this.tooltip["txt_" + 15].visible = false;
      }
      
      private function ColorX(t:*, col:String) : *
      {
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         t.setTextFormat(myTextFormat);
      }
      
      private function setEquipColor(equip:Equip) : *
      {
         if(equip.getColor() == 1)
         {
            this.ColorX(this.tooltip["name_txt"],"0xffffff");
         }
         if(equip.getColor() == 2)
         {
            this.ColorX(this.tooltip["name_txt"],"0x0066ff");
         }
         if(equip.getColor() == 3)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF33FF");
         }
         if(equip.getColor() == 4)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF9900");
         }
         if(equip.getColor() == 5)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF9900");
         }
         if(equip.getColor() == 6)
         {
            this.ColorX(this.tooltip["name_txt"],"0xCC3300");
         }
      }
      
      public function suitTooltip(id:uint, equip:Equip) : void
      {
         var suitarr:Array = EquipFactory.getAllSuitEquipPostionAddName(id);
         var pos:Number = equip.getPosition();
         var numX:int = pos - 1;
         if(pos >= 10 && pos <= 13)
         {
            numX = pos - 10;
         }
         var nameStr:* = "name" + numX;
         for(var i:uint = 0; i < suitarr.length; i++)
         {
            this.tooltip["right_mc"]["name" + i].text = suitarr[i][1];
            this.ColorX(this.tooltip["right_mc"][nameStr],"0x00ff00");
         }
         this.ColorX(this.tooltip["right_mc"][nameStr],"0x00ff00");
         this.tooltip["right_mc"]["append_txt"].text = EquipFactory.getSuitEquipSkillAttrib(id);
      }
      
      public function gemAttribShow(equip:Equip) : Number
      {
         this.tooltip["txt_12"].visible = false;
         this.tooltip["txt_13"].visible = false;
         this.tooltip["txt_14"].visible = false;
         this.tooltip["txt_15"].visible = false;
         var temp:uint = 1;
         var arr:Array = [];
         arr = equip.getGemAttrib();
         if(equip.getGemSlot().getType() == 3)
         {
            switch(equip.getGemSlot().getColor())
            {
               case 1:
                  this.tooltip["gemslot_mc"].gotoAndStop(2);
                  break;
               case 2:
                  this.tooltip["gemslot_mc"].gotoAndStop(3);
                  break;
               case 3:
                  this.tooltip["gemslot_mc"].gotoAndStop(4);
                  break;
               case 4:
                  this.tooltip["gemslot_mc"].gotoAndStop(5);
            }
            if(equip.getGemSlot().getID() >= 33610 && equip.getGemSlot().getID() <= 33615)
            {
               this.tooltip["gemslot_mc"].gotoAndStop(6);
            }
         }
         for(var i:uint = 0; i < arr.length; i++)
         {
            this.tooltip["txt_" + (12 + i)].text = arr[i];
            this.tooltip["txt_" + (12 + i)].visible = true;
         }
         if(arr.length >= 3)
         {
            temp = 0;
         }
         return temp;
      }
      
      public function equipTooltip(eq:Equip, role:Number = 1, temp:Boolean = false) : void
      {
         var strArr:Array = null;
         var addNum:int = 0;
         var starNum:int = 0;
         var starNumX:int = 0;
         var s:int = 0;
         var equip:Equip = eq;
         if(equip.getPosition() > 7 && equip.getPosition() < 10)
         {
            this.tooltip.visible = false;
            this.newItemsTool.visible = true;
            this.newItemsTool.equipTooltip(equip);
         }
         else
         {
            this.tooltip.visible = true;
            this.newItemsTool.visible = false;
         }
         this.setPosition();
         this.tooltip["name_txt"].text = equip.getName();
         this.tooltip["type_txt"].text = "部位:" + this.getParts(equip.getPosition());
         if(equip.getDressLevel() == 100)
         {
            this.tooltip["lv_txt"].text = "无限制";
         }
         else
         {
            this.tooltip["lv_txt"].text = equip.getDressLevel();
         }
         if(role == 1)
         {
            if(equip.getDressLevel() <= Main.player1.getLevel() || equip.getDressLevel() == 100)
            {
               this.ColorX(this.tooltip["lv_txt"],"0xffffff");
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         else if(role == 2)
         {
            if(equip.getDressLevel() <= Main.player2.getLevel() || equip.getDressLevel() == 100)
            {
               this.ColorX(this.tooltip["lv_txt"],"0xffffff");
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         else
         {
            this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
         }
         if(equip.getQianghuaMAX() - equip.getReinforceLevel() < 1)
         {
            this.tooltip["txt_qhmax"].text = "强化已达上限";
         }
         else
         {
            this.tooltip["txt_qhmax"].text = "剩余强化次数：" + int(equip.getQianghuaMAX() - equip.getReinforceLevel());
         }
         this.tooltip["explain"].text = equip.getDescript();
         this.tooltip["price"].text = "售价:" + equip.getPrice();
         this.tooltip["type_txt"].visible = true;
         var zhufuTemp:* = 0;
         var starTemp:* = 0;
         var gemTemp:* = 0;
         var zengfuTemp:* = 0;
         var characterTemp:* = 0;
         var strengthenTemp:* = 0;
         var baseTemp:* = 0;
         var appendTemp:* = 0;
         this.tooltip["line1"].visible = false;
         this.tooltip["txt_11"].visible = false;
         this.tooltip["txt_12"].visible = false;
         this.tooltip["txt_13"].visible = false;
         this.tooltip["txt_14"].visible = false;
         this.tooltip["txt_15"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         if(equip.getGrid() == -1)
         {
            gemTemp = 74;
         }
         else
         {
            this.tooltip["line1"].visible = true;
            this.tooltip["txt_11"].visible = true;
            this.tooltip["gemslot_mc"].visible = true;
            if(equip.getGrid() == 0)
            {
               gemTemp = this.gemAttribShow(equip) * 18;
            }
            else
            {
               this.tooltip["gemslot_mc"].gotoAndStop(1);
               gemTemp = 36;
            }
         }
         if(equip.getNewSkill() == 0)
         {
            this.tooltip["txt_10"].visible = false;
            zengfuTemp = 18;
         }
         else
         {
            this.tooltip["txt_10"].visible = true;
            this.tooltip["txt_10"].text = equip.getEquipNewSkill();
         }
         if(equip.getSkillAttrib() == 0)
         {
            this.tooltip["txt_9"].visible = false;
            characterTemp = 18;
         }
         else
         {
            this.tooltip["txt_9"].visible = true;
            this.tooltip["txt_9"].text = equip.getEquipSkillAttrib();
         }
         if(equip.getSuitId() == 0)
         {
            this.tooltip["right_mc"].visible = false;
         }
         else
         {
            this.tooltip["right_mc"].visible = true;
            this.suitTooltip(equip.getSuitId(),equip);
         }
         if(equip.getHuanHua() == 0)
         {
            this.tooltip["huanhua_mc"].visible = false;
         }
         else
         {
            this.tooltip["huanhua_mc"].visible = true;
            strArr = equip.getHHExplain().split("$");
            this.tooltip["huanhua_mc"]["hhtop_txt"].text = strArr[0];
            this.tooltip["huanhua_mc"]["hh_txt"].text = strArr[1];
         }
         if(equip.getReinforceLevel() == 0)
         {
            this.tooltip["txt_8"].visible = false;
            strengthenTemp = 18;
         }
         else
         {
            this.tooltip["name_txt"].text += "+" + equip.getReinforceLevel();
            this.tooltip["txt_8"].text = "强化：" + equip.showReinforceAttrib();
            this.tooltip["txt_8"].visible = true;
         }
         if(equip.getBlessAttrib())
         {
            this.tooltip["txt_88"].visible = true;
            this.tooltip["txt_88"].text = equip.showBlessAttrib();
         }
         else
         {
            zhufuTemp = 18;
            this.tooltip["txt_88"].visible = false;
         }
         for(var k:uint = 1; k < 8; k++)
         {
            this.tooltip["txt_" + k].visible = true;
         }
         var Attribarr:Array = equip.showBaseAttrib();
         var temp1:uint = 0;
         var temp2:uint = 4;
         for(var i:uint = 0; i < Attribarr.length; i++)
         {
            if(Attribarr[i][0] == 1)
            {
               temp1++;
               this.tooltip["txt_" + temp1].text = Attribarr[i][1];
               if(temp)
               {
                  this.tooltip["txt_" + temp1].text = EquipFactory.getShowStr1(eq.getId(),i);
               }
            }
            else
            {
               temp2++;
               this.tooltip["txt_" + temp2].text = Attribarr[i][1];
               if(temp)
               {
                  this.tooltip["txt_" + temp2].text = "随机属性";
               }
            }
         }
         if(equip.getId() == 14667 || equip.getId() == 14668 || equip.getId() == 14669)
         {
            if(role == 1)
            {
               myPlayer = Main.player_1;
            }
            else
            {
               myPlayer = Main.player_2;
            }
            if(role == 3)
            {
               addNum = 0;
            }
            else
            {
               addNum = int(ShengDan2013.WQchengzhang(equip.getId(),myPlayer.data.getLevel()));
            }
            this.tooltip["txt_1"].text = "攻击+" + addNum;
         }
         for(var j:uint = uint(temp1 + 1); j < 5; j++)
         {
            this.tooltip["txt_" + j].visible = false;
            baseTemp += 18;
         }
         if(temp2 == 6)
         {
            this.tooltip["txt_7"].visible = false;
            appendTemp += 18;
         }
         else if(temp2 == 5)
         {
            this.tooltip["txt_6"].visible = false;
            this.tooltip["txt_7"].visible = false;
            appendTemp += 18;
         }
         else if(temp2 == 4)
         {
            this.tooltip["txt_5"].visible = false;
            this.tooltip["txt_6"].visible = false;
            this.tooltip["txt_7"].visible = false;
            appendTemp += 36;
         }
         if(equip.getColor() < 5)
         {
            starTemp = 25;
         }
         else
         {
            this.tooltip["star_0"].visible = true;
            starNum = equip.getStar();
            starNumX = starNum % 5;
            for(s = 1; s <= 5; s++)
            {
               this.tooltip["star_" + s].visible = false;
               this.tooltip["star_" + s].gotoAndStop(1);
               if(starNum > 5)
               {
                  this.tooltip["star_" + s].visible = true;
               }
               if(s <= starNumX || starNum == 5 || starNum == 10)
               {
                  this.tooltip["star_" + s].visible = true;
                  if(starNum > 5)
                  {
                     this.tooltip["star_" + s].gotoAndStop(2);
                  }
               }
            }
         }
         this.tooltip["price"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["explain"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["middle_mc"].height -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["down_mc"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_15"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_14"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_13"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_12"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_11"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["line1"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["gemslot_mc"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_10"].y -= zhufuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_9"].y -= zhufuTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_8"].y -= zhufuTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_88"].y -= baseTemp + appendTemp + starTemp;
         this.tooltip["txt_7"].y -= baseTemp + appendTemp + starTemp;
         this.tooltip["txt_6"].y -= baseTemp + starTemp;
         this.tooltip["txt_5"].y -= baseTemp + starTemp;
         this.tooltip["txt_4"].y -= starTemp;
         this.tooltip["txt_3"].y -= starTemp;
         this.tooltip["txt_2"].y -= starTemp;
         this.tooltip["txt_1"].y -= starTemp;
         this.tooltip["line0"].y -= starTemp;
         this.tooltip["txt_qhmax"].y -= starTemp;
         this.tooltip["lv_txt"].y -= starTemp;
         this.tooltip["lv2_txt"].y -= starTemp;
         this.tooltip["type_txt"].y -= starTemp;
         this.setEquipColor(equip);
      }
      
      public function equipTooltipSP(eq:Equip, role:Number = 1) : void
      {
         var starNum:int = 0;
         var starNumX:int = 0;
         var s:int = 0;
         var equip:Equip = eq;
         if(equip.getPosition() > 7 && equip.getPosition() < 10)
         {
            this.tooltip.visible = false;
            this.newItemsTool.visible = true;
            this.newItemsTool.equipTooltip(equip);
         }
         else
         {
            this.tooltip.visible = true;
            this.newItemsTool.visible = false;
         }
         this.setPosition();
         this.tooltip["name_txt"].text = equip.getName();
         this.tooltip["type_txt"].text = "部位：" + this.getParts(equip.getPosition());
         if(equip.getDressLevel() == 100)
         {
            this.tooltip["lv_txt"].text = "无限制";
         }
         else
         {
            this.tooltip["lv_txt"].text = equip.getDressLevel();
         }
         if(role == 1)
         {
            if(equip.getDressLevel() <= Main.player1.getLevel() || equip.getDressLevel() == 100)
            {
               this.ColorX(this.tooltip["lv_txt"],"0xffffff");
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         else if(role == 2)
         {
            if(equip.getDressLevel() <= Main.player2.getLevel() || equip.getDressLevel() == 100)
            {
               this.ColorX(this.tooltip["lv_txt"],"0xffffff");
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         else
         {
            this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
         }
         if(equip.getQianghuaMAX() - equip.getReinforceLevel() < 1)
         {
            this.tooltip["txt_qhmax"].text = "强化已达上限";
         }
         else
         {
            this.tooltip["txt_qhmax"].text = "剩余强化次数：" + int(equip.getQianghuaMAX() - equip.getReinforceLevel());
         }
         this.tooltip["explain"].text = equip.getDescript();
         this.tooltip["price"].text = "售价:" + equip.getPrice();
         this.tooltip["type_txt"].visible = true;
         var zhufuTemp:* = 0;
         var starTemp:* = 0;
         var gemTemp:* = 0;
         var zengfuTemp:* = 0;
         var characterTemp:* = 0;
         var strengthenTemp:* = 0;
         var baseTemp:* = 0;
         var appendTemp:* = 0;
         this.tooltip["line1"].visible = false;
         this.tooltip["txt_11"].visible = false;
         this.tooltip["txt_12"].visible = false;
         this.tooltip["txt_13"].visible = false;
         this.tooltip["txt_14"].visible = false;
         this.tooltip["txt_15"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         if(equip.getGrid() == -1)
         {
            gemTemp = 74;
         }
         else
         {
            this.tooltip["line1"].visible = true;
            this.tooltip["txt_11"].visible = true;
            this.tooltip["gemslot_mc"].visible = true;
            if(equip.getGrid() == 0)
            {
               gemTemp = this.gemAttribShow(equip) * 18;
            }
            else
            {
               this.tooltip["gemslot_mc"].gotoAndStop(1);
               gemTemp = 36;
            }
         }
         if(equip.getNewSkill() == 0)
         {
            this.tooltip["txt_10"].visible = false;
            zengfuTemp = 18;
         }
         else
         {
            this.tooltip["txt_10"].visible = true;
            this.tooltip["txt_10"].text = equip.getEquipNewSkill();
         }
         if(equip.getSkillAttrib() == 0)
         {
            this.tooltip["txt_9"].visible = false;
            characterTemp = 18;
         }
         else
         {
            this.tooltip["txt_9"].visible = true;
            this.tooltip["txt_9"].text = equip.getEquipSkillAttrib();
         }
         this.tooltip["right_mc"].visible = false;
         this.tooltip["huanhua_mc"].visible = false;
         if(equip.getReinforceLevel() == 0)
         {
            this.tooltip["txt_8"].visible = false;
            strengthenTemp = 18;
         }
         else
         {
            this.tooltip["name_txt"].text += "+" + equip.getReinforceLevel();
            this.tooltip["txt_8"].text = "强化：" + equip.showReinforceAttrib();
            this.tooltip["txt_8"].visible = true;
         }
         if(equip.getBlessAttrib())
         {
            this.tooltip["txt_88"].visible = true;
            this.tooltip["txt_88"].text = equip.showBlessAttrib();
         }
         else
         {
            zhufuTemp = 18;
            this.tooltip["txt_88"].visible = false;
         }
         for(var k:uint = 1; k < 8; k++)
         {
            this.tooltip["txt_" + k].visible = true;
         }
         var Attribarr:Array = equip.showBaseAttrib();
         var temp1:uint = 0;
         var temp2:uint = 4;
         for(var i:uint = 0; i < Attribarr.length; i++)
         {
            if(Attribarr[i][0] == 1)
            {
               temp1++;
               this.tooltip["txt_" + temp1].text = Attribarr[i][1];
            }
            else
            {
               temp2++;
               this.tooltip["txt_" + temp2].text = Attribarr[i][1];
            }
         }
         for(var j:uint = uint(temp1 + 1); j < 5; j++)
         {
            this.tooltip["txt_" + j].visible = false;
            baseTemp += 18;
         }
         if(temp2 == 6)
         {
            this.tooltip["txt_7"].visible = false;
            appendTemp += 18;
         }
         else if(temp2 == 5)
         {
            this.tooltip["txt_6"].visible = false;
            this.tooltip["txt_7"].visible = false;
            appendTemp += 18;
         }
         else if(temp2 == 4)
         {
            this.tooltip["txt_5"].visible = false;
            this.tooltip["txt_6"].visible = false;
            this.tooltip["txt_7"].visible = false;
            appendTemp += 36;
         }
         if(equip.getColor() < 5)
         {
            starTemp = 25;
         }
         else
         {
            this.tooltip["star_0"].visible = true;
            starNum = equip.getStar();
            starNumX = starNum % 5;
            trace("显示升星数2:",starNum,starNumX);
            for(s = 1; s <= 5; s++)
            {
               this.tooltip["star_" + s].visible = false;
               if(starNum > 5)
               {
                  this.tooltip["star_" + s].visible = true;
               }
               if(s <= starNumX || starNum == 5 || starNum == 10)
               {
                  this.tooltip["star_" + s].visible = true;
                  this.tooltip["star_" + s].gotoAndStop(1);
                  if(starNum > 5)
                  {
                     this.tooltip["star_" + s].gotoAndStop(2);
                  }
               }
            }
         }
         this.tooltip["price"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["explain"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["middle_mc"].height -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["down_mc"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_15"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_14"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_13"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_12"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_11"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["line1"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["gemslot_mc"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_10"].y -= zhufuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_9"].y -= zhufuTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_88"].y -= baseTemp + appendTemp + starTemp;
         this.tooltip["txt_8"].y -= zhufuTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_7"].y -= baseTemp + appendTemp + starTemp;
         this.tooltip["txt_6"].y -= baseTemp + starTemp;
         this.tooltip["txt_5"].y -= baseTemp + starTemp;
         this.tooltip["txt_4"].y -= starTemp;
         this.tooltip["txt_3"].y -= starTemp;
         this.tooltip["txt_2"].y -= starTemp;
         this.tooltip["txt_1"].y -= starTemp;
         this.tooltip["line0"].y -= starTemp;
         this.tooltip["txt_qhmax"].y -= starTemp;
         this.tooltip["lv_txt"].y -= starTemp;
         this.tooltip["lv2_txt"].y -= starTemp;
         this.tooltip["type_txt"].y -= starTemp;
         this.setEquipColor(equip);
      }
      
      private function setSuppliesColor(supplies:Supplies) : *
      {
         if(supplies.getColor() == 1)
         {
            this.ColorX(this.tooltip["name_txt"],"0xffffff");
         }
         if(supplies.getColor() == 2)
         {
            this.ColorX(this.tooltip["name_txt"],"0x0066ff");
         }
         if(supplies.getColor() == 3)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF33FF");
         }
      }
      
      public function suppliesTooltip(su:Supplies, role:Number = 1) : void
      {
         this.tooltip.visible = true;
         this.newItemsTool.visible = false;
         var supplies:Supplies = su;
         this.setPosition();
         this.tooltip["name_txt"].text = supplies.getName();
         this.tooltip["lv_txt"].text = supplies.getUseLevel();
         if(role == 1)
         {
            if(supplies.getUseLevel() <= Main.player1.getLevel())
            {
               this.ColorX(this.tooltip["lv_txt"],"0xffffff");
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         else if(role == 2)
         {
            if(supplies.getUseLevel() <= Main.player2.getLevel())
            {
               this.ColorX(this.tooltip["lv_txt"],"0xffffff");
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         else
         {
            this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
         }
         this.tooltip["price"].text = "出售价：" + supplies.getPrice();
         this.tooltip["explain"].text = supplies.getDescript();
         this.tooltip["line0"].visible = false;
         this.tooltip["line1"].visible = false;
         this.tooltip["type_txt"].visible = false;
         this.tooltip["txt_qhmax"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         this.tooltip["right_mc"].visible = false;
         this.tooltip["huanhua_mc"].visible = false;
         for(var i:uint = 1; i < 16; i++)
         {
            this.tooltip["txt_" + i].visible = false;
         }
         this.tooltip["lv_txt"].y -= 36;
         this.tooltip["lv2_txt"].y -= 36;
         this.tooltip["price"].y -= 306;
         this.tooltip["explain"].y -= 306;
         this.tooltip["middle_mc"].height -= 306;
         this.tooltip["down_mc"].y -= 306;
         this.setSuppliesColor(supplies);
      }
      
      private function setGemColor(gem:Gem) : *
      {
         if(gem.getColor() == 1)
         {
            this.ColorX(this.tooltip["name_txt"],"0xffffff");
         }
         if(gem.getColor() == 2)
         {
            this.ColorX(this.tooltip["name_txt"],"0x0066ff");
         }
         if(gem.getColor() == 3)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF33FF");
         }
         if(gem.getColor() == 4)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF9900");
         }
      }
      
      public function gemTooltip(gg:Gem, role:Number = 1) : void
      {
         var arr:Array = null;
         this.tooltip.visible = true;
         this.newItemsTool.visible = false;
         var gem:Gem = gg;
         this.setPosition();
         if(gem.getIsStrengthen() == true)
         {
            if(gem.getStrengthenLevel() >= 1)
            {
               this.tooltip["name_txt"].text = gem.getName() + "+" + gem.getStrengthenLevel();
            }
            else
            {
               this.tooltip["name_txt"].text = gem.getName();
            }
         }
         else
         {
            this.tooltip["name_txt"].text = gem.getName();
         }
         this.tooltip["lv_txt"].text = gem.getUseLevel();
         this.tooltip["price"].text = "出售价：" + gem.getPrice();
         this.tooltip["explain"].text = gem.getDescript();
         this.tooltip["line0"].visible = false;
         this.tooltip["line1"].visible = false;
         this.tooltip["type_txt"].visible = false;
         this.tooltip["txt_qhmax"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         this.tooltip["right_mc"].visible = false;
         this.tooltip["huanhua_mc"].visible = false;
         for(var i:uint = 1; i < 16; i++)
         {
            this.tooltip["txt_" + i].visible = false;
         }
         var temp:int = 1;
         var temp_lv:* = 0;
         if(gem.getType() == GemTypeConst.ATTRIBSTONE)
         {
            arr = [];
            arr = gem.showGemAttrib();
            for(i = 0; i < arr.length; i++)
            {
               this.tooltip["txt_" + (12 + i)].text = arr[i];
               this.tooltip["txt_" + (12 + i)].visible = true;
            }
            if(arr.length >= 3)
            {
               temp = 0;
            }
            this.ColorX(this.tooltip["lv_txt"],"0xffffff");
         }
         if(gem.getType() == GemTypeConst.FRAGMENT || gem.getType() == GemTypeConst.LUCKSTONE || gem.getType() == GemTypeConst.STRENGTHENSTONE)
         {
            this.tooltip["lv2_txt"].visible = false;
            this.tooltip["lv_txt"].visible = false;
            temp_lv = 20;
         }
         else if(gem.getType() == GemTypeConst.SKILLSTONE)
         {
            if(role == 1)
            {
               if(gem.getUseLevel() <= Main.player1.getLevel())
               {
                  this.ColorX(this.tooltip["lv_txt"],"0xffffff");
               }
               else
               {
                  this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
               }
            }
            else if(role == 2)
            {
               if(gem.getUseLevel() <= Main.player2.getLevel())
               {
                  this.ColorX(this.tooltip["lv_txt"],"0xffffff");
               }
               else
               {
                  this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
               }
            }
            else
            {
               this.ColorX(this.tooltip["lv_txt"],"0xFF0000");
            }
         }
         this.tooltip["lv_txt"].y -= 40;
         this.tooltip["lv2_txt"].y -= 40;
         this.tooltip["txt_12"].y -= 284;
         this.tooltip["txt_13"].y -= 284;
         this.tooltip["txt_14"].y -= 284;
         this.tooltip["txt_15"].y -= 284;
         this.tooltip["price"].y -= 284 + 18 * temp + temp_lv;
         this.tooltip["explain"].y -= 284 + 18 * temp + temp_lv;
         this.tooltip["middle_mc"].height -= 284 + 18 * temp + temp_lv;
         this.tooltip["down_mc"].y -= 284 + 18 * temp + temp_lv;
         this.setGemColor(gem);
      }
      
      private function setEquipSlotColor(overNum:uint, equipSlot:EquipSlot) : *
      {
         if(equipSlot.getEquipFromSlot(overNum).getColor() == 1)
         {
            this.ColorX(this.tooltip["name_txt"],"0xffffff");
         }
         if(equipSlot.getEquipFromSlot(overNum).getColor() == 2)
         {
            this.ColorX(this.tooltip["name_txt"],"0x0066ff");
         }
         if(equipSlot.getEquipFromSlot(overNum).getColor() == 3)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF33FF");
         }
         if(equipSlot.getEquipFromSlot(overNum).getColor() == 4)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF9900");
         }
         if(equipSlot.getEquipFromSlot(overNum).getColor() == 5)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF9900");
         }
         if(equipSlot.getEquipFromSlot(overNum).getColor() == 6)
         {
            this.ColorX(this.tooltip["name_txt"],"0xCC3300");
         }
      }
      
      public function slotSuitTooltip(id:uint, overNum:uint, equipSlot:EquipSlot) : void
      {
         var j:* = undefined;
         var numXX:int = 0;
         var eq:Equip = null;
         this.tooltip.visible = true;
         this.newItemsTool.visible = false;
         var temp:uint = 0;
         var suitarr:Array = EquipFactory.getAllSuitEquipPostionAddName(id);
         for(var i:uint = 0; i < suitarr.length; i++)
         {
            this.tooltip["right_mc"]["name" + i].text = suitarr[i][1];
         }
         var bwArr:Array = [0,1,3,4];
         if(Main.water.getValue() != 1)
         {
            bwArr = [8,9,11,12];
         }
         for(j in bwArr)
         {
            numXX = int(bwArr[j]);
            eq = equipSlot.getEquipFromSlot(numXX);
            trace("套装对应装备槽位置 >>",id,j,numXX);
            if(Boolean(eq) && eq.getSuitId() == id)
            {
               this.ColorX(this.tooltip["right_mc"]["name" + j],"0x00ff00");
               temp++;
            }
         }
         this.tooltip["right_mc"]["append_txt"].text = EquipFactory.getSuitEquipSkillAttrib(id);
         if(temp == 4)
         {
            this.ColorX(this.tooltip["right_mc"]["append_txt"],"0x00CCFF");
         }
      }
      
      public function slotTooltip(overNum:uint, equipSlot:EquipSlot, other:Boolean = false) : void
      {
         var strArr:Array = null;
         var addNum:int = 0;
         var starNum:int = 0;
         var starNumX:int = 0;
         var s:int = 0;
         if(Main.water.getValue() != 1 && (overNum == 0 || overNum == 1 || overNum == 3 || overNum == 4))
         {
            overNum += 8;
         }
         if(equipSlot.getEquipFromSlot(overNum).getPosition() > 7 && equipSlot.getEquipFromSlot(overNum).getPosition() < 10)
         {
            this.tooltip.visible = false;
            this.newItemsTool.visible = true;
            this.newItemsTool.equipTooltip(equipSlot.getEquipFromSlot(overNum));
         }
         else
         {
            this.tooltip.visible = true;
            this.newItemsTool.visible = false;
         }
         this.setPosition();
         this.tooltip["name_txt"].text = equipSlot.getEquipFromSlot(overNum).getName();
         this.tooltip["type_txt"].text = "部位：" + this.getParts(equipSlot.getEquipFromSlot(overNum).getPosition());
         if(equipSlot.getEquipFromSlot(overNum).getDressLevel() == 100)
         {
            this.tooltip["lv_txt"].text = "无限制";
         }
         else
         {
            this.tooltip["lv_txt"].text = equipSlot.getEquipFromSlot(overNum).getDressLevel();
         }
         if(equipSlot.getEquipFromSlot(overNum).getQianghuaMAX() - equipSlot.getEquipFromSlot(overNum).getReinforceLevel() < 1)
         {
            this.tooltip["txt_qhmax"].text = "强化已达上限";
         }
         else
         {
            this.tooltip["txt_qhmax"].text = "剩余强化次数：" + int(equipSlot.getEquipFromSlot(overNum).getQianghuaMAX() - equipSlot.getEquipFromSlot(overNum).getReinforceLevel());
         }
         this.ColorX(this.tooltip["lv_txt"],"0xffffff");
         this.tooltip["explain"].text = equipSlot.getEquipFromSlot(overNum).getDescript();
         this.tooltip["price"].text = "出售价:" + equipSlot.getEquipFromSlot(overNum).getPrice();
         this.tooltip["type_txt"].visible = true;
         var zhufuTemp:* = 0;
         var starTemp:* = 0;
         var zengfuTemp:* = 0;
         var gemTemp:* = 0;
         var characterTemp:* = 0;
         var strengthenTemp:* = 0;
         var baseTemp:* = 0;
         var appendTemp:* = 0;
         this.tooltip["line1"].visible = false;
         this.tooltip["txt_11"].visible = false;
         this.tooltip["txt_12"].visible = false;
         this.tooltip["txt_13"].visible = false;
         this.tooltip["txt_14"].visible = false;
         this.tooltip["txt_15"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         if(equipSlot.getEquipFromSlot(overNum).getGrid() == -1)
         {
            gemTemp = 74;
         }
         else
         {
            this.tooltip["line1"].visible = true;
            this.tooltip["txt_11"].visible = true;
            this.tooltip["gemslot_mc"].visible = true;
            if(equipSlot.getEquipFromSlot(overNum).getGrid() == 0)
            {
               gemTemp = this.slotGemAttribShow(overNum,equipSlot) * 18;
            }
            else
            {
               this.tooltip["gemslot_mc"].gotoAndStop(1);
               gemTemp = 36;
            }
         }
         if(equipSlot.getEquipFromSlot(overNum).getSkillAttrib() == 0)
         {
            this.tooltip["txt_9"].visible = false;
            characterTemp = 18;
         }
         else
         {
            this.tooltip["txt_9"].visible = true;
            this.tooltip["txt_9"].text = equipSlot.getEquipFromSlot(overNum).getEquipSkillAttrib();
         }
         if(equipSlot.getEquipFromSlot(overNum).getNewSkill() == 0)
         {
            this.tooltip["txt_10"].visible = false;
            zengfuTemp = 18;
         }
         else
         {
            this.tooltip["txt_10"].visible = true;
            this.tooltip["txt_10"].text = equipSlot.getEquipFromSlot(overNum).getEquipNewSkill();
         }
         if(equipSlot.getEquipFromSlot(overNum).getSuitId() == 0)
         {
            this.tooltip["right_mc"].visible = false;
         }
         else
         {
            this.tooltip["right_mc"].visible = true;
            this.slotSuitTooltip(equipSlot.getEquipFromSlot(overNum).getSuitId(),overNum,equipSlot);
         }
         if(equipSlot.getEquipFromSlot(overNum).getHuanHua() == 0)
         {
            this.tooltip["huanhua_mc"].visible = false;
         }
         else
         {
            this.tooltip["huanhua_mc"].visible = true;
            strArr = equipSlot.getEquipFromSlot(overNum).getHHExplain().split("$");
            this.tooltip["huanhua_mc"]["hhtop_txt"].text = strArr[0];
            this.tooltip["huanhua_mc"]["hh_txt"].text = strArr[1];
         }
         if(equipSlot.getEquipFromSlot(overNum).getReinforceLevel() == 0)
         {
            this.tooltip["txt_8"].visible = false;
            strengthenTemp = 18;
         }
         else
         {
            this.tooltip["name_txt"].text += "+" + equipSlot.getEquipFromSlot(overNum).getReinforceLevel();
            this.tooltip["txt_8"].text = "强化：" + equipSlot.getEquipFromSlot(overNum).showReinforceAttrib();
            this.tooltip["txt_8"].visible = true;
         }
         if(equipSlot.getEquipFromSlot(overNum).getBlessAttrib())
         {
            this.tooltip["txt_88"].visible = true;
            this.tooltip["txt_88"].text = equipSlot.getEquipFromSlot(overNum).showBlessAttrib();
         }
         else
         {
            zhufuTemp = 18;
            this.tooltip["txt_88"].visible = false;
         }
         for(var k:uint = 1; k < 8; k++)
         {
            this.tooltip["txt_" + k].visible = true;
         }
         var Attribarr:Array = equipSlot.getEquipFromSlot(overNum).showBaseAttrib();
         var temp1:uint = 0;
         var temp2:uint = 4;
         for(var i:uint = 0; i < Attribarr.length; i++)
         {
            if(Attribarr[i][0] == 1)
            {
               temp1++;
               this.tooltip["txt_" + temp1].text = Attribarr[i][1];
            }
            else
            {
               temp2++;
               this.tooltip["txt_" + temp2].text = Attribarr[i][1];
            }
         }
         if(equipSlot.getEquipFromSlot(overNum).getId() == 14667 || equipSlot.getEquipFromSlot(overNum).getId() == 14668 || equipSlot.getEquipFromSlot(overNum).getId() == 14669)
         {
            addNum = int(ShengDan2013.WQchengzhang(equipSlot.getEquipFromSlot(overNum).getId(),equipSlot.who.getLevel()));
            this.tooltip["txt_1"].text = "攻击+" + addNum;
         }
         for(var j:uint = uint(temp1 + 1); j < 5; j++)
         {
            this.tooltip["txt_" + j].visible = false;
            baseTemp += 18;
         }
         if(temp2 == 6)
         {
            this.tooltip["txt_7"].visible = false;
            appendTemp += 18;
         }
         else if(temp2 == 5)
         {
            this.tooltip["txt_6"].visible = false;
            this.tooltip["txt_7"].visible = false;
            appendTemp += 18;
         }
         else if(temp2 == 4)
         {
            this.tooltip["txt_5"].visible = false;
            this.tooltip["txt_6"].visible = false;
            this.tooltip["txt_7"].visible = false;
            appendTemp += 36;
         }
         if(equipSlot.getEquipFromSlot(overNum).getColor() < 5)
         {
            starTemp = 25;
         }
         else
         {
            this.tooltip["star_0"].visible = true;
            starNum = equipSlot.getEquipFromSlot(overNum).getStar();
            starNumX = starNum % 5;
            trace("显示升星数3:",starNum,starNumX);
            for(s = 1; s <= 5; s++)
            {
               this.tooltip["star_" + s].visible = false;
               if(starNum > 5)
               {
                  this.tooltip["star_" + s].visible = true;
               }
               if(s <= starNumX || starNum == 5 || starNum == 10)
               {
                  this.tooltip["star_" + s].visible = true;
                  this.tooltip["star_" + s].gotoAndStop(1);
                  if(starNum > 5)
                  {
                     this.tooltip["star_" + s].gotoAndStop(2);
                  }
               }
            }
         }
         this.tooltip["price"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["explain"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["middle_mc"].height -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["down_mc"].y -= zhufuTemp + gemTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_15"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_14"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_13"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_12"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_11"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["line1"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["gemslot_mc"].y -= zhufuTemp + zengfuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_10"].y -= zhufuTemp + characterTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_9"].y -= zhufuTemp + strengthenTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_88"].y -= baseTemp + appendTemp + starTemp;
         this.tooltip["txt_8"].y -= zhufuTemp + baseTemp + appendTemp + starTemp;
         this.tooltip["txt_7"].y -= baseTemp + appendTemp + starTemp;
         this.tooltip["txt_6"].y -= baseTemp + starTemp;
         this.tooltip["txt_5"].y -= baseTemp + starTemp;
         this.tooltip["txt_4"].y -= starTemp;
         this.tooltip["txt_3"].y -= starTemp;
         this.tooltip["txt_2"].y -= starTemp;
         this.tooltip["txt_1"].y -= starTemp;
         this.tooltip["line0"].y -= starTemp;
         this.tooltip["txt_qhmax"].y -= starTemp;
         this.tooltip["lv_txt"].y -= starTemp;
         this.tooltip["lv2_txt"].y -= starTemp;
         this.tooltip["type_txt"].y -= starTemp;
         this.setEquipSlotColor(overNum,equipSlot);
      }
      
      public function slotGemAttribShow(overNum:uint, equipSlot:EquipSlot) : Number
      {
         var idXX:int = 0;
         this.tooltip["txt_12"].visible = false;
         this.tooltip["txt_13"].visible = false;
         this.tooltip["txt_14"].visible = false;
         this.tooltip["txt_15"].visible = false;
         var temp:uint = 1;
         var arr:Array = [];
         arr = equipSlot.getEquipFromSlot(overNum).getGemAttrib();
         if(equipSlot.getEquipFromSlot(overNum).getGemSlot().getType() == 3)
         {
            switch(equipSlot.getEquipFromSlot(overNum).getGemSlot().getColor())
            {
               case 1:
                  this.tooltip["gemslot_mc"].gotoAndStop(2);
                  break;
               case 2:
                  this.tooltip["gemslot_mc"].gotoAndStop(3);
                  break;
               case 3:
                  this.tooltip["gemslot_mc"].gotoAndStop(4);
                  break;
               case 4:
                  this.tooltip["gemslot_mc"].gotoAndStop(5);
            }
            idXX = equipSlot.getEquipFromSlot(overNum).getGemSlot().getID();
            if(idXX >= 33610 && idXX <= 33615)
            {
               this.tooltip["gemslot_mc"].gotoAndStop(6);
            }
         }
         for(var i:uint = 0; i < arr.length; i++)
         {
            this.tooltip["txt_" + (12 + i)].text = arr[i];
            this.tooltip["txt_" + (12 + i)].visible = true;
         }
         if(arr.length >= 3)
         {
            temp = 0;
         }
         return temp;
      }
      
      public function questTooltip(que:Quest) : void
      {
         this.tooltip.visible = true;
         this.newItemsTool.visible = false;
         var quest:Quest = que;
         this.setPosition();
         this.tooltip["name_txt"].text = quest.getName();
         this.tooltip["lv_txt"].visible = false;
         this.tooltip["lv2_txt"].visible = false;
         this.tooltip["txt_qhmax"].visible = false;
         this.tooltip["price"].text = "出售价：" + quest.getGold();
         this.tooltip["explain"].text = quest.getIntroduction();
         this.tooltip["line0"].visible = false;
         this.tooltip["line1"].visible = false;
         this.tooltip["type_txt"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         this.tooltip["right_mc"].visible = false;
         this.tooltip["huanhua_mc"].visible = false;
         for(var i:uint = 1; i < 16; i++)
         {
            this.tooltip["txt_" + i].visible = false;
         }
         this.tooltip["price"].y -= 326;
         this.tooltip["explain"].y -= 326;
         this.tooltip["middle_mc"].height -= 326;
         this.tooltip["down_mc"].y -= 326;
      }
      
      public function otherTooltip(oobj:Otherobj) : void
      {
         this.tooltip.visible = true;
         this.newItemsTool.visible = false;
         this.setPosition();
         this.tooltip["name_txt"].text = oobj.getName();
         this.tooltip["lv_txt"].visible = false;
         this.tooltip["lv2_txt"].visible = false;
         this.tooltip["txt_qhmax"].visible = false;
         this.tooltip["price"].text = "出售价：" + oobj.getGold();
         this.tooltip["explain"].text = oobj.getIntroduction();
         this.tooltip["line0"].visible = false;
         this.tooltip["line1"].visible = false;
         this.tooltip["type_txt"].visible = false;
         this.tooltip["gemslot_mc"].visible = false;
         this.tooltip["right_mc"].visible = false;
         this.tooltip["huanhua_mc"].visible = false;
         for(var i:uint = 1; i < 16; i++)
         {
            this.tooltip["txt_" + i].visible = false;
         }
         this.tooltip["price"].y -= 326;
         this.tooltip["explain"].y -= 326;
         this.tooltip["middle_mc"].height -= 326;
         this.tooltip["down_mc"].y -= 326;
         this.setOtherColor(oobj);
      }
      
      private function setOtherColor(oobj:Otherobj) : void
      {
         if(oobj.getColor() == 1)
         {
            this.ColorX(this.tooltip["name_txt"],"0xffffff");
         }
         if(oobj.getColor() == 2)
         {
            this.ColorX(this.tooltip["name_txt"],"0x0066ff");
         }
         if(oobj.getColor() == 3)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF33FF");
         }
         if(oobj.getColor() == 4)
         {
            this.ColorX(this.tooltip["name_txt"],"0xFF9900");
         }
      }
   }
}

