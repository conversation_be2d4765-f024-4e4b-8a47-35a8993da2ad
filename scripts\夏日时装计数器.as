package
{
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1387")]
   public dynamic class 夏日时装计数器 extends MovieClip
   {
      
      public function 夏日时装计数器()
      {
         super();
         addFrameScript(281,this.frame282);
      }
      
      internal function frame282() : *
      {
         gotoAndStop(1);
      }
   }
}

