package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.quest.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol2632")]
   public class obj extends MovieClip
   {
      
      internal var objX:*;
      
      public function obj(ob:*, xx:int, yy:int, _frame:int = 0)
      {
         var frame:int = 0;
         super();
         addFrameScript(0,this.frame1);
         if(ob is Equip)
         {
            this.objX = ob;
         }
         else if(ob is Gem)
         {
            this.objX = ob;
         }
         else if(ob is Quest)
         {
            this.objX = ob;
         }
         else if(ob is Otherobj)
         {
            this.objX = ob;
         }
         else if(ob is PetEquip)
         {
            this.objX = ob;
         }
         else if(ob is Supplies)
         {
            this.objX = ob;
         }
         else
         {
            if(!(ob is Object))
            {
               return;
            }
            this.objX = ob;
         }
         if(_frame == 0)
         {
            frame = int(ob.getFrame());
         }
         else
         {
            frame = _frame;
         }
         this.x = xx;
         this.y = yy;
         Main.world.moveChild_Other.addChild(this);
         this.gotoAndStop(frame);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
         mouseChildren = mouseEnabled = false;
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         var p:Player = null;
         var moneyNum:int = 0;
         var xxx:int = 0;
         var yyy:int = 0;
         var killPointNum:int = 0;
         var xxx2:int = 0;
         var yyy2:int = 0;
         var mc:Quest = null;
         var fallLevel:Number = NaN;
         var num:Number = NaN;
         for(var i:int = Player.All.length - 1; i >= 0; i--)
         {
            p = Player.All[i];
            if(this.currentFrame == 360 && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               moneyNum = Math.random() * 4000 + 2000;
               p.MoneyUP(moneyNum);
               xxx = this.x + Math.random() * 100 - 50;
               yyy = this.y + Math.random() * 100 - 50;
               NewMC.Open("掉钱",Main.world.moveChild_Other,xxx,yyy,15,moneyNum,true);
               this.parent.removeChild(this);
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
            else if(this.currentFrame == 361 && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               killPointNum = Math.random() * 30 + 30;
               p.data.killPoint.setValue(p.data.killPoint.getValue() + killPointNum);
               xxx2 = this.x + Math.random() * 100 - 50;
               yyy2 = this.y + Math.random() * 100 - 50;
               NewMC.Open("击杀点",Main.world.moveChild_Other,xxx2,yyy2,20,killPointNum,true);
               this.parent.removeChild(this);
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
            else if(this.objX is Equip && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               if(p.data.getBag().backequipBagNum() > 0)
               {
                  Player.All[i].data.getBag().addEquipBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Gem && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               if(p.data.getBag().backGemBagNum() > 0)
               {
                  Player.All[i].data.getBag().addGemBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Otherobj && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               if(p.data.getBag().backOtherBagNum() > 0)
               {
                  Player.All[i].data.getBag().addOtherobjBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Supplies && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               if(p.data.getBag().backSuppliesBagNum() > 0)
               {
                  (Player.All[i].data.getBag() as Bag).addSuppliesBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is PetEquip && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               if(NewPetPanel.bag.backPetBagNum() > 0)
               {
                  NewPetPanel.bag.addPetBag(this.objX);
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  if(Main.newPlay == 2)
                  {
                     Main.newPlay = 3;
                  }
                  return;
               }
               NewMC.Open("文字提示",Main._stage,460,400,30,0,true,2,"背包已满");
            }
            else if(this.objX is Quest && p.data.getBag().backQuestBagNum() > 0 && p.hit && this.hitTestObject(p.hit) && p.getKeyStatus("下",2))
            {
               mc = this.objX as Quest;
               fallLevel = mc.getFallLevel();
               if(mc.getType() != 2)
               {
                  num = 0;
                  if(mc.getType() == 0)
                  {
                     num = mc.getGoodMaxNum();
                  }
                  else if(mc.getType() == 1)
                  {
                     num = mc.getFallMax();
                  }
                  if(Player.All[i].data.getBag().fallQusetBag(fallLevel) < num)
                  {
                     Player.All[i].data.getBag().addQuestBag(this.objX);
                     this.parent.removeChild(this);
                     removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                     TaskData.addGoods(this.objX.getId());
                     TaskData.isOk();
                     if(Main.newPlay == 2)
                     {
                        Main.newPlay = 3;
                     }
                     return;
                  }
                  this.parent.removeChild(this);
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
               }
               else if(Player.All[i].data.isTransferOk() == false)
               {
                  if(Player.All[i].data.getBag().fallQusetBag(fallLevel) < mc.getFallMax())
                  {
                     Player.All[i].data.getBag().addQuestBag(this.objX);
                     this.parent.removeChild(this);
                     removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                     if(Main.newPlay == 2)
                     {
                        Main.newPlay = 3;
                     }
                     return;
                  }
               }
            }
         }
         if(this.parent == Main.world.moveChild_Other && !JhitTestPoint.hitTestPoint(this,Main.world.MapData))
         {
            this.y += 10;
         }
      }
      
      private function onREMOVED_FROM_STAGE(e:*) : *
      {
         stop();
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         removeEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      internal function frame1() : *
      {
         stop();
      }
   }
}

