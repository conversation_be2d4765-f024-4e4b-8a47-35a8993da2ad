package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4093")]
   public class PK_UI extends MovieClip
   {
      
      public static var _this:PK_UI;
      
      public static var duiHuan_mc:MovieClip;
      
      public static var lingQu_mc:MovieClip;
      
      public static var xPk_nextPlayer:*;
      
      public static var xPk_nextPlayer_ID:*;
      
      public static var getTimeYn:Boolean;
      
      public static var whoData:PlayerData;
      
      public static var whoII:uint;
      
      private static var DengDaiYn:Boolean;
      
      public static var playerNum:int = 5000;
      
      public static var whoNumX:VT = VT.createVT(5000);
      
      public static var whoNum:int = 0;
      
      public static var PK_ing:Boolean = false;
      
      public static var loadPlayerNum:int = 0;
      
      public static var PlayerNumMax_XX:int = 1;
      
      public static var PlayerNum_XX:int = 0;
      
      public static var DataArr:Array = new Array();
      
      public static var DataArr2:Array = new Array();
      
      public static var playerDataArr:Array = new Array();
      
      public static var playerDataArr_id:Array = new Array();
      
      public static var xPk_Player100Arr:Array = new Array();
      
      public static var xPk_Player_min:int = 100;
      
      public static var jiFenArr:Array = [];
      
      public static var getTimeArr:Array = [];
      
      public static var equipLoad_YN:Boolean = true;
      
      public static var playerTime:uint = 81;
      
      public static var Pk_timeNum:int = 12960;
      
      public static var killNum70up:VT = VT.createVT();
      
      public static var killNum70down:VT = VT.createVT();
      
      public var P1_killPoint_txt:TextField;
      
      public var P1_money_txt:TextField;
      
      public var P2_killPoint_txt:TextField;
      
      public var P2_money_txt:TextField;
      
      public var close_btn:SimpleButton;
      
      public var duihuan_btn:SimpleButton;
      
      public var go_btnXX:SimpleButton;
      
      public var num_0:MovieClip;
      
      public var num_1:MovieClip;
      
      public var num_10:MovieClip;
      
      public var num_2:MovieClip;
      
      public var num_3:MovieClip;
      
      public var num_4:MovieClip;
      
      public var num_5:MovieClip;
      
      public var num_6:MovieClip;
      
      public var num_7:MovieClip;
      
      public var num_8:MovieClip;
      
      public var num_9:MovieClip;
      
      public var pageDOWN_btn:SimpleButton;
      
      public var pageUP_btn:SimpleButton;
      
      public var page_txt:TextField;
      
      public var skin_mc:MovieClip;
      
      public var 初始化mc:MovieClip = new 游戏初始化();
      
      public var pageNum:int = 1;
      
      public var pageX:int = 10;
      
      public var selPageX:int = 50;
      
      public var timeNum:uint = 0;
      
      public var userPage:VT = VT.createVT(-99);
      
      public var paiHang_MAX:uint = 5000;
      
      private var duiHuanPage:int = 1;
      
      private var duiHuanPageMax:int = 2;
      
      public var duiHuanArr:Array = [[],[63303,"",50,"天下第一(称号)"],[63300,"",50,"勇者武器幻化券"],[31217,"",6,"四级强化石"],[63156,"",2,"星级碎片"],[63138,"",4,"圣光水晶"],[33511,"",6,"中级幸运石"],[63210,"",4,"洗练卷"],[63363,"",60,"功夫小子宠物蛋"]];
      
      public function PK_UI()
      {
         super();
         this.go_btnXX.addEventListener(MouseEvent.CLICK,this.GameStart);
         this.close_btn.addEventListener(MouseEvent.CLICK,Close);
         this.pageDOWN_btn.addEventListener(MouseEvent.CLICK,this.PageDOWN);
         this.pageUP_btn.addEventListener(MouseEvent.CLICK,this.PageUP);
         this.duihuan_btn.addEventListener(MouseEvent.CLICK,this.DuiHuan_Fun);
         addEventListener(Event.ENTER_FRAME,this.onTime);
         if(playerDataArr.length == 0)
         {
            if(Main.P1P2)
            {
               Api_4399_All.GetRankListsData(1,xPk_Player_min,1364);
            }
            else
            {
               Api_4399_All.GetRankListsData(1,xPk_Player_min,1365);
            }
            this.go_btnXX.visible = false;
         }
         InitSave();
      }
      
      public static function xPk_Player100_Fun(dataAry:Array) : *
      {
         var i:* = undefined;
         if(dataAry == null || dataAry.length == 0)
         {
            return;
         }
         TiaoShi.txtShow("列表长度 = " + dataAry.length);
         PK_UI._this.go_btnXX.visible = true;
         for(i in dataAry)
         {
            xPk_Player100Arr[i] = dataAry[i];
         }
      }
      
      public static function InitSave() : *
      {
         var i:int = 0;
         if(!jiFenArr[0])
         {
            jiFenArr[0] = VT.createVT(20141124);
            jiFenArr[1] = VT.createVT();
            jiFenArr[2] = VT.createVT();
            for(i = 3; i < 6; i++)
            {
               jiFenArr[i] = 0;
            }
         }
         if(!jiFenArr[6])
         {
            jiFenArr[6] = 0;
         }
         if(!jiFenArr[7])
         {
            jiFenArr[7] = 0;
         }
      }
      
      public static function Open() : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         var classRef2:Class = null;
         var classRef3:Class = null;
         Main.DuoKai_Fun();
         if(!NewLoad.Other_YN)
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"加载中,请稍候...");
            return;
         }
         Main.allClosePanel();
         if(!_this)
         {
            _this = new PK_UI();
            classRef = NewLoad.OtherData.getClass("_PK_JiFen") as Class;
            xxMov = new classRef();
            _this.skin_mc.addChild(xxMov);
            classRef2 = NewLoad.OtherData.getClass("JingJiCangDuiHuan") as Class;
            duiHuan_mc = new classRef2();
            _this.addChild(duiHuan_mc);
            duiHuan_mc.x = duiHuan_mc.y = -5000;
            classRef3 = NewLoad.OtherData.getClass("pk_jiFen_Up") as Class;
            lingQu_mc = new classRef3();
            _this.addChild(lingQu_mc);
            lingQu_mc.x = lingQu_mc.y = -5000;
         }
         InitSave();
         _this.visible = true;
         _this.x = _this.y = 0;
         _this.Show();
         Main._this.addChild(_this);
         if(jiFenArr[2].getValue() >= 3 && jiFenArr[2].getValue() < 999)
         {
            jiFenArr[2].setValue(999);
            jiFenArr[1].setValue(jiFenArr[1].getValue() + 2);
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"追加奖励:获得2积分");
         }
      }
      
      public static function Close(e:* = null) : *
      {
         if(!_this)
         {
            return;
         }
         _this.visible = false;
         _this.x = _this.y = 5000;
      }
      
      public static function TiaoJiao_1() : *
      {
         GameData.TiJiaoFenShu(InitData.BuyNum_1.getValue());
      }
      
      private static function onXingQiJi(xxx:String) : int
      {
         var xxx2:String = null;
         var C:int = int(xxx.substr(0,2));
         var Y:int = int(xxx.substr(2,2));
         var M:int = int(xxx.substr(4,2));
         var D:int = int(xxx.substr(6,2));
         if(M <= 2)
         {
            M += 12;
            trace("M = " + M);
            xxx2 = String(int(xxx.substr(0,4)) - 1);
            trace("xxx2 = " + xxx2);
            C = int(xxx2.substr(0,2));
            Y = int(xxx2.substr(2,2));
         }
         var W:int = C / 4 - 2 * C + Y + Y / 4 + 13 * (M + 1) / 5 + D - 1;
         trace(W % 7);
         return W;
      }
      
      public static function xxxTime() : *
      {
         if(!getTimeYn)
         {
            return;
         }
         getTimeYn = false;
         var day:Number = Number(onXingQiJi(getTimeArr[0]));
         var hour:Number = Number(getTimeArr[1]);
         var minutes:Number = Number(getTimeArr[2]);
         if(day == 0 && hour > 16 && hour < 18)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"排行榜数据重置期间无法挑战,请稍候再试");
            return;
         }
         if(xPk_Player100Arr.length < xPk_Player_min)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"排行榜数据重置期间无法挑战");
            return;
         }
         if(Main.player1.getGold() < 5000 || Boolean(Main.P1P2) && (Main.player1.getGold() < 5000 || Main.player2.getGold() < 5000))
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
            return;
         }
         if(Main.player1.killPoint.getValue() < 10 || Boolean(Main.P1P2) && (Main.player1.killPoint.getValue() < 10 || Main.player2.killPoint.getValue() < 10))
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点不足");
            return;
         }
         Main.player1.payGold(5000);
         Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - 10);
         if(Main.P1P2)
         {
            Main.player2.payGold(5000);
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - 10);
         }
         _this.P1_money_txt.text = Main.player1.getGold();
         _this.P1_killPoint_txt.text = Main.player1.killPoint.getValue();
         if(Main.P1P2)
         {
            _this.P2_money_txt.text = Main.player2.getGold();
            _this.P2_killPoint_txt.text = Main.player2.killPoint.getValue();
         }
         _this.addChild(_this.初始化mc);
         _this.goGame(0);
         if(Main.P1P2)
         {
            PlayerNumMax_XX = 2;
         }
         else
         {
            PlayerNumMax_XX = 1;
         }
         PlayerNum_XX = 0;
         equipLoad_YN = true;
         DengDaiYn = false;
      }
      
      public static function AddOtherPlayer() : *
      {
         if(PK_ing && PlayerNum_XX < PlayerNumMax_XX && equipLoad_YN)
         {
            equipLoad_YN = false;
            Load_OtherPlayer();
            ++PlayerNum_XX;
         }
         TimeNumFun();
      }
      
      public static function reAddOtherPlayer() : *
      {
         TiaoShi.txtShow("数据出错重新刷新~~");
         if(PK_ing)
         {
            Load_OtherPlayer();
         }
         TimeNumFun();
      }
      
      private static function Load_OtherPlayer() : *
      {
         loadPlayerNum = Math.random() * xPk_Player_min;
         TiaoShi.txtShow("<pk玩家> 随机:" + loadPlayerNum);
         Load_OtherPlayerData(loadPlayerNum);
         playerTime = 81;
      }
      
      public static function Load_OtherPlayerData(num:int = 0) : *
      {
         var tmpObj:Object = null;
         if(xPk_nextPlayer == null)
         {
            tmpObj = xPk_Player100Arr[num];
            Api_4399_All.GetUserData(tmpObj.uId,tmpObj.index);
            TiaoShi.txtShow("<pk玩家> 数据 读取中...>>>" + num);
         }
         else
         {
            whoData = DeepCopyUtil.clone(xPk_nextPlayer);
            TiaoShi.txtShow("<pk玩家> 数据读取完成, 装备加载中... >>>" + num);
            NewLoad.Loading(4);
            xPk_nextPlayer = null;
         }
      }
      
      public static function LoadEnd_And_AddPlayer2() : *
      {
         TiaoShi.txtShow("<pk玩家> 装备加载完成....>>>" + loadPlayerNum);
         if(playerTime > 0)
         {
            TiaoShi.txtShow("<pk玩家> 3秒等待 playerTime...>>>" + playerTime);
            DengDaiYn = true;
            return;
         }
         var NP:Player2 = new Player2();
         NP.data = whoData;
         NP.Load_All_Player_Data();
         Main.world.moveChild_Player.addChild(NP);
         NP.x = Math.random() * 1000 + 100;
         NP.y = 400;
         NP.newSkin();
         NP.pkmc._txt.text = xPk_nextPlayer_ID;
         equipLoad_YN = true;
         TiaoShi.txtShow("<pk玩家> 生成玩家!!...>>>" + loadPlayerNum);
      }
      
      public static function TimeNumFun() : *
      {
         if(Pk_timeNum > 0)
         {
            --Pk_timeNum;
         }
         if(Pk_timeNum <= 0 && PK_ing)
         {
            GameData.PkJiFun(true);
         }
      }
      
      public function DuiHuan_Fun(e:*) : *
      {
         duiHuan_mc.x = duiHuan_mc.y = 0;
         duiHuan_mc.jifen_txt.text = PK_UI.jiFenArr[1].getValue();
         duiHuan_mc.close_btn.addEventListener(MouseEvent.CLICK,this.DuiHuan_Close);
         duiHuan_mc.back_btn.addEventListener(MouseEvent.CLICK,this.DuiHuan_back);
         duiHuan_mc.next_btn.addEventListener(MouseEvent.CLICK,this.DuiHuan_next);
         this.DuiHuan_Show();
      }
      
      public function DuiHuan_back(e:*) : *
      {
         if(this.duiHuanPage - 1 > 0)
         {
            --this.duiHuanPage;
            this.DuiHuan_Show();
         }
      }
      
      public function DuiHuan_next(e:*) : *
      {
         if(this.duiHuanPage + 1 <= this.duiHuanPageMax)
         {
            ++this.duiHuanPage;
            this.DuiHuan_Show();
         }
      }
      
      public function DuiHuan_Close(e:*) : *
      {
         duiHuan_mc.x = duiHuan_mc.y = -5000;
      }
      
      public function lingQu_Close(e:*) : *
      {
         lingQu_mc.x = lingQu_mc.y = -5000;
      }
      
      public function DuiHuan_Show() : *
      {
         var numX:int = 0;
         var xMc:MovieClip = null;
         duiHuan_mc.duiHuanPage_txt.text = this.duiHuanPage + "/" + this.duiHuanPageMax;
         duiHuan_mc.jifen_txt.text = "" + jiFenArr[1].getValue();
         var num:int = (this.duiHuanPage - 1) * 4;
         for(var i:int = 1; i < 5; i++)
         {
            numX = num + i;
            xMc = duiHuan_mc["duiHuan_" + i];
            if(this.duiHuanArr[numX])
            {
               xMc.name_txt.text = this.duiHuanArr[numX][3];
               xMc.pic_mc.gotoAndStop(numX);
               xMc.money_txt.text = this.duiHuanArr[numX][2] + "积分";
               xMc.tiaoJian_txt.text = "";
               if(numX == 1)
               {
                  xMc.tiaoJian_txt.text = "当前已获得" + jiFenArr[3] + "次第一名";
               }
            }
            else
            {
               xMc.name_txt.text = "";
               xMc.pic_mc.gotoAndStop(20);
               xMc.money_txt.text = "";
               xMc.tiaoJian_txt.text = "";
            }
            xMc.duihuanXXX.addEventListener(MouseEvent.CLICK,this.DuiHuan_num);
         }
      }
      
      public function DuiHuan_num(e:MouseEvent) : *
      {
         var num:int = int((e.target.parent.name as String).substr(8,1)) + (this.duiHuanPage - 1) * 4;
         TiaoShi.txtShow(e.target.name + "兑换" + num);
         if(!this.duiHuanArr[num] || num < 1 || num > 8)
         {
            return;
         }
         if(jiFenArr[1].getValue() < this.duiHuanArr[num][2])
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
            return;
         }
         if(num == 1 || num == 2 || num == 4 || num == 5 || num == 7 || num == 8)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"其他类背包空间不足!");
               return;
            }
            if(num == 1 && jiFenArr[3] < 10)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"兑换条件不满足!");
               return;
            }
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(this.duiHuanArr[num][0]));
            jiFenArr[1].setValue(jiFenArr[1].getValue() - this.duiHuanArr[num][2]);
         }
         else if(num == 3 || num == 6)
         {
            if(Main.player1.getBag().backGemBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"宝石类背包空间不足!");
               return;
            }
            Main.player1.getBag().addGemBag(GemFactory.creatGemById(this.duiHuanArr[num][0]));
            jiFenArr[1].setValue(jiFenArr[1].getValue() - this.duiHuanArr[num][2]);
         }
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"兑换成功!");
         if(num == 1)
         {
            ++jiFenArr[6];
         }
         if(num == 2)
         {
            ++jiFenArr[7];
         }
         this.DuiHuan_Show();
         Main.Save();
      }
      
      public function Show() : *
      {
         this.P1_money_txt.text = Main.player1.getGold();
         this.P1_killPoint_txt.text = Main.player1.killPoint.getValue();
         if(Main.P1P2)
         {
            this.P2_money_txt.text = Main.player2.getGold();
            this.P2_killPoint_txt.text = Main.player2.killPoint.getValue();
         }
         for(var i:int = 0; i < 11; i++)
         {
            this["num_" + i].x_txt.text = "";
            this["num_" + i].id_txt.text = "";
            this["num_" + i].save_txt.text = "";
            this["num_" + i].score_txt.text = "";
            this["num_" + i].X123_mc.gotoAndStop(4);
         }
         this.pageNum = 1;
         this.page_txt.text = "1/5";
         Api_4399_All.GetRankListsData(1,this.selPageX,1365);
         Api_4399_All.GetOneRankInfo();
      }
      
      public function get_PaiHang_Data(dataAry:Array, pageN:int) : *
      {
         var i:* = undefined;
         if(dataAry == null || dataAry.length == 0)
         {
            return;
         }
         var x:int = (pageN - 1) * this.selPageX;
         for(i in dataAry)
         {
            DataArr[x + i] = dataAry[i];
         }
         if(this.pageNum == 1)
         {
            this.PaiHang_Show(this.pageNum);
         }
      }
      
      public function PaiHang_Show(num:uint) : *
      {
         var xx:int = 0;
         var tmpObj:Object = null;
         if(DataArr == null || DataArr.length == 0)
         {
            return;
         }
         var x:int = (num - 1) * this.pageX;
         var x10:int = 0;
         for(var i:* = x; i < x + 10; i++)
         {
            if(!DataArr[i])
            {
               this["num_" + x10].x_txt.text = "";
               this["num_" + x10].id_txt.text = "";
               this["num_" + x10].save_txt.text = "";
               this["num_" + x10].score_txt.text = "";
               this["num_" + x10].X123_mc.gotoAndStop(4);
            }
            else
            {
               xx = x10 + 1 + x;
               tmpObj = DataArr[i];
               if(tmpObj.score < 10)
               {
                  break;
               }
               this["num_" + x10].x_txt.text = x10 + 10 * (num - 1) + 1;
               this["num_" + x10].id_txt.text = tmpObj.userName;
               this["num_" + x10].save_txt.text = uint(tmpObj.index) + 1;
               this["num_" + x10].score_txt.text = tmpObj.score;
               if(xx < 4)
               {
                  this["num_" + x10].X123_mc.gotoAndStop(xx);
               }
               else
               {
                  this["num_" + x10].X123_mc.gotoAndStop(4);
               }
            }
            x10++;
         }
      }
      
      public function PaiHang_Show_X(dataAry:Array) : *
      {
         var i:int = 0;
         var tmpObj:Object = null;
         if(dataAry != null && dataAry.length != 0)
         {
            for(i in dataAry)
            {
               tmpObj = dataAry[i];
               if(int(tmpObj.index) == Main.saveNum)
               {
                  if(tmpObj.score < 10)
                  {
                     break;
                  }
                  this["num_10"].x_txt.text = tmpObj.rank;
                  playerNum = int(tmpObj.rank);
                  whoNumX.setValue(int(tmpObj.rank));
                  this["num_10"].id_txt.text = tmpObj.userName;
                  this["num_10"].save_txt.text = uint(tmpObj.index) + 1;
                  this["num_10"].score_txt.text = tmpObj.score;
                  this.JiFenUP(tmpObj.rank);
                  if(tmpObj.rank < 4)
                  {
                     this["num_10"].X123_mc.gotoAndStop(tmpObj.rank);
                  }
                  this.userPage.setValue(int(tmpObj.rank) / this.pageX);
                  return;
               }
            }
            this["num_10"].x_txt.text = "?";
            this["num_10"].id_txt.text = Main.logName;
            this["num_10"].save_txt.text = Main.saveNum + 1;
            this["num_10"].score_txt.text = "未上榜";
            this["num_10"].X123_mc.gotoAndStop(4);
         }
         else
         {
            this["num_10"].x_txt.text = "?";
            this["num_10"].id_txt.text = Main.logName;
            this["num_10"].save_txt.text = Main.saveNum + 1;
            this["num_10"].score_txt.text = "未上榜";
            this["num_10"].X123_mc.gotoAndStop(4);
         }
      }
      
      private function JiFenUP(num:int) : *
      {
         var jf:int = 0;
         var jifenNum:int = 0;
         var CJNum:int = 0;
         var kill_P:int = 0;
         var exp_P:int = 0;
         lingQu_mc.close_btn.addEventListener(MouseEvent.CLICK,this.lingQu_Close);
         TiaoShi.txtShow("当前挑战时间:" + jiFenArr[0].getValue());
         TiaoShi.txtShow("当前serverTime:" + Main.serverTime.getValue());
         if(Main.serverTime.getValue() != 0 && jiFenArr[0].getValue() < Main.serverTime.getValue())
         {
            TiaoShi.txtShow("挑战奖励已领取! 名次:" + num);
            jiFenArr[0].setValue(Main.serverTime.getValue());
            jiFenArr[2] = VT.createVT();
            jf = int((jiFenArr[1] as VT).getValue());
            jifenNum = 0;
            CJNum = 0;
            kill_P = 0;
            exp_P = 0;
            if(num == InitData.tiaoZhanJF_1.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_8.getValue());
               CJNum = 350;
               kill_P = 200;
               exp_P = 14;
            }
            else if(num == InitData.tiaoZhanJF_2.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_4.getValue());
               CJNum = 250;
               kill_P = 150;
               exp_P = 11;
            }
            else if(num == InitData.tiaoZhanJF_3.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_4.getValue());
               CJNum = 200;
               kill_P = 100;
               exp_P = 9;
            }
            else if(num <= InitData.tiaoZhanJF_10.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_2.getValue());
               CJNum = 145;
               kill_P = 100;
               exp_P = 8;
            }
            else if(num <= InitData.tiaoZhanJF_100.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_2.getValue());
               CJNum = 105;
               kill_P = 80;
               exp_P = 6;
            }
            else if(num <= InitData.tiaoZhanJF_500.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_1.getValue());
               CJNum = 70;
               kill_P = 50;
               exp_P = 5;
            }
            else if(num <= InitData.tiaoZhanJF_5000.getValue())
            {
               jifenNum = int(InitData.tiaoZhanJF_0.getValue());
               CJNum = 20;
               kill_P = 20;
               exp_P = 5;
            }
            jiFenArr[1] = VT.createVT(jf + jifenNum);
            AchData.cjPoint_1.setValue(AchData.cjPoint_1.getValue() + CJNum);
            Main.player1.AddKillPoint(kill_P);
            Main.player_1.ExpUP(exp_P,3);
            if(Main.P1P2)
            {
               Main.player2.AddKillPoint(kill_P);
               Main.player_2.ExpUP(exp_P,3);
            }
            TiaoShi.txtShow("竞技场积分:" + jiFenArr[1].getValue());
            TiaoShi.txtShow(jifenNum + "," + CJNum + "," + kill_P);
            if(num == 1)
            {
               ++jiFenArr[3];
            }
            else if(num <= 10)
            {
               ++jiFenArr[4];
            }
            else if(num <= 5000)
            {
               ++jiFenArr[5];
            }
            lingQu_mc.x = lingQu_mc.y = 0;
            lingQu_mc.num_txt.text = "第" + num + "名";
            lingQu_mc._1_txt.text = jifenNum;
            lingQu_mc._2_txt.text = "+" + exp_P + "%";
            lingQu_mc._3_txt.text = kill_P;
            lingQu_mc._4_txt.text = CJNum;
         }
      }
      
      private function PageUP(e:*) : *
      {
         if(this.pageNum < 5)
         {
            ++this.pageNum;
            this.PaiHang_Show(this.pageNum);
            this.page_txt.text = this.pageNum + "/5";
         }
      }
      
      private function PageDOWN(e:*) : *
      {
         if(this.pageNum > 1)
         {
            --this.pageNum;
            this.PaiHang_Show(this.pageNum);
            this.page_txt.text = this.pageNum + "/5";
         }
      }
      
      private function GameStart(e:*) : *
      {
         if(!getTimeYn)
         {
            Main.GetServerTime(true);
            getTimeYn = true;
         }
      }
      
      private function goGame(num:int = 0) : *
      {
         if(this.初始化mc.parent)
         {
            this.初始化mc.parent.removeChild(this.初始化mc);
         }
         PK_UI.Close();
         Main.gameNum.setValue(999);
         Main.gameNum2.setValue(1);
         Main._this.Loading();
         whoNum = num;
         Pk_timeNum = 12960;
         killNum70down.setValue(0);
         killNum70up.setValue(0);
      }
      
      public function onTime(e:*) : *
      {
         if(playerTime > 0)
         {
            --playerTime;
         }
         else if(Boolean(DengDaiYn) && PK_ing)
         {
            LoadEnd_And_AddPlayer2();
            DengDaiYn = false;
         }
      }
   }
}

