package src.tool
{
   public class ShengDan2013
   {
      
      public function ShengDan2013()
      {
         super();
      }
      
      public static function W<PERSON><PERSON><PERSON>hang(id:int, LVplayer:int) : int
      {
         var addNum:int = 0;
         if(id == 14667)
         {
            if(LVplayer >= 0 && LVplayer < 5)
            {
               addNum = 47;
            }
            if(LVplayer >= 5 && LVplayer < 10)
            {
               addNum = 53;
            }
            else if(LVplayer >= 10 && LVplayer < 15)
            {
               addNum = 62;
            }
            else if(LVplayer >= 15 && LVplayer < 20)
            {
               addNum = 72;
            }
            else if(LVplayer >= 20 && LVplayer < 25)
            {
               addNum = 84;
            }
            else if(LVplayer >= 25 && LVplayer < 30)
            {
               addNum = 98;
            }
            else if(LVplayer >= 30 && LVplayer < 35)
            {
               addNum = 114;
            }
            else if(LVplayer >= 35 && LVplayer < 40)
            {
               addNum = 133;
            }
            else if(LVplayer >= 40 && LVplayer < 45)
            {
               addNum = 155;
            }
            else if(LVplayer >= 45 && LVplayer < 50)
            {
               addNum = 181;
            }
            else if(LVplayer >= 50 && LVplayer < 55)
            {
               addNum = 212;
            }
            else if(LVplayer >= 55 && LVplayer < 60)
            {
               addNum = 248;
            }
            else if(LVplayer >= 60 && LVplayer < 65)
            {
               addNum = 289;
            }
            else if(LVplayer >= 65 && LVplayer < 70)
            {
               addNum = 335;
            }
            else if(LVplayer >= 70 && LVplayer < 75)
            {
               addNum = 386;
            }
            else if(LVplayer >= 75 && LVplayer < 80)
            {
               addNum = 442;
            }
            else if(LVplayer >= 80 && LVplayer < 85)
            {
               addNum = 503;
            }
            else if(LVplayer >= 85 && LVplayer < 90)
            {
               addNum = 569;
            }
            else if(LVplayer >= 90 && LVplayer < 95)
            {
               addNum = 640;
            }
            else if(LVplayer >= 95 && LVplayer < 100)
            {
               addNum = 716;
            }
         }
         if(id == 14668)
         {
            if(LVplayer >= 0 && LVplayer < 5)
            {
               addNum = 56;
            }
            if(LVplayer >= 5 && LVplayer < 10)
            {
               addNum = 62;
            }
            else if(LVplayer >= 10 && LVplayer < 15)
            {
               addNum = 73;
            }
            else if(LVplayer >= 15 && LVplayer < 20)
            {
               addNum = 84;
            }
            else if(LVplayer >= 20 && LVplayer < 25)
            {
               addNum = 99;
            }
            else if(LVplayer >= 25 && LVplayer < 30)
            {
               addNum = 115;
            }
            else if(LVplayer >= 30 && LVplayer < 35)
            {
               addNum = 134;
            }
            else if(LVplayer >= 35 && LVplayer < 40)
            {
               addNum = 156;
            }
            else if(LVplayer >= 40 && LVplayer < 45)
            {
               addNum = 182;
            }
            else if(LVplayer >= 45 && LVplayer < 50)
            {
               addNum = 212;
            }
            else if(LVplayer >= 50 && LVplayer < 55)
            {
               addNum = 249;
            }
            else if(LVplayer >= 55 && LVplayer < 60)
            {
               addNum = 291;
            }
            else if(LVplayer >= 60 && LVplayer < 65)
            {
               addNum = 339;
            }
            else if(LVplayer >= 65 && LVplayer < 70)
            {
               addNum = 393;
            }
            else if(LVplayer >= 70 && LVplayer < 75)
            {
               addNum = 453;
            }
            else if(LVplayer >= 75 && LVplayer < 80)
            {
               addNum = 519;
            }
            else if(LVplayer >= 80 && LVplayer < 85)
            {
               addNum = 590;
            }
            else if(LVplayer >= 85 && LVplayer < 90)
            {
               addNum = 668;
            }
            else if(LVplayer >= 90 && LVplayer < 95)
            {
               addNum = 751;
            }
            else if(LVplayer >= 95 && LVplayer < 100)
            {
               addNum = 840;
            }
         }
         if(id == 14669)
         {
            if(LVplayer >= 0 && LVplayer < 5)
            {
               addNum = 43;
            }
            if(LVplayer >= 5 && LVplayer < 10)
            {
               addNum = 49;
            }
            else if(LVplayer >= 10 && LVplayer < 15)
            {
               addNum = 57;
            }
            else if(LVplayer >= 15 && LVplayer < 20)
            {
               addNum = 66;
            }
            else if(LVplayer >= 20 && LVplayer < 25)
            {
               addNum = 77;
            }
            else if(LVplayer >= 25 && LVplayer < 30)
            {
               addNum = 90;
            }
            else if(LVplayer >= 30 && LVplayer < 35)
            {
               addNum = 105;
            }
            else if(LVplayer >= 35 && LVplayer < 40)
            {
               addNum = 122;
            }
            else if(LVplayer >= 40 && LVplayer < 45)
            {
               addNum = 143;
            }
            else if(LVplayer >= 45 && LVplayer < 50)
            {
               addNum = 167;
            }
            else if(LVplayer >= 50 && LVplayer < 55)
            {
               addNum = 195;
            }
            else if(LVplayer >= 55 && LVplayer < 60)
            {
               addNum = 228;
            }
            else if(LVplayer >= 60 && LVplayer < 65)
            {
               addNum = 266;
            }
            else if(LVplayer >= 65 && LVplayer < 70)
            {
               addNum = 308;
            }
            else if(LVplayer >= 70 && LVplayer < 75)
            {
               addNum = 355;
            }
            else if(LVplayer >= 75 && LVplayer < 80)
            {
               addNum = 407;
            }
            else if(LVplayer >= 80 && LVplayer < 85)
            {
               addNum = 463;
            }
            else if(LVplayer >= 85 && LVplayer < 90)
            {
               addNum = 523;
            }
            else if(LVplayer >= 90 && LVplayer < 95)
            {
               addNum = 589;
            }
            else if(LVplayer >= 95 && LVplayer < 100)
            {
               addNum = 659;
            }
         }
         return addNum;
      }
      
      public static function WQwhite(id:int, LVplayer:int) : int
      {
         var addNum:int = 0;
         if(id == 14667)
         {
            if(LVplayer >= 0 && LVplayer < 5)
            {
               addNum = 47;
            }
            if(LVplayer >= 5 && LVplayer < 10)
            {
               addNum = 53;
            }
            else if(LVplayer >= 10 && LVplayer < 15)
            {
               addNum = 62;
            }
            else if(LVplayer >= 15 && LVplayer < 20)
            {
               addNum = 72;
            }
            else if(LVplayer >= 20 && LVplayer < 25)
            {
               addNum = 84;
            }
            else if(LVplayer >= 25 && LVplayer < 30)
            {
               addNum = 98;
            }
            else if(LVplayer >= 30 && LVplayer < 35)
            {
               addNum = 114;
            }
            else if(LVplayer >= 35 && LVplayer < 40)
            {
               addNum = 133;
            }
            else if(LVplayer >= 40 && LVplayer < 45)
            {
               addNum = 155;
            }
            else if(LVplayer >= 45 && LVplayer < 50)
            {
               addNum = 181;
            }
            else if(LVplayer >= 50 && LVplayer < 55)
            {
               addNum = 212;
            }
            else if(LVplayer >= 55 && LVplayer < 60)
            {
               addNum = 248;
            }
            else if(LVplayer >= 60 && LVplayer < 65)
            {
               addNum = 289;
            }
            else if(LVplayer >= 65 && LVplayer < 70)
            {
               addNum = 335;
            }
            else if(LVplayer >= 70 && LVplayer < 75)
            {
               addNum = 386;
            }
            else if(LVplayer >= 75 && LVplayer < 80)
            {
               addNum = 442;
            }
            else if(LVplayer >= 80 && LVplayer < 85)
            {
               addNum = 503;
            }
            else if(LVplayer >= 85 && LVplayer < 90)
            {
               addNum = 569;
            }
            else if(LVplayer >= 90 && LVplayer < 95)
            {
               addNum = 640;
            }
            else if(LVplayer >= 95 && LVplayer < 100)
            {
               addNum = 716;
            }
            return addNum - 47;
         }
         if(id == 14668)
         {
            if(LVplayer >= 0 && LVplayer < 5)
            {
               addNum = 56;
            }
            if(LVplayer >= 5 && LVplayer < 10)
            {
               addNum = 62;
            }
            else if(LVplayer >= 10 && LVplayer < 15)
            {
               addNum = 73;
            }
            else if(LVplayer >= 15 && LVplayer < 20)
            {
               addNum = 84;
            }
            else if(LVplayer >= 20 && LVplayer < 25)
            {
               addNum = 99;
            }
            else if(LVplayer >= 25 && LVplayer < 30)
            {
               addNum = 115;
            }
            else if(LVplayer >= 30 && LVplayer < 35)
            {
               addNum = 134;
            }
            else if(LVplayer >= 35 && LVplayer < 40)
            {
               addNum = 156;
            }
            else if(LVplayer >= 40 && LVplayer < 45)
            {
               addNum = 182;
            }
            else if(LVplayer >= 45 && LVplayer < 50)
            {
               addNum = 212;
            }
            else if(LVplayer >= 50 && LVplayer < 55)
            {
               addNum = 249;
            }
            else if(LVplayer >= 55 && LVplayer < 60)
            {
               addNum = 291;
            }
            else if(LVplayer >= 60 && LVplayer < 65)
            {
               addNum = 339;
            }
            else if(LVplayer >= 65 && LVplayer < 70)
            {
               addNum = 393;
            }
            else if(LVplayer >= 70 && LVplayer < 75)
            {
               addNum = 453;
            }
            else if(LVplayer >= 75 && LVplayer < 80)
            {
               addNum = 519;
            }
            else if(LVplayer >= 80 && LVplayer < 85)
            {
               addNum = 590;
            }
            else if(LVplayer >= 85 && LVplayer < 90)
            {
               addNum = 668;
            }
            else if(LVplayer >= 90 && LVplayer < 95)
            {
               addNum = 751;
            }
            else if(LVplayer >= 95 && LVplayer < 100)
            {
               addNum = 840;
            }
            return addNum - 56;
         }
         if(id == 14669)
         {
            if(LVplayer >= 0 && LVplayer < 5)
            {
               addNum = 43;
            }
            if(LVplayer >= 5 && LVplayer < 10)
            {
               addNum = 49;
            }
            else if(LVplayer >= 10 && LVplayer < 15)
            {
               addNum = 57;
            }
            else if(LVplayer >= 15 && LVplayer < 20)
            {
               addNum = 66;
            }
            else if(LVplayer >= 20 && LVplayer < 25)
            {
               addNum = 77;
            }
            else if(LVplayer >= 25 && LVplayer < 30)
            {
               addNum = 90;
            }
            else if(LVplayer >= 30 && LVplayer < 35)
            {
               addNum = 105;
            }
            else if(LVplayer >= 35 && LVplayer < 40)
            {
               addNum = 122;
            }
            else if(LVplayer >= 40 && LVplayer < 45)
            {
               addNum = 143;
            }
            else if(LVplayer >= 45 && LVplayer < 50)
            {
               addNum = 167;
            }
            else if(LVplayer >= 50 && LVplayer < 55)
            {
               addNum = 195;
            }
            else if(LVplayer >= 55 && LVplayer < 60)
            {
               addNum = 228;
            }
            else if(LVplayer >= 60 && LVplayer < 65)
            {
               addNum = 266;
            }
            else if(LVplayer >= 65 && LVplayer < 70)
            {
               addNum = 308;
            }
            else if(LVplayer >= 70 && LVplayer < 75)
            {
               addNum = 355;
            }
            else if(LVplayer >= 75 && LVplayer < 80)
            {
               addNum = 407;
            }
            else if(LVplayer >= 80 && LVplayer < 85)
            {
               addNum = 463;
            }
            else if(LVplayer >= 85 && LVplayer < 90)
            {
               addNum = 523;
            }
            else if(LVplayer >= 90 && LVplayer < 95)
            {
               addNum = 589;
            }
            else if(LVplayer >= 95 && LVplayer < 100)
            {
               addNum = 659;
            }
            return addNum - 43;
         }
      }
   }
}

