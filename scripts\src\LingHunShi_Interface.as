package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class LingHunShi_Interface extends MovieClip
   {
      
      public static var lhs_Data:Array;
      
      public static var noMovPlayArr:Array;
      
      public static var _this:LingHunShi_Interface;
      
      public static var loadData:ClassLoader;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "LHS_Panel_v1840.swf";
      
      public static var pageNum:int = 1;
      
      public static var pageMax:int = 5;
      
      private static var fanYe_YN:Boolean = true;
      
      private static var dianQuanYN:Boolean = false;
      
      public var skin:MovieClip;
      
      public function LingHunShi_Interface()
      {
         super();
         InitDataX();
         this.LoadSkin();
         _this = this;
      }
      
      public static function InitDataX() : *
      {
         var i:int = 0;
         var i2:int = 0;
         if(!lhs_Data)
         {
            lhs_Data = new Array();
            for(i = 0; i < 6; i++)
            {
               lhs_Data[i] = VT.createVT();
            }
         }
         if(!noMovPlayArr)
         {
            noMovPlayArr = new Array();
            for(i2 = 0; i2 < 6; i2++)
            {
               noMovPlayArr[i2] = false;
            }
         }
      }
      
      public static function Add_LHS(num:int) : *
      {
         InitDataX();
         if((lhs_Data[num] as VT).getValue() >= 10)
         {
            return;
         }
         (lhs_Data[num] as VT).setValue((lhs_Data[num] as VT).getValue() + 1);
         pageNum = num;
         Open();
      }
      
      public static function get_LHS() : int
      {
         if(!lhs_Data)
         {
            return 0;
         }
         var nnn:int = 0;
         for(i in lhs_Data)
         {
            if((lhs_Data[i] as VT).getValue() >= 10)
            {
               nnn++;
            }
         }
         return nnn;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.skin.visible = true;
            _this.x = _this.y = 0;
            Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Show() : *
      {
         var i:int = 0;
         for(i = 1; i <= pageMax; i++)
         {
            _this.skin["LHS_mc_" + i].visible = false;
         }
         _this.skin["LHS_mc_" + pageNum].visible = true;
         InitDataX();
         _this.skin.info_mc.gotoAndStop(pageNum);
         _this.skin.page_txt.text = pageNum + "/6";
         var numX:int = int((lhs_Data[pageNum] as VT).getValue());
         _this.skin["DianQuan_btn"].visible = true;
         if(numX >= 10)
         {
            _this.skin["DianQuan_btn"].visible = false;
         }
         var mcX:MovieClip = _this.skin["LHS_mc_" + pageNum];
         for(i = 1; i <= 10; i++)
         {
            if(i > numX)
            {
               mcX["mc" + i].visible = false;
            }
            else
            {
               mcX["mc" + i].visible = true;
               if(i == numX)
               {
                  mcX["mc" + i].gotoAndPlay(2);
               }
               else
               {
                  mcX["mc" + i].gotoAndStop("over");
               }
            }
         }
         _this.skin.X0_txt.text = "已修复: " + numX + "/10";
         if(pageNum == 1)
         {
            _this.skin.X1_txt.text = "攻击力+" + numX + "%";
            _this.skin.X2_txt.text = "对暗黑王的伤害提高" + numX * 100 + "%";
         }
         else if(pageNum == 2)
         {
            _this.skin.X1_txt.text = "防御力+" + numX + "%";
            _this.skin.X2_txt.text = "受到暗黑王的伤害减少" + numX + "%";
         }
         else if(pageNum == 3)
         {
            _this.skin.X1_txt.text = "暴击伤害+" + numX + "%";
            _this.skin.X2_txt.text = "受到暗黑王的伤害减少" + numX + "%";
         }
         else if(pageNum == 4)
         {
            _this.skin.X1_txt.text = "生命+" + numX + "%";
            _this.skin.X2_txt.text = "对暗黑王的伤害提高" + numX * 100 + "%";
         }
         else if(pageNum == 5)
         {
            _this.skin.X1_txt.text = "魔抗+" + numX + "%";
            _this.skin.X2_txt.text = "受到暗黑王伤害减少" + numX + "%";
         }
      }
      
      private static function backFun(e:*) : *
      {
         if(pageNum > 1 && Boolean(fanYe_YN))
         {
            --pageNum;
            Show();
         }
      }
      
      private static function nextFun(e:*) : *
      {
         if(pageNum < pageMax && Boolean(fanYe_YN))
         {
            ++pageNum;
            Show();
         }
      }
      
      public static function DianquanGo() : *
      {
         if(dianQuanYN)
         {
            dianQuanYN = false;
            _this.skin["DianQuan_btn"].visible = true;
            fanYe_YN = true;
            Add_LHS(pageNum);
         }
      }
      
      private static function InitOpen() : *
      {
         var temp:LingHunShi_Interface = new LingHunShi_Interface();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.skin.visible = false;
         }
      }
      
      public function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("LHS_Panel") as Class;
         this.skin = new classRef();
         _this.skin.addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addChild(this.skin);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         _this.skin["close_btn"].addEventListener(MouseEvent.CLICK,this.Close);
         _this.skin["DianQuan_btn"].addEventListener(MouseEvent.CLICK,this.DianQuanFun);
         _this.skin["back_btn"].addEventListener(MouseEvent.CLICK,backFun);
         _this.skin["next_btn"].addEventListener(MouseEvent.CLICK,nextFun);
      }
      
      private function DianQuanFun(e:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 30)
         {
            Api_4399_All.BuyObj(261);
            dianQuanYN = true;
            _this.skin["DianQuan_btn"].visible = false;
            fanYe_YN = false;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
         }
      }
      
      private function Close(e:*) : *
      {
         this.visible = false;
         this.x = this.y = 5000;
      }
   }
}

