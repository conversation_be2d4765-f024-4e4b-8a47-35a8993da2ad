package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class TiaoZhan_Interface extends MovieClip
   {
      
      public static var loadData:ClassLoader;
      
      private static var _this:TiaoZhan_Interface;
      
      private static var skin:MovieClip;
      
      public static var loadingOK:int = 0;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "TiaoZhan_v1840.swf";
      
      private static var dianQuanYN:Boolean = false;
      
      public function TiaoZhan_Interface()
      {
         super();
         _this = this;
      }
      
      public static function LoadSkin() : *
      {
         var temp:TiaoZhan_Interface = null;
         var temp2:TiaoZhanPaiHang_Interface = null;
         if(loadingOK == 0)
         {
            loadingOK = 1;
            temp = new TiaoZhan_Interface();
            temp2 = new TiaoZhanPaiHang_Interface();
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData,false);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         loadingOK = 2;
         var classRef:Class = loadData.getClass("Skin") as Class;
         skin = new classRef();
         skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
         skin.go_btn.addEventListener(MouseEvent.CLICK,GameGo);
         _this.addChild(skin);
         if(SelMap.selMapX)
         {
            SelMap.selMapX.tiaozhan_loading.visible = false;
         }
         var classRef2:Class = loadData.getClass("Skin_PaiHang") as Class;
         TiaoZhanPaiHang_Interface.skin = new classRef2();
         TiaoZhanPaiHang_Interface.InitSkin();
         var classRef3:Class = loadData.getClass("skin_TJ") as Class;
         WinShow2.skin = new classRef3();
         var classRef4:Class = loadData.getClass("Skin_time") as Class;
         TimeShow.skin = new classRef4();
         new TimeShow();
      }
      
      private static function GameGo(e:*) : *
      {
         var money:int = int((PaiHang_Data.dataArr[Main.gameNum.getValue()][0] as VT).getValue());
         if(Main.P1P2)
         {
            if(Main.player1.getGold() < money || Main.player2.getGold() < money)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"金币不足");
               return;
            }
            if(Main.player1.killPoint.getValue() < 10 || Main.player2.killPoint.getValue() < 10)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"击杀点不足");
               return;
            }
            if(Main.player1.level.getValue() < 30 || Main.player2.level.getValue() < 30)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"等级30级后方可进入");
               return;
            }
         }
         else
         {
            if(Main.player1.level.getValue() < 30)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"等级30级后方可进入");
               return;
            }
            if(Main.player1.getGold() < money)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"金币不足");
               return;
            }
            if(Main.player1.killPoint.getValue() < 10)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"击杀点不足");
               return;
            }
         }
         if(Main.guanKa[Main.gameNum.getValue()] < 3)
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"开启3星难度方可进入");
            return;
         }
         var inGameNum:int = int(PaiHang_Data.inGameNum[Main.gameNum.getValue()]);
         if(inGameNum <= 0)
         {
            DianQuan_Fun();
            return;
         }
         var numXX:int = int(GongHui_jiTan.killPointXX(10));
         Main.player1.payGold(money);
         Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - numXX);
         if(Main.P1P2)
         {
            Main.player2.payGold(money);
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - numXX);
         }
         --PaiHang_Data.inGameNum[Main.gameNum.getValue()];
         Main.serviceHold.getServerTime();
         GameData.gameLV = 5;
         Close();
         SelMap.Close();
         Main.gameNum2.setValue(1);
         Main._this.Loading();
         Main.Save();
      }
      
      private static function DianQuan_Fun() : *
      {
         skin.dianQuan_mc.visible = true;
         skin.dianQuan_mc.ok_btn.addEventListener(MouseEvent.CLICK,DianQuan_ok);
         skin.dianQuan_mc.close_btn.addEventListener(MouseEvent.CLICK,DianQuan_close);
      }
      
      private static function DianQuan_ok(e:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 5)
         {
            Api_4399_All.BuyObj(127);
            skin.xxx_mc.visible = true;
            dianQuanYN = true;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
            skin.dianQuan_mc.visible = false;
         }
      }
      
      public static function DianQuan_GO() : *
      {
         if(dianQuanYN)
         {
            Main.serviceHold.getServerTime();
            dianQuanYN = false;
            skin.xxx_mc.visible = true;
            GameData.gameLV = 5;
            Close();
            SelMap.Close();
            Main.gameNum2.setValue(1);
            Main._this.Loading();
         }
      }
      
      private static function DianQuan_close(e:*) : *
      {
         skin.dianQuan_mc.visible = false;
      }
      
      public static function Open() : *
      {
         LoadInGame.Open(loadData);
         SelMap.selMapX.addChild(_this);
         _this.visible = true;
         _this.x = _this.y = 0;
         Show();
      }
      
      public static function Close(e:* = null) : *
      {
         _this.visible = false;
         _this.x = _this.y = 5000;
      }
      
      private static function Show() : *
      {
         var p1p2:int = 0;
         var xxNum:int = 0;
         var selTypeX:int = 0;
         var tmpObj1:Object = null;
         var tmpObj2:Object = null;
         skin.xxx_mc.visible = false;
         skin.dianQuan_mc.visible = false;
         skin.x1_txt.text = "";
         skin.x2_txt.text = "";
         skin.x3_txt.text = "";
         skin.show_mc.gotoAndStop("d" + Main.gameNum.getValue());
         if(Main.P1P2)
         {
            p1p2 = 2;
         }
         else
         {
            p1p2 = 1;
         }
         var gameNumX:int = int(Main.gameNum.getValue());
         if(gameNumX >= 1 && gameNumX <= 9)
         {
            xxNum = 1;
            selTypeX = gameNumX;
         }
         else if(gameNumX >= 10 && gameNumX <= 16)
         {
            xxNum = 2;
            selTypeX = gameNumX - 9;
         }
         else if(gameNumX >= 51 && gameNumX <= 62)
         {
            xxNum = 3;
            selTypeX = gameNumX - 50;
         }
         else if(gameNumX >= 18 && gameNumX <= 29)
         {
            xxNum = 4;
            selTypeX = gameNumX - 17;
         }
         var pId:int = int(PaiHang_Data["gameNum_x" + p1p2 + "_" + xxNum][selTypeX]);
         if(!PaiHang_Data.paiHangArr[pId])
         {
            Api_4399_All.GetRankListsData(1,50,pId);
            Api_4399_All.GetOneRankInfo(Main.logName,pId,1);
         }
         if(Boolean(PaiHang_Data.paiHangArr[pId]) && Boolean(PaiHang_Data.paiHangArr[pId][0]))
         {
            tmpObj1 = PaiHang_Data.paiHangArr[pId][0];
            skin.x3_txt.text = tmpObj1.score;
         }
         else
         {
            skin.x3_txt.text = "暂无";
         }
         if(Boolean(PaiHang_Data.paiHangArr[pId]) && Boolean(PaiHang_Data.paiHangArr[pId][1]))
         {
            tmpObj2 = PaiHang_Data.paiHangArr[pId][1];
            skin.x1_txt.text = tmpObj2.userName;
            skin.x2_txt.text = tmpObj2.score;
         }
         else
         {
            skin.x1_txt.text = "暂无";
            skin.x2_txt.text = "暂无";
         }
         var money:int = int((PaiHang_Data.dataArr[Main.gameNum.getValue()][0] as VT).getValue());
         var inGameNum:int = int(PaiHang_Data.inGameNum[Main.gameNum.getValue()]);
         skin["txt1"].text = "消耗击杀点:10   消耗金币:" + money;
         skin["txt2"].text = "本关剩余挑战次数:" + inGameNum;
      }
      
      public static function TxtShow1(dataAry:Array, paiHangId:int) : *
      {
         var tmpObj:Object = null;
         if(PaiHang_Data.paiHangArr[paiHangId][1])
         {
            tmpObj = PaiHang_Data.paiHangArr[paiHangId][1];
            skin.x1_txt.text = tmpObj.userName;
            skin.x2_txt.text = tmpObj.score;
         }
         else
         {
            skin.x1_txt.text = "暂无";
            skin.x2_txt.text = "暂无";
         }
      }
      
      public static function TxtShow2(dataAry:Array, paiHangId:int) : *
      {
         var i:int = 0;
         var tmpObj:Object = null;
         if(dataAry != null && dataAry.length != 0)
         {
            for(i in dataAry)
            {
               tmpObj = dataAry[i];
               if(int(tmpObj.index) == Main.saveNum)
               {
                  skin.x3_txt.text = tmpObj.score;
                  return;
               }
            }
            skin.x3_txt.text = "未上榜";
         }
         else
         {
            skin.x3_txt.text = "未上榜";
         }
      }
   }
}

