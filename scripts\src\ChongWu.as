package src
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.other.*;
   
   public class ChongWu extends MovieClip
   {
      
      public static var dataXml:XML;
      
      public static var loadData:ClassLoader;
      
      public static var loadData2:ClassLoader;
      
      public static var chongWu_Data:Array = new Array();
      
      public static var jiaZaiOK:Array = [];
      
      public static var chongWu_Data2:Array = new Array();
      
      public static var jiaZaiOK2:Array = [];
      
      public static var gongJiYn1:Boolean = false;
      
      public static var gongJiYn2:Boolean = false;
      
      public var who:Player;
      
      public var data:Pet;
      
      public var food:VT = VT.createVT(100);
      
      public var RL:Boolean = true;
      
      private var distance_X:int = 0;
      
      private var distanceMax:int = 1200;
      
      private var walk_power:VT = VT.createVT(7);
      
      private var gravity:int = 20;
      
      public var skin:MovieClip;
      
      public var runType:String = "站立";
      
      public var runType2:String = "站立";
      
      public var continuous:Boolean;
      
      public var noMove:Boolean = false;
      
      public var MoveYn:Boolean = false;
      
      public var gongJiEnemy:Enemy;
      
      public var goEnemy:Boolean = false;
      
      public var goJiCD_max:Array = [];
      
      public var goJiCD_now:Array = [];
      
      public var nullTime:int = 0;
      
      public var cdOK_Arr:Array = [];
      
      public var typeClass:int;
      
      public var noLoad:Boolean = false;
      
      private var wudiTime:int = 0;
      
      public var riyanTime:int = 0;
      
      public var riyanAdd:int = 0;
      
      public var tempTT:int = 0;
      
      public var tempTTT:int = 0;
      
      public var nai_C:Class = NewLoad.XiaoGuoData.getClass("Nai") as Class;
      
      public var naizui_C:Class = NewLoad.XiaoGuoData.getClass("Naizui") as Class;
      
      public var fire_C:Class = NewLoad.XiaoGuoData.getClass("Fire") as Class;
      
      public var nai:MovieClip = new this.nai_C();
      
      public var naizui:MovieClip = new this.naizui_C();
      
      public var fire:MovieClip = new this.fire_C();
      
      public function ChongWu(_data:Pet, p:Player)
      {
         super();
         this.who = p;
         this.who.data.playerCW_Data = this.data = _data;
         this.who.playerCW = this;
         this.goJiCD_max = this.data.getPetCD();
         for(var i:* = 0; i < this.goJiCD_max.length; i++)
         {
            this.goJiCD_now[i] = 90;
         }
         this.goEnemy = this.who.data.getPetSlot().getMode();
         this.addSkin();
      }
      
      public static function get_XML_data() : *
      {
         dataXml = XMLAsset.createXML(Data2.petHit_data);
      }
      
      public function Close() : *
      {
         this.noLoad = true;
         trace("关闭宠物");
         if(this.skin)
         {
            this.skin.gotoAndStop(1);
            this.parent.removeChild(this);
         }
         removeEventListener(Event.ENTER_FRAME,this.riyan);
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.who.playerCW = null;
         this.who.data.playerCW_Data = null;
      }
      
      private function LoadCWGo(num:int, str:String) : *
      {
         chongWu_Data[num] = new ClassLoader(str);
         if(NewPetPanel.petPanel)
         {
            NewPetPanel.petPanel["touming"].visible = true;
         }
         chongWu_Data[num].addEventListener(Event.COMPLETE,this.LoadCWEnd);
      }
      
      private function LoadCWGo2(num:int, str:String) : *
      {
         chongWu_Data2[num] = new ClassLoader(str);
         chongWu_Data2[num].addEventListener(Event.COMPLETE,this.LoadCWEnd2);
      }
      
      private function LoadCWEnd(e:* = null) : *
      {
         var classNum:int = int(PetFactory.getClassName(this.data._id.getValue()));
         if(classNum == 14)
         {
            if(!jiaZaiOK2[classNum])
            {
               this.LoadCWGo2(14,"CWBS14_v1920.swf");
               trace("宠物变身 加载 >>>>>>>>>>>" + classNum);
               return;
            }
         }
         jiaZaiOK[classNum] = true;
         if(NewPetPanel.petPanel)
         {
            NewPetPanel.petPanel["touming"].visible = false;
         }
         trace("宠物加载完成 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>" + classNum);
         this.addSkin();
      }
      
      public function LoadCWEnd2(e:*) : *
      {
         var classNum:int = int(PetFactory.getClassName(this.data._id.getValue()));
         jiaZaiOK2[classNum] = true;
         trace("宠物变身 加载完成 >>>>>>>>>>>" + classNum);
         this.LoadCWEnd();
      }
      
      private function addSkin() : *
      {
         var classRef:Class = null;
         var classNum:int = int(PetFactory.getClassName(this.data._id.getValue()));
         var classStr:String = classNum;
         this.typeClass = int(classStr);
         if(classNum == 4)
         {
            classStr = "4_v942";
         }
         else if(classNum == 18)
         {
            classStr = "18_v2";
         }
         else if(classNum == 20)
         {
            classStr = "20_v842";
         }
         else if(classNum == 14)
         {
            classStr = "14_v941";
         }
         else if(classNum == 22)
         {
            classStr = "22_v900";
         }
         else if(classNum == 29)
         {
            classStr = "29_v1041";
         }
         else if(classNum == 31)
         {
            classStr = "31_v1051";
         }
         var SWFStr:* = "CW" + classStr + ".swf";
         var classStrX:String = "CW" + classNum;
         if(!chongWu_Data[classNum])
         {
            this.LoadCWGo(classNum,SWFStr);
            trace("宠物加载 >>>>>>>>>>>>>>>>>>>>>>>>>" + classNum);
            return;
         }
         if(!jiaZaiOK[classNum])
         {
            return;
         }
         if(this.noLoad)
         {
            return;
         }
         classRef = ChongWu.chongWu_Data[classNum].getClass(classStrX) as Class;
         this.skin = new classRef();
         addChild(this.skin);
         this.y = 500;
         this.who.data.playerCW_Data = this.data;
         this.who.playerCW = this;
         Main.world.moveChild_ChongWu.addChild(this);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.SearchPlayer();
         mouseChildren = mouseEnabled = false;
         trace("宠物 >>>>>>>>>>>>>>>>> ChongWu");
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         if(this.noLoad)
         {
            this.Close();
            return;
         }
         this.GongJi();
         if(!this.noMove)
         {
            this.WhereAreYou();
         }
         this.GoToPlay();
         this.WhoDead();
      }
      
      private function WhoDead() : *
      {
         if(!this.who || this.who.hp.getValue() <= 0)
         {
            this.Close();
         }
      }
      
      private function WhereAreYou() : *
      {
         var i:int = 0;
         var distance_X2:uint = 0;
         if(this.parent != Main.world.moveChild_ChongWu)
         {
            Main.world.moveChild_ChongWu.addChild(this);
            this.SearchPlayer();
         }
         if(this.goEnemy && Enemy.All.length > 0)
         {
            if(!this.gongJiEnemy)
            {
               this.gongJiEnemy = Enemy.All[0];
               this.distance_X = this.x - this.gongJiEnemy.x;
               for(i = 1; i < Enemy.All.length; i++)
               {
                  distance_X2 = this.x - Enemy.All[i].x;
                  if(Math.abs(this.distance_X) > Math.abs(distance_X2))
                  {
                     this.distance_X = distance_X2;
                  }
               }
            }
            else
            {
               this.distance_X = this.x - this.gongJiEnemy.x;
               if(this.gongJiEnemy.life.getValue() <= 0)
               {
                  this.gongJiEnemy = null;
               }
            }
         }
         else
         {
            this.distance_X = this.x - this.who.x;
            distance_Y = this.y - this.who.y;
            if(Math.abs(this.distance_X) > this.distanceMax)
            {
               this.SearchPlayer();
            }
            this.gongJiEnemy = null;
         }
         this.MoveRun();
      }
      
      public function SearchPlayer() : *
      {
         this.x = this.who.x + Math.random() * 200 - 100;
         this.y = this.who.y - 100;
      }
      
      private function MoveRun() : *
      {
         var i:int = 0;
         var BB:Boolean = false;
         var AA:Boolean = false;
         var CC:Boolean = false;
         var forecast_x:int = this.x + Main.world.x;
         var forecast_y:int = this.y;
         for(i = int(this.gravity); i > 0; i--)
         {
            BB = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 3,true));
            AA = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 1,true));
            CC = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y + 6,true));
            if(BB)
            {
               forecast_y -= 2;
            }
            else if(AA)
            {
               runY = 0;
            }
            else if(CC)
            {
               forecast_y += 3;
            }
            else
            {
               forecast_y++;
            }
         }
         this.y = forecast_y;
         var id:int = this.data._id.getValue();
         var juLiEnemy:int = 100;
         if(id == 73117 || id == 73118)
         {
            this.walk_power.setValue(12);
            juLiEnemy = 300;
         }
         for(i = Math.abs(this.walk_power.getValue()); i > 0; i--)
         {
            AA = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 30,true));
            if(!AA)
            {
               if(this.distance_X > 5)
               {
                  this.getRL(false);
               }
               else if(this.distance_X < -5)
               {
                  this.getRL(true);
               }
               if(this.gongJiEnemy && this.distance_X > juLiEnemy || this.distance_X > 200)
               {
                  forecast_x--;
                  this.GoTo("移动",true);
               }
               else if(this.gongJiEnemy && this.distance_X < -juLiEnemy || this.distance_X < -200)
               {
                  forecast_x++;
                  this.GoTo("移动",true);
               }
               else
               {
                  this.GoTo("站立");
               }
            }
         }
         var xxx:int = forecast_x - Main.world.x;
         if(xxx > Main.world._width + 100)
         {
            this.x = Main.world._width + 100;
         }
         else if(xxx < -100)
         {
            this.x = -100;
         }
         else
         {
            this.x = xxx;
         }
      }
      
      public function getRL(_RL:Boolean) : *
      {
         if(this.skin.currentLabel != "移动")
         {
            return;
         }
         this.RL = _RL;
         if(_RL)
         {
            scaleX = -1;
         }
         else if(!_RL)
         {
            scaleX = 1;
         }
      }
      
      private function GoToPlay() : *
      {
         var runTypeX:String = null;
         if(this.isRunOver())
         {
            if(this.runType2 != "攻击")
            {
               runTypeX = this.runType;
               if(runTypeX == "站立")
               {
                  runTypeX = "站立1";
               }
               this.skin.gotoAndPlay(runTypeX);
            }
            else
            {
               this.GoTo("站立");
            }
         }
      }
      
      public function isRunOver() : Boolean
      {
         if(this.skin.currentLabel != this.runType)
         {
            this.noMove = false;
            return true;
         }
         return false;
      }
      
      private function GongJi() : *
      {
         if(this.nullTime > 0)
         {
            --this.nullTime;
         }
         this.cdOK_Arr = new Array();
         for(var i:int = 1; i < 6; i++)
         {
            if(this.goJiCD_now[i] > 0)
            {
               --this.goJiCD_now[i];
            }
            else
            {
               this.cdOK_Arr[i] = true;
            }
         }
         if(this.gongJiEnemy || this.who == Main.player_1 && gongJiYn1 || Main.player_2 && this.who == Main.player_2 && gongJiYn2)
         {
            if(Enemy.All.length > 0 && this.nullTime <= 0 && this.cdOK_Arr.length > 0)
            {
               this.GoTo("攻击",true);
            }
         }
         var classNum:int = int(PetFactory.getClassName(this.data._id.getValue()));
         if(classNum == 20)
         {
            this.CW20_GongJi();
         }
         if(classNum == 24)
         {
            this.CW24_GongJi();
         }
         if(classNum == 26)
         {
            this.CW26_GongJi();
         }
         if(classNum == 34)
         {
            this.CW34_GongJi();
         }
      }
      
      public function CW34_GongJi() : *
      {
         if(this.skin.currentLabel == "攻击3")
         {
            Cw34_Fly3x.Move(this);
         }
      }
      
      public function CW26_GongJi() : *
      {
         if(this.skin.currentFrame >= 36 + 24 && this.skin.currentFrame <= 36 + 28 || this.skin.currentFrame >= 36 + 35 && this.skin.currentFrame <= 36 + 38 || this.skin.currentFrame >= 36 + 47 && this.skin.currentFrame <= 36 + 52)
         {
            if(this.RL)
            {
               this.x += 28;
            }
            else
            {
               this.x -= 28;
            }
            return;
         }
         if(this.skin.currentFrame >= 100 + 24 && this.skin.currentFrame <= 100 + 70)
         {
            if(this.RL)
            {
               this.x += 15;
            }
            else
            {
               this.x -= 15;
            }
            return;
         }
      }
      
      public function CW20_GongJi() : *
      {
         if(this.skin.currentFrame >= 522 && this.skin.currentFrame <= 522 + 14)
         {
            if(this.RL)
            {
               this.x += 48;
            }
            else
            {
               this.x -= 48;
            }
            return;
         }
      }
      
      public function CW24_GongJi() : *
      {
         if(this.skin.currentFrame >= 489 + 35 && this.skin.currentFrame <= 489 + 95)
         {
            if(this.RL)
            {
               this.x += 7;
            }
            else
            {
               this.x -= 7;
            }
         }
         if(this.skin.currentFrame >= 594 + 13 && this.skin.currentFrame <= 594 + 117)
         {
            if(this.RL)
            {
               this.x += 6;
            }
            else
            {
               this.x -= 6;
            }
         }
      }
      
      public function GoTo(str:String, YN:Boolean = false) : *
      {
         var jiNengID:int = 0;
         var arr:Array = null;
         var xx:int = 0;
         var h:HitXX = null;
         if(this.noMove)
         {
            return;
         }
         var num:uint = 1;
         if(this.data._id.getValue() == 73105)
         {
            num = 5;
         }
         if(this.data._id.getValue() == 73108)
         {
            num = 4;
         }
         if(this.data._id.getValue() == 73118)
         {
            num = 2;
         }
         var str2:String = str;
         if(str == "站立")
         {
            str2 = "站立" + int(Math.random() * num + 1);
         }
         else if(str == "攻击")
         {
            arr = this.data.getPetSkillType();
            xx = int(Math.random() * 4);
            jiNengID = int(arr[xx]);
            if(!(jiNengID > 0 && Boolean(this.cdOK_Arr[jiNengID])))
            {
               if(jiNengID <= 0)
               {
                  this.nullTime = 270;
                  if(this.who == Main.player_1)
                  {
                     gongJiYn1 = false;
                  }
                  else
                  {
                     gongJiYn2 = false;
                  }
               }
               return;
            }
            str2 = "攻击" + jiNengID;
            if(this.data.getPetEquip())
            {
               if(this.data.getPetEquip().getType() == 7)
               {
                  arr = this.data.getPetEquip().getAffect();
                  this.riyanAdd = this.who.use_gongji.getValue() * arr[0];
                  this.riyanTime = arr[2];
                  this.who.playerCW.addChild(this.fire);
                  addEventListener(Event.ENTER_FRAME,this.riyan);
               }
               if(this.data.getPetEquip().getType() == 6)
               {
                  arr = this.data.getPetEquip().getAffect();
                  if(Math.random() * 100 <= arr[1])
                  {
                     this.wudiTime = arr[0];
                     this.who.addChild(this.naizui);
                     this.who.addChild(this.nai);
                     this.who.addEventListener(Event.ENTER_FRAME,this.goWuDi);
                     this.who.addEventListener(Event.ENTER_FRAME,this.goWuDiMC);
                  }
               }
            }
         }
         if(this.runType != str2 && (this.isRunOver() || YN))
         {
            this.runType = str2;
            this.runType2 = str;
            if(str == "攻击")
            {
               if(this.data._id.getValue() == 73107 && str2 == "攻击4")
               {
                  h = new HitXX();
                  h.type = 100;
                  h.totalTime = 12 * 27;
                  new BuffEffect(h,this.who);
               }
               this.noMove = true;
               this.nullTime = this.goJiCD_now[jiNengID] = this.goJiCD_max[jiNengID];
               this.XXCD(jiNengID);
               if(this.who == Main.player_1)
               {
                  gongJiYn1 = false;
               }
               else
               {
                  gongJiYn2 = false;
               }
            }
            if(str == "站立")
            {
               this.skin.gotoAndStop(this.runType);
            }
            else
            {
               this.skin.gotoAndPlay(this.runType);
            }
         }
      }
      
      public function XXCD(jiNengID:int) : *
      {
         var arr:Array = null;
         if(this.data.getPetEquip())
         {
            if(this.data.getPetEquip().getType() == 14 && Enemy.wscd > 135)
            {
               Enemy.wscd = 0;
               arr = this.data.getPetEquip().getAffect();
               this.goJiCD_now[jiNengID] = 0;
            }
         }
      }
      
      public function riyan(e:*) : *
      {
         ++this.tempTT;
         --this.riyanTime;
         if(this.tempTT > 27)
         {
            for(i in Enemy.All)
            {
               if(Enemy.All[i])
               {
                  if(Math.abs(Enemy.All[i].x - this.x) < 200)
                  {
                     Enemy.All[i].hpCount(this.riyanAdd);
                     HPdown.Open(this.riyanAdd,Enemy.All[i].x,Enemy.All[i].y - Enemy.All[i].height);
                  }
               }
            }
            this.tempTT = 0;
         }
         if(this.riyanTime <= 0)
         {
            this.removeChild(this.fire);
            removeEventListener(Event.ENTER_FRAME,this.riyan);
         }
      }
      
      private function goWuDiMC(e:*) : *
      {
         ++this.tempTTT;
         if(this.tempTTT >= 24)
         {
            this.nai.parent.removeChild(this.nai);
            this.tempTTT = 0;
            this.who.removeEventListener(Event.ENTER_FRAME,this.goWuDiMC);
         }
      }
      
      private function goWuDi(e:*) : *
      {
         --this.wudiTime;
         if(this.who.skin.currentLabel == "站")
         {
            this.who.rexuewudi = true;
            if(this.wudiTime % 10 > 4)
            {
               this.who.alpha = 1;
            }
            else
            {
               this.who.alpha = 0.7;
            }
         }
         else
         {
            this.who.rexuewudi = false;
            this.who.alpha = 1;
         }
         if(this.wudiTime < 0)
         {
            this.naizui.parent.removeChild(this.naizui);
            this.who.rexuewudi = false;
            this.who.alpha = 1;
            this.who.removeEventListener(Event.ENTER_FRAME,this.goWuDi);
         }
      }
   }
}

