package com.hotpoint.braveManIII.models.basicBtn
{
   import com.hotpoint.braveManIII.events.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.text.TextField;
   
   public class OverBtn extends BasicBtn
   {
      
      public var cion_mast:MovieClip;
      
      public var diKuang:MovieClip;
      
      public var howNum:TextField;
      
      public function OverBtn()
      {
         super();
         this.buttonMode = false;
      }
      
      override protected function rollOver(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OVER)
         {
            this.dispatchEvent(new BtnEvent(BtnEvent.DO_OVER,this.id));
         }
      }
      
      override protected function rollOut(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OUT)
         {
            this.dispatchEvent(new BtnEvent(BtnEvent.DO_OUT,this.id));
         }
      }
      
      override protected function mouseDown(event:MouseEvent) : void
      {
      }
      
      override protected function mouseUp(event:MouseEvent) : void
      {
      }
   }
}

