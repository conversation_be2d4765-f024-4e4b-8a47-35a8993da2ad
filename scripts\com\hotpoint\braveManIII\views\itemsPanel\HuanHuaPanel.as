package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class HuanHuaPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var hhPanel:MovieClip;
      
      public static var hhp:HuanHuaPanel;
      
      public static var clickObj:MovieClip;
      
      public static var clickNum:Number;
      
      public static var myPlayer:PlayerData;
      
      public static var otherID:int;
      
      private static var className:String;
      
      private static var miaoshu:String;
      
      private static var nameStr:String;
      
      private static var loadData:ClassLoader;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_HH_v890.swf";
      
      public static var selbool:Boolean = false;
      
      public function HuanHuaPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!hhPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = hhPanel.getChildIndex(hhPanel["e" + i]);
            mm.x = hhPanel["e" + i].x;
            mm.y = hhPanel["e" + i].y;
            mm.name = "e" + i;
            hhPanel.removeChild(hhPanel["e" + i]);
            hhPanel["e" + i] = mm;
            hhPanel.addChild(mm);
            hhPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = hhPanel.getChildIndex(hhPanel["s" + i]);
            mm.x = hhPanel["s" + i].x;
            mm.y = hhPanel["s" + i].y;
            mm.name = "s" + i;
            hhPanel.removeChild(hhPanel["s" + i]);
            hhPanel["s" + i] = mm;
            hhPanel.addChild(mm);
            hhPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = hhPanel.getChildIndex(hhPanel["select"]);
         mm.x = hhPanel["select"].x;
         mm.y = hhPanel["select"].y;
         mm.name = "select";
         hhPanel.removeChild(hhPanel["select"]);
         hhPanel["select"] = mm;
         hhPanel.addChild(mm);
         hhPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("HHShow") as Class;
         hhPanel = new classRef();
         hhp.addChild(hhPanel);
         InitIcon();
         if(OpenYN)
         {
            open(myPlayer,otherID,miaoshu);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         hhp = new HuanHuaPanel();
         LoadSkin();
         Main._stage.addChild(hhp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         hhp = new HuanHuaPanel();
         Main._stage.addChild(hhp);
         OpenYN = false;
      }
      
      public static function open(pp:PlayerData, oid:int, ms:String) : void
      {
         Main.allClosePanel();
         if(hhPanel)
         {
            Main.stopXX = true;
            hhp.x = 0;
            hhp.y = 0;
            myPlayer = pp;
            otherID = oid;
            miaoshu = ms;
            addListenerP1();
            Main._stage.addChild(hhp);
            hhp.visible = true;
         }
         else
         {
            myPlayer = pp;
            otherID = oid;
            miaoshu = ms;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(hhPanel)
         {
            selbool = false;
            Main.stopXX = false;
            removeListenerP1();
            hhp.visible = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         hhPanel["us_btn"].addEventListener(MouseEvent.CLICK,doHH);
         hhPanel["close"].addEventListener(MouseEvent.CLICK,closeUS);
         for(var i:uint = 0; i < 24; i++)
         {
            hhPanel["e" + i].mouseChildren = false;
            hhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            hhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            hhPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            hhPanel["s" + i].mouseChildren = false;
            hhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            hhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            hhPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         hhPanel["select"].gotoAndStop(1);
         hhPanel["select"].visible = false;
         showAll();
         hhPanel["chose"].visible = false;
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         hhPanel["close"].removeEventListener(MouseEvent.CLICK,closeUS);
         hhPanel["us_btn"].removeEventListener(MouseEvent.CLICK,doHH);
         for(i = 0; i < 24; i++)
         {
            hhPanel["e" + i].mouseChildren = false;
            hhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            hhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            hhPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            hhPanel["s" + i].mouseChildren = false;
            hhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            hhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            hhPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
      }
      
      private static function selected(e:MouseEvent) : void
      {
         selbool = true;
         clickObj = e.target as MovieClip;
         hhPanel["chose"].visible = true;
         hhPanel["chose"].x = clickObj.x - 2;
         hhPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         hhPanel["select"].gotoAndStop(clickObj.currentFrame);
         hhPanel["select"].visible = true;
      }
      
      public static function closeUS(e:*) : *
      {
         close();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         hhPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(str == "e")
         {
            if(myPlayer.getBag().getEquipFromBag(overNum) != null)
            {
               if(myPlayer.getBag().getEquipFromBag(overNum).getPosition() == 0 || myPlayer.getBag().getEquipFromBag(overNum).getPosition() >= 5 && myPlayer.getBag().getEquipFromBag(overNum).getPosition() <= 7)
               {
                  itemsTooltip.equipTooltip(myPlayer.getBag().getEquipFromBag(overNum),1);
               }
            }
         }
         else if(myPlayer.getEquipSlot().getEquipFromSlot(overNum).getPosition() == 0 || myPlayer.getEquipSlot().getEquipFromSlot(overNum).getPosition() >= 5 && myPlayer.getEquipSlot().getEquipFromSlot(overNum).getPosition() <= 7)
         {
            itemsTooltip.slotTooltip(overNum,myPlayer.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = hhPanel.mouseX + 10;
         itemsTooltip.y = hhPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function showAll() : *
      {
         var buWei:* = undefined;
         for(var i:int = 0; i < 24; i++)
         {
            hhPanel["e" + i].t_txt.text = "";
            if(myPlayer.getBag().getEquipFromBag(i) != null)
            {
               buWei = myPlayer.getBag().getEquipFromBag(i).getPosition();
               if(buWei == 0 || buWei >= 5 && buWei <= 7)
               {
                  hhPanel["e" + i].gotoAndStop(myPlayer.getBag().getEquipFromBag(i).getFrame());
                  hhPanel["e" + i].visible = true;
               }
               else
               {
                  hhPanel["e" + i].visible = false;
               }
            }
            else
            {
               hhPanel["e" + i].visible = false;
            }
         }
         for(var j:uint = 0; j < 8; j++)
         {
            hhPanel["s" + j].t_txt.text = "";
            if(myPlayer.getEquipSlot().getEquipFromSlot(j) != null)
            {
               buWei = myPlayer.getEquipSlot().getEquipFromSlot(j).getPosition();
               if(buWei == 0 || buWei >= 5 && buWei <= 7)
               {
                  hhPanel["s" + j].gotoAndStop(myPlayer.getEquipSlot().getEquipFromSlot(j).getFrame());
                  hhPanel["s" + j].visible = true;
               }
               else
               {
                  hhPanel["s" + j].visible = false;
               }
            }
            else
            {
               hhPanel["s" + j].visible = false;
            }
         }
      }
      
      private static function doHH(e:*) : *
      {
         if(selbool == false)
         {
            return;
         }
         if(otherID == 63300)
         {
            if(nameStr == "e")
            {
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 5)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(1,"剑金6",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 6)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(1,"杖金6",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 7)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(1,"拳金6",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 0)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(1,"刀金6",miaoshu);
               }
            }
            else
            {
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 5)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(1,"剑金6",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 6)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(1,"杖金6",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 7)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(1,"拳金6",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 0)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(1,"刀金6",miaoshu);
               }
            }
            myPlayer.getBag().delOtherById(63300);
            if(PK_UI.jiFenArr[7] > 0)
            {
               --PK_UI.jiFenArr[7];
            }
         }
         if(otherID == 63309)
         {
            if(nameStr == "e")
            {
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 5)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 6)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 7)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 0)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
            }
            else
            {
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 5)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 6)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 7)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 0)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(2,"绿1",miaoshu);
               }
            }
            myPlayer.getBag().delOtherById(63309);
         }
         if(otherID == 63327)
         {
            if(nameStr == "e")
            {
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 5)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(3,"剑金7",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 6)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(3,"杖金7",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 7)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(3,"拳金7",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 0)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(3,"刀金7",miaoshu);
               }
            }
            else
            {
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 5)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(3,"剑金7",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 6)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(3,"杖金7",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 7)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(3,"拳金7",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 0)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(3,"刀金7",miaoshu);
               }
            }
            myPlayer.getBag().delOtherById(63327);
         }
         if(otherID == 63376)
         {
            if(nameStr == "e")
            {
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 5)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(4,"剑金9",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 6)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(4,"杖金9",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 7)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(4,"拳金9",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 0)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(4,"刀金9",miaoshu);
               }
            }
            else
            {
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 5)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(4,"剑金9",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 6)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(4,"杖金9",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 7)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(4,"拳金9",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 0)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(4,"刀金9",miaoshu);
               }
            }
            myPlayer.getBag().delOtherById(63376);
         }
         if(otherID == 63453 || otherID == 63455)
         {
            if(nameStr == "e")
            {
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 5)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(5,"剑金10",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 6)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(5,"杖金10",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 7)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(5,"拳金10",miaoshu);
               }
               if(myPlayer.getBag().getEquipFromBag(clickNum).getPosition() == 0)
               {
                  myPlayer.getBag().getEquipFromBag(clickNum).setHuanHua(5,"刀金10",miaoshu);
               }
            }
            else
            {
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 5)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(5,"剑金10",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 6)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(5,"杖金10",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 7)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(5,"拳金10",miaoshu);
               }
               if(myPlayer.getEquipSlot().getEquipFromSlot(clickNum).getPosition() == 0)
               {
                  myPlayer.getEquipSlot().getEquipFromSlot(clickNum).setHuanHua(5,"刀金10",miaoshu);
               }
            }
            if(otherID == 63453)
            {
               myPlayer.getBag().delOtherById(63453);
            }
            if(otherID == 63455)
            {
               myPlayer.getBag().delOtherById(63455);
            }
         }
         if(myPlayer == Main.player1)
         {
            Main.player_1.newSkin();
         }
         else
         {
            Main.player_2.newSkin();
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"幻化成功");
         close();
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
   }
}

