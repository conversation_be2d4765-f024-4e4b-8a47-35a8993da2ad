package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.jiangliPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.setTransfer.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import com.hotpoint.braveManIII.views.temaiPanel.*;
   import com.hotpoint.braveManIII.views.tuijianPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1617")]
   public class Play_Interface extends MovieClip
   {
      
      public static var interfaceX:Play_Interface;
      
      public static var SaveLoadMC:MovieClip;
      
      public static var Xfile_YN:Boolean;
      
      public static var allTooltip:MovieClip;
      
      public static var BossLifeYN:Boolean;
      
      public static var bossIS:Enemy;
      
      public static var SixOneOK:Boolean = false;
      
      public static var templianJiNum:Array = [0,0];
      
      public static const data:Class = Play_Interface_data;
      
      public static var cz_Open1:Boolean = true;
      
      private static var LunTan10Num_now:int = 0;
      
      public static var btnArr:Array = [];
      
      public static var btnNum:int = 0;
      
      public static var buChangHeCeng2:Boolean = false;
      
      public static var huaZhi:uint = 1;
      
      private static var OpenYN:Boolean = false;
      
      private static var OpenYN2:Boolean = false;
      
      private static var showTimeNum:uint = 60;
      
      private static var showTimeNum2:uint = 60;
      
      public var BossLife_mc:MovieClip;
      
      public var ChongZhiFuLi_btn:SimpleButton;
      
      public var DOWNxTime_mc:MovieClip;
      
      public var EXPxTime_mc:ShopObjTime;
      
      public var HuoDong10_btn:SimpleButton;
      
      public var Nian_btn:SimpleButton;
      
      public var Play1_mc:MovieClip;
      
      public var Play2_mc:MovieClip;
      
      public var _Move_mc:MovieClip;
      
      public var _Move_mc2:MovieClip;
      
      public var _PK_time_mc:MovieClip;
      
      public var _PK_time_mc2:MovieClip;
      
      public var black_guoDu_mc:关卡过渡黑幕;
      
      public var buCang_btn:SimpleButton;
      
      public var chongZhi_X1:SimpleButton;
      
      public var chongZhi_X2:SimpleButton;
      
      public var chongZhi_X3:SimpleButton;
      
      public var chongZhi_btn1:SimpleButton;
      
      public var chongZhi_btn2:SimpleButton;
      
      public var chongZhi_btn3:SimpleButton;
      
      public var chongZhi_btn4:SimpleButton;
      
      public var chongZhi_btn5:SimpleButton;
      
      public var chongZhi_btn6:SimpleButton;
      
      public var chongZhi_btn7:SimpleButton;
      
      public var chongZhi_btn8:SimpleButton;
      
      public var chunJie_btn:SimpleButton;
      
      public var dengji_btn:SimpleButton;
      
      public var duWu_btn:SimpleButton;
      
      public var file_mc:MovieClip;
      
      public var gameBox_btn:SimpleButton;
      
      public var goGame_btn:SimpleButton;
      
      public var gogogo_mc:MovieClip;
      
      public var huiTie8_btn:MovieClip;
      
      public var info_mc:MovieClip;
      
      public var info_mc2:MovieClip;
      
      public var jlNum_mc1:MovieClip;
      
      public var jlNum_mc2:MovieClip;
      
      public var jlNum_mc_X1:MovieClip;
      
      public var jlNum_mc_X2:MovieClip;
      
      public var jlNum_txt1:TextField;
      
      public var jlNum_txt2:TextField;
      
      public var libao10_btn:SimpleButton;
      
      public var load_mc:MovieClip;
      
      public var lunTan10_btn:SimpleButton;
      
      public var openBag_mc:MovieClip;
      
      public var qingrenjie_btn:SimpleButton;
      
      public var shanshuo_mc:MovieClip;
      
      public var teMai_btn:SimpleButton;
      
      public var weiBo_btn:SimpleButton;
      
      public var x51:SimpleButton;
      
      public var xiari1:夏日时装计数器;
      
      public var xiari2:夏日时装计数器;
      
      public var yhcs_txt:MovieClip;
      
      public var yinCang_btn:SimpleButton;
      
      public var zb_1:MovieClip;
      
      public var zb_2:MovieClip;
      
      public var zhuanPan_btn:SimpleButton;
      
      public var zhuanZhi_btn:转职任务图标;
      
      public var 画质提示_mc:MovieClip;
      
      public var myXml:XML;
      
      private var saveTime:int = 0;
      
      private var infoArr0:Array;
      
      private var infoArr1:Array;
      
      private var 滚动提示time:int = 135;
      
      public function Play_Interface()
      {
         var fpsX:fpsBox = null;
         this.myXml = new XML();
         this.infoArr0 = ["找“技能导师-奥古斯汀”可以学习各系武器的技能噢","去挑战关卡时，记得检查一下药品是否足够。如果不够，可以找“妮蒂亚”购买。","收集到第一块女神碎片后，村庄的左边会开启通往女神像地图的入口。","修复第一块女神碎片后，会在“技能导师”背后开启通往“神秘之地”的入口","装备属性低的话，可以找“装备商人-道格拉斯”进行装备强化，宝石合成、宝石镶嵌来提升。","2个“强化石”可以合成1个“精良的强化石”，用“精良的强化石”强化装备得到的属性会更高","2个“低级属性石”可以合成一个“中级属性石”，“中级属性石”会有两条属性噢。","“强化石碎片”可以强化史诗级以下的技能石，2个“强化石碎片”可以合成一个1级强化石。","到达25级时，可以找“技能导师”进行转职，转职后可学习4个新的转职技能。","在背包里“鼠标双击”或“拖动”装备可以进行穿戴，右手武器需要拖动武器到右手武器栏才能替换。","2段跳可以在“技能导师”的“通用技能”里习得。","“菜单”栏里可以进行“快捷键设置”“返回城镇”等操作。","如果游戏过程中比较卡，可以通过降低画质提升流畅性。","在仓库中可以进行1P和2P的物品交换。"];
         this.infoArr1 = ["注意观察怪物的发招动作，并采取相应的行动进行躲避，可以让你更轻松的过关","按“上+攻击”和“下+攻击”可以发动各职业的2种特有攻击方式","多使用技能攻击怪物，可以更快更安全的过关","按2次“A(←)”或者“D(→)”可以跑动，跑动中按“攻击”可以进行“跑攻”","注入“技能石”后，打怪时能量条会开始蓄气，满了之后按“N”可以发动怪物技能，2P按“PageDown”发动"];
         super();
         this.load_mc.visible = false;
         SaveLoadMC = new 游戏初始化();
         addChild(SaveLoadMC);
         SaveLoadMC.visible = false;
         interfaceX = this;
         if(Main.tiaoShiYN)
         {
            fpsX = new fpsBox();
            fpsX.x = 0;
            fpsX.y = 0;
            addChild(fpsX);
         }
         this.info_mc.mouseChildren = this.info_mc.mouseEnabled = false;
         this.初始化技能图标();
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this._Move_mc2.CW_mc.addEventListener(MouseEvent.MOUSE_DOWN,this.cw_Open);
         this._Move_mc2.Xbag_mc.addEventListener(MouseEvent.CLICK,this.bag_OPEN);
         this._Move_mc2.Xfile_mc.addEventListener(MouseEvent.CLICK,this.File_OPEN);
         this._Move_mc2.Xshop_mc.addEventListener(MouseEvent.CLICK,this.shop_OPEN);
         this.file_mc.addEventListener(MouseEvent.MOUSE_MOVE,this.画质提示开启);
         this.file_mc.addEventListener(MouseEvent.MOUSE_OUT,this.画质提示关闭);
         this.file_mc.A.addEventListener(MouseEvent.CLICK,this.画质);
         this.file_mc.C.addEventListener(MouseEvent.CLICK,this.画质);
         this.file_mc.A.gotoAndStop(1);
         this.file_mc.C.gotoAndStop(2);
         this.file_mc.addEventListener(MouseEvent.ROLL_OUT,this.系统菜单关闭);
         this.file_mc.visible = false;
         this.file_mc.gameStop_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.游戏暂停);
         this._Move_mc.renWuXX_mc.addEventListener(MouseEvent.MOUSE_DOWN,this.任务面板);
         this.zb_1.addEventListener(MouseEvent.MOUSE_OVER,this.showOpen);
         this.zb_1.addEventListener(MouseEvent.MOUSE_OUT,this.showClose);
         this.zb_2.addEventListener(MouseEvent.MOUSE_OVER,this.showOpen2);
         this.zb_2.addEventListener(MouseEvent.MOUSE_OUT,this.showClose);
         this._Move_mc.card_mc.addEventListener(MouseEvent.MOUSE_DOWN,this.卡片面板);
         this._Move_mc.xingLing_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.xingLing_Open);
         this._Move_mc.vip_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.vip_Open);
         this._Move_mc.QianDao_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.QianDaoFun);
         this._Move_mc.jh_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.jh_Open);
         this._Move_mc.paiHang_Btn.addEventListener(MouseEvent.MOUSE_DOWN,this.PaiHang_Open);
         this._Move_mc.linghun_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.linghun_Open);
         this._Move_mc.gongHui_btn.addEventListener(MouseEvent.MOUSE_DOWN,GongHui_Open);
         this._Move_mc.yueka_btn.addEventListener(MouseEvent.MOUSE_DOWN,yueKaOpen);
         this._Move_mc.baoZhu_btn.addEventListener(MouseEvent.MOUSE_DOWN,baoZhuOpen);
         this._Move_mc.moShen_btn.addEventListener(MouseEvent.MOUSE_DOWN,mosenOpen);
         this._Move_mc.jihua_mc.mouseEnabled = false;
         this._Move_mc.jihua_mc.mouseChildren = false;
         TiShiShow(false);
         this.HuaZhiInit();
         this._Move_mc.addEventListener(MouseEvent.MOUSE_MOVE,this.XOpen);
         this._Move_mc.addEventListener(MouseEvent.MOUSE_OUT,this.XClose);
         this._Move_mc2.addEventListener(MouseEvent.MOUSE_MOVE,this.XOpen2);
         this._Move_mc2.addEventListener(MouseEvent.MOUSE_OUT,this.XClose2);
         this.zhuanZhi_btn.addEventListener(MouseEvent.CLICK,this.转职面板);
         this.Play1_mc["yuhuo1"].addEventListener(MouseEvent.MOUSE_OVER,this.tipOpen);
         this.Play1_mc["yuhuo1"].addEventListener(MouseEvent.MOUSE_OUT,this.tipClose);
         this.Play2_mc["yuhuo2"].addEventListener(MouseEvent.MOUSE_OVER,this.tipOpen);
         this.Play2_mc["yuhuo2"].addEventListener(MouseEvent.MOUSE_OUT,this.tipClose);
         this.yinCang_btn.addEventListener(MouseEvent.CLICK,this.YingCang1P2P);
         this._Move_mc.tuijian_btn.addEventListener(MouseEvent.CLICK,this.TuiJianShop);
         this._Move_mc.caiYao_btn.addEventListener(MouseEvent.CLICK,this.caiYao_Fun);
         this.jlNum_mc1.addEventListener(MouseEvent.MOUSE_MOVE,this.jlNum_mcShow1);
         this.jlNum_mc2.addEventListener(MouseEvent.MOUSE_MOVE,this.jlNum_mcShow2);
         this.jlNum_mc1.addEventListener(MouseEvent.MOUSE_OUT,this.jlNum_mc_out1);
         this.jlNum_mc2.addEventListener(MouseEvent.MOUSE_OUT,this.jlNum_mc_out2);
         this.libao10_btn.addEventListener(MouseEvent.CLICK,this.libao10);
         this.lunTan10_btn.addEventListener(MouseEvent.CLICK,this.lunTan10);
         this.huiTie8_btn.addEventListener(MouseEvent.CLICK,this.huiTie8);
         this.qingrenjie_btn.addEventListener(MouseEvent.CLICK,this.qingrenjie);
         if(!Main.P1P2)
         {
            this.goGame_btn.x = 836;
            this.goGame_btn.y = 470;
            this.lunTan10_btn.x = 750;
            this.lunTan10_btn.y = 470;
         }
         this.goGame_btn.addEventListener(MouseEvent.MOUSE_DOWN,this.goGameXX);
      }
      
      private static function HuoDong51(e:*) : *
      {
         FiveOne_Interface.Open();
      }
      
      private static function ChunJie_Open(e:*) : *
      {
         ChunJiePanel.open();
      }
      
      private static function chongZhi_OpenXX(e:*) : *
      {
         ChongZhi_Interface.Open();
      }
      
      private static function chongZhi_Open1(e:*) : *
      {
         ChongZhi_Interface2.Open(1);
      }
      
      private static function chongZhi_Open2(e:*) : *
      {
         ChongZhi_Interface2.Open(2);
      }
      
      private static function chongZhi_Open3(e:*) : *
      {
         ChongZhi_Interface2.Open(3);
      }
      
      private static function chongZhi_Open4(e:*) : *
      {
         ChongZhi_Interface2.Open(4);
      }
      
      private static function chongZhi_Open5(e:*) : *
      {
         ChongZhi_Interface2.Open(5);
      }
      
      private static function chongZhi_Open6(e:*) : *
      {
         ChongZhi_Interface2.Open(6);
      }
      
      private static function chongZhi_Open7(e:*) : *
      {
         ChongZhi_Interface2.Open(7);
      }
      
      private static function chongZhi_Open8(e:*) : *
      {
         ChongZhi_Interface2.Open(8);
      }
      
      private static function onCongZhiXXXX(e:*) : *
      {
         ChongZhi_Interface4.Open();
      }
      
      private static function teMai_Open(e:*) : *
      {
         TeMaiPanel.open();
      }
      
      private static function GongHui_Open(e:*) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            GongHui_Interface.Open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"只能在主城中开启");
         }
      }
      
      private static function Nian_Open(e:*) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            NewYear_Interface.Open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"只能在主城中开启");
         }
      }
      
      private static function dengji_Open(e:*) : *
      {
         JiangLiPanel.open();
      }
      
      public static function yueKaOpen(e:*) : *
      {
         YueKa_Interface.Open();
      }
      
      public static function baoZhuOpen(e:*) : *
      {
         Panel_youling.Open();
      }
      
      public static function mosenOpen(e:*) : *
      {
         Mosen_Interface.Open();
      }
      
      public static function WeiBoFaFang() : *
      {
         interfaceX.ShowWeiBoFaFang();
      }
      
      public static function LunTan10_Show() : *
      {
         var i:int = 0;
         TiaoShi.txtShow("奖励" + Main.HuiTie8Num);
         if(Main.HuiTie8Num > 0)
         {
            for(i = int(Main.HuiTie8Num); i > 0; i--)
            {
               if(Main.HuiTie8Arr[i] == 0)
               {
                  interfaceX.huiTie8_btn.gotoAndStop(i);
                  interfaceX.huiTie8_btn.visible = true;
                  LunTan10Num_now = i;
                  return;
               }
            }
         }
         interfaceX.huiTie8_btn.visible = false;
      }
      
      private static function HuoDong10_Fun(e:*) : *
      {
         if(Main.serverTime.getValue() < 20131219)
         {
            NewMC.Open("文字提示",Main._stage,470,400,30,0,true,2,"活动还未开始, 开放时间:12月19日~12月31日");
            return;
         }
         FanPaiPanel.open();
      }
      
      private static function zhuanPan_Fun(e:*) : *
      {
         ZhuanPan.Open();
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var nowdate:Date = null;
         var hours:Number = NaN;
         var minute:Number = NaN;
         var num:int = 0;
         var nowTime:int = 0;
         if(!interfaceX)
         {
            interfaceX = new Play_Interface();
         }
         interfaceX.初始化技能图标();
         Main._this.addChild(interfaceX);
         interfaceX.xiari1.gotoAndStop(1);
         interfaceX.xiari2.gotoAndStop(1);
         if(Boolean(Main.player1.getEquipSlot().getEquipFromSlot(7)) && Main.player1.getEquipSlot().getEquipFromSlot(7).getFrame() == 447)
         {
            interfaceX.xiari1.visible = true;
            if(Main.player_1.jianCDnum < 11)
            {
               interfaceX.xiari1.gotoAndStop(Main.player_1.jianCDnum);
            }
            else
            {
               interfaceX.xiari1.gotoAndPlay(Main.player_1.jianCDnum);
            }
         }
         else
         {
            interfaceX.xiari1.visible = false;
         }
         if(Main.P1P2 && Main.player2.getEquipSlot().getEquipFromSlot(7) && Main.player2.getEquipSlot().getEquipFromSlot(7).getFrame() == 447)
         {
            interfaceX.xiari2.visible = true;
            if(Main.player_2.jianCDnum < 11)
            {
               interfaceX.xiari2.gotoAndStop(Main.player_2.jianCDnum);
            }
            else
            {
               interfaceX.xiari2.gotoAndPlay(Main.player_2.jianCDnum);
            }
         }
         else
         {
            interfaceX.xiari2.visible = false;
         }
         interfaceX.zhuanZhi_Fun();
         interfaceX.buCang_btn.visible = false;
         interfaceX.shanshuo_mc.mouseChildren = interfaceX.shanshuo_mc.mouseEnabled = interfaceX.shanshuo_mc.visible = false;
         interfaceX.libao10_btn.visible = false;
         interfaceX.dengji_btn.visible = false;
         interfaceX.huiTie8_btn.visible = false;
         interfaceX.shanshuo_mc.visible = false;
         interfaceX.lunTan10_btn.visible = false;
         interfaceX.dengji_btn.addEventListener(MouseEvent.MOUSE_DOWN,dengji_Open);
         interfaceX.ChongZhiFuLi_btn.visible = false;
         interfaceX.chongZhi_X1.visible = false;
         interfaceX.chongZhi_X2.visible = false;
         interfaceX.chongZhi_X3.visible = false;
         interfaceX.chongZhi_btn1.visible = false;
         interfaceX.chongZhi_btn2.visible = false;
         interfaceX.chongZhi_btn3.visible = false;
         interfaceX.chongZhi_btn4.visible = false;
         interfaceX.chongZhi_btn5.visible = false;
         interfaceX.chongZhi_btn6.visible = false;
         interfaceX.chongZhi_btn7.visible = false;
         interfaceX.chongZhi_btn8.visible = false;
         interfaceX.zhuanPan_btn.visible = false;
         interfaceX.teMai_btn.visible = false;
         interfaceX.Nian_btn.visible = false;
         interfaceX.qingrenjie_btn.visible = false;
         if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() != 1)
         {
            interfaceX.libao10_btn.visible = true;
            nowdate = new Date();
            hours = Number(nowdate.getHours());
            minute = Number(nowdate.getMinutes());
            num = hours * 60 + minute;
            nowTime = int(Main.serverTime.getValue());
            if(!buChangHeCeng2)
            {
               interfaceX.buCang_btn.visible = true;
               interfaceX.buCang_btn.addEventListener(MouseEvent.MOUSE_DOWN,buCang_Open);
            }
            interfaceX.xiari1.visible = false;
            interfaceX.xiari2.visible = false;
            interfaceX.x51.visible = false;
            interfaceX.duWu_btn.visible = true;
            interfaceX.gameBox_btn.visible = true;
            interfaceX.teMai_btn.visible = false;
            interfaceX.lunTan10_btn.visible = true;
            interfaceX.chunJie_btn.visible = false;
            btnArr = [];
            btnNum = 0;
            if(Main.serverTime.getValue() <= FiveOne_Interface.overTime)
            {
               interfaceX.x51.visible = true;
               interfaceX.x51.addEventListener(MouseEvent.MOUSE_DOWN,HuoDong51);
               BtnXY(interfaceX.x51);
            }
            if(Main.serverTime.getValue() <= ChunJiePanel.overTime)
            {
               interfaceX.chunJie_btn.visible = true;
               interfaceX.chunJie_btn.addEventListener(MouseEvent.MOUSE_DOWN,ChunJie_Open);
               BtnXY(interfaceX.chunJie_btn);
            }
            if(Main.serverTime.getValue() <= NewYear_Interface.overTime)
            {
               interfaceX.Nian_btn.visible = true;
               interfaceX.Nian_btn.addEventListener(MouseEvent.MOUSE_DOWN,Nian_Open);
               BtnXY(interfaceX.Nian_btn);
            }
            if(Main.serverTime.getValue() <= Panel_XianHua.overTime)
            {
               interfaceX.qingrenjie_btn.visible = true;
               BtnXY(interfaceX.qingrenjie_btn);
            }
            if(Main.serverTime.getValue() <= ZhuanPan.overTime)
            {
               interfaceX.zhuanPan_btn.visible = true;
               interfaceX.zhuanPan_btn.addEventListener(MouseEvent.MOUSE_DOWN,zhuanPan_Fun);
               BtnXY(interfaceX.zhuanPan_btn);
            }
            if(Main.serverTime.getValue() <= TeMaiPanel.overTime)
            {
               interfaceX.teMai_btn.visible = true;
               interfaceX.teMai_btn.addEventListener(MouseEvent.MOUSE_DOWN,teMai_Open);
               BtnXY(interfaceX.teMai_btn);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface4.overTime)
            {
               interfaceX.ChongZhiFuLi_btn.visible = true;
               interfaceX.ChongZhiFuLi_btn.addEventListener(MouseEvent.MOUSE_DOWN,onCongZhiXXXX);
               BtnXY(interfaceX.ChongZhiFuLi_btn);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface.overTime)
            {
               if(ChongZhi_Interface.type == 1)
               {
                  interfaceX.chongZhi_X1.visible = true;
                  interfaceX.chongZhi_X1.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_OpenXX);
                  BtnXY(interfaceX.chongZhi_X1);
               }
               else if(ChongZhi_Interface.type == 2)
               {
                  interfaceX.chongZhi_X2.visible = true;
                  interfaceX.chongZhi_X2.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_OpenXX);
                  BtnXY(interfaceX.chongZhi_X2);
               }
               else if(ChongZhi_Interface.type == 3)
               {
                  interfaceX.chongZhi_X3.visible = true;
                  interfaceX.chongZhi_X3.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_OpenXX);
                  BtnXY(interfaceX.chongZhi_X3);
               }
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time1)
            {
               interfaceX.chongZhi_btn1.visible = true;
               interfaceX.chongZhi_btn1.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open1);
               BtnXY(interfaceX.chongZhi_btn1);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time2)
            {
               interfaceX.chongZhi_btn2.visible = true;
               interfaceX.chongZhi_btn2.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open2);
               BtnXY(interfaceX.chongZhi_btn2);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time3)
            {
               interfaceX.chongZhi_btn3.visible = true;
               interfaceX.chongZhi_btn3.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open3);
               BtnXY(interfaceX.chongZhi_btn3);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time4)
            {
               interfaceX.chongZhi_btn4.visible = true;
               interfaceX.chongZhi_btn4.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open4);
               BtnXY(interfaceX.chongZhi_btn4);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time5)
            {
               interfaceX.chongZhi_btn5.visible = true;
               interfaceX.chongZhi_btn5.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open5);
               BtnXY(interfaceX.chongZhi_btn5);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time6)
            {
               interfaceX.chongZhi_btn6.visible = true;
               interfaceX.chongZhi_btn6.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open6);
               BtnXY(interfaceX.chongZhi_btn6);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time7)
            {
               interfaceX.chongZhi_btn7.visible = true;
               interfaceX.chongZhi_btn7.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open7);
               BtnXY(interfaceX.chongZhi_btn7);
            }
            if(Main.serverTime.getValue() <= ChongZhi_Interface2.time8)
            {
               interfaceX.chongZhi_btn8.visible = true;
               interfaceX.chongZhi_btn8.addEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open8);
               BtnXY(interfaceX.chongZhi_btn8);
            }
            if(JiangLiPanel.LQtimes.getValue() != 14)
            {
               interfaceX.dengji_btn.visible = true;
               BtnXY(interfaceX.dengji_btn);
            }
            btnNum = 0;
         }
         else
         {
            interfaceX.x51.visible = false;
            interfaceX.duWu_btn.visible = false;
            interfaceX.gameBox_btn.visible = false;
            interfaceX.buCang_btn.visible = false;
            interfaceX.Nian_btn.visible = false;
            interfaceX.teMai_btn.visible = false;
            interfaceX.zhuanPan_btn.visible = false;
            interfaceX.chunJie_btn.visible = false;
            interfaceX.chongZhi_X1.visible = false;
            interfaceX.chongZhi_X2.visible = false;
            interfaceX.chongZhi_X3.visible = false;
            interfaceX.chongZhi_btn1.visible = false;
            interfaceX.chongZhi_btn2.visible = false;
            interfaceX.chongZhi_btn3.visible = false;
            interfaceX.chongZhi_btn4.visible = false;
            interfaceX.chongZhi_btn5.visible = false;
            interfaceX.chongZhi_btn6.visible = false;
            interfaceX.chongZhi_btn7.visible = false;
            interfaceX.chongZhi_btn8.visible = false;
            interfaceX.ChongZhiFuLi_btn.visible = false;
            interfaceX.x51.removeEventListener(MouseEvent.MOUSE_DOWN,HuoDong51);
            interfaceX.ChongZhiFuLi_btn.removeEventListener(MouseEvent.MOUSE_DOWN,onCongZhiXXXX);
            interfaceX.teMai_btn.removeEventListener(MouseEvent.MOUSE_DOWN,teMai_Open);
            interfaceX.chongZhi_X1.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_OpenXX);
            interfaceX.chongZhi_X2.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_OpenXX);
            interfaceX.chongZhi_X3.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_OpenXX);
            interfaceX.chongZhi_btn1.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open1);
            interfaceX.chongZhi_btn2.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open2);
            interfaceX.chongZhi_btn3.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open3);
            interfaceX.chongZhi_btn4.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open4);
            interfaceX.chongZhi_btn5.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open5);
            interfaceX.chongZhi_btn6.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open6);
            interfaceX.chongZhi_btn7.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open7);
            interfaceX.chongZhi_btn8.removeEventListener(MouseEvent.MOUSE_DOWN,chongZhi_Open8);
            interfaceX.HuoDong10_btn.removeEventListener(MouseEvent.MOUSE_DOWN,HuoDong10_Fun);
            interfaceX.Nian_btn.removeEventListener(MouseEvent.MOUSE_DOWN,Nian_Open);
            interfaceX.chunJie_btn.removeEventListener(MouseEvent.MOUSE_DOWN,ChunJie_Open);
            interfaceX.zhuanPan_btn.removeEventListener(MouseEvent.MOUSE_DOWN,zhuanPan_Fun);
         }
         interfaceX.black_guoDu_mc.gotoAndPlay(14);
         interfaceX.x = xx;
         interfaceX.y = yy;
         interfaceX.visible = true;
         BossLifeYN = false;
         ShowXX();
         ShowXX2();
         if(cz_Open1 && NewLoad.chongZhiData && Boolean(NewLoad.chongZhiData.hasClass("mc1")))
         {
            cz_Open1 = false;
         }
      }
      
      public static function BtnXY(btnX:SimpleButton) : *
      {
         var i:* = undefined;
         var btnXX:SimpleButton = null;
         if(btnX)
         {
            btnArr.push(btnX);
            ++btnNum;
         }
         var sNum:Number = 470 - 50 - (btnNum - 1) * 50;
         for(i in btnArr)
         {
            btnXX = btnArr[i];
            btnXX.x = sNum + i * 100;
            btnXX.y = 80;
         }
      }
      
      private static function buCang_Open(e:*) : *
      {
         if(Main.player1.getBag().backOtherBagNum() > 0)
         {
            buChangHeCeng2 = true;
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63377));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点赞礼包领取成功!");
            interfaceX.buCang_btn.visible = false;
            interfaceX.buCang_btn.x = interfaceX.buCang_btn.y = -5000;
            Main.Save();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包其他栏空间不足");
         }
      }
      
      public static function getBuChang() : *
      {
         if(SixOneOK)
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63342));
            SixOneOK = false;
            SaveLoadMC.visible = false;
            Main.Save();
         }
      }
      
      private static function LongZou(e:*) : *
      {
         ChongZhi_Interface3.Open();
      }
      
      public static function Close() : *
      {
         if(!interfaceX)
         {
            interfaceX = new Play_Interface();
         }
         xx = 5000;
         interfaceX.y = 5000;
         interfaceX.visible = false;
         BossLifeYN = false;
      }
      
      private static function ShowXX() : *
      {
         interfaceX._Move_mc.x = 0;
         showTimeNum = 60;
      }
      
      private static function ShowXX2() : *
      {
         interfaceX._Move_mc2.y = 503;
         showTimeNum2 = 60;
      }
      
      public static function WhoIsBoss(e:Enemy) : *
      {
         bossIS = e;
      }
      
      public static function jihuaTiShi(yn:Boolean = true) : *
      {
         if(!interfaceX)
         {
            return;
         }
         if(yn)
         {
            interfaceX._Move_mc.jihua_mc.visible = true;
         }
         else
         {
            interfaceX._Move_mc.jihua_mc.visible = false;
         }
      }
      
      public static function TiShiShow(yn:Boolean = true, id:uint = 0) : *
      {
         var classRef:Class = null;
         var xx:MovieClip = null;
         if(!interfaceX)
         {
            return;
         }
         if(yn)
         {
            interfaceX._Move_mc.RenWuTiShi_mc.visible = true;
            TaskPanel.TiShi_wanCeng();
         }
         else
         {
            interfaceX._Move_mc.RenWuTiShi_mc.visible = false;
            if(id == 110097)
            {
               TiaoShi.txtShow("过场.............");
               classRef = SelMap.loadData.getClass("src.tool.XX_MC") as Class;
               xx = new classRef();
               Main._stage.addChild(xx);
            }
         }
      }
      
      private function goGameXX(e:*) : *
      {
         if(Main.gameNum2.getValue() == 4)
         {
            SelMap.Open(0,0,3,3);
         }
         else
         {
            SelMap.Open();
         }
      }
      
      private function DuWu(e:*) : *
      {
         DuWu_Interface.Open();
      }
      
      private function gameBox(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://news.4399.com/gonglue/ysdxy/gl/471540.html");
         navigateToURL(request,"_blank");
      }
      
      private function libao10(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/jifen/yx-ysdxy");
         navigateToURL(request,"_blank");
      }
      
      private function lunTan10(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums-mtag-tagid-81127.html");
         navigateToURL(request,"_blank");
      }
      
      private function jlNum_mcShow1(e:*) : *
      {
         if(Boolean(Main.player_1.playerJL) && Main.player1.playerJL_Data.getSKILL3() > 0)
         {
            this.info_mc2._txt.text = SkillFactory.getSkillById(Main.player1.playerJL_Data.getSKILL3()).getIntroduction();
            this.info_mc2.visible = true;
            this.info_mc2.x = mouseX + 5;
            this.info_mc2.y = mouseY + 5;
         }
         else
         {
            this.info_mc2._txt.text = "召唤精灵无主动圣物或未召唤精灵";
            this.info_mc2.visible = true;
            this.info_mc2.x = mouseX + 5;
            this.info_mc2.y = mouseY + 5;
         }
      }
      
      private function jlNum_mcShow2(e:*) : *
      {
         if(Boolean(Main.player_2.playerJL) && Main.player2.playerJL_Data.getSKILL3() > 0)
         {
            this.info_mc2._txt.text = SkillFactory.getSkillById(Main.player2.playerJL_Data.getSKILL3()).getIntroduction();
            this.info_mc2.visible = true;
            this.info_mc2.x = mouseX + 5;
            this.info_mc2.y = mouseY + 5;
         }
         else
         {
            this.info_mc2._txt.text = "召唤精灵无主动圣物或未召唤精灵";
            this.info_mc2.visible = true;
            this.info_mc2.x = mouseX + 5;
            this.info_mc2.y = mouseY + 5;
         }
      }
      
      private function jlNum_mc_out1(e:*) : *
      {
         this.info_mc2.visible = false;
      }
      
      private function jlNum_mc_out2(e:*) : *
      {
         this.info_mc2.visible = false;
      }
      
      private function reXue1(e:*) : *
      {
      }
      
      private function reXue2(e:*) : *
      {
      }
      
      private function PaiHang_Open(e:*) : *
      {
         TiaoZhanPaiHang_Interface.Open();
      }
      
      private function linghun_Open(e:*) : *
      {
         if(Main.LuoPanArr[0] == 1 && Main.LuoPanArr[1] == 1 && Main.LuoPanArr[2] == 1 && Main.LuoPanArr[3] == 1)
         {
            LingHunShi_Interface.Open();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"开启时空之门后才可使用");
         }
      }
      
      private function ShowWeiBoFaFang() : *
      {
         var property:XML = null;
         var name:String = null;
         var num:uint = 0;
         var str1:String = null;
         var str2:String = null;
         if(TeShuHuoDong.TeShuHuoDongArr[19])
         {
            this.weiBo_btn.x = this.weiBo_btn.y = -5000;
         }
         else
         {
            this.myXml = XMLAsset.createXML(Play_Interface.data);
            for each(property in this.myXml.奖励发放)
            {
               name = String(property.用户账号);
               num = uint(property.存档序号);
               str1 = Main.logName.toLocaleLowerCase();
               str2 = name.toLocaleLowerCase();
               if(str1 == str2 && Main.saveNum + 1 == num)
               {
                  this.weiBo_btn.x = 470;
                  this.weiBo_btn.y = 250;
                  this.weiBo_btn.addEventListener(MouseEvent.CLICK,this.WeiBoFaFang);
               }
            }
         }
      }
      
      private function WeiBoFaFang(e:*) : *
      {
         var xx:int = 60000 + InitData.BuyNum_0.getValue();
         if(TeShuHuoDong.TeShuHuoDongArr[19])
         {
            NewMC.Open("文字提示",this,470,420,30,0,true,2,"已经领取过");
         }
         else if(Main.player1.getBag().backOtherBagNum() < 2)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
         }
         else
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(xx + 3251));
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(xx + 3245));
            ++TeShuHuoDong.TeShuHuoDongArr[19];
            NewMC.Open("文字提示",this,470,420,90,0,true,2,"微博活动奖励领取成功!");
            this.weiBo_btn.x = this.weiBo_btn.y = -5000;
            this.weiBo_btn.removeEventListener(MouseEvent.CLICK,this.WeiBoFaFang);
         }
      }
      
      public function qingrenjie(e:*) : *
      {
         Panel_XianHua.Open();
      }
      
      public function huiTie8(e:*) : *
      {
         var x2:int = 0;
         var x3:int = 0;
         var x7:int = 0;
         if(LunTan10Num_now == 1)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'击杀点礼包\' 成功");
               Main.HuiTie8Arr[1] = 1;
            }
         }
         else if(LunTan10Num_now == 2)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               for(x2 = 0; x2 < 5; x2++)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'封印的星灵财宝\' 成功");
               Main.HuiTie8Arr[2] = 1;
            }
         }
         else if(LunTan10Num_now == 3)
         {
            if(Main.player1.getBag().backOtherBagNum() < 5)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               for(x3 = 0; x3 < 5; x3++)
               {
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'复活药\' 成功");
               Main.HuiTie8Arr[3] = 1;
            }
         }
         else if(LunTan10Num_now == 4)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'星灵礼包\' 成功");
               Main.HuiTie8Arr[4] = 1;
            }
         }
         else if(LunTan10Num_now == 5)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               for(x3 = 0; x3 < 3; x3++)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63368));
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'轮回炼狱入场卷\' 成功");
               Main.HuiTie8Arr[5] = 1;
            }
         }
         else if(LunTan10Num_now == 6)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               for(x3 = 0; x3 < 15; x3++)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'洗练券\' 成功");
               Main.HuiTie8Arr[6] = 1;
            }
         }
         else if(LunTan10Num_now == 7)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               for(x7 = 0; x7 < 3; x7++)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
               }
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'卓越4级强化石\' 成功");
               Main.HuiTie8Arr[7] = 1;
            }
         }
         else if(LunTan10Num_now == 8)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
            else
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63331));
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,3,"领取 \'幽冥守卫宠物蛋\' 成功");
               Main.HuiTie8Arr[8] = 1;
            }
         }
         LunTan10_Show();
      }
      
      private function tipOpen(e:*) : *
      {
         this.yhcs_txt.visible = true;
         this.yhcs_txt.x = mouseX + 5;
         this.yhcs_txt.y = mouseY + 5;
      }
      
      private function tipClose(e:*) : *
      {
         this.yhcs_txt.visible = false;
      }
      
      private function QianDaoFun(e:*) : *
      {
         QianDao.Open();
      }
      
      private function xingLing_Open(e:*) : *
      {
         XingLing_Interface.Open();
      }
      
      private function vip_Open(e:*) : *
      {
         Vip_Interface.Open();
      }
      
      private function jh_Open(e:*) : *
      {
         JiHua_Interface.Open();
      }
      
      private function zbP1P2() : *
      {
         this.zb_1.visible = false;
         this.zb_2.visible = false;
         if(Main.Map0_YN2)
         {
            if(Main.player_1.visible)
            {
               this.zb_1.visible = true;
            }
            if(Boolean(Main.P1P2) && Boolean(Main.player_2.visible))
            {
               this.zb_2.visible = true;
            }
            if(Main.water.getValue() != 1)
            {
               this.zb_1.gotoAndStop(1);
               this.zb_2.gotoAndStop(1);
            }
            else
            {
               this.zb_1.gotoAndStop(2);
               this.zb_2.gotoAndStop(2);
            }
         }
      }
      
      private function showOpen(e:*) : *
      {
         var i:int = 0;
         var suitTool:SuitToolShow = null;
         var itemsTooltip:ItemsTooltip = null;
         var num:int = 0;
         var j:int = 0;
         var suitID:Number = 0;
         var temp:int = 0;
         allTooltip = new MovieClip();
         addChild(allTooltip);
         allTooltip.x = mouseX;
         allTooltip.y = mouseY;
         if(Main.water.getValue() != 1)
         {
            for(i = 0; i < 4; i++)
            {
               suitTool = new SuitToolShow();
               itemsTooltip = new ItemsTooltip();
               num = i;
               if(num >= 2)
               {
                  num++;
               }
               if(Main.player1.getEquipSlot().getEquipFromSlot(num) != null)
               {
                  suitID += Main.player1.getEquipSlot().getEquipFromSlot(num).getSuitId();
                  itemsTooltip.equipTooltipSP(Main.player1.getEquipSlot().getEquipFromSlot(num),1);
                  allTooltip.addChild(itemsTooltip);
                  itemsTooltip.x = temp * 188;
                  temp++;
               }
               if(temp == 4 && suitID > 0)
               {
                  if(suitID / 4 == Main.player1.getEquipSlot().getEquipFromSlot(num).getSuitId())
                  {
                     suitTool.suitTooltipShow(Main.player1.getEquipSlot().getEquipFromSlot(num).getSuitId());
                     allTooltip.addChild(suitTool);
                     suitTool.x = temp * 188;
                  }
               }
            }
         }
         else
         {
            for(j = 8; j < 12; j++)
            {
               suitTool = new SuitToolShow();
               itemsTooltip = new ItemsTooltip();
               num = j;
               if(num >= 10)
               {
                  num++;
               }
               if(Main.player1.getEquipSlot().getEquipFromSlot(num) != null)
               {
                  suitID += Main.player1.getEquipSlot().getEquipFromSlot(num).getSuitId();
                  itemsTooltip.equipTooltipSP(Main.player1.getEquipSlot().getEquipFromSlot(num),1);
                  allTooltip.addChild(itemsTooltip);
                  itemsTooltip.x = temp * 188;
                  temp++;
               }
               if(temp == 4 && suitID > 0)
               {
                  if(suitID / 4 == Main.player1.getEquipSlot().getEquipFromSlot(num).getSuitId())
                  {
                     suitTool.suitTooltipShow(Main.player1.getEquipSlot().getEquipFromSlot(num).getSuitId());
                     allTooltip.addChild(suitTool);
                     suitTool.x = temp * 188;
                  }
               }
            }
         }
         allTooltip.visible = true;
      }
      
      private function showClose(e:*) : *
      {
         allTooltip.visible = false;
      }
      
      private function showOpen2(e:*) : *
      {
         var i:int = 0;
         var suitTool:SuitToolShow = null;
         var itemsTooltip:ItemsTooltip = null;
         var num:int = 0;
         var j:int = 0;
         var suitID:Number = 0;
         var temp:int = 1;
         allTooltip = new MovieClip();
         addChild(allTooltip);
         allTooltip.x = mouseX;
         allTooltip.y = mouseY;
         if(Main.water.getValue() != 1)
         {
            for(i = 0; i < 4; i++)
            {
               suitTool = new SuitToolShow();
               itemsTooltip = new ItemsTooltip();
               num = i;
               if(num >= 2)
               {
                  num++;
               }
               if(Main.player2.getEquipSlot().getEquipFromSlot(num) != null)
               {
                  suitID += Main.player2.getEquipSlot().getEquipFromSlot(num).getSuitId();
                  itemsTooltip.equipTooltipSP(Main.player2.getEquipSlot().getEquipFromSlot(num),1);
                  allTooltip.addChild(itemsTooltip);
                  itemsTooltip.x -= temp * 188;
                  temp++;
               }
               if(temp == 5 && suitID > 0)
               {
                  if(suitID / 4 == Main.player2.getEquipSlot().getEquipFromSlot(num).getSuitId())
                  {
                     suitTool.suitTooltipShow(Main.player2.getEquipSlot().getEquipFromSlot(num).getSuitId());
                     allTooltip.addChild(suitTool);
                     suitTool.x -= temp * 188;
                  }
               }
            }
         }
         else
         {
            for(j = 8; j < 12; j++)
            {
               suitTool = new SuitToolShow();
               itemsTooltip = new ItemsTooltip();
               num = j;
               if(num >= 10)
               {
                  num++;
               }
               if(Main.player2.getEquipSlot().getEquipFromSlot(num) != null)
               {
                  suitID += Main.player2.getEquipSlot().getEquipFromSlot(num).getSuitId();
                  itemsTooltip.equipTooltipSP(Main.player2.getEquipSlot().getEquipFromSlot(num),1);
                  allTooltip.addChild(itemsTooltip);
                  itemsTooltip.x -= temp * 188;
                  temp++;
               }
               if(temp == 5 && suitID > 0)
               {
                  if(suitID / 4 == Main.player2.getEquipSlot().getEquipFromSlot(num).getSuitId())
                  {
                     suitTool.suitTooltipShow(Main.player2.getEquipSlot().getEquipFromSlot(num).getSuitId());
                     allTooltip.addChild(suitTool);
                     suitTool.x -= temp * 188;
                  }
               }
            }
         }
         allTooltip.visible = true;
      }
      
      public function HuaZhiInit() : *
      {
         this.file_mc.A.gotoAndStop(2);
         this.file_mc.C.gotoAndStop(2);
         if(huaZhi == 1)
         {
            Main._stage.quality = StageQuality.HIGH;
            this.file_mc.A.gotoAndStop(1);
         }
         else if(huaZhi == 2)
         {
            Main._stage.quality = StageQuality.LOW;
            this.file_mc.C.gotoAndStop(1);
         }
      }
      
      private function 卡片面板(e:*) : *
      {
         CardPanel.open();
      }
      
      private function 游戏暂停(e:*) : *
      {
         var g:GameStop = new GameStop();
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         this.Boss血条();
         this.玩家状态();
         this.连击计数();
         this.Cd计数();
         this.怪物技能计数();
         this.滚动提示();
         this.ShopObjTime_Show();
         this.Pk_mc();
         this.BaoZhu_mc();
         this.Go_mc();
         this.zbP1P2();
         this.zhuanZhi_Fun();
         if(showTimeNum > 0 && !Play_Interface.OpenYN && Main.gameNum.getValue() != 0)
         {
            --showTimeNum;
            if(interfaceX._Move_mc.x > -60)
            {
               interfaceX._Move_mc.x -= 2;
            }
         }
         if(showTimeNum2 > 0 && !Play_Interface.OpenYN2 && Main.gameNum.getValue() != 0)
         {
            --showTimeNum2;
            if(interfaceX._Move_mc2.y <= 564)
            {
               interfaceX._Move_mc2.y += 2;
            }
         }
         if(interfaceX._Move_mc.x <= -55)
         {
            interfaceX._Move_mc.vip_btn.visible = interfaceX._Move_mc.QianDao_btn.visible = interfaceX._Move_mc.paiHang_Btn.visible = interfaceX._Move_mc.tuijian_btn.visible = interfaceX._Move_mc.caiYao_btn.visible = interfaceX._Move_mc.linghun_btn.visible = interfaceX._Move_mc.gongHui_btn.visible = interfaceX._Move_mc.jihua_mc.visible = interfaceX._Move_mc.baoZhu_btn.visible = interfaceX._Move_mc.yueka_btn.visible = interfaceX._Move_mc.moShen_btn.visible = false;
         }
         else
         {
            interfaceX._Move_mc.vip_btn.visible = interfaceX._Move_mc.QianDao_btn.visible = interfaceX._Move_mc.paiHang_Btn.visible = interfaceX._Move_mc.tuijian_btn.visible = interfaceX._Move_mc.caiYao_btn.visible = interfaceX._Move_mc.linghun_btn.visible = interfaceX._Move_mc.gongHui_btn.visible = interfaceX._Move_mc.jihua_mc.visible = interfaceX._Move_mc.baoZhu_btn.visible = interfaceX._Move_mc.yueka_btn.visible = interfaceX._Move_mc.moShen_btn.visible = true;
         }
         this.NewBag();
         if(Boolean(Main.P1P2) && Main.gameNum.getValue() == 0)
         {
            this.yinCang_btn.visible = true;
         }
         else
         {
            this.yinCang_btn.visible = false;
         }
         if(Main.gameNum.getValue() == 0)
         {
            this.goGame_btn.visible = true;
         }
         else
         {
            this.goGame_btn.visible = false;
         }
      }
      
      private function YingCang1P2P(e:*) : *
      {
         YinCang.Open();
      }
      
      private function TuiJianShop(e:*) : *
      {
         TuiJianPanel.open();
      }
      
      private function caiYao_Fun(e:*) : *
      {
         CaiYaoPanel.open();
      }
      
      private function QuanSouInfo() : *
      {
         if(Main.newPlay == 3)
         {
            this.openBag_mc.x = 380;
            this.openBag_mc.y = 465;
         }
         else
         {
            this.openBag_mc.x = this.openBag_mc.y = -5000;
         }
      }
      
      private function NewBag() : *
      {
         if(Main.newPlay == 3)
         {
            this.openBag_mc.x = 380;
            this.openBag_mc.y = 465;
         }
         else
         {
            this.openBag_mc.x = this.openBag_mc.y = -5000;
         }
      }
      
      private function XOpen(e:*) : *
      {
         OpenYN = true;
         Play_Interface.ShowXX();
      }
      
      private function XClose(e:*) : *
      {
         OpenYN = false;
      }
      
      private function XOpen2(e:*) : *
      {
         OpenYN2 = true;
         Play_Interface.ShowXX2();
      }
      
      private function XClose2(e:*) : *
      {
         OpenYN2 = false;
      }
      
      private function Go_mc() : *
      {
         if(!Main.world.stopYn && Main.gameNum.getValue() > 50 && Main.gameNum.getValue() < 81)
         {
            this.gogogo_mc.visible = true;
         }
         else
         {
            this.gogogo_mc.visible = false;
         }
      }
      
      private function Pk_mc() : *
      {
         var killNum:int = 0;
         var timeX:int = 0;
         if(PK_UI.PK_ing)
         {
            this._PK_time_mc.visible = true;
            if(uint(PK_UI.Pk_timeNum / 27 % 60) < 10)
            {
               this._PK_time_mc._time_txt.text = uint(PK_UI.Pk_timeNum / 27 / 60) + ":0" + uint(PK_UI.Pk_timeNum / 27 % 60);
            }
            else
            {
               this._PK_time_mc._time_txt.text = uint(PK_UI.Pk_timeNum / 27 / 60) + ":" + uint(PK_UI.Pk_timeNum / 27 % 60);
            }
            killNum = PK_UI.killNum70up.getValue() + PK_UI.killNum70down.getValue();
            this._PK_time_mc._kill_txt.text = killNum;
            if(PK_UI.playerTime == 0)
            {
               this._PK_time_mc.time3_mc.gotoAndStop("x0");
            }
            else
            {
               timeX = (PK_UI.playerTime + 26) / 27;
               this._PK_time_mc.time3_mc.gotoAndStop("x" + timeX);
            }
         }
         else
         {
            this._PK_time_mc.visible = false;
         }
      }
      
      private function BaoZhu_mc() : *
      {
         if(Main.gameNum.getValue() >= 7001 && Main.gameNum.getValue() <= 7003)
         {
            this._PK_time_mc2.visible = true;
            this._PK_time_mc2._time_txt.text = NPC_YouLing.ShowTime();
            this._PK_time_mc2._txt.text = NPC_YouLing.ShowHitEn();
         }
         else
         {
            this._PK_time_mc2.visible = false;
         }
      }
      
      private function Boss血条() : *
      {
         if(Boolean(bossIS) && BossLifeYN)
         {
            this.BossLife_mc.visible = true;
            this.BossLife_mc.touXiang.gotoAndStop("a" + bossIS.id);
            if(bossIS.life.getValue() > 0)
            {
               this.BossLife_mc.life_mc.Start(bossIS);
            }
         }
         else
         {
            this.BossLife_mc.visible = false;
            this.BossLife_mc.life_mc.Stop();
         }
      }
      
      private function 怪物技能计数() : *
      {
         if(Boolean(Main.player1) && Boolean(Main.player_1))
         {
            if(this.Play1_mc.guaiWu.currentFrame != Main.player_1.energySlot.getEnergyPer(Main.player_1))
            {
               this.Play1_mc.guaiWu.gotoAndStop(Main.player_1.energySlot.getEnergyPer(Main.player_1));
            }
         }
         if(Main.P1P2 && Main.player2 && Boolean(Main.player_2))
         {
            if(this.Play2_mc.guaiWu.currentFrame != Main.player_2.energySlot.getEnergyPer(Main.player_2))
            {
               this.Play2_mc.guaiWu.gotoAndStop(Main.player_2.energySlot.getEnergyPer(Main.player_2));
            }
         }
      }
      
      private function 玩家状态() : *
      {
         var hp:int = 0;
         var hpMax:int = 0;
         var mp:int = 0;
         var mpMax:int = 0;
         var exp:int = 0;
         var expMax:int = 0;
         var hpNum:int = 0;
         var mpNum:int = 0;
         var lv:int = 0;
         var expXX:int = 0;
         var headX:int = 0;
         var xx:int = 0;
         var xx2:int = 0;
         var num:int = 0;
         if(Main.player_1)
         {
            hp = Math.round(Main.player_1.hp.getValue());
            hpMax = Math.round(Main.player_1.use_hp_Max.getValue());
            mp = int(Main.player_1.mp.getValue());
            mpMax = int(Main.player_1.use_mp_Max.getValue());
            exp = int(Main.player_1.data.getEXP());
            expMax = int(Main.player_1.nextExp.getValue());
            hpNum = 100 - hp / hpMax * 100;
            mpNum = 100 - mp / mpMax * 100;
            lv = int(Main.player_1.data.getLevel());
            expXX = Main.player_1.data.getEXP() / Main.player_1.nextExp.getValue() * 100;
            headX = int(Main.player_1.headFrame);
            if(mp == 1)
            {
               mp = 0;
            }
            this.Play1_mc.plyerMC.hp_txt.text = hp + "/" + hpMax;
            this.Play1_mc.plyerMC.mp_txt.text = mp + "/" + mpMax;
            this.Play1_mc.plyerMC.hp_mc.load_mc.scaleX = 1 - hpNum / 100;
            this.Play1_mc.plyerMC.mp_mc.load_mc.scaleX = 1 - mpNum / 100;
            this.showLV(lv,1);
            this.Play1_mc.exp_mc.gotoAndStop(expXX);
            this.Play1_mc.plyerMC.head_mc.gotoAndStop(headX);
            if(Main.gameNum.getValue() == 0 && Main.gameNum2.getValue() != 1)
            {
               this.showShanShuo();
            }
            xx = 211;
            xx2 = 29;
            num = 0;
            this.Play1_mc["zhongqiu1"].visible = false;
            this.Play1_mc["jianren1"].visible = false;
            this.Play1_mc["shengming1"].visible = false;
            this.Play1_mc["yuhuo1"].visible = false;
            if(Main.player_1.zhongqiuState > 0)
            {
               this.Play1_mc["zhongqiu1"].visible = true;
               this.Play1_mc["zhongqiu1"].x = xx + xx2 * num;
               num++;
            }
            if(Main.player_1.jianrenState > 0)
            {
               this.Play1_mc["jianren1"].visible = true;
               this.Play1_mc["jianren1"].x = xx + xx2 * num;
               num++;
            }
            if(Main.player_1.shengmingState > 0)
            {
               this.Play1_mc["shengming1"].visible = true;
               this.Play1_mc["shengming1"].x = xx + xx2 * num;
               num++;
            }
            if(Main.player1.buffNine[0] > 0)
            {
               this.Play1_mc["yuhuo1"].visible = true;
               this.showYHCS1();
               this.Play1_mc["yuhuo1"].x = xx + xx2 * num;
               num++;
            }
            this.Play1_mc.plyerMC.visible = true;
            this.Play1_mc.bs_mc.visible = false;
            if(Main.player_1.playerCW2)
            {
               this.Play1_mc.plyerMC.visible = false;
               this.Play1_mc.bs_mc.visible = true;
               this.Play1_mc.bs_mc.jingDu_mc.scaleX = Main.player_1.playerCW2.hp / Main.player_1.playerCW2.hpMax;
            }
         }
         if(Main.P1P2)
         {
            hp = Math.round(Main.player_2.hp.getValue());
            hpMax = Math.round(Main.player_2.use_hp_Max.getValue());
            mp = int(Main.player_2.mp.getValue());
            mpMax = int(Main.player_2.use_mp_Max.getValue());
            exp = int(Main.player_2.data.getEXP());
            expMax = int(Main.player_2.nextExp.getValue());
            hpNum = 100 - hp / hpMax * 100;
            mpNum = 100 - mp / mpMax * 100;
            lv = int(Main.player_2.data.getLevel());
            expXX = Main.player_2.data.getEXP() / Main.player_2.nextExp.getValue() * 100;
            headX = int(Main.player_2.headFrame);
            if(mp == 1)
            {
               mp = 0;
            }
            this.Play2_mc.plyerMC.hp_txt.text = hp + "/" + hpMax;
            this.Play2_mc.plyerMC.mp_txt.text = mp + "/" + mpMax;
            this.Play2_mc.plyerMC.hp_mc.load_mc.scaleX = 1 - hpNum / 100;
            this.Play2_mc.plyerMC.mp_mc.load_mc.scaleX = 1 - mpNum / 100;
            this.showLV(lv,2);
            this.Play2_mc.exp_mc.gotoAndStop(expXX);
            this.Play2_mc.plyerMC.head_mc.gotoAndStop(headX);
            xx = -16;
            xx2 = 29;
            num = 0;
            this.Play2_mc["zhongqiu2"].visible = false;
            this.Play2_mc["shengming2"].visible = false;
            this.Play2_mc["jianren2"].visible = false;
            this.Play2_mc["yuhuo2"].visible = false;
            if(Main.player_2.zhongqiuState > 0)
            {
               this.Play2_mc["zhongqiu2"].visible = true;
               this.Play2_mc["zhongqiu2"].x = xx - xx2 * num;
               num++;
            }
            if(Main.player_2.shengmingState > 0)
            {
               this.Play2_mc["shengming2"].visible = true;
               this.Play2_mc["shengming2"].x = xx - xx2 * num;
               num++;
            }
            if(Main.player_2.jianrenState > 0)
            {
               this.Play2_mc["jianren2"].visible = true;
               this.Play2_mc["jianren2"].x = xx - xx2 * num;
               num++;
            }
            if(Main.player2.buffNine[0] > 0)
            {
               this.Play2_mc["yuhuo2"].visible = true;
               this.showYHCS2();
               this.Play2_mc["yuhuo2"].x = xx - xx2 * num;
               num++;
            }
            this.Play2_mc.plyerMC.visible = true;
            this.Play2_mc.bs_mc.visible = false;
            if(Main.player_2.playerCW2)
            {
               this.Play2_mc.plyerMC.visible = false;
               this.Play2_mc.bs_mc.visible = true;
               this.Play2_mc.bs_mc.jingDu_mc.scaleX = Main.player_2.playerCW2.hp / Main.player_2.playerCW2.hpMax;
            }
         }
         this.Play1_mc.visible = this.Play2_mc.visible = false;
         this.jlNum_mc_X1.visible = this.jlNum_mc_X2.visible = false;
         this.jlNum_mc1.visible = this.jlNum_mc2.visible = false;
         this.jlNum_txt1.visible = this.jlNum_txt2.visible = false;
         if(Boolean(Main.player_1) && Main.player_1.hp.getValue() > 0)
         {
            this.Play1_mc.visible = true;
            this.jlNum_mc_X1.visible = true;
            this.jlNum_mc1.visible = true;
            this.jlNum_txt1.visible = true;
         }
         if(Boolean(Main.player_2) && Main.player_2.hp.getValue() > 0)
         {
            this.Play2_mc.visible = true;
            this.jlNum_mc_X2.visible = true;
            this.jlNum_mc2.visible = true;
            this.jlNum_txt2.visible = true;
         }
      }
      
      private function showLV(level:Number, p1p2:int) : *
      {
         var str:String = null;
         var left:int = 0;
         var right:int = 0;
         if(level < 10)
         {
            this["Play" + p1p2 + "_mc"]["plyerMC"]["level_1_mc"].gotoAndStop(level);
            this["Play" + p1p2 + "_mc"]["plyerMC"]["level_2_mc"].visible = false;
            this["Play" + p1p2 + "_mc"]["bs_mc"]["level_1_mc"].gotoAndStop(level);
            this["Play" + p1p2 + "_mc"]["bs_mc"]["level_2_mc"].visible = false;
         }
         else
         {
            this["Play" + p1p2 + "_mc"]["plyerMC"]["level_2_mc"].visible = true;
            str = level.toString();
            left = int(str.substring(0,1));
            right = int(str.substring(1,2));
            this["Play" + p1p2 + "_mc"]["plyerMC"]["level_1_mc"].gotoAndStop(left);
            this["Play" + p1p2 + "_mc"]["bs_mc"]["level_1_mc"].gotoAndStop(left);
            if(right == 0)
            {
               this["Play" + p1p2 + "_mc"]["plyerMC"]["level_2_mc"].gotoAndStop(10);
               this["Play" + p1p2 + "_mc"]["bs_mc"]["level_2_mc"].gotoAndStop(10);
            }
            else
            {
               this["Play" + p1p2 + "_mc"]["plyerMC"]["level_2_mc"].gotoAndStop(right);
               this["Play" + p1p2 + "_mc"]["bs_mc"]["level_2_mc"].gotoAndStop(right);
            }
         }
      }
      
      private function 连击计数() : *
      {
         if(Boolean(Main.player1) && Boolean(Main.player_1))
         {
            if(templianJiNum[0] != Main.player_1.lianJi.getValue() && Main.player_1.lianJi.getValue() != 0)
            {
               templianJiNum[0] = Main.player_1.lianJi.getValue();
               this.ShowLianJi(1,templianJiNum[0]);
            }
            if(Main.player_1.lianjiBool)
            {
               if(Main.player_1.lianJiTime.getValue() < 87)
               {
                  this["Play1_mc"]["LianJi_mc"].visible = true;
               }
               else
               {
                  this["Play1_mc"]["LianJi_mc"].visible = false;
               }
            }
            else if(Main.player_1.lianJiTime.getValue() < 60)
            {
               this["Play1_mc"]["LianJi_mc"].visible = true;
            }
            else
            {
               this["Play1_mc"]["LianJi_mc"].visible = false;
            }
         }
         if(Main.P1P2 && Main.player2 && Boolean(Main.player_2))
         {
            if(templianJiNum[1] != Main.player_2.lianJi.getValue() && Main.player_2.lianJi.getValue() != 0)
            {
               templianJiNum[1] = Main.player_2.lianJi.getValue();
               this.ShowLianJi(2,templianJiNum[1]);
            }
            if(Main.player_2.lianjiBool)
            {
               if(Main.player_2.lianJiTime.getValue() < 87)
               {
                  this["Play2_mc"]["LianJi_mc"].visible = true;
               }
               else
               {
                  this["Play2_mc"]["LianJi_mc"].visible = false;
               }
            }
            else if(Main.player_2.lianJiTime.getValue() < 60)
            {
               this["Play2_mc"]["LianJi_mc"].visible = true;
            }
            else
            {
               this["Play2_mc"]["LianJi_mc"].visible = false;
            }
         }
      }
      
      private function ShowLianJi(who:int, num:int) : *
      {
         var ii:int = 0;
         this["Play" + who + "_mc"]["LianJi_mc"]["A_1"].visible = this["Play" + who + "_mc"]["LianJi_mc"]["A_2"].visible = this["Play" + who + "_mc"]["LianJi_mc"]["A_3"].visible = this["Play" + who + "_mc"]["LianJi_mc"]["A_4"].visible = false;
         var str:String = String(num);
         var i:int = str.length;
         if(num <= 0)
         {
            return;
         }
         while(i)
         {
            ii = int(str.substr(i - 1,1));
            this["Play" + who + "_mc"]["LianJi_mc"]["A_" + i].visible = true;
            if(ii == 0)
            {
               this["Play" + who + "_mc"]["LianJi_mc"]["A_" + i].gotoAndStop(10);
            }
            else
            {
               this["Play" + who + "_mc"]["LianJi_mc"]["A_" + i].gotoAndStop(ii);
            }
            i--;
         }
      }
      
      public function GetCdStr(num:Number = 1) : String
      {
         num = Number(Main["player" + num].skinArr[Main["player" + num].skinNum]);
         if(num == 0)
         {
            return "a";
         }
         if(num == 1)
         {
            return "b";
         }
         if(num == 2)
         {
            return "c";
         }
         if(num == 3)
         {
            return "k";
         }
      }
      
      private function 初始化技能图标() : *
      {
         var pX:Player = null;
         var s:String = null;
         var tbMC:MovieClip = null;
         var cwid:int = 0;
         var jnNum:int = 0;
         for(var x:* = 1; x <= 2; x++)
         {
            if(Boolean(Main["player" + x]) && Boolean(Main["player_" + x]))
            {
               pX = Main["player_" + x];
               s = this.GetCdStr(x);
               for(i = 8; i < 16; ++i)
               {
                  tbMC = this["Play" + x + "_mc"]["p_" + i];
                  tbMC["p_mc"].gotoAndStop(s + "" + i);
                  if(i >= 8 && i <= 11 && Boolean(pX.playerCW2))
                  {
                     cwid = pX.playerCW2.id;
                     jnNum = int(pX.playerCW2.jnIDArr[i - 8]);
                     if(jnNum >= 1 && jnNum <= 4)
                     {
                        tbMC["p_mc"].gotoAndStop("msk" + cwid + "_" + jnNum);
                     }
                  }
                  if(i > 11)
                  {
                     tbMC["heiAnJN_mc"].visible = false;
                  }
               }
            }
         }
      }
      
      private function Cd计数() : *
      {
         var playerX:Player = null;
         var playerDataX:PlayerData = null;
         var s:String = null;
         var i:int = 0;
         var tbMCXXX:MovieClip = null;
         var str:String = null;
         var num:int = 0;
         var tbMC:MovieClip = null;
         var jnNum:int = 0;
         var numXX:int = 0;
         var xx:Supplies = null;
         var name:String = null;
         var tbMC2:MovieClip = null;
         this.初始化技能图标();
         for(var x:* = 1; x <= 2; x++)
         {
            if(Boolean(Main["player" + x]) && Boolean(Main["player_" + x]))
            {
               playerX = Main["player_" + x];
               playerDataX = Main["player" + x];
               s = this.GetCdStr(x);
               for(i = 8; i < 16; i++)
               {
                  tbMCXXX = this["Play" + x + "_mc"]["p_" + i]["p_mc"];
                  tbMCXXX.visible = true;
                  str = s + String(i);
                  num = playerX.selCD50(str);
                  if(i >= 8 && i <= 11 && Boolean(playerX.playerCW2))
                  {
                     jnNum = int(playerX.playerCW2.jnIDArr[i - 8]);
                     if(jnNum != -1)
                     {
                        num = playerX.playerCW2.getCD(i - 8);
                     }
                     else
                     {
                        num = 1;
                        tbMCXXX.visible = false;
                     }
                  }
                  if(i >= 12 && i <= 15 && Boolean(playerX.playerCW2))
                  {
                     num = 1;
                     tbMCXXX.visible = false;
                  }
                  tbMC = this["Play" + x + "_mc"]["p_" + i];
                  tbMC["X50"].gotoAndStop(num);
                  if(i > 11)
                  {
                     tbMC["heiAnJN_mc"].visible = false;
                  }
                  else
                  {
                     numXX = i - 7;
                     if(playerDataX.skinNum == 1)
                     {
                        numXX += 4;
                     }
                     if(Boolean(playerX.heiAnJiNeng[numXX]) && num < 50)
                     {
                        tbMC.heiAnJN_mc.visible = true;
                     }
                     else
                     {
                        tbMC.heiAnJN_mc.visible = false;
                     }
                  }
               }
               for(i = 0; i < 3; i++)
               {
                  xx = playerDataX.getSuppliesSlot().getFromSuppliesSlot(i);
                  if(xx)
                  {
                     name = xx.getName();
                     num = playerX.sel_objCD50(name);
                     tbMC2 = this["Play" + x + "_mc"]["obj_" + i];
                     tbMC2["X50"].gotoAndStop(num);
                     tbMC2["pic_mc"].gotoAndStop(playerDataX.getSuppliesSlot().getFromSuppliesSlot(i).getFrame());
                     tbMC2["num_txt"].text = playerDataX.getSuppliesSlot().getNumFromSuppliesSlot(playerDataX.getBag(),i);
                  }
               }
            }
         }
      }
      
      private function bag_OPEN(e:*) : *
      {
         ItemsPanel.OpenFrist = true;
         ItemsPanel.open();
      }
      
      private function shop_OPEN(e:*) : *
      {
         Shop4399.Open();
      }
      
      private function 系统菜单关闭(e:* = null) : *
      {
         this.file_mc.visible = false;
      }
      
      private function 画质(e:MouseEvent) : *
      {
         this.file_mc.A.gotoAndStop(2);
         this.file_mc.C.gotoAndStop(2);
         if(e.currentTarget.name == "A")
         {
            Main._stage.quality = StageQuality.HIGH;
            this.file_mc.A.gotoAndStop(1);
            huaZhi = 1;
         }
         else if(e.currentTarget.name == "C")
         {
            Main._stage.quality = StageQuality.LOW;
            this.file_mc.C.gotoAndStop(1);
            huaZhi = 2;
         }
      }
      
      private function 转职面板(e:*) : *
      {
         SetTransferPanel.open();
      }
      
      private function File_OPEN(e:*) : *
      {
         if(this.file_mc.visible == false)
         {
            this.file_mc.visible = true;
            this.file_mc.key_mc.addEventListener(MouseEvent.CLICK,this.键位);
            this.file_mc.save_mc.addEventListener(MouseEvent.CLICK,this.保存);
            this.file_mc.city_mc.addEventListener(MouseEvent.CLICK,this.回城);
         }
         else
         {
            this.file_mc.visible = false;
         }
      }
      
      private function 键位(e:*) : *
      {
         SetKeyPanel.open();
         this.系统菜单关闭();
      }
      
      private function 回城(e:*) : *
      {
         Load.loadGameNum = 1;
         Load.loadGameNum2 = 1;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.Loading();
         Player.一起信春哥();
         this.系统菜单关闭();
         AchData.gkOk();
         TaskData.isOk();
         WinShow.All_0();
         PaiHang_Data.All_0();
      }
      
      private function 保存(e:*) : *
      {
         if(this.saveTime <= 0)
         {
            Main.Save();
            this.saveTime = 800;
            this.系统菜单关闭();
            addEventListener(Event.ENTER_FRAME,this.onTime);
         }
      }
      
      private function onTime(e:*) : *
      {
         if(this.saveTime > 0)
         {
            --this.saveTime;
         }
         else
         {
            removeEventListener(Event.ENTER_FRAME,this.onTime);
         }
         this.file_mc.time_txt.text = int(this.saveTime / 27);
      }
      
      private function 画质提示开启(e:*) : *
      {
         this.画质提示_mc.x = this.画质提示_mc.y = -5000;
      }
      
      private function 画质提示关闭(e:*) : *
      {
         this.画质提示_mc.x = this.画质提示_mc.y = -5000;
      }
      
      private function ShopObjTime_Show() : *
      {
         if(InitData.EXPxTime.getValue() > 0)
         {
            this.EXPxTime_mc.x = this.EXPxTime_mc.y = 0;
            this.EXPxTime_mc.t1_txt.text = this.TimeNum(InitData.EXPxTime.getValue());
         }
         else
         {
            this.EXPxTime_mc.x = this.EXPxTime_mc.y = 5000;
         }
         if(InitData.DOWNxTime.getValue() > 0)
         {
            this.DOWNxTime_mc.x = this.DOWNxTime_mc.y = 0;
            this.DOWNxTime_mc.t1_txt.text = this.TimeNum(InitData.DOWNxTime.getValue());
         }
         else
         {
            this.DOWNxTime_mc.x = this.DOWNxTime_mc.y = 5000;
         }
      }
      
      private function TimeNum(num:int) : String
      {
         var str2:String = null;
         var str3:String = null;
         var t1:int = num / 3600;
         var t2:int = (num - t1 * 3600) / 60;
         var t3:int = num - t1 * 3600 - t2 * 60;
         if(t2 < 10)
         {
            str2 = "0" + t2;
         }
         else
         {
            str2 = t2;
         }
         if(t3 < 10)
         {
            str3 = "0" + t3;
         }
         else
         {
            str3 = t3;
         }
         return t1 + ":" + str2 + ":" + str3;
      }
      
      private function 滚动提示() : *
      {
         var i:int = 0;
         ++this.滚动提示time;
         if(this.滚动提示time % 135 == 0)
         {
            if(Main.gameNum.getValue() == 0)
            {
               this.info_mc.x = this.info_mc.y = 0;
               i = Math.random() * this.infoArr0.length;
               this.info_mc._txt.text = this.infoArr0[i];
            }
            else if(Main.gameNum.getValue() == 1 && Main.gameNum2.getValue() != 5)
            {
               this.info_mc.x = this.info_mc.y = 0;
               i = Math.random() * this.infoArr1.length;
               this.info_mc._txt.text = this.infoArr1[i];
            }
            else
            {
               this.info_mc.x = this.info_mc.y = 5000;
            }
         }
      }
      
      private function 任务面板(e:*) : *
      {
         TaskPanel.open();
      }
      
      private function cw_Open(e:*) : *
      {
         if(Main.player_1 && Main.player_1.playerCW2 || Main.player_2 && Main.player_2.playerCW2)
         {
            return;
         }
         NewPetPanel.open();
      }
      
      public function zhuanZhi_Fun() : *
      {
         this.zhuanZhi_btn.visible = false;
         if(Main.gameNum.getValue() != 0)
         {
            return;
         }
         var yn:Boolean = false;
         var yn1:Boolean = false;
         if(Main.P1P2)
         {
            if(!SetTransferPanel.tbo1() || !SetTransferPanel.tbo2())
            {
               yn1 = true;
            }
         }
         else if(!SetTransferPanel.tbo1())
         {
            yn1 = true;
         }
         if(Main.P1P2)
         {
            if(yn1)
            {
               if(Boolean(Main.player_1) && Boolean(Main.player_2))
               {
                  if(!SetTransferPanel.tbo1() && !SetTransferPanel.tbo2())
                  {
                     if(Main.player1.getLevel() >= 25 || Main.player2.getLevel() >= 25)
                     {
                        yn = true;
                     }
                  }
                  else if(Boolean(SetTransferPanel.tbo1()) && !SetTransferPanel.tbo2())
                  {
                     if(Main.player2.getLevel() >= 25)
                     {
                        yn = true;
                     }
                  }
                  else if(Boolean(SetTransferPanel.tbo2()) && !SetTransferPanel.tbo1())
                  {
                     if(Main.player1.getLevel() >= 25)
                     {
                        yn = true;
                     }
                  }
               }
            }
         }
         else if(yn1)
         {
            if(Main.player_1)
            {
               if(Main.player1.getLevel() >= 25)
               {
                  yn = true;
               }
            }
         }
         if(yn && Main.gameNum.getValue() == 0)
         {
            this.zhuanZhi_btn.visible = true;
         }
      }
      
      public function showYHCS1() : *
      {
         var tTemp:int = 0;
         if(Main.player1.buffNine[0] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(1);
            tTemp = int(Main.player1.buffNine[0]);
            this.yhcs_txt.title_txt.text = "狱焰：一层";
            this.yhcs_txt._txt.text = "经验值获得15%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[1] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(2);
            tTemp = int(Main.player1.buffNine[1]);
            this.yhcs_txt.title_txt.text = "狱焰：二层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[2] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(3);
            tTemp = int(Main.player1.buffNine[2]);
            this.yhcs_txt.title_txt.text = "狱焰：三层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[3] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(4);
            tTemp = int(Main.player1.buffNine[3]);
            this.yhcs_txt.title_txt.text = "狱焰：四层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[4] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(5);
            tTemp = int(Main.player1.buffNine[4]);
            this.yhcs_txt.title_txt.text = "狱焰：五层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%   ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[5] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(6);
            tTemp = int(Main.player1.buffNine[5]);
            this.yhcs_txt.title_txt.text = "狱焰：六层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2   ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[6] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(7);
            tTemp = int(Main.player1.buffNine[6]);
            this.yhcs_txt.title_txt.text = "狱焰：七层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[7] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(8);
            tTemp = int(Main.player1.buffNine[7]);
            this.yhcs_txt.title_txt.text = "狱焰：八层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+20%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[8] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(9);
            tTemp = int(Main.player1.buffNine[8]);
            this.yhcs_txt.title_txt.text = "狱焰：九层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+5%   \n 破魔+5%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player1.buffNine[9] > 0)
         {
            this.Play1_mc["yuhuo1"].gotoAndStop(10);
            tTemp = int(Main.player1.buffNine[9]);
            this.yhcs_txt.title_txt.text = "狱焰：十层";
            this.yhcs_txt._txt.text = "经验值获得30%   \n 防御力+20%   \n 魔法值+20%   \n 生命值+20%   \n 攻击力+20%    \n 移动速度+4     \n 暴击+30%   \n 魔抗+10%   \n 破魔+10% ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
            if(Main.player1.reBorn > 0)
            {
               this.yhcs_txt.time_txt.text += "\n重生冷却" + Math.floor(Main.player1.reBorn / 60) + "分" + Main.player1.reBorn % 60 + "秒";
            }
            this.yhcs_txt.cs_txt.visible = true;
         }
         else
         {
            this.yhcs_txt.cs_txt.visible = false;
         }
      }
      
      public function showShanShuo() : *
      {
         if(JiangLiPanel.LQtimes.getValue() == 0)
         {
            if(Main.player1.getLevel() >= 1)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         if(JiangLiPanel.LQtimes.getValue() == 1)
         {
            if(Main.player1.getLevel() >= 8)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 2)
         {
            if(Main.player1.getLevel() >= 15)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 3)
         {
            if(Main.player1.getLevel() >= 20)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 4)
         {
            if(Main.player1.getLevel() >= 25)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 5)
         {
            if(Main.player1.getLevel() >= 30)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 6)
         {
            if(Main.player1.getLevel() >= 35)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 7)
         {
            if(Main.player1.getLevel() >= 40)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 8)
         {
            if(Main.player1.getLevel() >= 45)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 9)
         {
            if(Main.player1.getLevel() >= 50)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 10)
         {
            if(Main.player1.getLevel() >= 55)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 11)
         {
            if(Main.player1.getLevel() >= 60)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 12)
         {
            if(Main.player1.getLevel() >= 65)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 13)
         {
            if(Main.player1.getLevel() >= 70)
            {
               interfaceX.shanshuo_mc.visible = true;
            }
            else
            {
               interfaceX.shanshuo_mc.visible = false;
            }
         }
         else if(JiangLiPanel.LQtimes.getValue() == 14)
         {
            interfaceX.shanshuo_mc.visible = false;
            interfaceX.dengji_btn.visible = false;
         }
      }
      
      public function showYHCS2() : *
      {
         var tTemp:int = 0;
         if(Main.player2.buffNine[0] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(1);
            tTemp = int(Main.player2.buffNine[0]);
            this.yhcs_txt.title_txt.text = "狱焰：一层";
            this.yhcs_txt._txt.text = "经验值获得15%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[1] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(2);
            tTemp = int(Main.player2.buffNine[1]);
            this.yhcs_txt.title_txt.text = "狱焰：二层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[2] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(3);
            tTemp = int(Main.player2.buffNine[2]);
            this.yhcs_txt.title_txt.text = "狱焰：三层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[3] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(4);
            tTemp = int(Main.player2.buffNine[3]);
            this.yhcs_txt.title_txt.text = "狱焰：四层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[4] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(5);
            tTemp = int(Main.player2.buffNine[4]);
            this.yhcs_txt.title_txt.text = "狱焰：五层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%   ";
            this.yhcs_txt.time_txt.text += "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[5] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(6);
            tTemp = int(Main.player2.buffNine[5]);
            this.yhcs_txt.title_txt.text = "狱焰：六层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2   ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[6] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(7);
            tTemp = int(Main.player2.buffNine[6]);
            this.yhcs_txt.title_txt.text = "狱焰：七层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[7] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(8);
            tTemp = int(Main.player2.buffNine[7]);
            this.yhcs_txt.title_txt.text = "狱焰：八层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+20%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[8] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(9);
            tTemp = int(Main.player2.buffNine[8]);
            this.yhcs_txt.title_txt.text = "狱焰：九层";
            this.yhcs_txt._txt.text = "经验值获得15%   \n 防御力+10%   \n 魔法值+10%   \n 生命值+10%   \n 攻击力+10%    \n 移动速度+2     \n 暴击+20%   \n 魔抗+5%   \n 破魔+5%  ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
         }
         if(Main.player2.buffNine[9] > 0)
         {
            this.Play2_mc["yuhuo2"].gotoAndStop(10);
            tTemp = int(Main.player2.buffNine[9]);
            this.yhcs_txt.title_txt.text = "狱焰：十层";
            this.yhcs_txt._txt.text = "经验值获得30%   \n 防御力+20%   \n 魔法值+20%   \n 生命值+20%   \n 攻击力+20%    \n 移动速度+4     \n 暴击+30%   \n 魔抗+10%   \n 破魔+10% ";
            this.yhcs_txt.time_txt.text = "剩余" + Math.floor(tTemp / 3600) + "小时" + Math.floor(tTemp % 3600 / 60) + "分" + tTemp % 3600 % 60 + "秒";
            if(Main.player2.reBorn > 0)
            {
               this.yhcs_txt.time_txt.text += "\n重生冷却" + Math.floor(Main.player2.reBorn / 60) + "分" + Main.player2.reBorn % 60 + "秒";
            }
            this.yhcs_txt.cs_txt.visible = true;
         }
         else
         {
            this.yhcs_txt.cs_txt.visible = false;
         }
      }
   }
}

