package
{
   import flash.display.MovieClip;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1803")]
   public dynamic class Explain extends MovieClip
   {
      
      public var add1:TextField;
      
      public var add2:TextField;
      
      public var beforeSkillName:TextField;
      
      public var maxLevel:TextField;
      
      public var mpNumber:TextField;
      
      public var nowCd:TextField;
      
      public var nowEx:TextField;
      
      public var nowLevel:TextField;
      
      public var playerLevel:TextField;
      
      public var pointNumber:TextField;
      
      public var skillKey:MovieClip;
      
      public var skillKey1:MovieClip;
      
      public var skillKey2:MovieClip;
      
      public var skillName:TextField;
      
      public var typName:TextField;
      
      public function Explain()
      {
         super();
      }
   }
}

