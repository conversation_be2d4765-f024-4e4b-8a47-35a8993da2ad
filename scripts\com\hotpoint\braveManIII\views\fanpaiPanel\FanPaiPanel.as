package com.hotpoint.braveManIII.views.fanpaiPanel
{
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class FanPaiPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var fpp:FanPaiPanel;
      
      public static var ccc:ClassLoader;
      
      public static var fanPanel:MovieClip;
      
      private static var loadData:ClassLoader;
      
      private static var fanNum:int;
      
      public static var arr:Array = [0,1,2,3,4,5,6,7,8];
      
      public static var times:int = 0;
      
      public static var countRMB:int = 0;
      
      public static var saveArr:Array = [0,0,0,0,0,0,0,0,0];
      
      public static var JFOK:Boolean = false;
      
      public static var saveArr2:Array = [1,0,0,0];
      
      private static var loadName:String = "FanPai_v3.swf";
      
      private static var OpenYN:Boolean = false;
      
      private static var DayChange:Boolean = true;
      
      private static var clickBool:Boolean = true;
      
      private static var timeTemp:int = 0;
      
      public static var jifenTime:int = 0;
      
      public static var start:int = 0;
      
      public static var end:int = 0;
      
      public function FanPaiPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!fanPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("FanpaiShow") as Class;
         fanPanel = new classRef();
         fpp.addChild(fanPanel);
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         fpp = new FanPaiPanel();
         LoadSkin();
         Main._stage.addChild(fpp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         fpp = new FanPaiPanel();
         if(jifenTime <= 0)
         {
         }
         Main._stage.addChild(fpp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(fanPanel)
         {
            Main.stopXX = true;
            fpp.x = 0;
            fpp.y = 0;
            addListenerP1();
            fpp.visible = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(fanPanel)
         {
            fpp.visible = false;
            Main.stopXX = false;
            removeListenerP1();
         }
         else
         {
            InitClose();
         }
      }
      
      public static function closeCP(e:*) : *
      {
         close();
      }
      
      public static function countTimes() : *
      {
         var i:* = undefined;
         times = 0;
         for(i in saveArr)
         {
            if(saveArr[i] >= 1)
            {
               ++times;
            }
         }
      }
      
      public static function initFanPai() : *
      {
         saveArr = [0,0,0,0,0,0,0,0,0];
         saveArr2[1] = saveArr2[2] = saveArr2[3] = 0;
         setRandom();
         for(var i:uint = 0; i < 9; i++)
         {
            fanPanel["fan" + i].gotoAndStop(1);
            fanPanel["fan" + i].visible = true;
            fanPanel["pai" + i].visible = false;
            fanPanel["pai" + i]["duihuan"].visible = true;
            fanPanel["fan" + i]["tt"].visible = true;
         }
      }
      
      public static function showFanPai() : *
      {
         var i:uint = 0;
         countTimes();
         for(i = 0; i < 9; i++)
         {
            fanPanel["fan" + i]["tt"].text = "消耗" + 10 * (times + 1) + "积分翻牌";
         }
         fanPanel["tongji"].text = saveArr2[3];
         fanPanel["jifenRBM"]["dianjuan"].text = Shop4399.moneyAll.getValue();
         fanPanel["txt_jf"]["t1"].text = "在线积分:" + saveArr2[1] + "/300";
         fanPanel["txt_jf"]["t2"].text = "杀怪积分:" + saveArr2[2] + "/500";
         for(i in saveArr)
         {
            if(saveArr[i] == 0)
            {
               fanPanel["fan" + i].gotoAndStop(1);
               fanPanel["fan" + i].visible = true;
            }
            if(saveArr[i] >= 1)
            {
               fanPanel["fan" + i].visible = false;
               fanPanel["pai" + i].visible = true;
               fanPanel["pai" + i]["duihuan"].visible = true;
               if(saveArr[i] == 2)
               {
                  fanPanel["pai" + i]["duihuan"].visible = false;
               }
            }
         }
         fanPanel["pai" + arr[0]].x = 360.3;
         fanPanel["pai" + arr[0]].y = 102;
         fanPanel["pai" + arr[1]].x = 500.85;
         fanPanel["pai" + arr[1]].y = 102;
         fanPanel["pai" + arr[2]].x = 645.25;
         fanPanel["pai" + arr[2]].y = 102;
         fanPanel["pai" + arr[3]].x = 784.75;
         fanPanel["pai" + arr[3]].y = 102;
         fanPanel["pai" + arr[4]].x = 360.3;
         fanPanel["pai" + arr[4]].y = 280;
         fanPanel["pai" + arr[5]].x = 500.85;
         fanPanel["pai" + arr[5]].y = 280;
         fanPanel["pai" + arr[6]].x = 645.25;
         fanPanel["pai" + arr[6]].y = 280;
         fanPanel["pai" + arr[7]].x = 784.75;
         fanPanel["pai" + arr[7]].y = 280;
         fanPanel["pai" + arr[8]].x = 967.25;
         fanPanel["pai" + arr[8]].y = 102;
         fanPanel["fan" + arr[0]].x = 360.3;
         fanPanel["fan" + arr[0]].y = 102;
         fanPanel["fan" + arr[1]].x = 500.85;
         fanPanel["fan" + arr[1]].y = 102;
         fanPanel["fan" + arr[2]].x = 645.25;
         fanPanel["fan" + arr[2]].y = 102;
         fanPanel["fan" + arr[3]].x = 784.75;
         fanPanel["fan" + arr[3]].y = 102;
         fanPanel["fan" + arr[4]].x = 360.3;
         fanPanel["fan" + arr[4]].y = 280;
         fanPanel["fan" + arr[5]].x = 500.85;
         fanPanel["fan" + arr[5]].y = 280;
         fanPanel["fan" + arr[6]].x = 645.25;
         fanPanel["fan" + arr[6]].y = 280;
         fanPanel["fan" + arr[7]].x = 784.75;
         fanPanel["fan" + arr[7]].y = 280;
         fanPanel["fan" + arr[8]].x = 967.25;
         fanPanel["fan" + arr[8]].y = 102;
      }
      
      public static function addListenerP1() : *
      {
         if(saveArr2[0] < Main.serverTime.getValue())
         {
            initFanPai();
            saveArr2[0] = Main.serverTime.getValue();
         }
         showFanPai();
         for(var i:uint = 0; i < 9; i++)
         {
            fanPanel["fan" + i].addEventListener(MouseEvent.CLICK,gofan);
            fanPanel["pai" + i]["duihuan"].addEventListener(MouseEvent.CLICK,duiHuan);
            fanPanel["fan" + i]["tt"].mouseEnabled = false;
            fanPanel["m" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            fanPanel["m" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         }
         fanPanel["txt_jf"].visible = false;
         fanPanel["jfmx"].addEventListener(MouseEvent.MOUSE_OVER,txtOpen);
         fanPanel["jfmx"].addEventListener(MouseEvent.MOUSE_OUT,txtClose);
         fanPanel["close"].addEventListener(MouseEvent.CLICK,closeCP);
         fanPanel["jifenRBM"].visible = false;
         fanPanel["ifRMB"].visible = false;
         fanPanel["ifRMB"]["yes_btn"].addEventListener(MouseEvent.CLICK,buyRMB);
         fanPanel["ifRMB"]["no_btn"].addEventListener(MouseEvent.CLICK,closeIF);
         fanPanel["jifenRBM"]["close"].addEventListener(MouseEvent.CLICK,closeRMB);
         fanPanel["jifenRBM"]["buy_10"].addEventListener(MouseEvent.CLICK,buyRMB10);
         fanPanel["jifenRBM"]["buy_30"].addEventListener(MouseEvent.CLICK,buyRMB30);
         fanPanel["jifenRBM"]["buy_50"].addEventListener(MouseEvent.CLICK,buyRMB50);
         fanPanel["goumai"].addEventListener(MouseEvent.CLICK,openRMB);
      }
      
      public static function txtOpen(e:*) : *
      {
         fanPanel["txt_jf"].visible = true;
      }
      
      public static function txtClose(e:*) : *
      {
         fanPanel["txt_jf"].visible = false;
      }
      
      public static function openRMB(e:*) : *
      {
         fanPanel["jifenRBM"].visible = true;
      }
      
      public static function closeRMB(e:*) : *
      {
         fanPanel["jifenRBM"].visible = false;
      }
      
      public static function closeIF(e:*) : *
      {
         fanPanel["ifRMB"].visible = false;
      }
      
      public static function buyRMB10(e:*) : *
      {
         countRMB = 10;
         fanPanel["ifRMB"].visible = true;
      }
      
      public static function buyRMB30(e:*) : *
      {
         countRMB = 30;
         fanPanel["ifRMB"].visible = true;
      }
      
      public static function buyRMB50(e:*) : *
      {
         countRMB = 50;
         fanPanel["ifRMB"].visible = true;
      }
      
      public static function buyRMB(e:*) : *
      {
         if(countRMB == 10)
         {
            if(Shop4399.moneyAll.getValue() >= 10)
            {
               Api_4399_All.BuyObj(InitData.jifen10.getValue());
               JFOK = true;
               fanPanel["ifRMB"].visible = false;
               fanPanel["jifenRBM"].visible = false;
            }
            else
            {
               fanPanel["ifRMB"].visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
            }
         }
         if(countRMB == 30)
         {
            if(Shop4399.moneyAll.getValue() >= 30)
            {
               Api_4399_All.BuyObj(InitData.jifen30.getValue());
               JFOK = true;
               fanPanel["ifRMB"].visible = false;
               fanPanel["jifenRBM"].visible = false;
            }
            else
            {
               fanPanel["ifRMB"].visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
            }
         }
         if(countRMB == 50)
         {
            if(Shop4399.moneyAll.getValue() >= 50)
            {
               Api_4399_All.BuyObj(InitData.jifen50.getValue());
               JFOK = true;
               fanPanel["ifRMB"].visible = false;
               fanPanel["jifenRBM"].visible = false;
            }
            else
            {
               fanPanel["ifRMB"].visible = false;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
            }
         }
      }
      
      public static function jifenOK() : *
      {
         if(JFOK)
         {
            if(countRMB == 10)
            {
               saveArr2[3] += 50;
            }
            if(countRMB == 30)
            {
               saveArr2[3] += 160;
            }
            if(countRMB == 50)
            {
               saveArr2[3] += 280;
            }
            fanPanel["tongji"].text = saveArr2[3];
            fanPanel["jifenRBM"]["dianjuan"].text = Shop4399.moneyAll.getValue();
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功");
            JFOK = false;
         }
      }
      
      private static function setRandom() : *
      {
         var newArr:Array = [];
         while(arr.length > 0)
         {
            newArr.push(arr.splice(Math.floor(Math.random() * arr.length),1)[0]);
         }
         arr = newArr;
      }
      
      public static function removeListenerP1() : *
      {
      }
      
      public static function gofan(e:MouseEvent) : void
      {
         var fanMC:MovieClip = null;
         if(saveArr2[3] >= 10 * (times + 1))
         {
            if(clickBool)
            {
               saveArr2[3] -= 10 * (times + 1);
               fanPanel["tongji"].text = saveArr2[3];
               clickBool = false;
               fanMC = e.target as MovieClip;
               fanNum = int(fanMC.name.substr(3,1));
               saveArr[fanNum] = 1;
               fanPanel["fan" + fanNum]["tt"].visible = false;
               fanMC.play();
               Main.Save();
               fanPanel.addEventListener(Event.ENTER_FRAME,showPai);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
         }
      }
      
      public static function showPai(e:*) : *
      {
         var i:uint = 0;
         ++timeTemp;
         if(timeTemp > 20)
         {
            fanPanel["pai" + fanNum].visible = true;
         }
         if(timeTemp > 21)
         {
            countTimes();
            for(i = 0; i < 9; i++)
            {
               fanPanel["fan" + i]["tt"].text = "消耗" + 10 * (times + 1) + "积分翻牌";
            }
            clickBool = true;
            timeTemp = 0;
            fanPanel.removeEventListener(Event.ENTER_FRAME,showPai);
         }
      }
      
      public static function duiHuan(e:MouseEvent) : void
      {
         var paiMC:MovieClip = (e.target as SimpleButton).parent;
         if(paiMC.name == "pai0")
         {
            if(Main.player1.getBag().backSuppliesBagNum() > 0)
            {
               if(saveArr2[3] >= 20)
               {
                  saveArr2[3] -= 20;
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
                  (e.target as SimpleButton).visible = false;
                  saveArr[0] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"其他栏空间不足");
            }
         }
         if(paiMC.name == "pai1")
         {
            if(Main.player1.getBag().backOtherBagNum() > 0)
            {
               if(saveArr2[3] >= 700)
               {
                  saveArr2[3] -= 700;
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  (e.target as SimpleButton).visible = false;
                  saveArr[1] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"其他栏空间不足");
            }
         }
         if(paiMC.name == "pai2")
         {
            if(Main.player1.getBag().backSuppliesBagNum() > 0)
            {
               if(saveArr2[3] >= 100)
               {
                  saveArr2[3] -= 100;
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  (e.target as SimpleButton).visible = false;
                  saveArr[2] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"药品栏空间不足");
            }
         }
         if(paiMC.name == "pai3")
         {
            if(saveArr2[3] >= 130)
            {
               saveArr2[3] -= 130;
               NewPetPanel.XGkey.setValue(NewPetPanel.XGkey.getValue() + 1);
               (e.target as SimpleButton).visible = false;
               saveArr[3] = 2;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
            }
         }
         if(paiMC.name == "pai4")
         {
            if(Main.player1.getBag().backOtherBagNum() > 0)
            {
               if(saveArr2[3] >= 750)
               {
                  saveArr2[3] -= 750;
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
                  (e.target as SimpleButton).visible = false;
                  saveArr[4] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"其他栏空间不足");
            }
         }
         if(paiMC.name == "pai5")
         {
            if(Main.player1.getBag().backOtherBagNum() > 0)
            {
               if(saveArr2[3] >= 50)
               {
                  saveArr2[3] -= 50;
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  (e.target as SimpleButton).visible = false;
                  saveArr[5] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"其他栏空间不足");
            }
         }
         if(paiMC.name == "pai6")
         {
            if(Main.player1.getBag().backGemBagNum() > 0)
            {
               if(saveArr2[3] >= 700)
               {
                  saveArr2[3] -= 700;
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(33213));
                  (e.target as SimpleButton).visible = false;
                  saveArr[6] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足");
            }
         }
         if(paiMC.name == "pai7")
         {
            if(Main.player1.getBag().backOtherBagNum() > 0)
            {
               if(saveArr2[3] >= 250)
               {
                  saveArr2[3] -= 250;
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  (e.target as SimpleButton).visible = false;
                  saveArr[7] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"其他栏空间不足");
            }
         }
         if(paiMC.name == "pai8")
         {
            if(Main.player1.getBag().backOtherBagNum() > 0)
            {
               if(saveArr2[3] >= 60)
               {
                  saveArr2[3] -= 60;
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  (e.target as SimpleButton).visible = false;
                  saveArr[8] = 2;
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"积分不足");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"其他栏空间不足");
            }
         }
         fanPanel["tongji"].text = saveArr2[3];
      }
      
      public static function addZaiXianJiFen(e:*) : *
      {
         var mydate:Date = null;
         var hour:* = undefined;
         var minute:* = undefined;
         var second:* = undefined;
         ++jifenTime;
         if(jifenTime == 2)
         {
            mydate = new Date();
            hour = mydate.getHours();
            minute = mydate.getMinutes();
            second = mydate.getSeconds();
            start = (hour * 3600 + minute * 60 + second) * 27;
         }
         if(jifenTime / 12960 > 1)
         {
            mydate = new Date();
            hour = mydate.getHours();
            minute = mydate.getMinutes();
            second = mydate.getSeconds();
            end = (hour * 3600 + minute * 60 + second) * 27;
            if(jifenTime - (end - start) < 600 * 27)
            {
               if(saveArr2[1] < 300)
               {
                  saveArr2[1] += 10;
                  saveArr2[3] += 10;
               }
            }
            else
            {
               Main.NoGame("翻牌");
            }
            jifenTime = 1;
         }
      }
      
      public static function addShaGuaiJiFen() : *
      {
         if(saveArr2[2] < 500)
         {
            saveArr2[2] += 1;
            saveArr2[3] += 1;
         }
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         fanPanel.addChild(itemsTooltip);
         var str:String = overobj.name;
         if(str == "m0")
         {
            itemsTooltip.otherTooltip(OtherFactory.creatOther(63256));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m1")
         {
            itemsTooltip.suppliesTooltip(SuppliesFactory.getSuppliesById(21223));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m2")
         {
            itemsTooltip.otherTooltip(OtherFactory.creatOther(63255));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m3")
         {
            itemsTooltip.gemTooltip(GemFactory.creatGemById(33213));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m4")
         {
            itemsTooltip.otherTooltip(OtherFactory.creatOther(63204));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m5")
         {
            itemsTooltip.otherTooltip(OtherFactory.creatOther(63140));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m6")
         {
            itemsTooltip.suppliesTooltip(SuppliesFactory.getSuppliesById(21221));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m7")
         {
            itemsTooltip.otherTooltip(OtherFactory.creatOther(63235));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         else if(str == "m8")
         {
            itemsTooltip.otherTooltip(OtherFactory.creatOther(63100));
            itemsTooltip.y = fanPanel.mouseY - 150;
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = fanPanel.mouseX - 100;
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
   }
}

