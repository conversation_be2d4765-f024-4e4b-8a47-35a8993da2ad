package src
{
   import com.hotpoint.braveManIII.Tool.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import src.tool.*;
   
   public class Data_mosen extends MovieClip
   {
      
      public static var xml:XML;
      
      public static var data:Array = [];
      
      public static var paiXu:Array = [];
      
      public function Data_mosen()
      {
         super();
      }
      
      public static function Init() : *
      {
         Xml_init();
         PaiXuFun();
      }
      
      private static function Xml_init() : *
      {
         var i:int = 0;
         var id:int = 0;
         var x1:int = 0;
         var x2:int = 0;
         var x3:String = null;
         var x4:int = 0;
         var x5:String = null;
         var x6:int = 0;
         var x7:int = 0;
         var x8:int = 0;
         var x9:int = 0;
         var x10:int = 0;
         var x11:int = 0;
         for(i in xml.魔神系统)
         {
            id = int(xml.魔神系统[i].编号);
            x1 = int(xml.魔神系统[i].排序);
            x2 = int(xml.魔神系统[i].宠物图片帧数);
            x3 = String(xml.魔神系统[i].魔神名);
            x4 = int(xml.魔神系统[i].等级上限);
            x5 = String(xml.魔神系统[i].魔神形象链接名);
            x6 = int(xml.魔神系统[i].每级觉醒奖励攻击);
            x7 = int(xml.魔神系统[i].每级能量);
            x8 = int(xml.魔神系统[i].每级增伤);
            x9 = int(xml.魔神系统[i].觉醒物品1数量);
            x10 = int(xml.魔神系统[i].觉醒物品2数量);
            x11 = int(xml.魔神系统[i].觉醒物品2数量);
            data[id] = [id,x1,x2,x3,x4,x5,x6,x7,x8,x9,x10,x11];
         }
      }
      
      public static function PaiXuFun() : *
      {
         var objects:Array = [];
         for(var i:int = 0; i < data.length; i++)
         {
            if(data[i])
            {
               objects.push({
                  "value":data[i][1],
                  "index":i
               });
            }
         }
         objects.sortOn("value",Array.DESCENDING | Array.NUMERIC);
         for(var j:int = 0; j < objects.length; j++)
         {
            paiXu.push(objects[j].index);
         }
         trace("魔神系统 排序 >>>",paiXu);
      }
   }
}

