package com.hotpoint.braveManIII.models.basicBtn
{
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.utils.*;
   
   public class BasicBtn extends MovieClip
   {
      
      private var _id:Number;
      
      private var _set:Boolean;
      
      public function BasicBtn()
      {
         super();
         this.stop();
         this.focusRect = false;
         this.buttonMode = true;
         this.mouseChildren = false;
         this.addEventListener(Event.ADDED_TO_STAGE,this.init,false,0,true);
         this.addEventListener(Event.REMOVED_FROM_STAGE,this.destroy,false,0,true);
         this.addEventListener(MouseEvent.ROLL_OUT,this.rollOut);
         this.addEventListener(MouseEvent.ROLL_OVER,this.rollOver);
         this.addEventListener(MouseEvent.MOUSE_DOWN,this.mouseDown);
         this.addEventListener(MouseEvent.MOUSE_UP,this.mouseUp);
         this.addEventListener(MouseEvent.CLICK,this.clickFn);
      }
      
      public function get id() : Number
      {
         return this._id;
      }
      
      public function set id(id:Number) : void
      {
         if(this._set)
         {
            return;
         }
         this._id = id;
         this._set = true;
      }
      
      private function init(event:Event) : void
      {
         var name:String = getQualifiedClassName(this);
         var id:Number = Number(parseInt(name.substring(name.lastIndexOf("_") + 1,name.length)));
         if(isNaN(id))
         {
            return;
         }
         this.id = id;
      }
      
      private function destroy(event:Event) : void
      {
         this.removeEventListener(Event.ADDED_TO_STAGE,this.init,false);
         this.removeEventListener(Event.REMOVED_FROM_STAGE,this.destroy,false);
         this.removeEventListener(MouseEvent.ROLL_OUT,this.rollOut);
         this.removeEventListener(MouseEvent.ROLL_OVER,this.rollOver);
         this.removeEventListener(MouseEvent.MOUSE_DOWN,this.mouseDown);
         this.removeEventListener(MouseEvent.MOUSE_UP,this.mouseUp);
         this.removeEventListener(MouseEvent.CLICK,this.clickFn);
      }
      
      protected function rollOut(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OUT)
         {
            this.gotoAndStop(1);
         }
      }
      
      protected function rollOver(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OVER)
         {
            this.gotoAndStop(2);
         }
      }
      
      protected function mouseDown(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.MOUSE_DOWN)
         {
            this.gotoAndStop(3);
         }
      }
      
      protected function mouseUp(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.MOUSE_UP)
         {
            this.gotoAndStop(2);
         }
      }
      
      protected function clickFn(event:MouseEvent) : void
      {
      }
   }
}

