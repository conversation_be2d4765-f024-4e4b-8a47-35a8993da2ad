package _main_cs5_233333_fla
{
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4742")]
   public dynamic class 提示17_633 extends MovieClip
   {
      
      public function 提示17_633()
      {
         super();
         addFrameScript(36,this.frame37);
      }
      
      internal function frame37() : *
      {
         this.parent.visible = false;
      }
   }
}

