package src
{
   import com.adobe.serialization.json.JSONs;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.temaiPanel.*;
   import flash.events.*;
   import flash.net.*;
   
   public class Request
   {
      
      private static var _load:URLLoader;
      
      private static var callFun:Function;
      
      public function Request()
      {
         super();
      }
      
      public static function RequestX() : *
      {
         if(Main.gameNum.getValue() >= 7000 && Main.gameNum.getValue() <= 7003)
         {
            return;
         }
         trace("请求活动信息 ???????????????????");
         callFun = requestOver;
         var uRequest:URLRequest = new URLRequest("https://game.api.ayxhot.cn/api/xy/activity/");
         uRequest.method = URLRequestMethod.POST;
         var headData:Array = [];
         headData.push(new URLRequestHeader("AUTHORIZATION-PLAYER","44ca95050b6cf268238ace9209c1b161"));
         uRequest.requestHeaders = headData;
         uRequest.data = new Object();
         _load = new URLLoader();
         _load.dataFormat = URLLoaderDataFormat.BINARY;
         _load.addEventListener(Event.COMPLETE,completeHandler);
         _load.addEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
         _load.load(uRequest);
      }
      
      public static function requestOver(sdata:Object) : void
      {
         var time0:String = null;
         var time1:String = null;
         var timeS:* = null;
         var timeXX_0:String = null;
         var timeXX_1:String = null;
         var timeXX_S:* = null;
         if(sdata is String)
         {
            trace("连接错误-接口url >>> " + sdata);
         }
         else if(sdata.code == 200)
         {
            trace("服务器请求 成功 >>>>>>",sdata.data.list);
            trace("tip9 >>>",sdata.data.list.tip9.ver,sdata.data.list.tip9.end);
            trace("tip15 >>>",sdata.data.list.tip15);
            trace("tip16 >>>",sdata.data.list.tip16);
            ChongZhi_Interface2.time1 = sdata.data.list.tip1;
            ChongZhi_Interface2.time2 = sdata.data.list.tip2;
            ChongZhi_Interface2.time3 = sdata.data.list.tip3;
            ChongZhi_Interface2.time4 = sdata.data.list.tip4;
            ChongZhi_Interface2.time5 = sdata.data.list.tip5;
            ChongZhi_Interface2.time6 = sdata.data.list.tip6;
            ChongZhi_Interface2.time7 = sdata.data.list.tip15;
            ChongZhi_Interface2.time8 = sdata.data.list.tip16;
            ChunJiePanel.varXX = sdata.data.list.tip8.ver;
            ChunJiePanel.overTime = sdata.data.list.tip8.end;
            NewYear_Interface.varXX = sdata.data.list.tip9.ver;
            NewYear_Interface.overTime = sdata.data.list.tip9.end;
            Panel_XianHua.overTime = sdata.data.list.tip10;
            ZhuanPan.overTime = sdata.data.list.tip11;
            TeMaiPanel.overTime = sdata.data.list.tip12;
            FiveOne_Interface.varXX = sdata.data.list.tip14.ver;
            FiveOne_Interface.overTime = sdata.data.list.tip14.end;
            time0 = sdata.data.list.tip7.start;
            time1 = sdata.data.list.tip7.end;
            timeS = "活动时间: " + time0.substr(4,2) + "月" + time0.substr(6,2) + "日" + "至" + time1.substr(4,2) + "月" + time1.substr(6,2) + "日";
            time0 = time0.substr(0,4) + "-" + time0.substr(4,2) + "-" + time0.substr(6,2);
            time1 = time1.substr(0,4) + "-" + time1.substr(4,2) + "-" + time1.substr(6,2);
            ChongZhi_Interface4.varXX = sdata.data.list.tip7.ver;
            ChongZhi_Interface4.overTime = sdata.data.list.tip7.end;
            ChongZhi_Interface4.time0 = time0 + "|00:00:01";
            ChongZhi_Interface4.time1 = time1 + "|23:59:59";
            ChongZhi_Interface4.timeStr = timeS;
            timeXX_0 = sdata.data.list.tip13.start;
            timeXX_1 = sdata.data.list.tip13.end;
            timeXX_S = "活动时间: " + timeXX_0.substr(4,2) + "月" + timeXX_0.substr(6,2) + "日" + "至" + timeXX_1.substr(4,2) + "月" + timeXX_1.substr(6,2) + "日";
            timeXX_0 = timeXX_0.substr(0,4) + "-" + timeXX_0.substr(4,2) + "-" + timeXX_0.substr(6,2);
            timeXX_1 = timeXX_1.substr(0,4) + "-" + timeXX_1.substr(4,2) + "-" + timeXX_1.substr(6,2);
            ChongZhi_Interface.varXX = sdata.data.list.tip13.ver;
            ChongZhi_Interface.overTime = sdata.data.list.tip13.end;
            ChongZhi_Interface.time0 = timeXX_0 + "|00:00:01";
            ChongZhi_Interface.time1 = timeXX_1 + "|23:59:59";
            ChongZhi_Interface.timeStr = timeXX_S;
            ChongZhi_Interface.type = sdata.data.list.tip13.type;
            trace("timeS >>",timeXX_S,ChongZhi_Interface.type);
            trace("time0 >>",ChongZhi_Interface.time0);
            trace("time1 >>",ChongZhi_Interface.time1);
         }
         else
         {
            trace("服务器请求 错误内容 >>>>>",sdata.message);
         }
      }
      
      private static function completeHandler(event:Event) : void
      {
         var tempbojt:Object = null;
         var result:String = _load.data;
         var tfun:Function = callFun;
         remove();
         if(tfun != null)
         {
            tempbojt = JSONs.decode(result);
            if(!(tempbojt.code != null && tempbojt.code == 60011))
            {
               tfun(tempbojt);
            }
         }
      }
      
      private static function ioErrorHandler(ea:IOErrorEvent) : void
      {
         var tfun:Function = callFun;
         remove();
         if(tfun != null)
         {
            tfun("ioE" + ea.text);
         }
      }
      
      private static function remove() : void
      {
         if(_load != null)
         {
            callFun = null;
            _load.removeEventListener(Event.COMPLETE,completeHandler);
            _load.removeEventListener(IOErrorEvent.IO_ERROR,ioErrorHandler);
            try
            {
               _load.close();
            }
            catch(error:Error)
            {
               trace("urlrequest,load.close出错...");
            }
            _load = null;
         }
      }
   }
}

