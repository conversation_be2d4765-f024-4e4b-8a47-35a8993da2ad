package com.hotpoint.braveManIII.views.strPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.utils.*;
   import src.*;
   import src.tool.*;
   
   public class StrPanel extends MovieClip
   {
      
      public static var data:PlayerData;
      
      public static var _instance:StrPanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      public static var state:Number = 0;
      
      public static var stateTow:Number = 0;
      
      private static var loadName:String = "Panel_Str_v1903.swf";
      
      private var tooltip:ItemsTooltip;
      
      private var ps:Number = 1;
      
      private var oldStateTow:Number = 0;
      
      private var strBag:StrBag;
      
      private var strSlot:StrSlot;
      
      private var pro:VT;
      
      private var ranNum:VT;
      
      private var times:uint = 0;
      
      private var timer:Timer;
      
      private var stopTime:uint = 20;
      
      private var bo:Boolean = true;
      
      public var mast_hc:*;
      
      public var s1_mc:*;
      
      public var s2_mc:*;
      
      public var strengthen:*;
      
      public var s_0:*;
      
      public var s_1:*;
      
      public var s_2:*;
      
      public var gold_name:*;
      
      public var strBtn:*;
      
      public var mc_ss:*;
      
      public var p_0:*;
      
      public var p_1:*;
      
      public var p_2:*;
      
      public var p_3:*;
      
      public var p_4:*;
      
      public var p_5:*;
      
      public var p_6:*;
      
      public var p_7:*;
      
      public var player_gold:*;
      
      public var pla_0:*;
      
      public var pla_1:*;
      
      public var str_0:*;
      
      public var str_1:*;
      
      public var closePanel:*;
      
      public var pro_text:*;
      
      public var str_name:*;
      
      public var b_0:*;
      
      public var b_1:*;
      
      public var b_2:*;
      
      public var b_3:*;
      
      public var b_4:*;
      
      public var b_5:*;
      
      public var b_6:*;
      
      public var b_7:*;
      
      public var b_8:*;
      
      public var b_9:*;
      
      public var b_10:*;
      
      public var b_11:*;
      
      public var b_12:*;
      
      public var b_13:*;
      
      public var b_14:*;
      
      public var b_15:*;
      
      public var b_16:*;
      
      public var b_17:*;
      
      public var b_18:*;
      
      public var b_19:*;
      
      public var b_20:*;
      
      public var b_21:*;
      
      public var b_22:*;
      
      public var b_23:*;
      
      public var face_mc:*;
      
      public var face_txt:*;
      
      private var YN:Boolean = false;
      
      private var SaveOk:Boolean = false;
      
      public function StrPanel()
      {
         super();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("com.hotpoint.braveManIII.views.strPanel.StrPanel") as Class;
         StrPanel._instance = new classRef();
         StrPanel._instance.strBag = StrBag.creatBag();
         StrPanel._instance.strSlot = StrSlot.creatSlot();
         StrPanel._instance.tooltip = new ItemsTooltip();
         StrPanel._instance.addEvent();
         StrPanel._instance.addTimeEvent();
         InitIcon();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 3; i++)
         {
            mm = new Shop_picNEW();
            num = int(_instance["s_" + i].getChildIndex(_instance["s_" + i].pic_xx));
            mm.x = _instance["s_" + i].pic_xx.x;
            mm.y = _instance["s_" + i].pic_xx.y;
            mm.name = "pic_xx";
            _instance["s_" + i].removeChild(_instance["s_" + i].pic_xx);
            _instance["s_" + i].pic_xx = mm;
            _instance["s_" + i].addChild(mm);
            _instance["s_" + i].setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = int(_instance["p_" + i].getChildIndex(_instance["p_" + i].pic_xx));
            mm.x = _instance["p_" + i].pic_xx.x;
            mm.y = _instance["p_" + i].pic_xx.y;
            mm.name = "pic_xx";
            _instance["p_" + i].removeChild(_instance["p_" + i].pic_xx);
            _instance["p_" + i].pic_xx = mm;
            _instance["p_" + i].addChild(mm);
            _instance["p_" + i].setChildIndex(mm,num);
         }
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = int(_instance["b_" + i].getChildIndex(_instance["b_" + i].pic_xx));
            mm.x = _instance["b_" + i].pic_xx.x;
            mm.y = _instance["b_" + i].pic_xx.y;
            mm.name = "pic_xx";
            _instance["b_" + i].removeChild(_instance["b_" + i].pic_xx);
            _instance["b_" + i].pic_xx = mm;
            _instance["b_" + i].addChild(mm);
            _instance["b_" + i].setChildIndex(mm,num);
         }
      }
      
      public static function open() : void
      {
         trace("打开强化面板XXX");
         Main.allClosePanel();
         if(StrPanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         Main._stage.addChild(StrPanel._instance);
         StrPanel._instance.visible = true;
         StrPanel._instance.initPanel();
         StrPanel._instance.addChild(StrPanel._instance.tooltip);
         StrPanel._instance.tooltip.visible = false;
         StrPanel._instance.mast_hc.visible = false;
         StrPanel._instance.face_mc.visible = false;
      }
      
      public static function close() : void
      {
         if(StrPanel._instance != null)
         {
            if(StrPanel._instance.visible == true)
            {
               StrPanel._instance.visible = false;
            }
         }
         else
         {
            open_yn = false;
         }
      }
      
      public static function QiangHuaOK(e:* = null) : *
      {
         if(Boolean(_instance) && Boolean(_instance.SaveOk))
         {
            if(_instance.s1_mc.currentFrame > 75)
            {
               _instance.s1_mc.gotoAndStop(1);
               _instance.SaveOk = false;
               if(_instance.YN)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"强化成功");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"强化失败");
               }
               _instance.getPro();
               _instance.strName();
               _instance.goldName();
               _instance.proText();
               _instance.QiangHuaJiaCheng();
               _instance.mast_hc.visible = false;
               _instance.strBtn.visible = true;
               _instance.removeEventListener(Event.ENTER_FRAME,QiangHuaOK);
            }
            else
            {
               _instance.addEventListener(Event.ENTER_FRAME,QiangHuaOK);
            }
         }
      }
      
      private function initPanel() : void
      {
         this.pro = VT.createVT(0);
         this.ranNum = VT.createVT(100);
         this.ps = 1;
         data = Main["player" + this.ps];
         state = 0;
         stateTow = 0;
         this.getEquipIng();
         this.p1orp2();
         this.addBag(StrData.getEquipOrSkillGem());
         this.initFrame();
         this.mc_ss.visible = false;
         this.strSlot.clearBag();
         this.initFrameSlot();
         this.btnChange();
         this.btnStata(0,2,"pla_");
         this.btnStr();
         this.getPro();
         this.proText();
         this.s1_mc.gotoAndStop(1);
         this.s2_mc.gotoAndStop(1);
         this.strName();
         this.goldName();
         this.playerGold();
         this.QiangHuaJiaCheng();
      }
      
      private function p1orp2() : void
      {
         this.pla_0.visible = false;
         this.pla_1.visible = false;
         if(Main.P1P2)
         {
            this.pla_0.visible = true;
            this.pla_1.visible = true;
            this.btnStata(0,2,"pla_");
         }
      }
      
      private function addEvent() : void
      {
         this.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.addEventListener(DaoJuEvent.DAOJU_CLICK_MESSAGE,this.daoJuClick);
         this.addEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         this.addEventListener(BtnEvent.DO_CLICK,this.clickHandle);
         this.addEventListener(BtnEvent.DO_CLOSE,this.closeHandle);
      }
      
      private function removeEvent() : void
      {
         this.removeEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,this.daoJuOver);
         this.removeEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,this.daoJuOut);
         this.removeEventListener(DaoJuEvent.DAOJU_CLICK_MESSAGE,this.daoJuClick);
         this.removeEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         this.removeEventListener(BtnEvent.DO_CLICK,this.clickHandle);
         this.removeEventListener(BtnEvent.DO_CLOSE,this.closeHandle);
      }
      
      private function addTimeEvent() : void
      {
         this.timer = new Timer(1);
         this.timer.addEventListener(TimerEvent.TIMER,this.onTimerHandle);
      }
      
      private function getEquipIng() : void
      {
         var xx:uint = 0;
         var equip:Equip = null;
         for(var i:uint = 0; i < 8; i++)
         {
            xx = i;
            if((i == 0 || i == 1 || i == 3 || i == 4) && Main.water.getValue() != 1)
            {
               xx += 8;
            }
            equip = data.getEquipSlot().getEquipFromSlot(xx);
            if(equip != null)
            {
               this["p_" + i].howNum.text = "";
               this["p_" + i].pic_xx.gotoAndStop(equip.getFrame());
            }
            else
            {
               this["p_" + i].howNum.text = "";
               this["p_" + i].pic_xx.gotoAndStop(1);
            }
         }
      }
      
      private function btnChange() : void
      {
         this.str_0.type.gotoAndStop(1);
         this.str_1.type.gotoAndStop(1);
         this.btnStata(state,2,"str_");
         this["str_" + state].type.gotoAndStop(stateTow + 1);
      }
      
      private function btnStata(id:Number, num:Number, str:String) : void
      {
         this[str + id].isClick = true;
         for(var i:uint = 0; i < num; i++)
         {
            if(id != i)
            {
               this[str + i].isClick = false;
            }
         }
      }
      
      private function addBag(arr:Array) : void
      {
         var i:uint = 0;
         this.strBag.clearBag();
         if(arr != null)
         {
            for(i = 0; i < arr[0].length; i++)
            {
               this.strBag.addBag(arr[0][i],arr[1][i]);
            }
         }
      }
      
      private function initFrame() : void
      {
         var arr:Array = null;
         var num:int = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            if(this.strBag.getObj(i) != null)
            {
               arr = this.strBag.getObj(i);
               this["b_" + i].howNum.text = "";
               this["b_" + i].pic_xx.gotoAndStop(arr[0].getFrame());
               if(arr[0] is Gem)
               {
                  if(arr[0].getIsPile())
                  {
                     this["b_" + i].howNum.text = arr[0].getTimes();
                  }
               }
               num++;
            }
            else
            {
               this["b_" + i].pic_xx.gotoAndStop(1);
               this["b_" + i].howNum.text = "";
            }
         }
         this.face_mc.visible = false;
         if(num == 0)
         {
            if(StrPanel.state == 0 && StrPanel.stateTow == 1)
            {
               this.face_mc.face_txt.text = "背包中缺少强化石,可到商城购买";
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"背包中缺少强化石,可到商城购买");
               this.face_mc.visible = true;
            }
            else if(StrPanel.state == 1 && StrPanel.stateTow == 0)
            {
               this.face_mc.face_txt.text = "背包中缺少技能石";
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"背包中缺少技能石");
               this.face_mc.visible = true;
            }
            else if(StrPanel.state == 1 && StrPanel.stateTow == 1)
            {
               NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"背包中缺少宝石碎片");
               this.face_mc.face_txt.text = "背包中缺少宝石碎片";
               this.face_mc.visible = true;
            }
         }
         TiaoShi.txtShow("背包道具数量 = " + num);
         TiaoShi.txtShow("StrPanel.state = " + StrPanel.state);
         TiaoShi.txtShow("StrPanel.stateTow = " + StrPanel.stateTow);
      }
      
      private function daoJuClick(e:DaoJuEvent) : void
      {
         var ob:Object = null;
         var idX:uint = 0;
         var equip:Equip = null;
         var arr:Array = null;
         var nameId:String = e.target.name.substr(0,2);
         var id:String = String(e.target.name).substr(2);
         trace("点击背包装备或宝石 >>",e.target.name,nameId,id);
         if(nameId != "s_")
         {
            if(this.bo)
            {
               this.mc_ss.visible = true;
               this.mc_ss.x = e.target.x;
               this.mc_ss.y = e.target.y;
            }
            if(nameId == "b_")
            {
               if(this.strBag.getObj(id) != null)
               {
                  ob = (this.strBag.getObj(id) as Array)[0];
                  if(this.strSlot.addTj(ob))
                  {
                     this.strSlot.addBag(this.strBag.getObj(id),0);
                  }
               }
            }
            else if(nameId == "p_")
            {
               idX = uint(id);
               if((id == 0 || id == 1 || id == 3 || id == 4) && Main.water.getValue() != 1)
               {
                  idX += 8;
               }
               if(data.getEquipSlot().getEquipFromSlot(idX) != null)
               {
                  equip = data.getEquipSlot().getEquipFromSlot(idX);
                  trace("点击背包装备或宝石 >>>>>>>",id,idX,state,equip);
                  if(state == 1)
                  {
                     this.strSlot.clearBag();
                     state = 0;
                     stateTow = 0;
                  }
                  arr = [equip,idX];
                  if(this.strSlot.addTj(equip))
                  {
                     this.strSlot.addBag(arr,1);
                  }
               }
            }
         }
         else
         {
            this.strSlot.clearOnly(Number(id));
            this.mc_ss.visible = false;
         }
         this.initFrameSlot();
         this.changeBag();
         this.btnStr();
         this.getPro();
         this.proText();
         this.strName();
         this.goldName();
         this.playerGold();
      }
      
      private function proText() : void
      {
         if(this.pro.getValue() <= 0)
         {
            this.pro_text.text = "";
         }
         else
         {
            this.pro_text.text = String(this.pro.getValue()) + "%";
         }
      }
      
      private function changeBag() : void
      {
         if(this.strSlot.getObj(0) != null)
         {
            stateTow = 1;
         }
         else
         {
            stateTow = 0;
         }
         if(stateTow != this.oldStateTow)
         {
            this.mc_ss.visible = false;
            this.oldStateTow = stateTow;
         }
         this.addBag(StrData.getEquipOrSkillGem());
         this.btnChange();
         this.initFrame();
      }
      
      private function initFrameSlot() : void
      {
         var arr:Array = null;
         for(var i:uint = 0; i < 3; i++)
         {
            this["s_" + i].visible = false;
            if(this.strSlot.getObj(i) != null)
            {
               this["s_" + i].visible = true;
               arr = this.strSlot.getObj(i);
               this["s_" + i].howNum.text = "";
               this["s_" + i].pic_xx.gotoAndStop(arr[0].getFrame());
            }
         }
      }
      
      private function daoJuOver(e:DaoJuEvent) : void
      {
         var xx:uint = 0;
         var nameId:String = e.target.name.substr(0,2);
         var id:String = e.target.name.substr(2);
         var ob:Object = null;
         if(nameId == "b_" && this.strBag.getObj(id) != null)
         {
            ob = (this.strBag.getObj(id) as Array)[0];
         }
         else if(nameId == "p_" && data.getEquipSlot().getEquipFromSlot(id) != null)
         {
            xx = uint(id);
            if((id == 0 || id == 1 || id == 3 || id == 4) && Main.water.getValue() != 1)
            {
               xx += 8;
            }
            ob = data.getEquipSlot().getEquipFromSlot(xx);
         }
         else if(nameId == "s_" && this.strSlot.getObj(id) != null)
         {
            ob = (this.strSlot.getObj(id) as Array)[0];
         }
         if(ob != null)
         {
            this.tooltip.visible = true;
            this.tooltip.x = mouseX;
            this.tooltip.y = mouseY;
            if(ob is Equip)
            {
               this.tooltip.equipTooltip(ob as Equip);
            }
            else if(ob is Gem)
            {
               this.tooltip.gemTooltip(ob as Gem);
            }
         }
      }
      
      private function daoJuOut(e:DaoJuEvent) : void
      {
         this.tooltip.visible = false;
      }
      
      private function closeHandle(e:BtnEvent) : void
      {
         close();
      }
      
      private function clickHandle(e:BtnEvent) : void
      {
         this.timer.start();
         this.removeEvent();
         this.mast_hc.visible = true;
      }
      
      private function changeHandle(e:BtnEvent) : void
      {
         var nameId:String = e.target.name.substr(0,3);
         var id:String = e.target.name.substr(4,1);
         if(nameId == "str")
         {
            state = Number(id);
            stateTow = 0;
            this.oldStateTow = 0;
         }
         else if(nameId == "pla")
         {
            this.ps = Number(id) + 1;
            data = Main["player" + this.ps];
            this.btnStata(id,2,"pla_");
            state = 0;
            stateTow = 0;
            this.oldStateTow = 0;
            this.getEquipIng();
         }
         this.strSlot.clearBag();
         this.addBag(StrData.getEquipOrSkillGem());
         this.btnChange();
         this.initFrame();
         this.initFrameSlot();
         this.getPro();
         this.proText();
         this.strName();
         this.goldName();
         this.QiangHuaJiaCheng();
         this.btnStr();
         this.mc_ss.visible = false;
      }
      
      private function btnStr() : void
      {
         this.strBtn.gotoAndStop(2);
         if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
         {
            if(state == 0)
            {
               if(data.getGold() >= StrData.getStrenthenGold((this.strSlot.getObj(0) as Array)[0]))
               {
                  this.strBtn.gotoAndStop(1);
               }
            }
            else if(state == 1)
            {
               if(data.getGold() >= StrData.getGemUpGold((this.strSlot.getObj(0) as Array)[0]))
               {
                  this.strBtn.gotoAndStop(1);
               }
            }
         }
      }
      
      private function getPro() : void
      {
         var num1:Number = NaN;
         var num2:Number = NaN;
         this.pro.setValue(0);
         if(state == 0)
         {
            if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
            {
               if(this.strSlot.getObj(2) == null)
               {
                  this.pro.setValue(StrData.getEquipUpLevel((this.strSlot.getObj(0) as Array)[0],(this.strSlot.getObj(1) as Array)[0]));
               }
               else
               {
                  num1 = Number(StrData.getEquipUpLevel((this.strSlot.getObj(0) as Array)[0],(this.strSlot.getObj(1) as Array)[0]));
                  num2 = Number(StrData.getLuckGemPosition((this.strSlot.getObj(2) as Array)[0]));
                  this.pro.setValue(num1 + num2);
               }
            }
         }
         else if(state == 1)
         {
            if(this.strSlot.getObj(0) != null)
            {
               if(this.strSlot.getObj(2) == null)
               {
                  this.pro.setValue(StrData.getGemUpLevel((this.strSlot.getObj(0) as Array)[0]));
               }
               else
               {
                  num1 = Number(StrData.getGemUpLevel((this.strSlot.getObj(0) as Array)[0]));
                  num2 = Number(StrData.getLuckGemPosition((this.strSlot.getObj(2) as Array)[0]));
                  this.pro.setValue(num1 + num2);
               }
            }
         }
         if(this.pro.getValue() >= 100)
         {
            this.pro.setValue(100);
         }
      }
      
      private function onTimerHandle(e:TimerEvent) : void
      {
         ++this.times;
         if(this.s1_mc.currentFrame == 1)
         {
            this.s1_mc.gotoAndPlay(2);
            this.s2_mc.gotoAndPlay(2);
            this.strBtn.visible = false;
         }
         if(this.times >= this.stopTime)
         {
            this.timer.stop();
            this.times = 0;
            this.addEvent();
            if(Boolean(this.strSlot.addTj((this.strSlot.getObj(0) as Array)[0])) && Boolean(this.strSlot.addTj((this.strSlot.getObj(1) as Array)[0])))
            {
               if(Math.random() * this.ranNum.getValue() <= this.pro.getValue())
               {
                  this.addSx();
                  this.YN = true;
                  AchData.setStrOkNum(this.ps);
               }
               else
               {
                  this.YN = false;
                  AchData.setStrLostNum(this.ps);
               }
               this.removeGem();
               this.addBag(StrData.getEquipOrSkillGem());
               this.payGold();
            }
            else
            {
               stateTow = 0;
               this.strSlot.clearBag();
               this.addBag(StrData.getEquipOrSkillGem());
               trace("归位");
            }
            this.initFrameSlot();
            this.initFrame();
            this.btnStr();
            this.playerGold();
            trace("强化...........");
            this.SaveOk = true;
            Main.Save();
         }
      }
      
      private function removeGem() : void
      {
         var gem:Gem = null;
         if(this.strSlot.getObj(1) != null)
         {
            gem = (this.strSlot.getObj(1) as Array)[0];
            data.getBag().delGem((this.strSlot.getObj(1) as Array)[1],1);
            if(data.getBag().getGemById(gem.getId()) == null)
            {
               this.strSlot.clearOnly(1);
            }
         }
         if(this.strSlot.getObj(2) != null)
         {
            gem = (this.strSlot.getObj(2) as Array)[0];
            data.getBag().delGem((this.strSlot.getObj(2) as Array)[1],1);
            if(data.getBag().getGemById(gem.getId()) == null)
            {
               this.strSlot.clearOnly(2);
            }
         }
      }
      
      private function addSx() : void
      {
         if(state == 0)
         {
            this.strEquip();
         }
         else if(state == 1)
         {
            this.strGem();
         }
      }
      
      private function strEquip() : void
      {
         var equip:Equip = null;
         var point:Number = NaN;
         var who:Number = NaN;
         var gem:Gem = null;
         if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
         {
            point = Number((this.strSlot.getObj(0) as Array)[1]);
            who = Number((this.strSlot.getObj(0) as Array)[2]);
            gem = (this.strSlot.getObj(1) as Array)[0];
            trace(who + "who",point + "point");
            if(who == 0)
            {
               equip = data.getBag().getEquipFromBag(point);
            }
            else if(who == 1)
            {
               equip = data.getEquipSlot().getEquipFromSlot(point);
            }
            StrData.strengthenOver(equip,gem);
         }
      }
      
      private function strGem() : void
      {
         var point:Number = NaN;
         var oldGem:Gem = null;
         var gem:Gem = null;
         if(this.strSlot.getObj(0) != null && this.strSlot.getObj(1) != null)
         {
            point = Number((this.strSlot.getObj(0) as Array)[1]);
            oldGem = (this.strSlot.getObj(0) as Array)[0];
            gem = StrData.strengthenSkillOver(oldGem);
            data.getBag().delGem(point,1);
            data.getBag().addToGemBag(gem,point);
            this.strSlot.addBag(new Array(gem,point),0);
         }
      }
      
      private function strName() : void
      {
         var ob:Object = null;
         this.str_name.text = "";
         if(this.strSlot.getObj(0) != null)
         {
            ob = (this.strSlot.getObj(0) as Array)[0];
            if(ob is Equip)
            {
               this.str_name.text = (ob as Equip).getName() + "+" + (ob as Equip).getReinforceLevel();
            }
            else if(ob is Gem)
            {
               this.str_name.text = (ob as Gem).getName() + "+" + (ob as Gem).getStrengthenLevel();
            }
         }
      }
      
      private function goldName() : void
      {
         this.gold_name.text = "";
         if(this.strSlot.getObj(0) != null)
         {
            if(state == 0)
            {
               this.gold_name.text = StrData.getStrenthenGold((this.strSlot.getObj(0) as Array)[0]).toString();
            }
            else if(state == 1)
            {
               this.gold_name.text = StrData.getGemUpGold((this.strSlot.getObj(0) as Array)[0]).toString();
            }
         }
      }
      
      private function payGold() : void
      {
         if(this.strSlot.getObj(0) != null)
         {
            if(state == 0)
            {
               data.payGold(StrData.getStrenthenGold((this.strSlot.getObj(0) as Array)[0]));
            }
            else if(state == 1)
            {
               data.payGold(StrData.getGemUpGold((this.strSlot.getObj(0) as Array)[0]));
            }
         }
      }
      
      private function playerGold() : void
      {
         this.player_gold.text = String(data.getGold());
      }
      
      private function QiangHuaJiaCheng() : *
      {
         var sLV:int = data.getEquipSlot().getSuitStrength();
         if(sLV < 4)
         {
            this.strengthen.gotoAndStop(1);
            return;
         }
         switch(sLV)
         {
            case 4:
               this.strengthen.gotoAndStop(2);
               break;
            case 5:
               this.strengthen.gotoAndStop(3);
               break;
            case 6:
               this.strengthen.gotoAndStop(4);
               break;
            case 7:
               this.strengthen.gotoAndStop(5);
               break;
            case 8:
               this.strengthen.gotoAndStop(6);
               break;
            case 9:
               this.strengthen.gotoAndStop(7);
               break;
            case 10:
               this.strengthen.gotoAndStop(8);
         }
      }
   }
}

