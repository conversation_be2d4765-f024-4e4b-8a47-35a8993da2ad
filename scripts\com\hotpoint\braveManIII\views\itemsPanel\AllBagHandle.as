package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import flash.display.MovieClip;
   import src.*;
   
   public class AllBagHandle
   {
      
      private var bag:Bag = new Bag();
      
      private var equipSlot:EquipSlot = new EquipSlot();
      
      private var bag2P:Bag = new Bag();
      
      private var equipSlot2P:EquipSlot = new EquipSlot();
      
      public function AllBagHandle()
      {
         super();
      }
      
      public function equipHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var equip:Equip = null;
         this.bag = ItemsPanel.myplayer.data.getBag();
         this.equipSlot = ItemsPanel.myplayer.data.getEquipSlot();
         end += ItemsPanel.yeshu * 24;
         if(this.bag.getEquipFromBag(end) != null)
         {
            if(dragObj.name.substr(0,3) == "s1_")
            {
               begin += ItemsPanel.yeshu * 24;
               this.bag.equipBagMove(begin,end);
            }
            else
            {
               if(Main.water.getValue() != 1 && (begin == 0 || begin == 1 || begin == 3 || begin == 4))
               {
                  begin += 8;
               }
               if(this.bag.getEquipFromBag(end).getDressLevel() <= ItemsPanel.myplayer.data.getLevel() || this.bag.getEquipFromBag(end).getDressLevel() == 100)
               {
                  trace("交换装备:",this.equipSlot.getEquipFromSlot(begin).getPosition(),this.bag.getEquipFromBag(end).getPosition());
                  if(this.equipSlot.getEquipFromSlot(begin).getPosition() != this.bag.getEquipFromBag(end).getPosition())
                  {
                     return false;
                  }
                  equip = this.bag.delEquip(end);
                  this.bag.addToEquipBag(this.equipSlot.getEquipFromSlot(begin),end);
                  this.equipSlot.delSlot(begin);
                  this.equipSlot.addToSlot(equip,begin);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"等级不足");
               }
            }
         }
         else if(dragObj.name.substr(0,3) == "s1_")
         {
            begin += ItemsPanel.yeshu * 24;
            this.bag.equipBagMove(begin,end);
         }
         else
         {
            if(Main.water.getValue() != 1 && (begin == 0 || begin == 1 || begin == 3 || begin == 4))
            {
               begin += 8;
            }
            if(dragObj.name == "z1_2" || dragObj.name == "z1_5")
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"武器无法卸下");
            }
            else
            {
               this.bag.addToEquipBag(this.equipSlot.getEquipFromSlot(begin),end);
               this.equipSlot.delSlot(begin);
            }
         }
         ItemsPanel.myplayer.LoadPlayerLvData();
         BagItemsShow.equipShow();
         BagItemsShow.slotShow();
         BagItemsShow.informationShow();
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
         if(Main.newPlay == 0)
         {
            ItemsPanel.itemsPanel["xinshou"].visible = false;
         }
         return true;
      }
      
      public function gemHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var gem:Gem = null;
         this.bag = ItemsPanel.myplayer.data.getBag();
         if(dragObj.name.substr(0,3) == "s1_")
         {
            this.bag.gemBagMove(begin,end);
            BagItemsShow.gemShow();
            return true;
         }
         return false;
      }
      
      public function suppliesHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var supplies:Supplies = null;
         this.bag = ItemsPanel.myplayer.data.getBag();
         var temp:uint = uint(dragObj.currentFrame);
         if(dragObj.name.substr(0,3) == "s1_")
         {
            this.bag.suppliesBagMove(begin,end);
            BagItemsShow.suppliesShow();
            return true;
         }
         return false;
      }
      
      public function otherobjHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var otherobj:Otherobj = null;
         this.bag = ItemsPanel.myplayer.data.getBag();
         begin += ItemsPanel.yeshu * 24;
         end += ItemsPanel.yeshu * 24;
         var temp:uint = uint(dragObj.currentFrame);
         if(dragObj.name.substr(0,3) == "s1_")
         {
            this.bag.otherobjBagMove(begin,end);
            BagItemsShow.otherobjShow();
            return true;
         }
         return false;
      }
      
      private function equipKinds(id:uint) : uint
      {
         switch(id)
         {
            case 0:
               return 1;
            case 1:
               return 2;
            case 2:
               return 5;
            case 3:
               return 3;
            case 4:
               return 4;
            case 5:
               return 5;
            case 6:
               return 8;
            case 7:
               return 9;
            case 8:
               return 10;
            case 9:
               return 11;
            case 11:
               return 12;
            case 12:
               return 13;
            default:
               return null;
         }
      }
      
      public function slotHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var equip:Equip = null;
         trace("dragObj:",dragObj,begin,end);
         this.bag = ItemsPanel.myplayer.data.getBag();
         this.equipSlot = ItemsPanel.myplayer.data.getEquipSlot();
         begin += ItemsPanel.yeshu * 24;
         if(Main.water.getValue() != 1 && (end == 0 || end == 1 || end == 3 || end == 4))
         {
            end += 8;
         }
         var num:uint = uint(this.equipKinds(end));
         if(this.bag.getEquipFromBag(begin).getDressLevel() <= ItemsPanel.myplayer.data.getLevel() || this.bag.getEquipFromBag(begin).getDressLevel() == 100)
         {
            if(this.equipSlot.getEquipFromSlot(end) != null)
            {
               if(this.bag.getEquipFromBag(begin).getPosition() == num)
               {
                  equip = this.equipSlot.delSlot(end);
                  this.equipSlot.addToSlot(this.bag.getEquipFromBag(begin),end);
                  this.bag.delEquip(begin);
                  this.bag.addToEquipBag(equip,begin);
               }
               else
               {
                  if(num != 5)
                  {
                     return false;
                  }
                  if(this.bag.getEquipFromBag(begin).getPosition() == 0 || this.bag.getEquipFromBag(begin).getPosition() > 5 && this.bag.getEquipFromBag(begin).getPosition() <= 7)
                  {
                     equip = this.equipSlot.delSlot(end);
                     this.equipSlot.addToSlot(this.bag.getEquipFromBag(begin),end);
                     this.bag.delEquip(begin);
                     this.bag.addToEquipBag(equip,begin);
                  }
               }
            }
            else
            {
               if(this.bag.getEquipFromBag(begin).getPosition() != num)
               {
                  return false;
               }
               dragObj.visible = false;
               this.equipSlot.addToSlot(this.bag.getEquipFromBag(begin),end);
               this.bag.delEquip(begin);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"等级不足");
         }
         ItemsPanel.myplayer.LoadPlayerLvData();
         Player.getEquipDataXX();
         BagItemsShow.equipShow();
         BagItemsShow.slotShow();
         BagItemsShow.informationShow();
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
         return true;
      }
   }
}

