package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.repository.elves.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.pet.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.repository.title.*;
   import com.hotpoint.braveManIII.views.composePanel.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.strPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   import src.tool.*;
   
   public class MenuTooltip extends MovieClip
   {
      
      public static var selectType:uint;
      
      public static var oldNum:uint;
      
      public static var colorNum:uint;
      
      public static var whichDo:uint;
      
      public static var qixiOK:Boolean = false;
      
      public static var shizhuangOK:Boolean = false;
      
      public static var chibangOK:Boolean = false;
      
      public static var cwOK:Boolean = false;
      
      public static var cwOK_2:Boolean = false;
      
      public static var duanwuOK:Boolean = false;
      
      public static var xinglingOK:Boolean = false;
      
      public static var moneyOK:Boolean = false;
      
      public static var moneyOK2:Boolean = false;
      
      private static var menuTooltip:MovieClip = new MenuShow();
      
      private static var timetemp:int = 0;
      
      private var su:Supplies;
      
      private var amount:VT = VT.createVT(1);
      
      private var equipSlot:EquipSlot = new EquipSlot();
      
      private var skillSlot:EquipSkillSlot = new EquipSkillSlot();
      
      public function MenuTooltip()
      {
         super();
      }
      
      public static function lijifuhua() : *
      {
         var id:int = 0;
         if(moneyOK)
         {
            id = int(ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum).getValue_1());
            ItemsPanel.myplayer.data.getPetSlot().addPetSlot(PetFactory.creatPet(PetFactory.creatPet(id).getLink()));
            BagItemsShow.otherobjShow();
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"孵化成功！新宠物已加入宠物栏");
            menuTooltip["menu_9"]["fuhuaNow"].visible = true;
            moneyOK = false;
         }
      }
      
      public static function isFJ(e:MouseEvent) : *
      {
         if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getDressLevel() >= 50)
         {
            if(selectType == 1)
            {
               if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor() <= 3)
               {
                  doFenJie();
                  ItemsPanel.myplayer.data.getBag().delEquip(oldNum);
               }
               else
               {
                  ItemsPanel.itemsPanel["isFenJie"].visible = true;
                  ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isFenJie"]);
                  ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["mouseFJ"]);
               }
               BagItemsShow.equipShow();
               BagItemsShow.informationShow();
               JiHua_Interface.ppp3_14 = true;
            }
         }
         else
         {
            NewMC.Open("文字提示",ItemsPanel.itemsPanel,400,400,30,0,true,2,"装备等级不足50级");
         }
      }
      
      public static function heChengOpen(e:MouseEvent) : *
      {
         ComPosePanel.open();
      }
      
      public static function qiangHuaOpen(e:MouseEvent) : *
      {
         StrPanel.open();
      }
      
      public static function xiLianOpen(e:MouseEvent) : *
      {
         xilianPanel.open(true);
      }
      
      public static function xiangQianOpen(e:MouseEvent) : *
      {
         XiangQianPanel.open();
      }
      
      private static function ColorX(t:*, col:String) : *
      {
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         t.setTextFormat(myTextFormat);
      }
      
      private static function jianbian(e:*) : *
      {
         ++timetemp;
         ItemsPanel.itemsPanel["getFJ"].alpha -= 0.02;
         if(timetemp > 50)
         {
            ItemsPanel.itemsPanel["getFJ"].visible = false;
            ItemsPanel.itemsPanel.removeEventListener(Event.ENTER_FRAME,jianbian);
         }
      }
      
      private static function showPicAndNum(other:Otherobj, num:int) : *
      {
         timetemp = 0;
         ItemsPanel.itemsPanel["getFJ"].alpha = 1;
         ItemsPanel.itemsPanel["getFJ"].visible = true;
         ItemsPanel.itemsPanel.addEventListener(Event.ENTER_FRAME,jianbian);
         ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["getFJ"]);
         ItemsPanel.itemsPanel["getFJ"]["showPic"].gotoAndStop(other.getFrame());
         ItemsPanel.itemsPanel["getFJ"]["showPic"]["t_txt"].text = num;
      }
      
      private static function getColor1() : *
      {
         var i:int = 0;
         var rdm:int = Math.random() * 4;
         switch(rdm)
         {
            case 0:
               for(i = 0; i < 3; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63132.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63132.getValue()),3);
               break;
            case 1:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63133.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63133.getValue()),1);
               break;
            case 2:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63134.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63134.getValue()),1);
               break;
            case 3:
               for(i = 0; i < 3; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63135.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63135.getValue()),3);
         }
      }
      
      private static function getColor2() : *
      {
         var i:int = 0;
         var rdm:int = Math.random() * 4;
         switch(rdm)
         {
            case 0:
               for(i = 0; i < 6; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63132.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63132.getValue()),6);
               break;
            case 1:
               for(i = 0; i < 2; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63133.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63133.getValue()),2);
               break;
            case 2:
               for(i = 0; i < 2; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63134.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63134.getValue()),2);
               break;
            case 3:
               for(i = 0; i < 6; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63135.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63135.getValue()),6);
         }
      }
      
      private static function getColor3() : *
      {
         var rdm:int = Math.random() * 4;
         switch(rdm)
         {
            case 0:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63133.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63133.getValue()),1);
               break;
            case 1:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63134.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63134.getValue()),1);
               break;
            case 2:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63136.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63136.getValue()),1);
               break;
            case 3:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63137.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63137.getValue()),1);
         }
      }
      
      private static function getColor4() : *
      {
         var rdm:int = Math.random() * 3;
         switch(rdm)
         {
            case 0:
               for(i = 0; i < 15; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63136.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63136.getValue()),15);
               break;
            case 1:
               for(i = 0; i < 15; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63137.getValue()));
               }
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63137.getValue()),15);
               break;
            case 2:
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63138.getValue()));
               showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63138.getValue()),1);
         }
      }
      
      private static function getColor6() : *
      {
         ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.BuyNum_63138.getValue()));
         showPicAndNum(OtherFactory.creatOther(InitData.BuyNum_63138.getValue()),1);
      }
      
      public static function doFenJie() : *
      {
         switch(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor())
         {
            case 1:
               getColor1();
               break;
            case 2:
               getColor2();
               break;
            case 3:
               getColor3();
               break;
            case 4:
               getColor4();
               break;
            case 5:
               getColor4();
               break;
            case 6:
               getColor6();
         }
      }
      
      public static function xufeiRMB() : *
      {
         if(moneyOK2)
         {
            ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(oldNum).setRemainingTimeNow();
            ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(oldNum).setSysTime(Main.serverTime);
            ItemsPanel.open();
            moneyOK2 = false;
         }
      }
      
      public static function getChongWu() : *
      {
         var rdm:int = 0;
         if(cwOK)
         {
            rdm = int(Math.random() * 12);
            if(rdm == 0)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63150));
            }
            else if(rdm == 1)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
            }
            else if(rdm == 2)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
            }
            else if(rdm == 3)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
            }
            else if(rdm == 4)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63237));
            }
            else if(rdm == 5)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63237));
            }
            else if(rdm == 6)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
            }
            else if(rdm == 7)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
            }
            else if(rdm == 8)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63267));
            }
            else if(rdm == 9)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63271));
            }
            else if(rdm == 10)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63271));
            }
            else if(rdm == 11)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63271));
            }
            BagItemsShow.otherobjShow();
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得宠物蛋");
            cwOK = false;
         }
      }
      
      public static function getChongWu2() : *
      {
         var rdm:int = 0;
         if(cwOK_2)
         {
            rdm = int(Math.random() * 5);
            if(rdm == 0)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63266));
            }
            else if(rdm == 1)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63267));
            }
            else if(rdm == 2)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63251));
            }
            else if(rdm == 3)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63280));
            }
            else if(rdm == 4)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63291));
            }
            BagItemsShow.otherobjShow();
            ItemsPanel.itemsPanel["touming"].visible = false;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得宠物蛋");
            cwOK_2 = false;
         }
      }
      
      public static function getDuanWu() : *
      {
         var rdm:int = 0;
         if(duanwuOK)
         {
            rdm = int(Math.random() * 100);
            if(rdm >= 0 && rdm < 10)
            {
               ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21232));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"坚韧药剂");
            }
            else if(rdm >= 10 && rdm < 20)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"封印的星灵财宝");
            }
            else if(rdm >= 20 && rdm < 35)
            {
               ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21225));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"一捆粽子");
            }
            else if(rdm >= 35 && rdm < 40)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物蛋(冰霜狼)");
            }
            else if(rdm >= 40 && rdm < 50)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"洗练卷");
            }
            else if(rdm >= 50 && rdm < 65)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"合成添加剂");
            }
            else if(rdm >= 65 && rdm < 80)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63102));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"小礼袋");
            }
            else if(rdm >= 80 && rdm < 90)
            {
               ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31213));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"4级强化石");
            }
            else if(rdm >= 90 && rdm < 100)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"星源方块");
            }
            BagItemsShow.otherobjShow();
            ItemsPanel.itemsPanel["touming"].visible = false;
            duanwuOK = false;
         }
      }
      
      public static function getXingLing() : *
      {
         var rdm:int = 0;
         if(xinglingOK)
         {
            ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
            ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
            ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
            rdm = int(Math.random() * 120);
            if(rdm >= 0 && rdm < 10)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63162));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"巨蟹座能量-指环");
            }
            else if(rdm >= 10 && rdm < 20)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63169));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"双鱼座能量-项链");
            }
            else if(rdm >= 20 && rdm < 30)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63171));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"水瓶座能量-头饰");
            }
            else if(rdm >= 30 && rdm < 40)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63183));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"射手座能量-战甲");
            }
            else if(rdm >= 40 && rdm < 50)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63197));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"白羊座能量-武器");
            }
            else if(rdm >= 50 && rdm < 60)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63207));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金牛座能量-时装");
            }
            else if(rdm >= 60 && rdm < 70)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63246));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"天蝎座能量-指环");
            }
            else if(rdm >= 70 && rdm < 80)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63257));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"狮子座能量-项链");
            }
            else if(rdm >= 80 && rdm < 90)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63268));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"处女座能量-头饰");
            }
            else if(rdm >= 90 && rdm < 100)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63273));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"天秤座能量-战甲");
            }
            else if(rdm >= 100 && rdm < 110)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63337));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"双子座能量-武器");
            }
            else if(rdm >= 110 && rdm < 120)
            {
               for(i = 0; i < 8; ++i)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63340));
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"摩羯座能量-时装");
            }
            BagItemsShow.otherobjShow();
            ItemsPanel.itemsPanel["touming"].visible = false;
            xinglingOK = false;
         }
      }
      
      public static function getQiXi() : *
      {
         var rdm:int = 0;
         if(qixiOK)
         {
            rdm = int(Math.random() * 100);
            if(rdm >= 0 && rdm < 10)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"击杀点礼包");
            }
            else if(rdm >= 10 && rdm < 30)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"合成添加剂");
            }
            else if(rdm >= 30 && rdm < 50)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63102));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"小礼袋");
            }
            else if(rdm >= 50 && rdm < 60)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63141));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每日任务刷新卡");
            }
            else if(rdm >= 60 && rdm < 70)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"封印的星灵财宝");
            }
            else if(rdm >= 70 && rdm < 80)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"星源方块");
            }
            else if(rdm >= 80 && rdm < 85)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"随机翅膀");
            }
            else if(rdm >= 85 && rdm < 90)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"随机时装");
            }
            else if(rdm >= 90 && rdm < 95)
            {
               ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"4级强化石");
            }
            else if(rdm >= 95)
            {
               ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33314));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"高级属性石");
            }
            BagItemsShow.otherobjShow();
            ItemsPanel.itemsPanel["touming"].visible = false;
            qixiOK = false;
         }
      }
      
      public static function getChiBang() : *
      {
         var rdm:int = 0;
         if(chibangOK)
         {
            rdm = int(Math.random() * 7);
            if(rdm == 0)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14653));
            }
            else if(rdm == 1)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14646));
            }
            else if(rdm == 2)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14671));
            }
            else if(rdm == 3)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
            }
            else if(rdm == 4)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14683));
            }
            else if(rdm == 5)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14701));
            }
            else if(rdm == 6)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14719));
            }
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得翅膀，请在装备栏查看");
            ItemsPanel.itemsPanel["touming"].visible = false;
            chibangOK = false;
         }
      }
      
      public static function getShiZhuang() : *
      {
         var rdm:int = 0;
         if(shizhuangOK)
         {
            rdm = int(Math.random() * 6);
            if(rdm == 0)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14432));
            }
            else if(rdm == 1)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14450));
            }
            else if(rdm == 2)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14611));
            }
            else if(rdm == 3)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14620));
            }
            else if(rdm == 4)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14645));
            }
            else if(rdm == 5)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14650));
            }
            else if(rdm == 6)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14680));
            }
            else if(rdm == 7)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14698));
            }
            else if(rdm == 8)
            {
               ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14716));
            }
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得时装，请在装备栏查看");
            ItemsPanel.itemsPanel["touming"].visible = false;
            shizhuangOK = false;
         }
      }
      
      public function addListener() : *
      {
         this.addChild(menuTooltip);
         this.visible = false;
         ItemsPanel.itemsPanel["fhRMB"].visible = false;
         ItemsPanel.itemsPanel["xfRMB"].visible = false;
         ItemsPanel.itemsPanel["xfRMB"]["yes_btn"].addEventListener(MouseEvent.CLICK,this.yesXF);
         ItemsPanel.itemsPanel["xfRMB"]["no_btn"].addEventListener(MouseEvent.CLICK,this.noXF);
         ItemsPanel.itemsPanel["xfRMB"]["no2_btn"].addEventListener(MouseEvent.CLICK,this.noXF);
         ItemsPanel.itemsPanel["fhRMB"]["yes_btn"].addEventListener(MouseEvent.CLICK,this.yesFH);
         ItemsPanel.itemsPanel["fhRMB"]["no_btn"].addEventListener(MouseEvent.CLICK,this.noFH);
         ItemsPanel.itemsPanel["fhRMB"]["no2_btn"].addEventListener(MouseEvent.CLICK,this.noFH);
         ItemsPanel.itemsPanel["isFenJie"]["yesFJ"].addEventListener(MouseEvent.CLICK,this.yesFJ);
         ItemsPanel.itemsPanel["isFenJie"]["noFJ"].addEventListener(MouseEvent.CLICK,this.noFJ);
         ItemsPanel.itemsPanel["isFenJie"]["no2FJ"].addEventListener(MouseEvent.CLICK,this.noFJ);
         ItemsPanel.itemsPanel.addEventListener(BtnEvent.DO_CLICK,this.btnListen);
         ItemsPanel.itemsPanel["sellMenu"]["showPic"].addEventListener(MouseEvent.MOUSE_OVER,this.tipOpen);
         ItemsPanel.itemsPanel["sellMenu"]["showPic"].addEventListener(MouseEvent.MOUSE_OUT,this.tipClose);
         menuTooltip["menu_1"]["off_btn"].addEventListener(MouseEvent.CLICK,this.takeOff);
         menuTooltip["menu_11"]["ychh_btn"].addEventListener(MouseEvent.CLICK,this.ychh);
         menuTooltip["menu_12"]["xshh_btn"].addEventListener(MouseEvent.CLICK,this.xshh);
         menuTooltip["menu_2"]["wear_btn"].addEventListener(MouseEvent.CLICK,this.wear);
         menuTooltip["menu_2"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_2"]["all_Sell"].addEventListener(MouseEvent.CLICK,this.allSell);
         menuTooltip["menu_2"]["openHeCheng"].addEventListener(MouseEvent.CLICK,heChengOpen);
         menuTooltip["menu_2"]["openQiangHua"].addEventListener(MouseEvent.CLICK,qiangHuaOpen);
         menuTooltip["menu_2"]["openXiLian"].addEventListener(MouseEvent.CLICK,xiLianOpen);
         menuTooltip["menu_2"]["openXiangQian"].addEventListener(MouseEvent.CLICK,xiangQianOpen);
         menuTooltip["menu_3"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_3"]["key_btn1"].addEventListener(MouseEvent.CLICK,this.setKey_1);
         menuTooltip["menu_3"]["key_btn2"].addEventListener(MouseEvent.CLICK,this.setKey_2);
         menuTooltip["menu_3"]["key_btn3"].addEventListener(MouseEvent.CLICK,this.setKey_3);
         menuTooltip["menu_4"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_5"]["left_btn"].addEventListener(MouseEvent.CLICK,this.leftInto);
         menuTooltip["menu_5"]["right_btn"].addEventListener(MouseEvent.CLICK,this.rightInto);
         menuTooltip["menu_5"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_6"]["free_btn"].addEventListener(MouseEvent.CLICK,this.setFree);
         menuTooltip["menu_7"]["use_btn"].addEventListener(MouseEvent.CLICK,this.useOther);
         menuTooltip["menu_7"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_8"]["use_btn"].addEventListener(MouseEvent.CLICK,this.useOther);
         menuTooltip["menu_8"]["alluse_btn"].addEventListener(MouseEvent.CLICK,this.alluseOther);
         menuTooltip["menu_8"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_9"]["fuhua"].addEventListener(MouseEvent.CLICK,this.fuhua);
         menuTooltip["menu_9"]["fuhuaNow"].addEventListener(MouseEvent.CLICK,this.fuhuaNow);
         menuTooltip["menu_9"]["sell_btn"].addEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_10"]["xf_btn"].addEventListener(MouseEvent.CLICK,this.xufei);
         menuTooltip["menu_10"]["yc_btn"].addEventListener(MouseEvent.CLICK,this.yincang);
         menuTooltip["menu_10"]["xs_btn"].addEventListener(MouseEvent.CLICK,this.xianshi);
         menuTooltip["menu_10"]["off_btn"].addEventListener(MouseEvent.CLICK,this.takeOff);
      }
      
      public function removeListener() : *
      {
         ItemsPanel.itemsPanel["xfRMB"]["yes_btn"].addEventListener(MouseEvent.CLICK,this.yesXF);
         ItemsPanel.itemsPanel["xfRMB"]["no_btn"].addEventListener(MouseEvent.CLICK,this.noXF);
         ItemsPanel.itemsPanel["fhRMB"]["yes_btn"].removeEventListener(MouseEvent.CLICK,this.yesFH);
         ItemsPanel.itemsPanel["fhRMB"]["no_btn"].removeEventListener(MouseEvent.CLICK,this.noFH);
         ItemsPanel.itemsPanel["isFenJie"]["yesFJ"].removeEventListener(MouseEvent.CLICK,this.yesFJ);
         ItemsPanel.itemsPanel["isFenJie"]["noFJ"].removeEventListener(MouseEvent.CLICK,this.noFJ);
         ItemsPanel.itemsPanel.removeEventListener(BtnEvent.DO_CLICK,this.btnListen);
         ItemsPanel.itemsPanel["sellMenu"]["showPic"].removeEventListener(MouseEvent.MOUSE_OVER,this.tipOpen);
         ItemsPanel.itemsPanel["sellMenu"]["showPic"].removeEventListener(MouseEvent.MOUSE_OUT,this.tipClose);
         menuTooltip["menu_1"]["off_btn"].removeEventListener(MouseEvent.CLICK,this.takeOff);
         menuTooltip["menu_11"]["ychh_btn"].removeEventListener(MouseEvent.CLICK,this.ychh);
         menuTooltip["menu_12"]["xshh_btn"].removeEventListener(MouseEvent.CLICK,this.xshh);
         menuTooltip["menu_2"]["wear_btn"].removeEventListener(MouseEvent.CLICK,this.wear);
         menuTooltip["menu_2"]["sell_btn"].removeEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_2"]["all_Sell"].removeEventListener(MouseEvent.CLICK,this.allSell);
         menuTooltip["menu_2"]["openHeCheng"].removeEventListener(MouseEvent.CLICK,heChengOpen);
         menuTooltip["menu_2"]["openQiangHua"].removeEventListener(MouseEvent.CLICK,qiangHuaOpen);
         menuTooltip["menu_2"]["openXiLian"].removeEventListener(MouseEvent.CLICK,xiLianOpen);
         menuTooltip["menu_2"]["openXiangQian"].removeEventListener(MouseEvent.CLICK,xiangQianOpen);
         menuTooltip["menu_3"]["sell_btn"].removeEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_3"]["key_btn1"].removeEventListener(MouseEvent.CLICK,this.setKey_1);
         menuTooltip["menu_3"]["key_btn2"].removeEventListener(MouseEvent.CLICK,this.setKey_2);
         menuTooltip["menu_3"]["key_btn3"].removeEventListener(MouseEvent.CLICK,this.setKey_3);
         menuTooltip["menu_4"]["sell_btn"].removeEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_5"]["left_btn"].removeEventListener(MouseEvent.CLICK,this.leftInto);
         menuTooltip["menu_5"]["right_btn"].removeEventListener(MouseEvent.CLICK,this.rightInto);
         menuTooltip["menu_5"]["sell_btn"].removeEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_6"]["free_btn"].removeEventListener(MouseEvent.CLICK,this.setFree);
         menuTooltip["menu_7"]["use_btn"].removeEventListener(MouseEvent.CLICK,this.useOther);
         menuTooltip["menu_7"]["sell_btn"].removeEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_8"]["use_btn"].removeEventListener(MouseEvent.CLICK,this.useOther);
         menuTooltip["menu_8"]["alluse_btn"].removeEventListener(MouseEvent.CLICK,this.alluseOther);
         menuTooltip["menu_8"]["sell_btn"].removeEventListener(MouseEvent.CLICK,this.sell);
         menuTooltip["menu_9"]["fuhua"].removeEventListener(MouseEvent.CLICK,this.fuhua);
         menuTooltip["menu_9"]["fuhuaNow"].removeEventListener(MouseEvent.CLICK,this.fuhuaNow);
         menuTooltip["menu_10"]["xf_btn"].removeEventListener(MouseEvent.CLICK,this.xufei);
         menuTooltip["menu_10"]["yc_btn"].removeEventListener(MouseEvent.CLICK,this.yincang);
         menuTooltip["menu_10"]["xs_btn"].removeEventListener(MouseEvent.CLICK,this.xianshi);
         menuTooltip["menu_10"]["off_btn"].removeEventListener(MouseEvent.CLICK,this.takeOff);
      }
      
      public function setItemsMenu(type:uint, num:uint, whichPlayerData:uint) : *
      {
         whichDo = whichPlayerData;
         selectType = type;
         oldNum = num;
         menuTooltip["menu_1"].visible = false;
         menuTooltip["menu_2"].visible = false;
         menuTooltip["menu_3"].visible = false;
         menuTooltip["menu_4"].visible = false;
         menuTooltip["menu_5"].visible = false;
         menuTooltip["menu_6"].visible = false;
         menuTooltip["menu_7"].visible = false;
         menuTooltip["menu_8"].visible = false;
         menuTooltip["menu_9"].visible = false;
         menuTooltip["menu_10"].visible = false;
         menuTooltip["menu_11"].visible = false;
         menuTooltip["menu_12"].visible = false;
         switch(selectType)
         {
            case 1:
               menuTooltip["menu_2"].visible = true;
               break;
            case 2:
               menuTooltip["menu_3"].visible = true;
               break;
            case 3:
               if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getType() == 4)
               {
                  menuTooltip["menu_5"].visible = true;
               }
               else if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getType() == 5 || ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getType() == 6)
               {
                  menuTooltip["menu_7"].visible = true;
               }
               else
               {
                  menuTooltip["menu_4"].visible = true;
               }
               break;
            case 4:
               if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIsUse() == 1)
               {
                  menuTooltip["menu_7"].visible = true;
               }
               else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIsUse() == 0)
               {
                  menuTooltip["menu_4"].visible = true;
               }
               else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIsUse() == 3)
               {
                  menuTooltip["menu_8"].visible = true;
               }
               else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIsUse() == 4)
               {
                  menuTooltip["menu_9"].visible = true;
               }
               break;
            case 5:
               menuTooltip["menu_4"].visible = true;
         }
      }
      
      private function tipOpen(e:MouseEvent) : void
      {
         ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsTooltip);
         ItemsPanel.itemsTooltip.x = ItemsPanel.itemsPanel.mouseX;
         ItemsPanel.itemsTooltip.y = ItemsPanel.itemsPanel.mouseY;
         ItemsPanel.itemsTooltip.gemTooltip(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum));
         ItemsPanel.itemsTooltip.visible = true;
      }
      
      private function tipClose(e:MouseEvent) : void
      {
         ItemsPanel.itemsTooltip.visible = false;
      }
      
      private function getPart(num:Number) : uint
      {
         switch(num)
         {
            case 0:
               return 2;
            case 1:
               return 0;
            case 2:
               return 1;
            case 3:
               return 3;
            case 4:
               return 4;
            case 5:
               return 2;
            case 6:
               return 2;
            case 7:
               return 2;
            case 8:
               return 6;
            case 9:
               return 7;
            case 10:
               return 8;
            case 11:
               return 9;
            case 12:
               return 11;
            case 13:
               return 12;
            default:
               return null;
         }
      }
      
      public function setSlotMenu(num:uint, whichPlayerData:uint) : *
      {
         oldNum = num;
         whichDo = whichPlayerData;
         menuTooltip["menu_1"].visible = false;
         menuTooltip["menu_2"].visible = false;
         menuTooltip["menu_3"].visible = false;
         menuTooltip["menu_4"].visible = false;
         menuTooltip["menu_5"].visible = false;
         menuTooltip["menu_6"].visible = false;
         menuTooltip["menu_7"].visible = false;
         menuTooltip["menu_8"].visible = false;
         menuTooltip["menu_9"].visible = false;
         menuTooltip["menu_10"].visible = false;
         menuTooltip["menu_11"].visible = false;
         menuTooltip["menu_12"].visible = false;
         if(oldNum < 6)
         {
            menuTooltip["menu_1"].visible = true;
            menuTooltip["menu_10"].visible = false;
         }
         else
         {
            menuTooltip["menu_1"].visible = false;
            menuTooltip["menu_10"].visible = true;
            if(oldNum == 6)
            {
               if(Main["player_" + whichDo].skin_Z2_V)
               {
                  menuTooltip["menu_10"]["yc_btn"].visible = true;
               }
               else
               {
                  menuTooltip["menu_10"]["yc_btn"].visible = false;
               }
            }
            else if(oldNum == 7)
            {
               if(Main["player_" + whichDo].skin_Z3_V)
               {
                  menuTooltip["menu_10"]["yc_btn"].visible = true;
               }
               else
               {
                  menuTooltip["menu_10"]["yc_btn"].visible = false;
               }
            }
            if(Main["player" + whichDo].getEquipSlot().getEquipFromSlot(oldNum).getColor() >= 4)
            {
               menuTooltip["menu_10"]["xf_btn"].visible = false;
            }
            else
            {
               menuTooltip["menu_10"]["xf_btn"].visible = true;
            }
         }
         if(oldNum == 2 || oldNum == 5)
         {
            menuTooltip["menu_1"].visible = false;
            menuTooltip["menu_10"].visible = false;
            if(ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(oldNum).getHuanHua() == 0)
            {
               menuTooltip["menu_11"].visible = false;
            }
            else if(ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(oldNum).getHHVisible() == 0)
            {
               menuTooltip["menu_11"].visible = true;
               menuTooltip["menu_12"].visible = false;
            }
            else
            {
               menuTooltip["menu_11"].visible = false;
               menuTooltip["menu_12"].visible = true;
            }
         }
      }
      
      public function setSkillSlotMenu(num:uint, whichPlayerData:uint) : *
      {
         oldNum = num;
         whichDo = whichPlayerData;
         menuTooltip["menu_1"].visible = false;
         menuTooltip["menu_2"].visible = false;
         menuTooltip["menu_3"].visible = false;
         menuTooltip["menu_4"].visible = false;
         menuTooltip["menu_5"].visible = false;
         menuTooltip["menu_6"].visible = true;
         menuTooltip["menu_7"].visible = false;
         menuTooltip["menu_8"].visible = false;
         menuTooltip["menu_9"].visible = false;
         menuTooltip["menu_10"].visible = false;
         menuTooltip["menu_11"].visible = false;
         menuTooltip["menu_12"].visible = false;
      }
      
      private function bagNoBug() : *
      {
         if(whichDo == 1)
         {
            ItemsPanel.isPOne = true;
            ItemsPanel.itemsPanel["bagOne"].isClick = true;
         }
         else
         {
            ItemsPanel.isPOne = false;
            ItemsPanel.itemsPanel["bagTwo"].isClick = true;
         }
      }
      
      public function fuhua(e:*) : *
      {
         var id:int = 0;
         if(ItemsPanel.myplayer.data.getPetSlot().backPetSlotNum() > 0)
         {
            id = int(ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum).getValue_1());
            ItemsPanel.myplayer.data.getPetSlot().addPetSlot(PetFactory.creatPet(id));
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物栏空间不足");
         }
         BagItemsShow.otherobjShow();
      }
      
      public function fuhuaNow(e:*) : *
      {
         ItemsPanel.itemsPanel["fhRMB"].visible = true;
         ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["fhRMB"]);
      }
      
      public function yesFH(e:*) : *
      {
         if(ItemsPanel.myplayer.data.getPetSlot().backPetSlotNum() <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物栏空间不足");
            return;
         }
         Api_4399_All.BuyObj(InitData.fuHua.getValue());
         menuTooltip["menu_9"]["fuhuaNow"].visible = false;
         moneyOK = true;
         ItemsPanel.itemsPanel["fhRMB"].visible = false;
      }
      
      public function noFH(e:*) : *
      {
         ItemsPanel.itemsPanel["fhRMB"].visible = false;
      }
      
      public function wear(e:MouseEvent) : *
      {
         var equip:Equip = null;
         trace("穿戴装备");
         this.equipSlot = ItemsPanel.myplayer.data.getEquipSlot();
         var buweiX:* = ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getPosition();
         if(buweiX <= 4 && Main.water.getValue() != 1 || buweiX >= 10 && Main.water.getValue() == 1)
         {
            if(Main.water.getValue() == 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"只有在失落大陆才能穿戴");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"只有在勇者大陆才能穿戴");
            }
            return;
         }
         var part:uint = uint(this.getPart(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getPosition()));
         if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getDressLevel() <= ItemsPanel.myplayer.data.getLevel() || ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getDressLevel() == 100)
         {
            if(this.equipSlot.getEquipFromSlot(part) == null)
            {
               this.equipSlot.addToSlot(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum),part);
               ItemsPanel.myplayer.data.getBag().delEquip(oldNum);
            }
            else
            {
               equip = this.equipSlot.delSlot(part);
               this.equipSlot.addToSlot(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum),part);
               ItemsPanel.myplayer.data.getBag().delEquip(oldNum);
               ItemsPanel.myplayer.data.getBag().addToEquipBag(equip,oldNum);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"等级不足");
         }
         ItemsPanel.myplayer.LoadPlayerLvData();
         Player.getEquipDataXX();
         BagItemsShow.informationShow();
         BagItemsShow.equipShow();
         BagItemsShow.slotShow();
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
         if(Main.newPlay == 0)
         {
            ItemsPanel.itemsPanel["xinshou"].visible = false;
         }
      }
      
      public function ychh(e:MouseEvent) : *
      {
         ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(oldNum).changeHH(1);
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
      }
      
      public function xshh(e:MouseEvent) : *
      {
         ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(oldNum).changeHH(0);
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
      }
      
      public function takeOff(e:MouseEvent) : *
      {
         this.equipSlot = ItemsPanel.myplayer.data.getEquipSlot();
         if(Main.water.getValue() != 1 && (oldNum == 0 || oldNum == 1 || oldNum == 3 || oldNum == 4))
         {
            oldNum += 8;
         }
         if(ItemsPanel.myplayer.data.getBag().backequipBagNum() > 0)
         {
            ItemsPanel.myplayer.data.getBag().addEquipBag(this.equipSlot.getEquipFromSlot(oldNum));
            this.equipSlot.delSlot(oldNum);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         Player.getEquipDataXX();
         ItemsPanel.allFalse();
         ItemsPanel.itemsPanel["bg1_1"].isClick = true;
         ItemsPanel.itemsType = 1;
         BagItemsShow.equipShow();
         BagItemsShow.slotShow();
         BagItemsShow.informationShow();
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
      }
      
      public function leftInto(e:MouseEvent) : *
      {
         var gem:Gem = null;
         this.skillSlot = ItemsPanel.myplayer.data.getEquipSkillSlot();
         if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getUseLevel() <= ItemsPanel.myplayer.data.getLevel())
         {
            if(this.skillSlot.getGemFromSkillSlot(0) == null)
            {
               this.skillSlot.addToSkillSlot(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum),0);
               ItemsPanel.myplayer.data.getBag().delGem(oldNum,1);
            }
            else
            {
               gem = this.skillSlot.delSkillSlot(0);
               this.skillSlot.addToSkillSlot(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum),0);
               ItemsPanel.myplayer.data.getBag().delGem(oldNum,1);
               ItemsPanel.myplayer.data.getBag().addToGemBag(gem,oldNum);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
         ItemsPanel.myplayer.energySlot.energyLeftNum.setValue(0);
         ItemsPanel.myplayer.energySlot.energyLeftMax.setValue(SkillFactory.getSkillById(this.skillSlot.getGemFromSkillSlot(0).getGemSkill()).getEp());
         BagItemsShow.skillSlotShow();
         BagItemsShow.gemShow();
      }
      
      public function rightInto(e:MouseEvent) : *
      {
         var gem:Gem = null;
         this.skillSlot = ItemsPanel.myplayer.data.getEquipSkillSlot();
         if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getUseLevel() <= ItemsPanel.myplayer.data.getLevel())
         {
            if(this.skillSlot.getGemFromSkillSlot(1) == null)
            {
               this.skillSlot.addToSkillSlot(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum),1);
               ItemsPanel.myplayer.data.getBag().delGem(oldNum,1);
            }
            else
            {
               gem = this.skillSlot.delSkillSlot(1);
               this.skillSlot.addToSkillSlot(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum),1);
               ItemsPanel.myplayer.data.getBag().delGem(oldNum,1);
               ItemsPanel.myplayer.data.getBag().addToGemBag(gem,oldNum);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         ItemsPanel.changeModel(ItemsPanel.myplayer.data);
         ItemsPanel.myplayer.energySlot.energyRightMax.setValue(SkillFactory.getSkillById(this.skillSlot.getGemFromSkillSlot(1).getGemSkill()).getEp());
         ItemsPanel.myplayer.energySlot.energyRightNum.setValue(0);
         BagItemsShow.skillSlotShow();
         BagItemsShow.gemShow();
      }
      
      public function setFree(e:MouseEvent) : *
      {
         var gem:Gem = null;
         this.skillSlot = ItemsPanel.myplayer.data.getEquipSkillSlot();
         if(ItemsPanel.myplayer.data.getBag().backGemBagNum() > 0)
         {
            ItemsPanel.myplayer.data.getBag().addGemBag(this.skillSlot.delSkillSlot(oldNum));
            if(oldNum == 0)
            {
               ItemsPanel.myplayer.energySlot.energyLeftMax.setValue(0);
               ItemsPanel.myplayer.energySlot.energyLeftNum.setValue(0);
            }
            else
            {
               ItemsPanel.myplayer.energySlot.energyRightMax.setValue(0);
               ItemsPanel.myplayer.energySlot.energyRightNum.setValue(0);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         BagItemsShow.skillSlotShow();
         BagItemsShow.gemShow();
         ItemsPanel.allFalse();
         ItemsPanel.itemsPanel["bg1_3"].isClick = true;
         ItemsPanel.itemsType = 3;
      }
      
      public function allSell(e:MouseEvent) : *
      {
         if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor() == 1)
         {
            colorNum = 1;
            ItemsPanel.itemsPanel["isAllSell"].visible = true;
            ItemsPanel.itemsPanel["isAllSell"]["selltxt"].text = "是否确定出售所有的白色品质装备";
            ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isAllSell"]);
         }
         if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor() == 2)
         {
            colorNum = 2;
            ItemsPanel.itemsPanel["isAllSell"].visible = true;
            ItemsPanel.itemsPanel["isAllSell"]["selltxt"].text = "是否确定出售所有的蓝色品质装备";
            ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isAllSell"]);
         }
         if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor() == 3)
         {
            colorNum = 3;
            ItemsPanel.itemsPanel["isAllSell"].visible = true;
            ItemsPanel.itemsPanel["isAllSell"]["selltxt"].text = "是否确定出售所有的粉色品质装备";
            ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isAllSell"]);
         }
         if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor() >= 4)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金色品质装备无法一键出售");
         }
      }
      
      public function sell(e:MouseEvent) : *
      {
         if(selectType == 1)
         {
            if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getColor() <= 2)
            {
               ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getPrice());
               ItemsPanel.myplayer.data.getBag().delEquip(oldNum);
            }
            else
            {
               ItemsPanel.itemsPanel["isSell"].visible = true;
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isSell"]);
            }
            BagItemsShow.equipShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 2)
         {
            if(ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(oldNum).getColor() <= 2)
            {
               ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(oldNum).getPrice());
               ItemsPanel.myplayer.data.getBag().delSupplies(oldNum);
            }
            else
            {
               ItemsPanel.itemsPanel["isSell"].visible = true;
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isSell"]);
            }
            BagItemsShow.suppliesShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 3)
         {
            if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getTimes() > 1)
            {
               ItemsPanel.itemsPanel["sellMenu"].visible = true;
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["sellMenu"]);
               this.amount.setValue(1);
               ItemsPanel.itemsPanel["sellMenu"]["amount"].text = this.amount.getValue();
               ItemsPanel.itemsPanel["sellMenu"]["showPic"].gotoAndStop(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getFrame());
               ItemsPanel.itemsPanel["sellMenu"]["showPic"]["t_txt"].visible = false;
            }
            else if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getColor() <= 2)
            {
               ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getPrice());
               ItemsPanel.myplayer.data.getBag().delGem(oldNum,1);
            }
            else
            {
               ItemsPanel.itemsPanel["isSell"].visible = true;
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isSell"]);
            }
            BagItemsShow.gemShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 4)
         {
            if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getTimes() > 1)
            {
               ItemsPanel.itemsPanel["sellMenu"].visible = true;
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["sellMenu"]);
               this.amount.setValue(1);
               ItemsPanel.itemsPanel["sellMenu"]["amount"].text = this.amount.getValue();
               ItemsPanel.itemsPanel["sellMenu"]["showPic"].gotoAndStop(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getFrame());
               ItemsPanel.itemsPanel["sellMenu"]["showPic"]["t_txt"].visible = false;
            }
            else
            {
               ItemsPanel.itemsPanel["isSell"].visible = true;
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isSell"]);
            }
         }
         if(selectType == 5)
         {
            ItemsPanel.itemsPanel["isSell"].visible = true;
            ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["isSell"]);
         }
      }
      
      private function noAllSell() : *
      {
         this.bagNoBug();
         ItemsPanel.itemsPanel["isAllSell"].visible = false;
      }
      
      private function yesAllSell(color:int = 1) : *
      {
         this.bagNoBug();
         for(var i:* = 0; i < 48; i++)
         {
            if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(i) != null)
            {
               if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(i).getColor() == color && ItemsPanel.myplayer.data.getBag().getEquipFromBag(i).getPosition() <= 7)
               {
                  ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getEquipFromBag(i).getPrice());
                  ItemsPanel.myplayer.data.getBag().delEquip(i);
               }
            }
         }
         BagItemsShow.equipShow();
         BagItemsShow.informationShow();
         ItemsPanel.itemsPanel["isAllSell"].visible = false;
      }
      
      private function yesHandle() : *
      {
         this.bagNoBug();
         if(selectType == 1)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getEquipFromBag(oldNum).getPrice());
            ItemsPanel.myplayer.data.getBag().delEquip(oldNum);
            BagItemsShow.equipShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 2)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(oldNum).getPrice());
            ItemsPanel.myplayer.data.getBag().delSupplies(oldNum);
            BagItemsShow.suppliesShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 3)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getPrice());
            ItemsPanel.myplayer.data.getBag().delGem(oldNum,1);
            BagItemsShow.gemShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 4)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getGold());
            ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            BagItemsShow.otherobjShow();
            BagItemsShow.informationShow();
         }
         if(selectType == 5)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getQuestFromBag(oldNum).getGold());
            ItemsPanel.myplayer.data.getBag().delQuset(oldNum);
            BagItemsShow.questShow();
            BagItemsShow.informationShow();
         }
         ItemsPanel.itemsPanel["isSell"].visible = false;
      }
      
      public function btnListen(e:BtnEvent) : *
      {
         var btn:MovieClip = e.target as MovieClip;
         switch(btn.name)
         {
            case "yesBtn_All":
               this.yesAllSell(colorNum);
               break;
            case "noBtn_All":
               this.noAllSell();
               break;
            case "closeAll":
               this.noAllSell();
               break;
            case "yesBtn":
               this.yesHandle();
               break;
            case "noBtn":
               this.bagNoBug();
               ItemsPanel.itemsPanel["isSell"].visible = false;
               break;
            case "noBtn2":
               this.bagNoBug();
               ItemsPanel.itemsPanel["isSell"].visible = false;
               break;
            case "sellUp":
               this.setUp();
               break;
            case "sellDown":
               this.setDown();
               break;
            case "sellBtn":
               this.sellBatch();
               this.bagNoBug();
               break;
            case "closeSellMenu":
               this.bagNoBug();
               ItemsPanel.itemsPanel["sellMenu"].visible = false;
         }
      }
      
      private function sellBatch() : void
      {
         if(selectType == 3)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getPrice() * this.amount.getValue());
            ItemsPanel.myplayer.data.getBag().delGem(oldNum,this.amount.getValue());
            BagItemsShow.gemShow();
            BagItemsShow.informationShow();
         }
         else if(selectType == 4)
         {
            ItemsPanel.myplayer.data.addGold(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getGold() * this.amount.getValue());
            ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,this.amount.getValue());
            BagItemsShow.otherobjShow();
            BagItemsShow.informationShow();
         }
         ItemsPanel.itemsPanel["sellMenu"].visible = false;
      }
      
      private function setUp() : void
      {
         if(selectType == 3)
         {
            if(this.amount.getValue() == ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getTimes())
            {
               this.amount.setValue(1);
            }
            else
            {
               this.amount.setValue(this.amount.getValue() + 1);
            }
         }
         else if(selectType == 4)
         {
            if(this.amount.getValue() == ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getTimes())
            {
               this.amount.setValue(1);
            }
            else
            {
               this.amount.setValue(this.amount.getValue() + 1);
            }
         }
         ItemsPanel.itemsPanel["sellMenu"]["amount"].text = this.amount.getValue();
      }
      
      private function setDown() : void
      {
         ItemsPanel.itemsPanel["sellMenu"]["amount"].text = this.amount.getValue();
         if(selectType == 3)
         {
            if(this.amount.getValue() == 1)
            {
               this.amount.setValue(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getTimes());
            }
            else
            {
               this.amount.setValue(this.amount.getValue() - 1);
            }
         }
         else if(selectType == 4)
         {
            if(this.amount.getValue() == 1)
            {
               this.amount.setValue(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getTimes());
            }
            else
            {
               this.amount.setValue(this.amount.getValue() - 1);
            }
         }
         ItemsPanel.itemsPanel["sellMenu"]["amount"].text = this.amount.getValue();
      }
      
      public function alluseOther(e:*) : *
      {
         if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 11)
         {
            while(Boolean(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum)) && ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getTimes() > 0)
            {
               if(ItemsPanel.myplayer.data.getLevel() > Player.maxLevel)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"等级达到上限,无法继续使用");
                  return;
               }
               if(ItemsPanel.myplayer.hp.getValue() > 0)
               {
                  ItemsPanel.myplayer.ExpUP(InitData.EXP_num.getValue(),2);
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功");
               }
            }
         }
         else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 31)
         {
            while(Boolean(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum)) && ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getTimes() > 0)
            {
               if(ItemsPanel.myplayer.data.getLevel() > Player.maxLevel)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"等级达到上限,无法继续使用");
                  return;
               }
               if(ItemsPanel.myplayer.hp.getValue() > 0)
               {
                  ItemsPanel.myplayer.ExpUP(InitData.EXP_big_num.getValue(),2);
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功");
               }
            }
         }
         else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 9)
         {
            while(Boolean(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum)) && ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getTimes() > 0)
            {
               ItemsPanel.myplayer.data.killPoint.setValue(ItemsPanel.myplayer.data.killPoint.getValue() + InitData.BuyNum_300.getValue());
               ItemsPanel.myplayer.data.addGold(InitData.BuyNum_80000.getValue());
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               Main.Save();
            }
         }
         BagItemsShow.otherobjShow();
         BagItemsShow.informationShow();
      }
      
      public function yesFJ(e:*) : *
      {
         ItemsPanel.itemsPanel["isFenJie"].visible = false;
         if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
         {
            doFenJie();
            ItemsPanel.myplayer.data.getBag().delEquip(oldNum);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         BagItemsShow.equipShow();
         BagItemsShow.informationShow();
      }
      
      public function noFJ(e:*) : *
      {
         ItemsPanel.itemsPanel["isFenJie"].visible = false;
      }
      
      public function setKey_1(e:MouseEvent) : *
      {
         this.su = ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(oldNum);
         ItemsPanel.myplayer.data.getSuppliesSlot().setToSuppliesSlot(this.su,0);
      }
      
      public function setKey_2(e:MouseEvent) : *
      {
         this.su = ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(oldNum);
         ItemsPanel.myplayer.data.getSuppliesSlot().setToSuppliesSlot(this.su,1);
      }
      
      public function setKey_3(e:MouseEvent) : *
      {
         this.su = ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(oldNum);
         ItemsPanel.myplayer.data.getSuppliesSlot().setToSuppliesSlot(this.su,2);
      }
      
      public function yesXF(e:*) : *
      {
         Api_4399_All.BuyObj(InitData.fuHua.getValue());
         moneyOK2 = true;
         ItemsPanel.itemsPanel["xfRMB"].visible = false;
      }
      
      public function noXF(e:*) : *
      {
         ItemsPanel.itemsPanel["xfRMB"].visible = false;
      }
      
      public function xufei(e:MouseEvent) : *
      {
         ItemsPanel.itemsPanel["xfRMB"].visible = true;
         ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["xfRMB"]);
      }
      
      public function xianshi(e:MouseEvent) : *
      {
         if(oldNum == 6 && Main["player_" + whichDo].skin_Z2_V == false)
         {
            Main["player_" + whichDo].skin_Z2_V = true;
         }
         else if(oldNum == 7 && Main["player_" + whichDo].skin_Z3_V == false)
         {
            Main["player_" + whichDo].skin_Z3_V = true;
         }
         (Main["player_" + whichDo] as Player).noHead = false;
         Main["player_" + whichDo].newSkin();
      }
      
      public function yincang(e:MouseEvent) : *
      {
         if(oldNum == 6 && Main["player_" + whichDo].skin_Z2_V == true)
         {
            Main["player_" + whichDo].skin_Z2_V = false;
         }
         else if(oldNum == 7 && Main["player_" + whichDo].skin_Z3_V == true)
         {
            Main["player_" + whichDo].skin_Z3_V = false;
         }
         Main["player_" + whichDo].newSkin();
      }
      
      public function useOther(e:MouseEvent) : *
      {
         var i:int = 0;
         var rdm:int = 0;
         var jinbi:int = 0;
         var jishadian:int = 0;
         var arrStr:Array = null;
         var strjiequ:String = null;
         var x129:int = 0;
         var x150:int = 0;
         var x135:int = 0;
         var skn:uint = 0;
         var skstr:String = null;
         var star:int = 0;
         var usstr:String = null;
         if(selectType == 4)
         {
            if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 4)
            {
               InitData.EXPxTimeUp(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getRemaining());
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               Main.Save();
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 5)
            {
               InitData.DOWNxTimeUp(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getRemaining());
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               Main.Save();
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 7)
            {
               MakeData.open(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId());
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功，解锁了该物品的使用权限");
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 9)
            {
               ItemsPanel.myplayer.data.killPoint.setValue(ItemsPanel.myplayer.data.killPoint.getValue() + InitData.BuyNum_300.getValue());
               ItemsPanel.myplayer.data.addGold(InitData.BuyNum_80000.getValue());
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               Main.Save();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得300击杀点,80000金币");
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 10)
            {
               TaskData.otherUpdataTask();
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每日任务重置成功");
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 11)
            {
               if(ItemsPanel.myplayer.data.getLevel() <= Player.maxLevel)
               {
                  if(ItemsPanel.myplayer.hp.getValue() > 0)
                  {
                     ItemsPanel.myplayer.ExpUP(InitData.EXP_num.getValue(),2);
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功");
                  }
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 31)
            {
               if(ItemsPanel.myplayer.data.getLevel() <= Player.maxLevel)
               {
                  if(ItemsPanel.myplayer.hp.getValue() > 0)
                  {
                     ItemsPanel.myplayer.ExpUP(InitData.EXP_big_num.getValue(),2);
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功");
                  }
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 17)
            {
               QiangHuaPanel.open(true,oldNum);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 18)
            {
               ItemsPanel.myplayer.data.getBadgeSlot().addToSlot(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum),0);
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 19)
            {
               ItemsPanel.myplayer.data.getBadgeSlot().addToSlot(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum),1);
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 20)
            {
               ItemsPanel.myplayer.data.getBadgeSlot().addToSlot(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum),2);
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 21)
            {
               ItemsPanel.myplayer.data.getBadgeSlot().addToSlot(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum),3);
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 22)
            {
               ItemsPanel.myplayer.data.getBadgeSlot().addToSlot(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum),4);
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 23)
            {
               ItemsPanel.myplayer.data.getBadgeSlot().addToSlot(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum),5);
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 24)
            {
               if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId() == 63303 && (!PK_UI.jiFenArr[6] || PK_UI.jiFenArr[6] <= 0))
               {
                  Main.NoGame();
                  return;
               }
               if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId() == 63303 && PK_UI.jiFenArr[6] > 0)
               {
                  --PK_UI.jiFenArr[6];
               }
               ItemsPanel.myplayer.data.getTitleSlot().addToSlot(TitleFactory.creatTitle(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getValue_1()));
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               ItemsPanel.myplayer.cengHao_mc.Show(1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 25)
            {
               ItemsPanel.myplayer.data.killPoint.setValue(ItemsPanel.myplayer.data.killPoint.getValue() + InitData.Temp50.getValue());
               ItemsPanel.myplayer.data.addGold(InitData.BuyNum_18000.getValue());
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得50击杀点,18000金币");
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 27)
            {
               if(ItemsPanel.myplayer.data.getElvesSlot().backElvesSlotNum() > 0)
               {
                  ItemsPanel.myplayer.data.getElvesSlot().addElvesSlot(ElvesFactory.creatElves(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getValue_1()));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵已进入精灵槽，点击精灵面板查看");
                  ItemsPanel.itemsPanel["jinglingOPEN"].visible = true;
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵槽已满，请开启精灵界面扩充");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 29)
            {
               UpgradePanel.open(ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 26)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 4)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                  }
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 28)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  }
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63195));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 32)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 9)
               {
                  for(i = 0; i < 10; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13524));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13424));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13724));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13624));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14311));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14211));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14111));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14450));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 34)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 4)
               {
                  for(i = 0; i < 22; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                  }
                  for(i = 0; i < 8; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  for(i = 0; i < 12; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  for(i = 0; i < 1; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63234));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 194)
            {
               trace("打开 194自选时装礼盒");
               ChongZhi_Interface4.openBoX(194,ItemsPanel.itemsPanel,ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 195)
            {
               trace("打开 195自选翅膀礼盒");
               ChongZhi_Interface4.openBoX(195,ItemsPanel.itemsPanel,ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 196)
            {
               trace("打开 196自选幻化券礼盒");
               ChongZhi_Interface4.openBoX(196,ItemsPanel.itemsPanel,ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 197)
            {
               trace("打开 197自选精灵礼盒");
               ChongZhi_Interface4.openBoX(197,ItemsPanel.itemsPanel,ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 198)
            {
               trace("打开 198自选宠物礼盒");
               ChongZhi_Interface4.openBoX(198,ItemsPanel.itemsPanel,ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 199)
            {
               trace("打开 199自选星灵礼盒");
               ChongZhi_Interface4.openBoX(199,ItemsPanel.itemsPanel,ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 200)
            {
               trace("打开 200随机红色神权利器礼包");
               ChongZhi_Interface4.openBoX_200(ItemsPanel.myplayer.data);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 92)
            {
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["fudai3"]);
               ItemsPanel.itemsPanel["fudai3"].visible = true;
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 35)
            {
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["fudai"]);
               ItemsPanel.itemsPanel["fudai"].visible = true;
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 39)
            {
               ItemsPanel.itemsPanel.addChild(ItemsPanel.itemsPanel["fudai2"]);
               ItemsPanel.itemsPanel["fudai2"].visible = true;
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 37)
            {
               ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63237));
               for(i = 0; i < 10; i++)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
               }
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 38)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63242));
                  for(i = 0; i < 10; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 33)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 6 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 6)
               {
                  for(i = 0; i < 23; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                  }
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  }
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63106));
                  }
                  for(i = 0; i < 10; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13427));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13527));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13627));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(13727));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14452));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14435));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 40)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63243));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14643));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14644));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 41)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 6 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2)
               {
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63162));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63169));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63171));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63183));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63197));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63207));
                  }
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 42)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 5)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14649));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14652));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 43)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  }
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14650));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14653));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 44)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14650));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14653));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 45)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21116));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21216));
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33512));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14667));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14668));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14669));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 46)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33512));
                  }
                  rdm = int(Math.random() * 9);
                  if(rdm == 0)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14432));
                  }
                  else if(rdm == 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14433));
                  }
                  else if(rdm == 2)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14450));
                  }
                  else if(rdm == 3)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14611));
                  }
                  else if(rdm == 4)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14620));
                  }
                  else if(rdm == 5)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14645));
                  }
                  else if(rdm == 6)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14646));
                  }
                  else if(rdm == 7)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14650));
                  }
                  else if(rdm == 8)
                  {
                     ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14653));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 48)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  for(i = 0; i < 10; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31216));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14611));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14671));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 49)
            {
               rdm = int(Math.random() * 2);
               if(rdm == 0)
               {
                  jinbi = 30000 + Math.random() * 70000;
                  ItemsPanel.myplayer.data.addGold(jinbi);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得" + jinbi + "金币！");
               }
               else
               {
                  jishadian = 200 + Math.random() * 300;
                  ItemsPanel.myplayer.data.AddKillPoint(jishadian);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得" + jishadian + "击杀点!");
               }
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               Main.Save();
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 55)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 5 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63263));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63262));
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 56)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 9)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63162));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63169));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63171));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63183));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63197));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63207));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63246));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63257));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63268));
                  }
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"消耗栏需要2格空间，宝石栏需要1格空间，其他栏需要9格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 57)
            {
               Yayale_LingQue.DuiHuanOpen(ItemsPanel.myplayer);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 58)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"消耗栏需要2格空间，其他栏需要2格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 60)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 4)
               {
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21225));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  Main.Save();
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"消耗栏需要4格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 61)
            {
               jishadian = 100;
               ItemsPanel.myplayer.data.AddKillPoint(jishadian);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得" + jishadian + "击杀点!");
               ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               Main.Save();
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 64)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  cwOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 80)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  cwOK_2 = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 103)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  duanwuOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 113)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  xinglingOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 128)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 5)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  xinglingOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 105)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  qixiOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 65)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 5 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 4 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63141));
                  for(i = 0; i < 4; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                  }
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 68)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14452));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21216));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21116));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 69)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 70)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63243));
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21228));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21228));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 74)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14113));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14213));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14313));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 75)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.AddKillPoint(300);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63294));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 76)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14612));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14621));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 77)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(120000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 78)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21229));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21232));
                  PaiHang_Data.jiFenArr[1].setValue(PaiHang_Data.jiFenArr[1].getValue() + 5);
                  PaiHang_Data.jiFenArr[2].setValue(PaiHang_Data.jiFenArr[2].getValue() + 5);
                  PaiHang_Data.jiFenArr[3].setValue(PaiHang_Data.jiFenArr[3].getValue() + 5);
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 124)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 124)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 125)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 126)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 127)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 82)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 83)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63160));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 84)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 85)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63342));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 86)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 87)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.AddKillPoint(300);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 88)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63323));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 89)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(120000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 106)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 107)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 108)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63160));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 109)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 114)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 115)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 116)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 6)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,需要6格消耗栏，1格其他栏");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 117)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33511));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33511));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 130)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 131)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(30000);
                  ItemsPanel.myplayer.data.killPoint.setValue(ItemsPanel.myplayer.data.killPoint.getValue() + 300);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 132)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63368));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63368));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 133)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63326));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63326));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 136)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 137)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 138)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 6)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 139)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63342));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 140)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 141)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 142)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 143)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 146)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 147)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 148)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 149)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 153)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 154)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 155)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 156)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 110)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(30000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63138));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 90)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14643));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14644));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 98)
            {
               if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  for(i = 0; i < 4; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31216));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 93)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21232));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 94)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  for(i = 0; i < 10; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  }
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21232));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 81)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                  }
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63106));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63106));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14671));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,其他栏需要3格，装备栏需要1格，宝石栏需要1格");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 51)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 1)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  chibangOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备栏空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 52)
            {
               if(ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 1)
               {
                  ItemsPanel.itemsPanel["touming"].visible = true;
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  shizhuangOK = true;
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备栏空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 53)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 4)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63260));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63261));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63262));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63263));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 71)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  rdm = int(Math.random() * 9);
                  if(rdm == 0)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 复活药 ");
                  }
                  else if(rdm == 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 野性之血 ");
                  }
                  else if(rdm == 2)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31210));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 1级强化石");
                  }
                  else if(rdm == 3)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31211));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 2级强化石");
                  }
                  else if(rdm == 4)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31212));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 3级强化石");
                  }
                  else if(rdm == 5)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 封印的星灵财宝 ");
                  }
                  else if(rdm == 6)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 经验药水");
                  }
                  else if(rdm == 7)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 合成添加剂");
                  }
                  else if(rdm == 8)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21230));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得 疯狂月饼");
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 73)
            {
               arrStr = ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIntroduction().split("#");
               strjiequ = arrStr[1];
               HuanHuaPanel.open(ItemsPanel.myplayer.data,ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId(),strjiequ);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 79)
            {
               arrStr = ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIntroduction().split("#");
               strjiequ = arrStr[1];
               HuanHuaPanel.open(ItemsPanel.myplayer.data,ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId(),strjiequ);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 95)
            {
               arrStr = ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIntroduction().split("#");
               strjiequ = arrStr[1];
               HuanHuaPanel.open(ItemsPanel.myplayer.data,ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId(),strjiequ);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 119)
            {
               arrStr = ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIntroduction().split("#");
               strjiequ = arrStr[1];
               HuanHuaPanel.open(ItemsPanel.myplayer.data,ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId(),strjiequ);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 192)
            {
               arrStr = ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getIntroduction().split("#");
               strjiequ = arrStr[1];
               HuanHuaPanel.open(ItemsPanel.myplayer.data,ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getId(),strjiequ);
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 96)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  }
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,其他栏需要2格，宝石栏需要1格");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 97)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63255));
                  for(i = 0; i < 8; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14680));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14683));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,其他栏需要2格，装备栏需要2格，宝石栏需要1格");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 101)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,其他栏需要3格，宝石栏需要1格");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 102)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 5)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21225));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,消耗栏需要5格空间，其他栏需要3格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 118)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14702));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14699));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,消耗栏需要1格空间，其他栏需要1格空间，宝石栏需要1格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 120)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,消耗栏需要1格空间，其他栏需要1格空间，宝石栏需要1格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 121)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  for(i = 0; i < 2; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
                  }
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  for(i = 0; i < 10; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63102));
                  }
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,消耗栏需要1格空间，其他栏需要2格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 122)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  }
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63342));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足, 其他栏需要3格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 123)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  for(i = 0; i < 3; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  }
                  for(i = 0; i < 5; i++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  }
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足, 需要消耗栏1格,其他栏1格,宝石栏1格");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 111)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31213));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,消耗栏需要1格空间，其他栏需要1格空间，宝石栏需要1格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 129)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  for(x129 = 0; x129 < 10; x129++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 134)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 4)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  for(x129 = 0; x129 < 3; x129++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  }
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 144)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 4)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  for(x129 = 0; x129 < 3; x129++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  }
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 150)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  for(x150 = 0; x150 < 10; x150++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 151)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 157)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 4)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 152)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 4)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 145)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 135)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 5 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  for(x135 = 0; x135 < 10; x135++)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  }
                  for(x135 = 0; x135 < 5; x135++)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                  }
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 112)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63266));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,其他栏需要1格空间");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 163)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 175)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 158)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 159)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 160)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 161)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(120000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 162)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(150000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63311));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63369));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 164)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 165)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 166)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 4)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 167)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(120000);
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63141));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 168)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(150000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63311));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 174)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backequipBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14752));
                  ItemsPanel.myplayer.data.getBag().addEquipBag(EquipFactory.createEquipByID(14755));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 169)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 170)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 171)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 172)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(120000);
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21224));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 173)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(150000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 176)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 177)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 178)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 179)
            {
               if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(120000);
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 180)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(150000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 181)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 182)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 183)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 184)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 185)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(150000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 187)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2)
               {
                  ItemsPanel.myplayer.data.addGold(50000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 188)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(60000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 189)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(80000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 190)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.addGold(100000);
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21226));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 191)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 3)
               {
                  ItemsPanel.myplayer.data.addGold(150000);
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63204));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63352));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63353));
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 186)
            {
               if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 2 && ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1 && ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
               {
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63210));
                  ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                  ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63140));
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功，物品已放入背包");
                  ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(oldNum).getType() == 50)
            {
               rdm = int(Math.random() * 18);
               if(rdm == 0)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63203).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 1)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33213));
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(33213).getName());
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 2)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63211));
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63211).getName());
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 3)
               {
                  if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21222));
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + SuppliesFactory.getSuppliesById(21222).getName());
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 4)
               {
                  if(ItemsPanel.myplayer.data.getBag().backSuppliesBagNum() >= 3)
                  {
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                     ItemsPanel.myplayer.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + SuppliesFactory.getSuppliesById(21221).getName());
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 5)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63105));
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63105).getName());
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 6)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63106));
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63106).getName());
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 7)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63100));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63100).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 8)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63155));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63155).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 9)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63147));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63147).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 10)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63141));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63141).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 11)
               {
                  if(ItemsPanel.myplayer.data.getBag().backOtherBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                     ItemsPanel.myplayer.data.getBag().addOtherobjBag(OtherFactory.creatOther(63235));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + OtherFactory.creatOther(63235).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 12)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31214));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31214));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31214));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(31214).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 13)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31215));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31215));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31215));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(31215).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 14)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31216));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31216));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31216));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(31216).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 15)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(31217));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(31217).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 16)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33512));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33512));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(33512).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
               else if(rdm == 17)
               {
                  if(ItemsPanel.myplayer.data.getBag().backGemBagNum() >= 1)
                  {
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33511));
                     ItemsPanel.myplayer.data.getBag().addGemBag(GemFactory.creatGemById(33511));
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"恭喜你获得 " + GemFactory.creatGemById(33511).getName());
                     ItemsPanel.myplayer.data.getBag().delOtherobj(oldNum,1);
                     Main.Save();
                  }
                  else
                  {
                     NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
                  }
               }
            }
            BagItemsShow.badgeSlotShow();
            BagItemsShow.otherobjShow();
            BagItemsShow.informationShow();
         }
         else if(selectType == 3)
         {
            if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getType() == 5)
            {
               skn = uint(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getGemSkill());
               skstr = ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getDescript();
               if(ItemsPanel.myplayer == Main.player_1)
               {
                  zengfuPanel.open(true,skstr,skn,oldNum);
               }
               else
               {
                  zengfuPanel.open(false,skstr,skn,oldNum);
               }
            }
            else if(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getType() == 6)
            {
               star = int(ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getStrengthenLevel());
               usstr = ItemsPanel.myplayer.data.getBag().getGemFromBag(oldNum).getDescript();
               if(ItemsPanel.myplayer == Main.player_1)
               {
                  upstarPanel.open(true,usstr,star,oldNum);
               }
               else
               {
                  upstarPanel.open(false,usstr,star,oldNum);
               }
            }
         }
      }
   }
}

