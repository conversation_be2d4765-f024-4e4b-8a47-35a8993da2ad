package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class InWaterDoor extends MovieClip
   {
      
      private static var iwd:InWaterDoor;
      
      public var youLing_btn:SimpleButton;
      
      public var go_btn:SimpleButton;
      
      public var close_btn:SimpleButton;
      
      public function InWaterDoor()
      {
         super();
         this.go_btn.addEventListener(MouseEvent.CLICK,this.gotoWaterWorld);
         this.youLing_btn.addEventListener(MouseEvent.CLICK,this.GoYouLing);
         this.close_btn.addEventListener(MouseEvent.CLICK,this.closed);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         if(!iwd)
         {
            classRef = All_Npc.loadData.getClass("src.npc.InWaterDoor") as Class;
            xxMov = new classRef();
            iwd = xxMov;
         }
         Main._stage.addChild(iwd);
         iwd.x = xx;
         iwd.y = yy;
         iwd.visible = true;
      }
      
      public static function Close() : *
      {
         if(!iwd)
         {
            iwd = new InWaterDoor();
         }
         iwd.x = 5000;
         iwd.y = 5000;
         iwd.visible = false;
      }
      
      private function closed(e:*) : *
      {
         Close();
      }
      
      private function gotoWaterWorld(e:*) : *
      {
         Main.water.setValue(Math.random() * 999 + 1);
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.Loading();
         Close();
      }
      
      private function GoYouLing(e:*) : *
      {
         Main.gameNum.setValue(7000);
         Main.gameNum2.setValue(1);
         GameData.gameLV = 1;
         Close();
         Main._this.Loading();
      }
   }
}

