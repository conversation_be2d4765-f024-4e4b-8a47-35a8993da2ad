package src.tool
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   public class PaiHang_Data
   {
      
      public static var now_pID:int;
      
      public static var now_pID_0:int;
      
      public static var now_pID_x:int;
      
      public static var showTime:String;
      
      public static var saveXX:Array;
      
      public static var newTime:int = 0;
      
      public static var inGameNum:Array = [-1,0,0,0,0,0,0,0,0,0];
      
      public static var sArr2:Array = new Array();
      
      public static var jiFenArr:Array = new Array();
      
      public static var daLu:int = 0;
      
      public static var paiHangArr:Array = new Array();
      
      public static var gameNum_x1_1:Array = [2212,2218,2220,2222,2224,2226,2228,2230,2232,2234];
      
      public static var gameNum_x2_1:Array = [2213,2219,2221,2223,2225,2227,2229,2231,2233,2235];
      
      public static var gameNum_x1_2:Array = [2214,2236,2238,2240,2242,2244,2246,2248];
      
      public static var gameNum_x2_2:Array = [2215,2237,2239,2241,2243,2245,2247,2249];
      
      public static var gameNum_x1_3:Array = [2210,2250,2252,2254,2256,2258,2260,2262,2264,2266,2268,2270,2272];
      
      public static var gameNum_x2_3:Array = [2211,2251,2253,2255,2257,2259,2261,2263,2265,2267,2269,2271,2273];
      
      public static var gameNum_x1_4:Array = [2216,2274,2276,2278,2280,2282,2284,2286,2288,2290,2292,2294,2296];
      
      public static var gameNum_x2_4:Array = [2217,2275,2277,2279,2281,2283,2285,2287,2289,2291,2293,2295,2297];
      
      public static var gameNum_x1_0:Array = [2208];
      
      public static var gameNum_x2_0:Array = [2209];
      
      public static var time:uint = 0;
      
      public static var sNum:VT = VT.createVT();
      
      public static var sNumX:int = 0;
      
      public static var myXml:XML = new XML();
      
      public static var dataArr:Array = [];
      
      public static var myXml2:XML = new XML();
      
      public static var AllData:Array = new Array();
      
      public function PaiHang_Data()
      {
         super();
      }
      
      public static function creatFactory() : *
      {
         var property:XML = null;
         var Xid:uint = 0;
         myXml2 = XMLAsset.createXML(Data2.duiHuanData);
         for each(property in myXml2.兑换神权利器)
         {
            Xid = uint(property.序号);
            AllData[Xid] = new Array();
            AllData[Xid][0] = VT.createVT(Number(property.物品ID));
            AllData[Xid][1] = int(property.帧数);
            AllData[Xid][2] = VT.createVT(Number(property.概率));
         }
      }
      
      public static function TimeStart() : String
      {
         if(time > 0)
         {
            --time;
         }
         var timex:int = int(time);
         var temp:int = timex / 27;
         var s1:int = temp / 3600;
         var s2:int = (temp - s1 * 3600) / 60;
         var s3:int = (temp - s1 * 3600) % 60;
         var s4:int = (timex - (s1 * 3600 + s2 * 60 + s3) * 27) * 37;
         var s2Str:String = String(s2);
         if(s2 < 10)
         {
            s2Str = "0" + s2;
         }
         var s3Str:String = String(s3);
         if(s3 < 10)
         {
            s3Str = "0" + s3;
         }
         var s4Str:String = String(s4).substr(0,2);
         var str:String = s2Str + ":" + s3Str + "." + s4;
         showTime = str;
         return str;
      }
      
      public static function EnemyOver() : int
      {
         var num:int = 0;
         if(time <= 0)
         {
            return;
         }
         if(GameData.gameLV == 5)
         {
            num = Math.random() * InitData.tNum_100.getValue();
            if(num < 2)
            {
               ++sNumX;
               sNum.setValue(sNum.getValue() + InitData.tNum_300.getValue());
               NewMC.Open("文字提示",Main._stage,470,350,30,0,true,2,"获得300积分");
               TiaoShi.txtShow("积分球:300");
               return 300;
            }
            if(num < 5)
            {
               ++sNumX;
               sNum.setValue(sNum.getValue() + InitData.tNum_200.getValue());
               NewMC.Open("文字提示",Main._stage,470,350,30,0,true,2,"获得200积分");
               TiaoShi.txtShow("积分球:200");
               return 200;
            }
            if(num < 10)
            {
               ++sNumX;
               sNum.setValue(sNum.getValue() + InitData.tNum_100.getValue());
               NewMC.Open("文字提示",Main._stage,470,350,30,0,true,2,"获得100积分");
               TiaoShi.txtShow("积分球:100");
               return 100;
            }
         }
      }
      
      public static function All_0() : *
      {
         if(Boolean(PaiHang_Data.dataArr[Main.gameNum.getValue()]) && Main.gameNum2.getValue() == 1)
         {
            time = (PaiHang_Data.dataArr[Main.gameNum.getValue()][1] as VT).getValue();
            sNum = VT.createVT();
            sNumX = 0;
         }
      }
      
      public static function InitDataX() : *
      {
         var property:XML = null;
         var id:uint = 0;
         for each(property in myXml.挑战关卡)
         {
            id = uint(property.关卡);
            dataArr[id] = new Array();
            dataArr[id][0] = VT.createVT(Number(property.金币));
            dataArr[id][1] = VT.createVT(Number(property.时间));
         }
      }
      
      public static function InitSave() : *
      {
         var i:int = 0;
         if(inGameNum[0] < Main.serverTime.getValue())
         {
            TiaoShi.txtShow("InitSave() serverTime = " + Main.serverTime.getValue());
            inGameNum[0] = Main.serverTime.getValue();
            for(i = 1; i <= 62; i++)
            {
               inGameNum[i] = 2;
            }
         }
         var timeXXX:int = Main.serverTime.getValue() / 100;
         if(newTime != timeXXX)
         {
            TiaoShi.txtShow("重置挑战排行榜:" + newTime + "," + timeXXX);
            sArr2 = new Array();
            newTime = timeXXX;
         }
         if(!sArr2[0])
         {
            sArr2[0] = new Array();
         }
         for(i = 1; i <= 9; i++)
         {
            if(!sArr2[0][i])
            {
               sArr2[0][i] = VT.createVT();
            }
         }
         if(!sArr2[1])
         {
            sArr2[1] = new Array();
         }
         for(i = 10; i <= 16; i++)
         {
            if(!sArr2[1][i])
            {
               sArr2[1][i] = VT.createVT();
            }
         }
         if(!sArr2[2])
         {
            sArr2[2] = new Array();
         }
         for(i = 51; i <= 62; i++)
         {
            if(!sArr2[2][i])
            {
               sArr2[2][i] = VT.createVT();
            }
         }
         if(!sArr2[4])
         {
            sArr2[4] = new Array();
         }
         for(i = 18; i <= 29; i++)
         {
            if(!sArr2[4][i])
            {
               sArr2[4][i] = VT.createVT();
            }
         }
         for(i = 0; i < 5; i++)
         {
            if(!jiFenArr[i])
            {
               jiFenArr[i] = VT.createVT();
            }
         }
      }
      
      private static function SelDaLu() : int
      {
         var xx:int = int(Main.gameNum.getValue());
         if(xx >= 1 && xx <= 9)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆1 ");
            return 0;
         }
         if(xx >= 10 && xx <= 16)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆2 ");
            return 1;
         }
         if(xx >= 51 && xx <= 62)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆3 ");
            return 2;
         }
         if(xx >= 18 && xx <= 29)
         {
            TiaoShi.txtShow("SelDaLu() 提交大陆4 ");
            return 4;
         }
         return 3;
      }
      
      public static function Show() : Array
      {
         var timeS:int = 0;
         var xArr:Array = new Array();
         TiaoShi.txtShow("Show~~~~~~~~1");
         var kouFen:uint = 0;
         TiaoShi.txtShow("Show~~~~~~~~2");
         if(Main.player_1.playerCW)
         {
            if(Main.player_1.playerCW.data.getPetEquip())
            {
               if(Main.player_1.playerCW.data.getPetEquip().getType() == 15)
               {
                  WinShow.txt_3 -= Main.player_1.playerCW.data.getPetEquip().getColor();
                  if(WinShow.txt_3 < 0)
                  {
                     WinShow.txt_3 = 0;
                  }
               }
               if(Main.player_1.playerCW.data.getPetEquip().getType() == 16)
               {
                  time += Main.player_1.playerCW.data.getPetEquip().getColor() * 27;
               }
            }
         }
         if(Boolean(Main.P1P2) && Boolean(Main.player_2.playerCW))
         {
            if(Main.player_2.playerCW.data.getPetEquip())
            {
               if(Main.player_2.playerCW.data.getPetEquip().getType() == 15)
               {
                  WinShow.txt_3 -= Main.player_2.playerCW.data.getPetEquip().getColor();
                  if(WinShow.txt_3 < 0)
                  {
                     WinShow.txt_3 = 0;
                  }
               }
               if(Main.player_2.playerCW.data.getPetEquip().getType() == 16)
               {
                  time += Main.player_2.playerCW.data.getPetEquip().getColor() * 27;
               }
            }
         }
         timeS = time * 37;
         TiaoShi.txtShow("Show~~~~~~~~3");
         if(WinShow.txt_3 > 0 && WinShow.txt_3 < 50)
         {
            kouFen = (timeS + sNum.getValue()) * WinShow.txt_3 / 100;
         }
         else if(WinShow.txt_3 > 50 && WinShow.txt_3 < 999999)
         {
            kouFen = (timeS + sNum.getValue()) * 50 / 100;
         }
         TiaoShi.txtShow("Show~~~~~~~~4");
         TiaoShi.txtShow("剩余时间:" + time);
         TiaoShi.txtShow("时间积分:" + timeS);
         TiaoShi.txtShow("关卡积分:" + sNum.getValue());
         TiaoShi.txtShow("被击扣分:" + kouFen);
         var all:uint = timeS + sNum.getValue() - kouFen;
         TiaoShi.txtShow("~~~~~~~~关卡积分:" + all + "\n");
         trace("挑战积分>>>>>>>>>>>>>>>>",timeS,sNum.getValue(),kouFen,all);
         if(sArr2[daLu][Main.gameNum.getValue()])
         {
            TiaoShi.txtShow("~~~~~~~~之前最高分:" + (sArr2[daLu][Main.gameNum.getValue()] as VT).getValue() + "\n");
         }
         daLu = SelDaLu();
         if(all > (sArr2[daLu][Main.gameNum.getValue()] as VT).getValue())
         {
            xArr[8] = true;
            sArr2[daLu][Main.gameNum.getValue()] = VT.createVT(all);
         }
         else
         {
            xArr[8] = false;
         }
         TiJiaoFun(all);
         xArr[0] = TimeStart();
         xArr[1] = timeS;
         xArr[2] = sNumX;
         xArr[3] = sNum.getValue();
         xArr[4] = WinShow.txt_3;
         xArr[5] = kouFen;
         xArr[6] = sArr2[daLu][Main.gameNum.getValue()].getValue();
         xArr[7] = all;
         return xArr;
      }
      
      private static function TiJiaoFun(all:int) : *
      {
         var allXXX:int = 0;
         var i:int = 0;
         var allXXX2:int = 0;
         Sel_now_pID();
         var arr:Array = new Array();
         var pId:int = int(PaiHang_Data.now_pID);
         arr[0] = new Object();
         arr[0].rId = pId;
         arr[0].score = int(all);
         arr[0].extra = Main.player_1.paiHangShow;
         if(Main.tiaoShiYN)
         {
            arr[0].score = int(all / 3);
         }
         var pId2:int = int(PaiHang_Data.now_pID_0);
         var arr2:Array = new Array();
         for(i = 1; i <= 100; i++)
         {
            if(sArr2[daLu][i])
            {
               allXXX += sArr2[daLu][i].getValue();
            }
         }
         arr2[0] = new Object();
         arr2[0].rId = pId2;
         arr2[0].score = int(allXXX);
         arr2[0].extra = Main.player_1.paiHangShow;
         if(Main.tiaoShiYN)
         {
            arr2[0].score = int(allXXX / 3);
         }
         var pId3:int = int(PaiHang_Data.now_pID_x);
         var arr3:Array = new Array();
         for(var i2:int = 0; i2 <= 4; i2++)
         {
            if(i2 != 3)
            {
               for(i = 1; i <= 100; i++)
               {
                  if(sArr2[i2][i])
                  {
                     allXXX2 += sArr2[i2][i].getValue();
                  }
               }
            }
         }
         arr3[0] = new Object();
         arr3[0].rId = pId3;
         arr3[0].score = int(allXXX2);
         arr3[0].extra = Main.player_1.paiHangShow;
         if(Main.tiaoShiYN)
         {
            arr3[0].score = int(allXXX2 / 3);
         }
         TiaoShi.txtShow("提交成绩:\n" + pId + "关卡积分" + arr[0].score + "\n" + pId2 + "大陆积分" + arr2[0].score + "\n" + pId3 + "总积分" + arr3[0].score);
         Api_4399_All.SubmitScore(Main.saveNum,arr);
         Api_4399_All.SubmitScore(Main.saveNum,arr2);
         Api_4399_All.SubmitScore(Main.saveNum,arr3);
         paiHangArr[pId] = null;
         paiHangArr[pId2] = null;
         paiHangArr[pId3] = null;
      }
      
      private static function Sel_now_pID() : *
      {
         var pId:int = 0;
         var p1p2:int = 0;
         if(Main.P1P2)
         {
            p1p2 = 2;
         }
         else
         {
            p1p2 = 1;
         }
         var xxNum:int = 0;
         var xID:int = 0;
         var xx:int = int(Main.gameNum.getValue());
         if(xx >= 1 && xx <= 9)
         {
            xxNum = 1;
            xID = xx;
         }
         if(xx >= 10 && xx <= 16)
         {
            xxNum = 2;
            xID = xx - 9;
         }
         if(xx >= 51 && xx <= 62)
         {
            xxNum = 3;
            xID = xx - 50;
         }
         if(xx >= 18 && xx <= 29)
         {
            xxNum = 4;
            xID = xx - 17;
         }
         now_pID_x = PaiHang_Data["gameNum_x" + p1p2 + "_0"][0];
         now_pID_0 = PaiHang_Data["gameNum_x" + p1p2 + "_" + xxNum][0];
         now_pID = PaiHang_Data["gameNum_x" + p1p2 + "_" + xxNum][xID];
         TiaoShi.txtShow("Sel_now_pID:\n" + "gameNum_x" + p1p2 + "_" + xxNum + ", xID" + xID);
      }
      
      public static function getJiFen(daLu:int, numX:int, fenShu:int) : *
      {
         if(daLu <= 0 || numX <= 0)
         {
            return;
         }
         if(daLu == 2)
         {
            numX += 9;
         }
         if(daLu == 3)
         {
            numX += 50;
         }
         if(daLu == 4)
         {
            numX += 17;
         }
         daLu--;
         sArr2[daLu][numX] = VT.createVT(fenShu);
         TiaoShi.txtShow("查询排行榜分数 替换存档分数" + daLu + " ? " + numX + " ? " + fenShu);
      }
      
      public static function DataAry1(dataAry:Array, paiHangId:int) : *
      {
         var i:int = 0;
         if(!PaiHang_Data.paiHangArr[paiHangId])
         {
            PaiHang_Data.paiHangArr[paiHangId] = new Array();
         }
         for(i in dataAry)
         {
            PaiHang_Data.paiHangArr[paiHangId][i + 1] = dataAry[i];
         }
      }
      
      public static function DataAry2(dataAry:Array, paiHangId:int) : *
      {
         var i:int = 0;
         var tmpObj:Object = null;
         if(!PaiHang_Data.paiHangArr[paiHangId])
         {
            PaiHang_Data.paiHangArr[paiHangId] = new Array();
         }
         if(dataAry != null && dataAry.length != 0)
         {
            for(i in dataAry)
            {
               tmpObj = dataAry[i];
               if(int(tmpObj.index) == Main.saveNum)
               {
                  PaiHang_Data.paiHangArr[paiHangId][0] = tmpObj;
                  return;
               }
            }
         }
      }
   }
}

