package src
{
   import com.ByteArrayXX.*;
   import com.hotpoint.braveManIII.views.chunjiePanel.*;
   import com.hotpoint.braveManIII.views.elvesPanel.*;
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.fanpaiPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.stampPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import com.hotpoint.braveManIII.views.temaiPanel.*;
   import com.hotpoint.braveManIII.views.tuijianPanel.*;
   import com.hotpoint.braveManIII.views.wantedPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src._data.*;
   import src.tool.*;
   import unit4399.events.*;
   
   public class Api_4399_All
   {
      
      private static var buy_Obj:Object;
      
      public static var tempArr:Array;
      
      public static var sel_PaiHang_Over:Boolean = false;
      
      public static var totalRecharged_Arr:Array = new Array();
      
      private static var selYN:Boolean = false;
      
      public static var xxxArr:Array = new Array();
      
      public static var PaiMing_Arr:Array = new Array();
      
      public static var ycTime:int = 0;
      
      private static var GetUserData_Arr:Array = new Array();
      
      public static var getObjX:uint = 1;
      
      public function Api_4399_All()
      {
         super();
      }
      
      public static function Init(_stage:Stage) : *
      {
         PayMoneyVar.getInstance();
         _stage.addEventListener("usePayApi",onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.LOG,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.INC_MONEY,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.DEC_MONEY,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.GET_MONEY,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.PAY_MONEY,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.PAIED_MONEY,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.RECHARGED_MONEY,onPayEventHandler,false,0,true);
         _stage.addEventListener(PayEvent.PAY_ERROR,onPayEventHandler,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_ERROR,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_DEL_SUCC,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_GET_PACKAGEINFO,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_UPDATE_EXTEND,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_ADDFREE_SUCC,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_UPDATEPRO_SUCC,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_GET_FREEPACKAGEINFO,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_ADD_SUCC,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_GET_PAYPACKAGEINFO,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_GET_TYPENOTICE,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_MODIFY_EX,onShopHandler2,false,0,true);
         _stage.addEventListener(ShopEvent.SHOP_ERROR_ND,onShopEventHandler);
         _stage.addEventListener(ShopEvent.SHOP_BUY_ND,onShopEventHandler);
         _stage.addEventListener(ShopEvent.SHOP_GET_LIST,onShopEventHandler);
         _stage.addEventListener(RankListEvent.RANKLIST_ERROR,onRankListErrorHandler);
         _stage.addEventListener(RankListEvent.RANKLIST_SUCCESS,onRankListSuccessHandler);
      }
      
      private static function onPayEventHandler(e:PayEvent) : void
      {
         var date:Object = null;
         switch(e.type)
         {
            case "logsuccess":
               break;
            case "incMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  Money_sel_Num(e.data.balance);
                  Shop4399.moneyAll.setValue(e.data.balance);
               }
               break;
            case "decMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  Money_sel_Num(e.data.balance);
                  Shop4399.moneyAll.setValue(e.data.balance);
               }
               break;
            case "getMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  Shop4399.moneyAll.setValue(e.data.balance);
                  Money_sel_Num(e.data.balance);
               }
               break;
            case "payMoney":
               break;
            case "paiedMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  Shop4399.totalPaiedMoney.setValue(e.data.balance);
               }
               break;
            case "rechargedMoney":
               if(e.data !== null && !(e.data is Boolean))
               {
                  if(totalRecharged_Arr[0] == 0)
                  {
                     Shop4399.totalRecharged.setValue(e.data.balance);
                     Shop_LiBao.selYN = Shop_LiBao.selYN2 = false;
                  }
                  else if(totalRecharged_Arr[0] == 1)
                  {
                     Shop_LiBao.HDpoint.setValue(e.data.balance);
                  }
                  else if(totalRecharged_Arr[0] == 2)
                  {
                     ChongZhi_Interface.HDpoint2.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint2 = " + ChongZhi_Interface.HDpoint2.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 3)
                  {
                     ChongZhi_Interface.HDpoint3.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint3 = " + ChongZhi_Interface.HDpoint3.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 4)
                  {
                     ChongZhi_Interface.HDpoint4.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint4 = " + ChongZhi_Interface.HDpoint4.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 5)
                  {
                     ChongZhi_Interface.HDpoint5.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint5 = " + ChongZhi_Interface.HDpoint5.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 6)
                  {
                     ChongZhi_Interface.HDpoint6.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint6 = " + ChongZhi_Interface.HDpoint6.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 8)
                  {
                     ChongZhi_Interface.HDpoint8.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint8 = " + ChongZhi_Interface.HDpoint8.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 9)
                  {
                     ChongZhi_Interface.HDpoint9.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint9 = " + ChongZhi_Interface.HDpoint9.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 10)
                  {
                     ChongZhi_Interface.HDpoint10.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint10 = " + ChongZhi_Interface.HDpoint10.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 11)
                  {
                     ChongZhi_Interface.HDpoint11.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint11 = " + ChongZhi_Interface.HDpoint11.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 12)
                  {
                     ChongZhi_Interface.HDpoint12.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint12 = " + ChongZhi_Interface.HDpoint12.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 13)
                  {
                     ChongZhi_Interface.HDpoint13.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint13 = " + ChongZhi_Interface.HDpoint13.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 14)
                  {
                     ChongZhi_Interface.HDpointNUM.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint14 = " + ChongZhi_Interface.HDpointNUM.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 15)
                  {
                     ChongZhi_Interface4.HDpointNUM15.setValue(e.data.balance);
                     ChongZhi_Interface4.Show();
                     TiaoShi.txtShow("HDpoint15 = " + ChongZhi_Interface4.HDpointNUM15.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 16)
                  {
                     ChongZhi_Interface.HDpointNUM.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint16 = " + ChongZhi_Interface.HDpointNUM.getValue());
                  }
                  else if(totalRecharged_Arr[0] == 17)
                  {
                     ChongZhi_Interface.HDpointNUM.setValue(e.data.balance);
                     ChongZhi_Interface.Show();
                     TiaoShi.txtShow("HDpoint17 = " + ChongZhi_Interface.HDpointNUM.getValue());
                  }
                  totalRecharged_Arr.shift();
                  if(totalRecharged_Arr.length > 0)
                  {
                     if(totalRecharged_Arr[0] == 0)
                     {
                        Main.serviceHold.getTotalRechargedFun();
                     }
                     else if(totalRecharged_Arr[0] == 1)
                     {
                        date = new Object();
                        date.eDate = "2012-08-09|17:00:00";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 2)
                     {
                        date = new Object();
                        date.sDate = "2014-01-23|23:59:00";
                        date.eDate = "2014-02-10|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 3)
                     {
                        date = new Object();
                        date.sDate = "2014-04-17|23:59:00";
                        date.eDate = "2014-05-04|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 4)
                     {
                        date = new Object();
                        date.sDate = "2014-05-28|23:59:00";
                        date.eDate = "2014-06-08|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 5)
                     {
                        date = new Object();
                        date.sDate = "2014-07-03|23:59:00";
                        date.eDate = "2014-07-20|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 6)
                     {
                        date = new Object();
                        date.sDate = "2014-08-07|23:59:00";
                        date.eDate = "2014-08-31|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 8)
                     {
                        date = new Object();
                        date.sDate = "2015-02-14|23:59:00";
                        date.eDate = "2015-02-28|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 9)
                     {
                        date = new Object();
                        date.sDate = "2015-04-26|23:59:00";
                        date.eDate = "2015-05-15|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 10)
                     {
                        date = new Object();
                        date.sDate = "2015-12-24|20:59:00";
                        date.eDate = "2016-01-10|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 11)
                     {
                        date = new Object();
                        date.sDate = "2016-02-03|00:00:01";
                        date.eDate = "2016-02-25|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 12)
                     {
                        date = new Object();
                        date.sDate = "2016-01-23|00:00:01";
                        date.eDate = "2016-02-15|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 13)
                     {
                        date = new Object();
                        date.sDate = "2020-04-30|00:00:01";
                        date.eDate = "2020-05-31|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 14)
                     {
                        date = new Object();
                        date.sDate = "2022-04-14|00:00:01";
                        date.eDate = "2022-05-15|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 15)
                     {
                        date = new Object();
                        date.sDate = ChongZhi_Interface4.time0;
                        date.eDate = ChongZhi_Interface4.time1;
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 16)
                     {
                        date = new Object();
                        date.sDate = "2023-07-03|00:00:01";
                        date.eDate = "2023-07-16|23:59:59";
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else if(totalRecharged_Arr[0] == 17)
                     {
                        date = new Object();
                        date.sDate = ChongZhi_Interface.time0;
                        date.eDate = ChongZhi_Interface.time1;
                        Main.serviceHold.getTotalRechargedFun(date);
                     }
                     else
                     {
                        TiaoShi.XXX.push("查询条件错误?!");
                     }
                  }
                  if(Shop_LiBao.shopX)
                  {
                     Shop_LiBao.shopX.ShowPoint();
                  }
                  if(Shop_LiBao2.shopX)
                  {
                     Shop_LiBao2.shopX.ShowPoint();
                  }
               }
               break;
            case "payError":
               if(e.data == null)
               {
                  break;
               }
               if((e.data.info as String).substr(0,1) == "4")
               {
                  Shop4399.NoMoney_info_Open();
               }
               break;
         }
      }
      
      public static function GetTotalRecharged(type:int = 0) : *
      {
         var date:Object = null;
         if(Main.serviceHold)
         {
            totalRecharged_Arr.push(type);
            if(totalRecharged_Arr.length == 1)
            {
               if(type == 0)
               {
                  Main.serviceHold.getTotalRechargedFun();
               }
               else if(type == 1)
               {
                  date = new Object();
                  date.eDate = "2012-08-09|17:00:00";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(type == 2)
               {
                  date = new Object();
                  date.sDate = "2014-01-23|23:59:59";
                  date.eDate = "2014-02-16|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(type == 3)
               {
                  date = new Object();
                  date.sDate = "2014-04-17|23:59:00";
                  date.eDate = "2014-05-04|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 4)
               {
                  date = new Object();
                  date.sDate = "2014-05-28|23:59:00";
                  date.eDate = "2014-06-08|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 5)
               {
                  date = new Object();
                  date.sDate = "2014-07-03|23:59:00";
                  date.eDate = "2014-07-20|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 6)
               {
                  date = new Object();
                  date.sDate = "2014-08-07|23:59:00";
                  date.eDate = "2014-08-31|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 8)
               {
                  date = new Object();
                  date.sDate = "2015-02-14|23:59:00";
                  date.eDate = "2015-02-28|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 9)
               {
                  date = new Object();
                  date.sDate = "2015-04-26|23:59:00";
                  date.eDate = "2015-05-15|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 10)
               {
                  date = new Object();
                  date.sDate = "2015-12-24|20:59:00";
                  date.eDate = "2016-01-10|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 11)
               {
                  date = new Object();
                  date.sDate = "2016-02-03|00:00:01";
                  date.eDate = "2016-02-25|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 12)
               {
                  date = new Object();
                  date.sDate = "2017-01-23|00:00:01";
                  date.eDate = "2017-02-15|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 13)
               {
                  date = new Object();
                  date.sDate = "2020-04-30|00:00:01";
                  date.eDate = "2020-05-31|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 14)
               {
                  date = new Object();
                  date.sDate = "2022-04-14|00:00:01";
                  date.eDate = "2022-05-15|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 15)
               {
                  date = new Object();
                  date.sDate = ChongZhi_Interface4.time0;
                  date.eDate = ChongZhi_Interface4.time1;
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 16)
               {
                  date = new Object();
                  date.sDate = "2023-07-03|00:00:01";
                  date.eDate = "2023-07-16|23:59:59";
                  Main.serviceHold.getTotalRechargedFun(date);
               }
               else if(totalRecharged_Arr[0] == 17)
               {
                  date = new Object();
                  date.sDate = ChongZhi_Interface.time0;
                  date.eDate = ChongZhi_Interface.time1;
                  Main.serviceHold.getTotalRechargedFun(date);
               }
            }
         }
      }
      
      private static function onShopHandler2(evt:ShopEvent) : void
      {
         var obj:Object = null;
         var getType:String = null;
         var curPage:int = 0;
         var totalPage:int = 0;
         var itemNum:int = 0;
         var exObj:Object = null;
         var tmpAry2:Array = null;
         var noticeStr:String = null;
         var typeAry:Array = null;
         var i:int = 0;
         var tmpObj:Object = null;
         var j:* = undefined;
         var jj:* = undefined;
         var ee:* = undefined;
         var typeObj:Object = null;
         var callIdx:String = "";
         var str:String = "";
         switch(evt.type)
         {
            case ShopEvent.SHOP_ERROR:
               str = "商城API错误信息------>" + String(evt.data);
               break;
            case ShopEvent.SHOP_DEL_SUCC:
               callIdx = String(Object(evt.data).callIdx);
               str = "删除物品成功，接口标记为:" + callIdx;
               break;
            case ShopEvent.SHOP_GET_PACKAGEINFO:
               str = "成功获取了背包的数据:\n";
               obj = evt.data as Object;
               if(obj == null)
               {
                  break;
               }
               getType = String(obj.getType);
               callIdx = String(obj.callIdx);
               curPage = int(obj.cur);
               totalPage = int(obj.total);
               itemNum = int(obj.itemNum);
               str = "接口标记为: " + callIdx + "当前操作来自:" + getType + "  总共有" + itemNum + "类物品  当前是第" + curPage;
               str += "页  总共有" + totalPage + "页";
               for(i = 0; i < itemNum; i++)
               {
                  tmpObj = obj["item" + i];
                  str = "物品" + (i + 1);
                  str = " 物品id:" + tmpObj.proId + " 背包id:" + tmpObj.pId + " 数量:" + tmpObj.count;
                  str += " 价格:" + tmpObj.price + " 名称:" + tmpObj.title;
                  str += " 图片地址:" + tmpObj.thumb + " 描述:" + tmpObj.description;
                  if(tmpObj.keys != undefined)
                  {
                     for(j in tmpObj.keys)
                     {
                        str = "显示----------->属性名:" + j + "  属性值:" + tmpObj.keys[j];
                     }
                  }
                  if(tmpObj.hideKeys != undefined)
                  {
                     for(jj in tmpObj.hideKeys)
                     {
                        str = "隐藏--------->属性名:" + jj + "  属性值:" + tmpObj.hideKeys[jj];
                     }
                  }
               }
               break;
            case ShopEvent.SHOP_UPDATE_EXTEND:
               str = "请设置扩展字段，你可以不填哦\n";
               exObj = evt.data as Object;
               str += "所要购买的物品id为:" + exObj.proId;
               break;
            case ShopEvent.SHOP_MODIFY_EX:
               callIdx = String(Object(evt.data).callIdx);
               str = "修改扩展属性成功，接口标记为:" + callIdx;
               break;
            case ShopEvent.SHOP_ADDFREE_SUCC:
               callIdx = String(Object(evt.data).callIdx);
               str = "添加免费物品成功，接口标记为:" + callIdx;
               break;
            case ShopEvent.SHOP_UPDATEPRO_SUCC:
               callIdx = String(Object(evt.data).callIdx);
               str = "修改平铺物品属性成功，接口标记为:" + callIdx;
               break;
            case ShopEvent.SHOP_GET_FREEPACKAGEINFO:
               break;
            case ShopEvent.SHOP_ADD_SUCC:
               str = "成功购买付费物品";
               break;
            case ShopEvent.SHOP_GET_TYPENOTICE:
               str = "成功获取公告及物品分类信息";
               tmpAry2 = evt.data as Array;
               if(tmpAry2 == null)
               {
                  break;
               }
               noticeStr = String(tmpAry2[0]);
               str = "公告信息---->" + noticeStr;
               typeAry = tmpAry2[1] as Array;
               if(typeAry == null)
               {
                  break;
               }
               for(ee in typeAry)
               {
                  typeObj = typeAry[ee];
                  str = "分类id:" + typeObj.id + "  分类名:" + typeObj.label;
               }
               break;
            case ShopEvent.SHOP_GET_PAYPACKAGEINFO:
         }
      }
      
      private static function saveProcess(e:SaveEvent) : void
      {
         var data:Array = null;
         switch(e.type)
         {
            case SaveEvent.SAVE_GET:
               break;
            case SaveEvent.SAVE_SET:
               if(e.ret as Boolean == true)
               {
               }
               break;
            case SaveEvent.SAVE_LIST:
               data = e.ret as Array;
               if(data == null)
               {
               }
               break;
         }
      }
      
      private static function netSaveErrorHandler(evt:Event) : void
      {
      }
      
      public static function Money_sel() : *
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getBalance();
         }
      }
      
      public static function TotalPaied() : *
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getTotalPaiedFun();
         }
      }
      
      public static function Money_sel2() : *
      {
         if(Main.serviceHold)
         {
            Main.serviceHold.getBalance();
         }
      }
      
      private static function Money_sel_Num(num:int) : *
      {
         if(Shop4399.shopX)
         {
            Shop4399.shopX.money_All_txt.text = "" + num;
         }
         if(ShopKillPoint.shopX)
         {
            ShopKillPoint.shopX.point_txt.text = "" + num;
         }
         if(xilianPanel.xlPanel)
         {
            xilianPanel.xlPanel.point_txt.text = "" + num;
         }
         if(ItemsPanel.itemsPanel)
         {
            ItemsPanel.itemsPanel.djPoint.text = "" + num;
         }
         if(SelMap.selMapX)
         {
            SelMap.selMapX.Sel_nanDu_mc2.x4_txt.text = "" + num;
         }
         if(ElvesPanel.elvesPanel)
         {
            ElvesPanel.elvesPanel.dianjuan.text = "" + num;
         }
         if(MakePanel._instance)
         {
            MakePanel._instance.dianQun_Point.text = "" + num;
         }
         if(StampPanel.qnPanel)
         {
            StampPanel.qnPanel.djPoint.text = "" + num;
         }
      }
      
      public static function getPayShow(e:Event = null) : *
      {
         if(Main.serviceHold)
         {
            if(Shop4399.ShopArr.length == 0)
            {
               Main.serviceHold.getPayPacInfoFun(200,1,0);
            }
         }
      }
      
      public static function Money_up(num:int) : *
      {
         if(Main.serviceHold)
         {
            PayMoneyVar.instance.money = num;
            Main.serviceHold.incMoney_As3(PayMoneyVar.instance);
         }
      }
      
      public static function BuyObj(id:uint, num:uint = 1) : void
      {
         if(Main.serviceHold)
         {
            buy_Obj = ShopFactory.GetObjData(id,num);
            Main.DuoKai_Fun(true);
         }
      }
      
      public static function PaiHangUP(id:uint) : *
      {
         var arr:Array = [];
         arr[0] = new Object();
         if(id == 144)
         {
            arr[0].rId = 857;
         }
         else if(id == 145)
         {
            arr[0].rId = 858;
         }
         else if(id == 146)
         {
            arr[0].rId = 859;
         }
         else if(id == 147)
         {
            arr[0].rId = 860;
         }
         else if(id == 148)
         {
            arr[0].rId = 861;
         }
         else
         {
            if(id != 149)
            {
               return;
            }
            arr[0].rId = 862;
         }
         arr[0].score = id;
         Api_4399_All.SubmitScore(Main.saveNum,arr);
      }
      
      public static function BuyObj_GO() : void
      {
         if(Main.serviceHold)
         {
            TiaoShi.txtShow("BuyObj_GO>>>>>> GetObjData id = " + buy_Obj.propId + ", dataObj.price = " + buy_Obj.price);
            Main.serviceHold.buyPropNd(buy_Obj);
         }
      }
      
      private static function onShopEventHandler(evt:ShopEvent) : void
      {
         switch(evt.type)
         {
            case ShopEvent.SHOP_ERROR_ND:
               errorFun(evt.data);
               break;
            case ShopEvent.SHOP_BUY_ND:
               buySuccFun(evt.data);
               break;
            case ShopEvent.SHOP_GET_LIST:
               getSuccFun(evt.data as Array);
         }
      }
      
      private static function errorFun(error:Object) : void
      {
         trace("eId:" + error.eId + "  message:" + error.msg + "\n");
         TiaoShi.txtShow("eId:" + error.eId + ", message:" + error.msg + "\n");
         Shop4399.Close();
      }
      
      private static function getSuccFun(data:Array) : void
      {
         var i:* = undefined;
         var propData:Object = null;
         if(data == null)
         {
            trace("获取物品列表时，返回空值了\n");
            return;
         }
         if(data.length == 0)
         {
            trace("无商品列表\n");
            return;
         }
         for(i in data)
         {
            propData = data[i];
            trace("propNum:" + i + "  propId:" + propData.propId + "  price:" + propData.price + "   propType:" + propData.propType + "\n");
         }
      }
      
      private static function buySuccFun(data:Object) : void
      {
         trace("propId:" + data.propId + ",count:" + data.count + ",balance:" + data.balance + ",tag:" + data.tag + "\n");
         TiaoShi.txtShow("? 商品ID:" + data.propId + ",数量:" + data.count + ",余额:" + data.balance + ",信息:" + data.tag + "\n");
         Shop4399.moneyAll.setValue(data.balance);
         Money_sel_Num(data.balance);
         Shop4399.GetObj();
         Panel_youling.DianQuanOk();
         ShopKillPoint.GetBuyNum();
         ShopKillPoint.GetBuyS();
         xilianPanel.xlOK();
         MenuTooltip.lijifuhua();
         MenuTooltip.xufeiRMB();
         TuiJianPanel.goumaiOK();
         NewPetPanel.jnRMB();
         NewPetPanel.sjRMB();
         NewPetPanel.wxRMB();
         NewPetPanel.fhRMB();
         NewPetPanel.xgRMB();
         NewPetPanel.bagRMB();
         NewPetPanel.slotRMB();
         SuppliesShopPanel.yaoRMBOK();
         EquipShopPanel.equipShopRMBOK();
         NewWantedPanel.lqczOK();
         StampPanel.tyKeyOK();
         ElvesPanel.slotRMBOK();
         ElvesPanel.blueXSOK();
         ItemsPanel.kuoBagRMBOK();
         ElvesPanel.pinkXSOK();
         ElvesPanel.goldXSOK();
         ElvesPanel.kssjOK();
         ElvesPanel.chongzhiOK();
         MakePanel.GetBuyObj();
         Sel_NanDu2.DianQuanGoGame();
         FanPaiPanel.jifenOK();
         TeMaiPanel.RMB29_OK();
         TeMaiPanel.RMB499_OK();
         TeMaiPanel.RMB499_1_OK();
         TeMaiPanel.RMB499_2_OK();
         TeMaiPanel.RMB79_OK();
         TeMaiPanel.RMB990_OK();
         TeMaiPanel.RMB2601_OK();
         TeMaiPanel.RMB2602_OK();
         FiveOne_Interface.jiaoguanOK();
         FiveOne_Interface.zhaoyaoOK();
         FiveOne_Interface.zhongxiaOK();
         ChunJiePanel.chunjieOK();
         XingLing_Interface.UP_Fun_YES();
         XingLing_Interface.UP2_Fun_YES();
         XingLing_Interface.UpPinZhi_Fun_YES();
         NewYear_Interface.newYearInRMB();
         Vip_Interface.GetVip();
         Vip_Interface.DianQuanChuanSong();
         YueKa_Interface.GetBuy();
         Panel_XianHua.GetBuy();
         QianDao.GetMoneyOK();
         TiaoZhan_Interface.DianQuan_GO();
         Sel_NanDu2.DianQuan_GO();
         TiaoZhanPaiHang_Interface.DianQuanDuiGetObj();
         ChongZhi_Interface2.Buy_OK();
         SkillPanel.reSkill_buy();
         YinCang.BuyGo();
         ZhuanPan.DianquanGo();
         LingHunShi_Interface.DianquanGo();
         Mosen_Interface.BuyGo();
         Main.Save2();
      }
      
      public static function GetOneRankInfo(uname:String = "", rankListId:uint = 1365, selType:int = 1, pageNum:uint = 1, pageSize:uint = 20) : *
      {
         if(rankListId == 1365)
         {
            if(Main.P1P2)
            {
               rankListId = 1364;
            }
         }
         if(uname == "" && Boolean(Main.logName))
         {
            uname = Main.logName;
         }
         sel_PaiHang_Over = false;
         var arr:Array = [pageNum,pageSize,rankListId,selType,uname];
         PaiMing_Arr.push(arr);
         if(PaiMing_Arr.length == 1)
         {
            Main.serviceHold.getOneRankInfo(rankListId,uname);
         }
      }
      
      public static function GetRankListByOwn(rankListId:uint = 1365, idx:uint = 1, rankNum:uint = 10, selType:int = 2) : *
      {
         if(rankListId == 1365)
         {
            if(Main.P1P2)
            {
               rankListId = 1364;
            }
         }
         sel_PaiHang_Over = false;
         var arr:Array = [rankListId,idx,rankNum,selType];
         PaiMing_Arr.push(arr);
         if(PaiMing_Arr.length == 1)
         {
            Main.serviceHold.getRankListByOwn(rankListId,idx,rankNum);
         }
      }
      
      public static function GetRankListsData(pageNum:uint = 1, pageSize:uint = 50, rankListId:uint = 1365, selType:int = 3, uname:String = "") : *
      {
         if(rankListId == 1365)
         {
            if(Main.P1P2)
            {
               rankListId = 1364;
            }
         }
         sel_PaiHang_Over = false;
         var arr:Array = [pageNum,pageSize,rankListId,selType,uname];
         PaiMing_Arr.push(arr);
         if(PaiMing_Arr.length == 1)
         {
            Main.serviceHold.getRankListsData(rankListId,pageSize,pageNum);
         }
      }
      
      private static function GetRankListsData2() : *
      {
         var selType:int = int(PaiMing_Arr[0][3]);
         if(selType == 1)
         {
            Main.serviceHold.getOneRankInfo(PaiMing_Arr[0][2],PaiMing_Arr[0][4]);
         }
         else if(selType == 2)
         {
            Main.serviceHold.GetRankListByOwn(PaiMing_Arr[0][0],PaiMing_Arr[0][1],PaiMing_Arr[0][2]);
         }
         else if(selType == 3)
         {
            Main.serviceHold.getRankListsData(PaiMing_Arr[0][2],PaiMing_Arr[0][1],PaiMing_Arr[0][0]);
         }
         TiaoShi.txtShow("搜索排行榜?" + selType + "?" + PaiMing_Arr[0][2]);
      }
      
      public static function SubmitScore(idx:uint, rankInfoAry:Array) : *
      {
         Main.serviceHold.submitScoreToRankLists(idx,rankInfoAry);
      }
      
      public static function onRankListSuccessHandler(evt:RankListEvent) : void
      {
         var obj:Object = evt.data;
         var data:* = obj.data;
         switch(obj.apiName)
         {
            case "1":
               decodeRankListInfo1(data);
               break;
            case "2":
               decodeRankListInfo2(data);
               break;
            case "4":
               decodeRankListInfo3(data);
               break;
            case "3":
               decodeSumitScoreInfo(data);
               break;
            case "5":
               decodeUserData(data);
         }
      }
      
      public static function onRankListErrorHandler(evt:RankListEvent) : void
      {
         var obj:Object = evt.data;
         var str:* = "apiFlag:" + obj.apiName + ", errorCode:" + obj.code + ", message:" + obj.message + "\n";
         TiaoShi.ShowPaiHang(str);
         if(obj.apiName == "5")
         {
            GetUserData_Arr.shift();
            PK_UI.reAddOtherPlayer();
         }
      }
      
      private static function decodeSumitScoreInfo(dataAry:Array) : void
      {
         var i:* = undefined;
         var tmpObj:Object = null;
         var str:String = "";
         if(dataAry == null || dataAry.length == 0)
         {
            TiaoShi.ShowPaiHang("没有数据,返回结果有问题！\n");
            return;
         }
         tempArr = dataAry;
         for(i in dataAry)
         {
            tmpObj = dataAry[i];
            str += ">>>>>>>>>>第" + (i + 1) + "条数据 排行榜ID:" + tmpObj.rId + "\n信息码值:" + tmpObj.code + "\n";
            if(tmpObj.code == "10000")
            {
               str += "\n当前排名:" + tmpObj.curRank + ", 当前分数:" + tmpObj.curScore + "\n上局排名:" + tmpObj.lastRank + ", 上局分数:" + tmpObj.lastScore + "\n";
            }
            else
            {
               str += "该排行榜提交的分数出问题了 信息:" + tmpObj.message + "\n";
            }
         }
         TiaoShi.ShowPaiHang(str);
      }
      
      private static function decodeRankListInfo1(dataAry:Array) : void
      {
         var paiHangId:int = int(PaiMing_Arr[0][2]);
         if(paiHangId == 1270 || paiHangId == 1271)
         {
            PK_UI._this.PaiHang_Show_X(dataAry);
         }
         else if(paiHangId == 1365 || paiHangId == 1364)
         {
            PK_UI._this.PaiHang_Show_X(dataAry);
         }
         else if(paiHangId > 600)
         {
            PaiHang_Data.DataAry2(dataAry,paiHangId);
            TiaoZhan_Interface.TxtShow2(dataAry,paiHangId);
            Sel_NanDu2.TxtShow2(dataAry,paiHangId);
            TiaoZhanPaiHang_Interface.dengDai = false;
            TiaoZhanPaiHang_Interface.Show();
         }
         TiaoShi.txtShow("~~~~~~查询排行榜 玩家数据结束>>>" + PaiMing_Arr.length + "?" + paiHangId);
         PaiMing_Arr.shift();
         if(PaiMing_Arr.length > 0)
         {
            Main._this.addEventListener(Event.ENTER_FRAME,GetRankListsData2XXX);
         }
         else
         {
            sel_PaiHang_Over = true;
         }
      }
      
      private static function decodeRankListInfo2(dataAry:Array) : void
      {
         TiaoShi.txtShow("根据自己的排名及范围取排行榜信息 >>>>>>>>>>>>>>>>>> ");
         PaiHang_Show_X2(dataAry);
         PaiMing_Arr.shift();
         if(PaiMing_Arr.length > 0)
         {
            Main._this.addEventListener(Event.ENTER_FRAME,GetRankListsData2XXX);
         }
         else
         {
            sel_PaiHang_Over = true;
         }
      }
      
      private static function PaiHang_Show_X2(dataAry:Array) : *
      {
         var i:* = undefined;
         var tmpObj:Object = null;
         TiaoShi.txtShow("根据自己的排名及范围取排行榜(测试用)~~~~~~~~~~~~~~");
         if(dataAry == null || dataAry.length == 0)
         {
            TiaoShi.txtShow("排行榜范围查询 ===============> null");
            return;
         }
         TiaoShi.txtShow("排行榜范围查询 length ===============> " + dataAry.length);
         for(i in dataAry)
         {
            tmpObj = dataAry[i];
            TiaoShi.txtShow(i + ": tmpObj.rank = " + tmpObj.rank + ", userName = " + tmpObj.userName + ", uId = " + tmpObj.uId + ", score = " + tmpObj.score);
         }
      }
      
      private static function decodeRankListInfo3(dataAry:Array) : void
      {
         var paiHangId:int = int(PaiMing_Arr[0][2]);
         if(paiHangId == 1365 || paiHangId == 1364)
         {
            if(PaiMing_Arr[0][1] == PK_UI._this.selPageX)
            {
               PK_UI._this.get_PaiHang_Data(dataAry,PaiMing_Arr[0][0]);
            }
            else
            {
               PK_UI.xPk_Player100_Fun(dataAry);
            }
         }
         else if(paiHangId == 1270 || paiHangId == 1271)
         {
            PK_UI._this.get_PaiHang_Data(dataAry,PaiMing_Arr[0][0]);
         }
         else if(paiHangId > 600)
         {
            PaiHang_Data.DataAry1(dataAry,paiHangId);
            TiaoZhan_Interface.TxtShow1(dataAry,paiHangId);
            Sel_NanDu2.TxtShow1(dataAry,paiHangId);
            TiaoZhanPaiHang_Interface.Show();
         }
         PaiMing_Arr.shift();
         if(PaiMing_Arr.length > 0)
         {
            Main._this.addEventListener(Event.ENTER_FRAME,GetRankListsData2XXX);
         }
         else
         {
            sel_PaiHang_Over = true;
         }
      }
      
      public static function GetRankListsData2XXX(e:*) : *
      {
         ++ycTime;
         if(ycTime > 5)
         {
            ycTime = 0;
            GetRankListsData2();
            Main._this.removeEventListener(Event.ENTER_FRAME,GetRankListsData2XXX);
         }
      }
      
      private static function decodeUserData(dataObj:Object) : void
      {
         if(PK_UI.PK_ing == false)
         {
            GetUserData_Arr = new Array();
            return;
         }
         if(dataObj == null)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"可接任务已达上限");
            return;
         }
         var tmpStr:String = String(dataObj.data);
         var newByteArray:ByteArray = Base64.decodeToByteArray(tmpStr) as ByteArray;
         newByteArray.position = 0;
         var objXX:Object = newByteArray.readObject();
         if(getObjX == 1)
         {
            PK_UI.xPk_nextPlayer_ID = GetUserData_Arr[0][0];
            PK_UI.xPk_nextPlayer = objXX["p1"];
            PK_UI.Load_OtherPlayerData(PK_UI.loadPlayerNum);
            GetUserData_Arr.shift();
            if(GetUserData_Arr.length > 0)
            {
               GetUserData2(GetUserData_Arr[0][0],GetUserData_Arr[0][1]);
               TiaoShi.txtShow("2读取存档队列处理 查询后面数据" + GetUserData_Arr.length);
            }
         }
         else if(getObjX == 2)
         {
            TiaoShi._this.FaFang_saveFun(objXX);
         }
      }
      
      public static function GetUserData(uid:String, idx:uint, getObj:uint = 1) : *
      {
         getObjX = getObj;
         TiaoShi.txtShow("GetUserData uid = " + uid + ",idx = " + idx);
         var arr:Array = [uid,idx];
         GetUserData_Arr.push(arr);
         if(GetUserData_Arr.length == 1)
         {
            Main.serviceHold.getUserData(uid,idx);
         }
      }
      
      private static function GetUserData2(uid:String, idx:uint) : *
      {
         Main.serviceHold.getUserData(uid,idx);
      }
   }
}

