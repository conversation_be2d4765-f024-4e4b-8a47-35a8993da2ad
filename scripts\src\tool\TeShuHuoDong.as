package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class TeShuHuoDong
   {
      
      public static var TeShuHuoDongArr:Array = [];
      
      public static var liBaoNumMax:int = 232;
      
      public function TeShuHuoDong()
      {
         super();
      }
      
      public static function DayInit() : *
      {
         TiaoShi.txtShow("可每天领取礼包重置~~");
         TeShuHuoDongArr[82] = 0;
      }
      
      public static function Init() : *
      {
         for(var i:int = 0; i <= liBaoNumMax; i++)
         {
            if(!TeShuHuoDongArr[i])
            {
               TeShuHuoDongArr[i] = 0;
            }
         }
      }
      
      public static function GetLiBaoNum(num:int) : int
      {
         if(!TeShuHuoDongArr[num])
         {
            TeShuHuoDongArr[num] = 0;
         }
         return TeShuHuoDongArr[num];
      }
      
      public static function AddLiBaoNum(num:int) : *
      {
         if(!TeShuHuoDongArr[num])
         {
            TeShuHuoDongArr[num] = 1;
         }
         else
         {
            ++TeShuHuoDongArr[num];
         }
      }
   }
}

