package src.tool
{
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   public class <PERSON><PERSON><PERSON><PERSON> extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      public static var _this:QianDao;
      
      private static var loadName:String = "QianDao_v2.swf";
      
      private static var OpenYN:Boolean = false;
      
      public static var year:uint = 0;
      
      public static var month:uint = 0;
      
      public static var day:uint = 0;
      
      public static var qdArr:Array = [];
      
      private static var qianDao_money:Boolean = false;
      
      private var skin:MovieClip;
      
      private var infoArr:Array = [["月份"],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品"
      ,3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[301,"高级道具兑换券\n可在特殊任务里的【兑换任务】兑换高级物品的珍贵道具。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[372,"炎煌铠甲(粉)\n传说中唯有进入死亡峡谷中浴火重生者，将得到火神认可，并赠予神赐装备之一“炎煌铠甲”",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[373,"炎煌之翼(紫)\n可以使陨石冲击滑行一段距离。两件相同翅膀可合成为一更高等级翅膀”",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质"
      ,5],[295,"复活药X10\n使用后可原地复活",10],[377,"风华之逝(粉)\n攻击时有几率释放强袭飓风。两件相同外套可合成为一更高等级外套。",1]],[[302,"星灵财宝\n在星灵财宝解封器可解封，解封后可获得珍贵道具，包括“高级道具兑换券”等稀有物品",3],[359,"星源方块\n蕴含巨大能量的神秘晶体,用于星灵附体的星源珠强化,强化时有一定几率提升星源珠的品质",5],[295,"复活药X10\n使用后可原地复活",10],[378,"风华之殇(粉)\n飓风存在时移动速度大幅提升。两件相同翅膀可合成为一更高等级翅膀。",1]]];
      
      public function QianDao()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var temp:QianDao = new QianDao();
         Main._stage.addChild(temp);
         OpenYN = true;
         InitDataFun();
      }
      
      public static function Open() : *
      {
         if(year == 0)
         {
            TiaoShi.txtShow("系统时间未获取");
            return;
         }
         if(_this == null)
         {
            InitOpen();
         }
         else
         {
            _this.visible = true;
            _this.skin._BLACK_mc.visible = false;
            _this.x = _this.y = 0;
            _this.Show();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
         }
      }
      
      private static function InitDataFun() : *
      {
         var i:uint = 0;
         if(month != qdArr[32])
         {
            for(i = 0; i < 32; i++)
            {
               qdArr[i] = false;
            }
            qdArr[32] = month;
            qdArr[33] = qdArr[34] = qdArr[35] = qdArr[36] = false;
         }
         else
         {
            for(i = 0; i < 32; i++)
            {
               if(!qdArr[i])
               {
                  qdArr[i] = false;
               }
            }
         }
      }
      
      public static function GetMoneyOK() : *
      {
         var i:int = 0;
         if(qianDao_money)
         {
            qianDao_money = false;
            i = int(_this.SelBuQian());
            qdArr[i] = true;
            _this.Show();
            _this.skin._BLACK_mc.visible = false;
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         Play_Interface.interfaceX["load_mc"].visible = false;
         var classRef:Class = loadData.getClass("Skin") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         this.skin.qianDao_btn.addEventListener(MouseEvent.CLICK,this.QianDaoFun);
         this.skin.buQian_btn.addEventListener(MouseEvent.CLICK,this.BuQian);
         this.skin.LingQue_btn.addEventListener(MouseEvent.CLICK,this.LingQueFun);
         for(var i:int = 1; i <= 4; i++)
         {
            this.skin["_LingQue_" + i].mouseChildren = this.skin["_LingQue_" + i].mouseEnabled = false;
         }
         this.skin._LingQue_Sel_1.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.skin._LingQue_Sel_2.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.skin._LingQue_Sel_3.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.skin._LingQue_Sel_4.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.skin._LingQue_Sel_1.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.skin._LingQue_Sel_2.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.skin._LingQue_Sel_3.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.skin._LingQue_Sel_4.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.skin.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function onMOUSE_MOVE(e:MouseEvent) : *
      {
         var i:int = int((e.target.name as String).substr(13,1));
         this.skin.info_mc.x = 345;
         this.skin.info_mc.y = e.target.y;
         this.skin.info_mc.visible = true;
         this.skin.info_mc.pic.gotoAndStop(this.infoArr[month][i - 1][0]);
         this.skin.info_mc.txt.text = this.infoArr[month][i - 1][1];
         this.skin.info_mc.pic.howNum.text = "x" + this.infoArr[month][i - 1][2];
      }
      
      private function onMOUSE_OUT(e:MouseEvent) : *
      {
         this.skin.info_mc.x = this.skin.info_mc.y = -2000;
         this.skin.info_mc.visible = false;
      }
      
      private function Close(e:* = null) : *
      {
         CloseX();
      }
      
      public function Show() : *
      {
         var j:uint = 0;
         var xMC:MovieClip = null;
         var nowDay:uint = 0;
         this.skin._now_txt.text = year + "年" + month + "月";
         var date:Date = new Date(year,month - 1,1);
         var one:uint = date.day;
         var maxDay:uint = 0;
         if(month == 2)
         {
            if(year % 4 == 0)
            {
               maxDay = 29;
            }
            else
            {
               maxDay = 28;
            }
         }
         else if(month == 4 || month == 6 || month == 9 || month == 11)
         {
            maxDay = 30;
         }
         else
         {
            maxDay = 31;
         }
         for(var i:uint = 0; i < 6; i++)
         {
            for(j = 0; j < 7; j++)
            {
               xMC = this.skin["d_" + i + j];
               nowDay = i * 7 + j - one + 1;
               if(nowDay > 31 || i == 0 && j < one || i != 0 && nowDay > maxDay)
               {
                  xMC._txt.text = "";
                  xMC.visible = false;
               }
               else
               {
                  xMC._txt.text = nowDay;
                  if(nowDay <= day)
                  {
                     if(qdArr[nowDay - 1])
                     {
                        xMC.gotoAndStop(3);
                     }
                     else
                     {
                        xMC.gotoAndStop(2);
                     }
                  }
               }
            }
         }
         if(qdArr[36])
         {
            this.skin.LingQue_btn.visible = false;
         }
         this.SelBuQian();
         this.SelQianDao();
         this.SelLingQue();
         var arr:* = this.Sel_Total_QianDao();
         this.skin.total_1.text = "已连续签到: " + arr[0] + "次";
         this.skin.total_2.text = "已累计签到: " + arr[1] + "次";
      }
      
      private function SelLingQue() : *
      {
         for(var x:int = 1; x <= 4; x++)
         {
            if(!qdArr[32 + x])
            {
               this.skin["_LingQue_" + x].visible = false;
            }
            else
            {
               this.skin["_LingQue_" + x].visible = true;
            }
         }
      }
      
      private function Sel_Total_QianDao() : Array
      {
         var totalx1:int = -1;
         var totalx2:int = 0;
         for(var i:int = int(day - 1); i >= 0; i--)
         {
            if(i < 0)
            {
               break;
            }
            if(qdArr[i])
            {
               totalx2++;
            }
            if(totalx1 < 0 && (!qdArr[i - 1] || i == 0))
            {
               totalx1 = totalx2;
            }
         }
         return [totalx1,totalx2];
      }
      
      private function SelBuQian() : int
      {
         for(var i:int = day - 2; i >= 0; i--)
         {
            if(i < 0)
            {
               this.skin.buQian_btn.visible = false;
               return -1;
            }
            if(!qdArr[i])
            {
               return i;
            }
         }
         this.skin.buQian_btn.visible = false;
         return -1;
      }
      
      private function SelQianDao() : *
      {
         if(qdArr[day - 1])
         {
            this.skin.qianDao_btn.visible = false;
         }
      }
      
      private function QianDaoFun(e:*) : *
      {
         qdArr[day - 1] = true;
         var arr:Array = this.Sel_Total_QianDao();
         var num:uint = uint(arr[0]);
         if(num > 7)
         {
            num = 7;
         }
         num *= InitData.LingQue_10.getValue();
         Main.player1.AddKillPoint(num);
         if(Main.P1P2)
         {
            Main.player2.AddKillPoint(num);
         }
         NewMC.Open("文字提示",this,400,400,30,0,true,2,"获得" + num + "击杀点");
         this.Show();
         Main.Save();
      }
      
      private function BuQian(e:*) : *
      {
         if(this.SelBuQian() != -1)
         {
            this.GetMoney();
         }
      }
      
      private function GetMoney() : *
      {
         if(Shop4399.moneyAll.getValue() >= 12)
         {
            this.skin._BLACK_mc.visible = true;
            qianDao_money = true;
            Api_4399_All.BuyObj(InitData.BuyNum_119.getValue());
         }
         else
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
            this.skin._BLACK_mc.visible = false;
         }
      }
      
      private function LingQueFun(e:*) : *
      {
         var arr:* = this.Sel_Total_QianDao();
         for(var i:int = 1; i <= 4; i++)
         {
            if(!qdArr[32 + i] && arr[1] >= InitData["LingQue_" + i].getValue())
            {
               this.LingQueObj(i - 1);
               this.Show();
               return;
            }
         }
      }
      
      private function LingQueObj(num:int) : *
      {
         var i:int = 0;
         TiaoShi.txtShow("~~~领取累计奖励" + num);
         if(num == 0)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
               return;
            }
            for(i = 0; i < 3; i++)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.LingQue_X0.getValue()));
            }
            qdArr[33 + num] = true;
         }
         else if(num == 1)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
               return;
            }
            for(i = 0; i < 5; i++)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.LingQue_X1.getValue()));
            }
            qdArr[33 + num] = true;
         }
         else if(num == 2)
         {
            if(Main.player1.getBag().backSuppliesBagNum() < 1)
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"消耗类背包空间不足!");
               return;
            }
            Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(InitData.LingQue_X2.getValue()));
            qdArr[33 + num] = true;
         }
         else if(num == 3)
         {
            if(month >= 1 && month <= 8)
            {
               if(Main.player1.getBag().backOtherBagNum() < 1)
               {
                  NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(InitData.LingQue_X3[month].getValue()));
               qdArr[33 + num] = true;
            }
            else
            {
               if(Main.player1.getBag().backequipBagNum() < 1)
               {
                  NewMC.Open("文字提示",this,400,400,30,0,true,2,"装备类背包空间不足!");
                  return;
               }
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(InitData.LingQue_X3[month].getValue()));
               qdArr[33 + num] = true;
            }
         }
      }
   }
}

