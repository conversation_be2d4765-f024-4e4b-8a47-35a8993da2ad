package src
{
   import com.*;
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.strengthenPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src._data.*;
   import src.tool.*;
   
   public class Shop_LiBao extends MovieClip
   {
      
      public static var shopX:Shop_LiBao;
      
      public static var loadData:ClassLoader;
      
      public static var open_yn:Boolean;
      
      public static const data:Class = Shop_LiBao_data;
      
      public static const data2:Class = Shop_LiBao_data2;
      
      public static var HDpoint:VT = VT.createVT(-1);
      
      public static var loadName:String = "Panel_CZ_v1.swf";
      
      public static var selYN:Boolean = true;
      
      public static var selYN2:Boolean = false;
      
      public var btn_0:*;
      
      public var btn_1:*;
      
      public var btn_2:*;
      
      public var btn_3:*;
      
      public var btn_4:*;
      
      public var btn_5:*;
      
      public var btn_6:*;
      
      public var btn_7:*;
      
      public var Close_btn:*;
      
      public var _txt_infoMC:*;
      
      public var QieHuan_btn:*;
      
      public var pic_mc1:*;
      
      public var pic_mc2:*;
      
      public var pic_mc3:*;
      
      public var pic_mc4:*;
      
      public var pic_mc5:*;
      
      public var pic_mc6:*;
      
      public var HDpoint_txt:*;
      
      public var lingQu_btn:*;
      
      public var add_Money_btn:*;
      
      public var v0:*;
      
      public var v1:*;
      
      public var v2:*;
      
      public var v3:*;
      
      public var v4:*;
      
      public var v5:*;
      
      public var v6:*;
      
      public var v7:*;
      
      public var v8:*;
      
      public var v9:*;
      
      public var myXml:XML;
      
      public var myXml2:XML;
      
      public var selGetX_Num:int = 1;
      
      private var liBaoPointArr:Array;
      
      public function Shop_LiBao()
      {
         var i:int = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         this.myXml = new XML();
         this.myXml2 = new XML();
         this.liBaoPointArr = [0,99,499,999,1999,2999,3999,4999,9999,9999,9999];
         super();
         Main._stage.addEventListener(Event.ACTIVATE,this.onACTIVATEx2);
         this.myXml = XMLAsset.createXML(Shop_LiBao.data);
         this.myXml2 = XMLAsset.createXML(Shop_LiBao.data2);
         this.Close_btn.addEventListener(MouseEvent.CLICK,this.CloseX);
         this.lingQu_btn.addEventListener(MouseEvent.CLICK,this.GetX);
         for(i = 0; i < 8; i++)
         {
            this["btn_" + i].addEventListener(MouseEvent.CLICK,this.ShowObjArr);
         }
         for(i = 1; i < 7; i++)
         {
            mm = new Shop_picNEW();
            num = int(this["pic_mc" + i].getChildIndex(this["pic_mc" + i].pic_xx));
            mm.x = this["pic_mc" + i].pic_xx.x;
            mm.y = this["pic_mc" + i].pic_xx.y;
            mm.name = "pic_xx";
            this["pic_mc" + i].removeChild(this["pic_mc" + i].pic_xx);
            this["pic_mc" + i].pic_xx = mm;
            this["pic_mc" + i].addChild(mm);
            this["pic_mc" + i].setChildIndex(mm,num);
            this["pic_mc" + i].pic_xx.gotoAndStop(1);
         }
         this.LingQu_btn_YN();
         this.QieHuan_btn.addEventListener(MouseEvent.CLICK,this.QieHuanFun);
         this.add_Money_btn.addEventListener(MouseEvent.CLICK,this.Open_AddMoney);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         if(!shopX)
         {
            Loading();
            open_yn = true;
            return;
         }
         SelType = "全部";
         Main.allClosePanel();
         shopX.x = xx;
         shopX.y = yy;
         shopX.visible = true;
         Main._stage.addChild(shopX);
         shopX.Show();
      }
      
      public static function Close() : *
      {
         if(shopX)
         {
            shopX.x = 5000;
            shopX.y = 5000;
            shopX.visible = false;
         }
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("src.Shop_LiBao") as Class;
         shopX = new classRef();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
      
      public static function JianCe() : *
      {
         var i:int = 0;
         var xx:int = int(Shop4399.totalRecharged.getValue());
         if(xx < 0)
         {
            for(i = 3; i < Main.lingQueArr.length; i++)
            {
               if(Main.lingQueArr[i] > 0)
               {
                  SelliBao();
               }
            }
            return;
         }
         for(var j:int = 0; j < Main.player1.getTitleSlot().getListLength(); j++)
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(j).getId() == 25)
            {
               if(xx < 900)
               {
                  Main.NoGame("vip称号");
               }
            }
         }
         for(i = 3; i < shopX.liBaoPointArr.length; i++)
         {
            if(xx >= 0 && Main.lingQueArr && Main.lingQueArr[i] != 0 && xx < shopX.liBaoPointArr[i])
            {
               SaveXX.Save(12,xx,false,false,false);
            }
         }
      }
      
      public static function SelliBao() : *
      {
         if(selYN && Shop4399.totalRecharged.getValue() < 0)
         {
            Api_4399_All.GetTotalRecharged(1);
            Api_4399_All.GetTotalRecharged();
            selYN = false;
         }
      }
      
      public static function subString() : *
      {
         var x:int = 0;
         var temStr:String = null;
         var strArr:Array = null;
         for(x in Strat.SAVE_LIST)
         {
            temStr = Strat.SAVE_LIST[x];
            if(temStr.substr(0,1) == "$")
            {
               strArr = temStr.split("$");
               Strat.SAVE_LIST[x] = strArr[2];
            }
         }
      }
      
      public function Show() : *
      {
         var i:int = 0;
         var arr2:Array = null;
         for(i = 0; i < 10; i++)
         {
            this["v" + i].visible = false;
         }
         this["v" + (this.selGetX_Num - 1)].visible = true;
         trace("选中按钮 = " + this.selGetX_Num);
         var arr:Array = this.isObjArr(this.selGetX_Num);
         trace("选中按钮arr = " + arr);
         for(i in arr)
         {
            if(arr[i] != 0)
            {
               arr2 = this.isType(arr[i]);
               trace("选中按钮arr2 = = = " + arr2);
               this["pic_mc" + (i + 1)].pic_xx.gotoAndStop(arr2[3]);
               this["pic_mc" + (i + 1)].mouseChildren = false;
               this["pic_mc" + (i + 1)].addEventListener(MouseEvent.MOUSE_OVER,this.onMOUSE_OVER);
               this["pic_mc" + (i + 1)].addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
            }
            else
            {
               this["pic_mc" + (i + 1)].pic_xx.gotoAndStop(1);
               this["pic_mc" + (i + 1)].mouseChildren = false;
               this["pic_mc" + (i + 1)].removeEventListener(MouseEvent.MOUSE_OVER,this.onMOUSE_OVER);
               this["pic_mc" + (i + 1)].removeEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
            }
         }
         this.LingQu_btn_YN();
         this.ShowPoint();
      }
      
      public function ShowPoint() : *
      {
         var xx:int = Shop4399.totalRecharged.getValue() - Shop_LiBao.HDpoint.getValue();
         if(xx < 0)
         {
            this.HDpoint_txt.text = "数据查询中请稍候";
         }
         else
         {
            this.HDpoint_txt.text = xx + "点券";
         }
      }
      
      private function CloseX(e:*) : *
      {
         Close();
      }
      
      private function GetX(e:*) : *
      {
         var x:int = 0;
         var id:int = 0;
         var num:int = 0;
         var type_And_Id:Array = null;
         var y:int = 0;
         if(Boolean(this.isNumOK(this.selGetX_Num)) && Main.lingQueArr[this.selGetX_Num] <= 0)
         {
            for(x = 1; x < 7; x++)
            {
               id = int(this.myXml2.充值礼包[this.selGetX_Num - 1]["id" + x]);
               num = int(this.myXml2.充值礼包[this.selGetX_Num - 1]["数量" + x]);
               trace("@@@ id = " + id + "??");
               if(id != 0)
               {
                  type_And_Id = this.isType(id);
                  trace("@@@ id = " + id + " , num = " + num + ", type = " + type_And_Id);
                  for(y = 0; y < num; y++)
                  {
                     if(type_And_Id[0] == "装备类")
                     {
                        StoragePanel.storage.addEquipStorage(EquipFactory.createEquipByID(type_And_Id[1]));
                        if(Main.P1P2)
                        {
                           StoragePanel.storage.addEquipStorage(EquipFactory.createEquipByID(type_And_Id[1]));
                        }
                     }
                     else if(type_And_Id[0] == "宝石类")
                     {
                        StoragePanel.storage.addGemStorage(GemFactory.creatGemById(type_And_Id[1]));
                     }
                     else if(type_And_Id[0] == "消耗类")
                     {
                        StoragePanel.storage.addSuppliesStorage(SuppliesFactory.getSuppliesById(type_And_Id[1]));
                     }
                     else if(type_And_Id[0] == "其他类")
                     {
                        StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(type_And_Id[1]));
                        if(Main.P1P2)
                        {
                           StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(type_And_Id[1]));
                        }
                     }
                     else if(type_And_Id[0] == "属性类")
                     {
                        if(type_And_Id[4] == 1)
                        {
                           Main.player1.addPoint(type_And_Id[5]);
                           if(Main.P1P2)
                           {
                              Main.player2.addPoint(type_And_Id[5]);
                           }
                        }
                     }
                  }
               }
            }
            ++Main.lingQueArr[this.selGetX_Num];
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功 物品已放入仓库");
            this.LingQu_btn_YN();
         }
      }
      
      private function isNumOK(id:int) : Boolean
      {
         var i:int = 0;
         var 装备数量:int = 0;
         var 宝石数量:int = 0;
         var 消耗数量:int = 0;
         var 其他数量:int = 0;
         var idX:int = 0;
         var i1:int = int(StoragePanel.storage.backEquipEmptyNum());
         var i2:int = int(StoragePanel.storage.backGemEmptyNum());
         var i3:int = int(StoragePanel.storage.backSuppliesEmptyNum());
         var i4:int = int(StoragePanel.storage.backOtherEmptyNum());
         for(i in this.myXml.充值礼包物品)
         {
            装备数量 = int(this.myXml2.充值礼包[i].格数_装备);
            宝石数量 = int(this.myXml2.充值礼包[i].格数_宝石);
            消耗数量 = int(this.myXml2.充值礼包[i].格数_消耗);
            其他数量 = int(this.myXml2.充值礼包[i].格数_其他);
            if(Main.P1P2)
            {
               装备数量 *= 2;
               其他数量 *= 2;
            }
            idX = int(this.myXml.充值礼包物品[i].ID);
            if(id == idX)
            {
               if(装备数量 >= i1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               if(宝石数量 >= i2)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               if(消耗数量 >= i3)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               if(其他数量 >= i4)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
                  return false;
               }
               return true;
            }
         }
         return true;
      }
      
      private function isType(id:int) : Array
      {
         var i:int = 0;
         var idX:int = 0;
         var arr:Array = null;
         for(i in this.myXml.充值礼包物品)
         {
            idX = int(this.myXml.充值礼包物品[i].ID);
            if(id == idX)
            {
               return [String(this.myXml.充值礼包物品[i].类型),int(this.myXml.充值礼包物品[i].物品ID),String(this.myXml.充值礼包物品[i].名称),int(this.myXml.充值礼包物品[i].图标),Number(this.myXml.充值礼包物品[i].备用1),Number(this.myXml.充值礼包物品[i].备用2),Number(this.myXml.充值礼包物品[i].备用3),String(this.myXml.充值礼包物品[i].说明)];
            }
         }
         return null;
      }
      
      private function isObjArr(id:int) : Array
      {
         var i:int = 0;
         var arr:Array = null;
         for(i in this.myXml2.充值礼包)
         {
            if(id == this.myXml2.充值礼包[i].ID)
            {
               return [this.myXml2.充值礼包[i].id1,this.myXml2.充值礼包[i].id2,this.myXml2.充值礼包[i].id3,this.myXml2.充值礼包[i].id4,this.myXml2.充值礼包[i].id5,this.myXml2.充值礼包[i].id6];
            }
         }
         return null;
      }
      
      private function ShowObjArr(e:MouseEvent) : *
      {
         this.selGetX_Num = int((e.target.name as String).substr(4,1)) + 1;
         shopX.Show();
      }
      
      private function LingQu_btn_YN() : *
      {
         var xx:int = Shop4399.totalRecharged.getValue() - Shop_LiBao.HDpoint.getValue();
         if(!Main.lingQueArr[this.selGetX_Num] && xx > this.liBaoPointArr[this.selGetX_Num])
         {
            this.lingQu_btn.visible = true;
         }
         else
         {
            this.lingQu_btn.visible = false;
         }
      }
      
      private function onMOUSE_OVER(e:MouseEvent) : *
      {
         var i:int = int((e.target.name as String).substr(6,1));
         this.objInfo(i,e.target.x + 50,e.target.y);
      }
      
      private function onMOUSE_OUT(e:*) : *
      {
         this._txt_infoMC.x = this._txt_infoMC.y = 5000;
      }
      
      private function objInfo(num:int, xx:int, yy:int) : *
      {
         var arr2:Array = null;
         this._txt_infoMC.x = xx;
         this._txt_infoMC.y = yy;
         var arr:Array = this.isObjArr(this.selGetX_Num);
         if(arr[num - 1] != 0)
         {
            arr2 = this.isType(arr[num - 1]);
            this._txt_infoMC._txt.text = arr2[2] + "\n" + arr2[7];
         }
      }
      
      private function onACTIVATEx2(e:*) : *
      {
         if(this.visible && selYN2 && Shop4399.totalRecharged.getValue() < 0)
         {
            Api_4399_All.GetTotalRecharged();
         }
      }
      
      private function QieHuanFun(e:*) : *
      {
         Main.allClosePanel();
         Shop_LiBao2.Open();
      }
      
      private function Open_AddMoney(e:* = null) : *
      {
         Main.ChongZhi();
         selYN2 = true;
         this.HDpoint_txt.text = "数据查询中请稍候";
      }
   }
}

