package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.strengthenPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class 星冰界面 extends MovieClip
   {
      
      private static var only:星冰界面;
      
      public var close2:SimpleButton;
      
      public function 星冰界面()
      {
         super();
         this.close2.addEventListener(MouseEvent.CLICK,this.CloseXX);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         MusicBox.MusicPlay2("m14");
         if(!only)
         {
            classRef = All_Npc.loadData.getClass("src.npc.星冰界面") as Class;
            xxMov = new classRef();
            only = xxMov;
         }
         Main._stage.addChild(only);
         only.x = xx;
         only.y = yy;
         only.visible = true;
      }
      
      public static function Close() : *
      {
         if(only)
         {
            only.x = 5000;
            only.y = 5000;
            only.visible = false;
         }
      }
      
      private function CloseXX(e:*) : *
      {
         Close();
      }
   }
}

