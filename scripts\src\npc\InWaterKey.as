package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class InWater<PERSON>ey extends MovieClip
   {
      
      private static var iwk:InWaterKey;
      
      public var txt_1:TextField;
      
      public var txt_2:TextField;
      
      public var txt_3:TextField;
      
      public var in_btn:SimpleButton;
      
      public var close_btn:SimpleButton;
      
      public function InWaterKey()
      {
         super();
         this.in_btn.addEventListener(MouseEvent.CLICK,this.nextWord);
         this.in_btn.visible = false;
         this.close_btn.addEventListener(MouseEvent.CLICK,this.closed);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         if(!iwk)
         {
            classRef = All_Npc.loadData.getClass("src.npc.InWaterKey") as Class;
            xxMov = new classRef();
            iwk = xxMov;
         }
         Main._stage.addChild(iwk);
         iwk.x = xx;
         iwk.y = yy;
         iwk.visible = true;
         panduan();
      }
      
      public static function Close() : *
      {
         if(!iwk)
         {
            iwk = new InWaterKey();
         }
         iwk.x = 5000;
         iwk.y = 5000;
         iwk.visible = false;
      }
      
      private static function panduan() : *
      {
         var temp:int = 0;
         if(Main.player1.getBag().getOtherobjNum(63223) > 0)
         {
            iwk.txt_1.text = "1/1";
            temp++;
         }
         else
         {
            iwk.txt_1.text = "0/1";
         }
         if(Main.player1.getBag().getOtherobjNum(63224) > 0)
         {
            iwk.txt_2.text = "1/1";
            temp++;
         }
         else
         {
            iwk.txt_2.text = "0/1";
         }
         if(Main.player1.getBag().getOtherobjNum(63225) > 0)
         {
            iwk.txt_3.text = "1/1";
            temp++;
         }
         else
         {
            iwk.txt_3.text = "0/1";
         }
         if(temp >= 3)
         {
            iwk.in_btn.visible = true;
         }
      }
      
      private function closed(e:*) : *
      {
         Close();
      }
      
      private function nextWord(e:*) : *
      {
         Main.player1.getBag().delOtherById(63223);
         Main.player1.getBag().delOtherById(63224);
         Main.player1.getBag().delOtherById(63225);
         if(Main.P1P2)
         {
            Main.player2.getBag().delOtherById(63223);
            Main.player2.getBag().delOtherById(63224);
            Main.player2.getBag().delOtherById(63225);
         }
         Main.Map0_YN2 = true;
         Main.Save();
         InWaterDoor.Open();
         Close();
      }
   }
}

