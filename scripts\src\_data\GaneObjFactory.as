package src._data
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   
   public class GaneObjFactory
   {
      
      public static var dataArr:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function GaneObjFactory()
      {
         super();
      }
      
      public static function creatFactory() : *
      {
         myXml = XMLAsset.createXML(Data2.gameObjData);
         InitDataX();
      }
      
      private static function InitDataX() : *
      {
         var i:uint = 0;
         var property:XML = null;
         var id1:uint = 0;
         var id2:uint = 0;
         var tempObj:Object = null;
         for(i = 1; i <= 4; i++)
         {
            dataArr[i] = new Array();
         }
         for each(property in myXml.关卡掉落)
         {
            id1 = Number(property.关卡);
            id2 = Number(property.星级);
            tempObj = new Object();
            for(i = 1; i <= 12; i++)
            {
               tempObj["tb" + i] = uint(property["物品图标" + i]);
               tempObj["mc" + i] = String(property["物品名称" + i]);
               tempObj["sm" + i] = String(property["物品说明" + i]);
               tempObj["pz" + i] = String(property["物品品质" + i]);
            }
            dataArr[id2][id1] = tempObj;
         }
      }
      
      public static function GetNumArr(g1:uint, g2:uint) : Array
      {
         var tempArr:Array = new Array();
         for(var i:uint = 1; i <= 12; i++)
         {
            if(Boolean(dataArr[g2]) && Boolean(dataArr[g2][g1]))
            {
               tempArr[i] = dataArr[g2][g1]["tb" + i];
            }
         }
         return tempArr;
      }
      
      public static function GetInfo(g1:uint, g2:uint, num:uint) : Array
      {
         var tempArr:Array = new Array();
         if(Boolean(dataArr[g2]) && Boolean(dataArr[g2][g1]))
         {
            tempArr[0] = dataArr[g2][g1]["mc" + num];
            tempArr[1] = dataArr[g2][g1]["sm" + num];
            tempArr[2] = dataArr[g2][g1]["pz" + num];
         }
         return tempArr;
      }
   }
}

