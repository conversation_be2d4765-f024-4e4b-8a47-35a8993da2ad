package src.tool
{
   import com.adobe.serialization.json.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1642")]
   public class GengXin1 extends MovieClip
   {
      
      public var no_btn:SimpleButton;
      
      public var no_btn2:SimpleButton;
      
      public var yes_btn:SimpleButton;
      
      public function GengXin1()
      {
         super();
         this.yes_btn.addEventListener(MouseEvent.CLICK,this.OK);
         this.no_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.no_btn2.addEventListener(MouseEvent.CLICK,this.Close);
      }
      
      private function OK(e:*) : *
      {
         GengXin.reGame();
      }
      
      private function Close(e:*) : *
      {
         this.x = this.y = -5000;
      }
   }
}

