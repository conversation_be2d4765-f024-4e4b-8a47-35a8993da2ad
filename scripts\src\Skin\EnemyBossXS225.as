package src.Skin
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.*;
   import src.other.*;
   import src.tool.*;
   
   public class EnemyBossXS225 extends EnemySkin
   {
      
      public static var xishou_Value:int = 0;
      
      internal var tempT:int = 0;
      
      internal var tempX:int = 0;
      
      public function EnemyBossXS225()
      {
         super();
      }
      
      override public function otherGoTo(str:String) : *
      {
         if(str == "攻击1" && this.tempX == 0)
         {
            this.tempX = 50;
            TiaoShi.txtShow("aa2323a" + this.tempX);
            this.addEventListener(Event.ENTER_FRAME,this.daotui);
         }
         if(str == "死亡")
         {
            Main.wts.wantedTaskComplete();
         }
      }
      
      internal function daotui(e:*) : *
      {
         ++this.tempT;
         if(this.tempT > 42)
         {
            if(this.tempX > 0)
            {
               if((this.parent as Enemy).RL)
               {
                  (this.parent as Enemy).x -= this.tempX;
               }
               else
               {
                  (this.parent as Enemy).x += this.tempX;
               }
               this.tempX -= 5;
            }
         }
         if(this.tempT > 100)
         {
            this.tempT = 0;
            this.tempX = 0;
            this.removeEventListener(Event.ENTER_FRAME,this.daotui);
         }
      }
   }
}

