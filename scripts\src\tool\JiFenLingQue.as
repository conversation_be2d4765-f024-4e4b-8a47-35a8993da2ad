package src.tool
{
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol3555")]
   public class JiFenLingQue extends MovieClip
   {
      
      public var _2_btn:SimpleButton;
      
      public var _2_txt:TextField;
      
      public var _3_btn:SimpleButton;
      
      public var _3_txt:TextField;
      
      public var _btn:SimpleButton;
      
      public var _close_btn:SimpleButton;
      
      public var _txt:TextField;
      
      public var duihuan_1:SimpleButton;
      
      public var duihuan_2:SimpleButton;
      
      public var duihuan_3:SimpleButton;
      
      public var http_btn:SimpleButton;
      
      public var qieHuan_btn:SimpleButton;
      
      public var liBaoArr:Array = new Array();
      
      private var urlLoader:URLLoader = new URLLoader();
      
      private var phpUrl:URLRequest = new URLRequest("http://my.4399.com/jifen/activation");
      
      private var liBao2Time:int = 20170830;
      
      private var liBao2Str:String = "活动8月30日开启";
      
      private var liBao2ID:String = "4399287";
      
      private var liBao2ID_X:int = 197;
      
      private var liBao2key:String = "asdakjapsdfajslkjasdkWPUls";
      
      private var urlLoader2:URLLoader = new URLLoader();
      
      private var phpUrl2:URLRequest = new URLRequest("http://huodong.4399.com/2016/djyx/api.php?");
      
      private var liBao3Time:int = 20170727;
      
      private var liBao3Str:String = "活动7月27日开启";
      
      private var liBao3ID:String = "3";
      
      private var liBao3ID_X:int = 190;
      
      private var liBao3key:String = "asdakjapsdfajslkjasdkWPUls";
      
      private var urlLoader3:URLLoader = new URLLoader();
      
      private var phpUrl3:URLRequest = new URLRequest("http://huodong.4399.com/2017/ndyx/api.php?");
      
      public function JiFenLingQue()
      {
         super();
         this.Init_liBaoArr();
         this._txt.addEventListener(MouseEvent.CLICK,this.onTXT);
         this._btn.addEventListener(MouseEvent.CLICK,this.onLingQue);
         this._2_txt.addEventListener(MouseEvent.CLICK,this.onTXT2);
         this._2_btn.addEventListener(MouseEvent.CLICK,this.onLingQue2);
         this._3_txt.addEventListener(MouseEvent.CLICK,this.onTXT3);
         this._3_btn.addEventListener(MouseEvent.CLICK,this.onLingQue3);
         this.duihuan_1.addEventListener(MouseEvent.CLICK,this.WangZhiLianJie);
         this.duihuan_2.addEventListener(MouseEvent.CLICK,this.WangZhiLianJie2);
         this.duihuan_3.addEventListener(MouseEvent.CLICK,this.WangZhiLianJie3);
         this.http_btn.addEventListener(MouseEvent.CLICK,this.论坛);
         this._close_btn.addEventListener(MouseEvent.CLICK,this.onClose);
      }
      
      private function Init_liBaoArr() : *
      {
         this.liBaoArr[33053] = [233,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33054] = [234,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33055] = [235,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33056] = [236,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33057] = [237,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33058] = [238,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33059] = [239,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33060] = [240,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33061] = [241,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33062] = [242,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33063] = [243,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33064] = [244,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33065] = [245,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33066] = [246,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33067] = [247,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33216] = [248,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33217] = [249,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33219] = [250,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33221] = [251,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33222] = [252,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33239] = [253,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33240] = [254,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33242] = [255,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33243] = [256,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33244] = [257,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33245] = [258,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33246] = [259,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33247] = [260,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33248] = [261,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33249] = [262,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33385] = [263,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33386] = [264,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33387] = [265,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33388] = [266,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33389] = [267,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33390] = [268,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33391] = [269,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33392] = [270,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33393] = [271,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33394] = [272,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33395] = [273,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33396] = [274,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33397] = [275,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33398] = [276,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33399] = [277,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33545] = [278,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33546] = [279,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33547] = [280,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33548] = [281,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33549] = [282,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33550] = [283,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33551] = [284,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33552] = [285,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33553] = [286,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33554] = [287,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33555] = [288,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33556] = [289,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33557] = [290,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33558] = [291,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33559] = [292,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33821] = [293,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33822] = [294,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33823] = [295,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33824] = [296,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33825] = [297,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33826] = [298,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33827] = [299,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33828] = [300,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33829] = [301,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33830] = [302,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[33831] = [303,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[33832] = [304,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[33833] = [305,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[33834] = [306,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[33835] = [307,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[34059] = [308,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[34060] = [309,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[34061] = [310,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[34062] = [311,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[34063] = [312,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[34064] = [313,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[34065] = [314,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[34066] = [315,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[34067] = [316,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[34068] = [317,63451,"满勤签到礼包 领取成功!!"];
         this.liBaoArr[34069] = [318,63447,"1天签到礼包 领取成功!!"];
         this.liBaoArr[34070] = [319,63448,"5天签到礼包 领取成功!!"];
         this.liBaoArr[34071] = [320,63449,"14天签到礼包 领取成功!!"];
         this.liBaoArr[34072] = [321,63450,"25天签到礼包 领取成功!!"];
         this.liBaoArr[34073] = [322,63451,"满勤签到礼包 领取成功!!"];
      }
      
      private function 论坛(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-31978375.html");
         navigateToURL(request,"_blank");
      }
      
      private function onClose(e:* = null) : *
      {
         this._close_btn.removeEventListener(MouseEvent.CLICK,this.onClose);
         this._btn.removeEventListener(MouseEvent.CLICK,this.onLingQue);
         this._2_btn.removeEventListener(MouseEvent.CLICK,this.onLingQue2);
         this.parent.removeChild(this);
      }
      
      private function onTXT(e:*) : *
      {
         this._txt.text = "";
      }
      
      private function WangZhiLianJie(e:MouseEvent) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-40081638.html");
         navigateToURL(request,"_blank");
      }
      
      public function onLingQue(e:*) : *
      {
         if(Main.P1P2)
         {
            if(Main.player1.getBag().backOtherBagNum() < 1 || Main.player2.getBag().backOtherBagNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
               return;
            }
         }
         else if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
            return;
         }
         this.phpUrl.method = URLRequestMethod.POST;
         var str:String = this._txt.text;
         var uv:URLVariables = new URLVariables();
         uv.activation = str;
         uv.uid = Main.userId;
         uv.uniqueId = 7;
         var tempStr:* = uv.activation + "-" + uv.uid + "-" + uv.uniqueId + "-" + "d7adb1789427ea38327ad146e92e1dd4";
         var tokenStr:String = MD5contrast.GetScoreMD5(tempStr);
         uv.token = tokenStr;
         this.phpUrl.data = uv;
         this.urlLoader.load(this.phpUrl);
         this.urlLoader.addEventListener(Event.COMPLETE,this.completeHandler_All);
         this._btn.visible = false;
      }
      
      private function completeHandler_All2(id:int) : void
      {
         if(!this.liBaoArr[id])
         {
            trace(">>>>>>>>>>>>>>>>>>>>>>>>>>>  通用领取改版 数据未找到 id" + id);
            return;
         }
         var arr:Array = this.liBaoArr[id];
         var idX:int = int(arr[0]);
         var idX2:int = int(arr[1]);
         var strX:String = arr[2];
         if(TeShuHuoDong.TeShuHuoDongArr[idX])
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
            return;
         }
         Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(idX2));
         TeShuHuoDong.AddLiBaoNum([idX]);
         NewMC.Open("文字提示",Main._stage,480,450,60,0,true,1,strX);
         Main.Save();
      }
      
      private function completeHandler_All(e:Event) : void
      {
         var str:String = (e.currentTarget as URLLoader).data;
         var objX:Object = JSONs.decode(str,true);
         this._btn.visible = true;
         if(objX.code == 100)
         {
            TiaoShi.txtShow("礼包ID = " + objX.result);
            if(objX.result >= 33053)
            {
               this.completeHandler_All2(objX.result);
               return;
            }
            if(objX.result == 75)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[7] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63209));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[7];
               str = "双节礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 25)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[4] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63194));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[4];
               str = "安全礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 78)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[6] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63193));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[6];
               str = "桌面礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 95)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[8] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63220));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[8];
               str = "速升礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 113)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[9] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63221));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[9];
               str = "豆娃礼包领取成功!";
               Main.Save();
            }
            else if(objX.result == 123)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[12] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63231));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[12];
               str = "绝世礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 154)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[14] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63238));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[14];
               str = "周年礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 190)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63245));
               }
               str = "星灵礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 290)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[21] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63254));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[21];
               str = "双蛋豪华礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 269)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[22] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63253));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[22];
               str = "双蛋礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 314)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[23] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63259));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[23];
               str = "4399新春礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 570)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[25] != 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63270));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[25];
               str = "客户端专属礼包 领取成功!!";
               Api_4399_GongHui.upNum(15);
               Main.Save();
            }
            else if(objX.result == 894)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[26] >= 3)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取3次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63272));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[26];
               str = "五一礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 1362)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[29] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63281));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[29];
               str = "赛龙舟礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 1878)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[30] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63287));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[30];
               str = "尊享礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 2270)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[31] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63288));
               }
               ++TeShuHuoDong.TeShuHuoDongArr[31];
               str = "七夕礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 2922)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[34] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63297));
               }
               TeShuHuoDong.AddLiBaoNum(35);
               str = "中秋礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 7686)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[48] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63321));
               }
               TeShuHuoDong.AddLiBaoNum(48);
               str = "羊年礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 10012)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[57] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63333));
               }
               TeShuHuoDong.AddLiBaoNum(57);
               str = "4399游戏盒尊贵礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 10014)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[58] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63332));
               }
               TeShuHuoDong.AddLiBaoNum(58);
               str = "4399游戏盒分享礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 10996)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[59] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63334));
               }
               TeShuHuoDong.AddLiBaoNum(59);
               str = "勇士的信仰翻牌礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 13770)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[68] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63343));
               }
               TeShuHuoDong.AddLiBaoNum(68);
               str = "豪华礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 13772)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[69] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63344));
               }
               TeShuHuoDong.AddLiBaoNum(69);
               str = "伴我同行礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 19114)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[82] >= 2)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档每天只能领取2次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63365));
               }
               TeShuHuoDong.AddLiBaoNum(82);
               str = "国庆礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 19422)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63367));
               }
               TeShuHuoDong.AddLiBaoNum(83);
               str = "星灵礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 19420)
            {
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
                  Main.player2.getBag().addOtherobjBag(OtherFactory.creatOther(63366));
               }
               TeShuHuoDong.AddLiBaoNum(84);
               str = "神宠礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21050)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[97] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63374));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63374));
               }
               TeShuHuoDong.AddLiBaoNum(97);
               str = "双旦pk礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22122)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[102] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               if(!Main.P1P2)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63380));
               }
               else
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63380));
               }
               TeShuHuoDong.AddLiBaoNum(102);
               str = "猴年礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 23200)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[111] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63378));
               TeShuHuoDong.AddLiBaoNum(111);
               str = "比巴卜普通礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 23202)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[112] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63379));
               TeShuHuoDong.AddLiBaoNum(112);
               str = "比巴卜至尊礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21482)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[98] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63354));
               TeShuHuoDong.AddLiBaoNum(98);
               str = "1月份1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21484)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[99] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63355));
               TeShuHuoDong.AddLiBaoNum(99);
               str = "1月份7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21486)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[100] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63356));
               TeShuHuoDong.AddLiBaoNum(100);
               str = "1月份15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 21488)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[101] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63357));
               TeShuHuoDong.AddLiBaoNum(101);
               str = "1月份25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22520)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[98] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(98);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22522)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[99] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(99);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22524)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[100] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(100);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22526)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[101] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(101);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22528)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[107] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(107);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22530)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[108] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(108);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22532)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[109] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(109);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22534)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[110] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(110);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22536)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[113] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(113);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22538)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[114] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(114);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22540)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[115] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(115);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 22542)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[116] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(116);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24498)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[117] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(117);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24502)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[118] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(118);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24504)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[119] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(119);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24506)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[120] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(120);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24510)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[121] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63381));
               TeShuHuoDong.AddLiBaoNum(121);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24512)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[122] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63382));
               TeShuHuoDong.AddLiBaoNum(122);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24514)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[123] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63383));
               TeShuHuoDong.AddLiBaoNum(123);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 24516)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[124] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63384));
               TeShuHuoDong.AddLiBaoNum(124);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27532)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[126] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63388));
               TeShuHuoDong.AddLiBaoNum(126);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27534)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[127] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63389));
               TeShuHuoDong.AddLiBaoNum(127);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27536)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[128] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63390));
               TeShuHuoDong.AddLiBaoNum(128);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27538)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[129] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63391));
               TeShuHuoDong.AddLiBaoNum(129);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27542)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[130] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63388));
               TeShuHuoDong.AddLiBaoNum(130);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27544)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[131] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63389));
               TeShuHuoDong.AddLiBaoNum(131);
               str = "7天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27546)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[132] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63390));
               TeShuHuoDong.AddLiBaoNum(132);
               str = "15天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 27548)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[133] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63391));
               TeShuHuoDong.AddLiBaoNum(133);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 30680)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[135] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63395));
               TeShuHuoDong.AddLiBaoNum(135);
               str = "手游通礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 30722)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[136] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63396));
               TeShuHuoDong.AddLiBaoNum(136);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 30724)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[137] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63397));
               TeShuHuoDong.AddLiBaoNum(137);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 30726)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[138] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63398));
               TeShuHuoDong.AddLiBaoNum(138);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 30728)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[139] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63399));
               TeShuHuoDong.AddLiBaoNum(139);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31776)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[140] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63400));
               TeShuHuoDong.AddLiBaoNum(140);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31778)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[141] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63401));
               TeShuHuoDong.AddLiBaoNum(141);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31780)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[142] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63402));
               TeShuHuoDong.AddLiBaoNum(142);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31782)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[143] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63403));
               TeShuHuoDong.AddLiBaoNum(143);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31900)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[145] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63400));
               TeShuHuoDong.AddLiBaoNum(145);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31902)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[146] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63401));
               TeShuHuoDong.AddLiBaoNum(146);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31904)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[147] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63402));
               TeShuHuoDong.AddLiBaoNum(147);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31906)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[148] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63403));
               TeShuHuoDong.AddLiBaoNum(148);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31990)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[149] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63400));
               TeShuHuoDong.AddLiBaoNum(149);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31992)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[150] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63401));
               TeShuHuoDong.AddLiBaoNum(150);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31994)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[151] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63402));
               TeShuHuoDong.AddLiBaoNum(151);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 31996)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[152] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63403));
               TeShuHuoDong.AddLiBaoNum(152);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32076)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[154] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63406));
               TeShuHuoDong.AddLiBaoNum(154);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32078)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[155] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63407));
               TeShuHuoDong.AddLiBaoNum(155);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32080)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[156] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63408));
               TeShuHuoDong.AddLiBaoNum(156);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32082)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[157] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63409));
               TeShuHuoDong.AddLiBaoNum(157);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32084)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[159] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63406));
               TeShuHuoDong.AddLiBaoNum(159);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32086)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[160] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63407));
               TeShuHuoDong.AddLiBaoNum(160);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32088)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[161] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63408));
               TeShuHuoDong.AddLiBaoNum(161);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32090)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[162] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63409));
               TeShuHuoDong.AddLiBaoNum(162);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32361)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[165] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63413));
               TeShuHuoDong.AddLiBaoNum(165);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32362)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[166] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63414));
               TeShuHuoDong.AddLiBaoNum(166);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32363)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[167] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63415));
               TeShuHuoDong.AddLiBaoNum(167);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32364)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[168] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63416));
               TeShuHuoDong.AddLiBaoNum(168);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32365)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[169] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63413));
               TeShuHuoDong.AddLiBaoNum(169);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32366)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[170] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63414));
               TeShuHuoDong.AddLiBaoNum(170);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32367)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[171] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63415));
               TeShuHuoDong.AddLiBaoNum(171);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32368)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[172] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63416));
               TeShuHuoDong.AddLiBaoNum(172);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32408)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[173] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63417));
               TeShuHuoDong.AddLiBaoNum(173);
               str = "礼包领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32024)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[153] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63405));
               TeShuHuoDong.AddLiBaoNum(153);
               str = "微信专属礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32218)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[163] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63411));
               TeShuHuoDong.AddLiBaoNum(163);
               str = "新春礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32450)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[175] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63418));
               TeShuHuoDong.AddLiBaoNum(175);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32451)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[176] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63419));
               TeShuHuoDong.AddLiBaoNum(176);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32452)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[177] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63420));
               TeShuHuoDong.AddLiBaoNum(177);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32453)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[178] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63421));
               TeShuHuoDong.AddLiBaoNum(178);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32454)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[179] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63422));
               TeShuHuoDong.AddLiBaoNum(179);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32498)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[180] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63424));
               TeShuHuoDong.AddLiBaoNum(180);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32499)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[181] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63425));
               TeShuHuoDong.AddLiBaoNum(181);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32500)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[182] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63426));
               TeShuHuoDong.AddLiBaoNum(182);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32501)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[183] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63427));
               TeShuHuoDong.AddLiBaoNum(183);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32502)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[184] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63428));
               TeShuHuoDong.AddLiBaoNum(184);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32502)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[190] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63434));
               TeShuHuoDong.AddLiBaoNum(190);
               str = "夏日激情礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32524)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[185] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63429));
               TeShuHuoDong.AddLiBaoNum(185);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32525)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[186] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63430));
               TeShuHuoDong.AddLiBaoNum(186);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32526)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[187] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63431));
               TeShuHuoDong.AddLiBaoNum(187);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32527)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[188] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63432));
               TeShuHuoDong.AddLiBaoNum(188);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32528)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[189] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63433));
               TeShuHuoDong.AddLiBaoNum(189);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32606)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[192] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63436));
               TeShuHuoDong.AddLiBaoNum(192);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32607)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[193] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63437));
               TeShuHuoDong.AddLiBaoNum(193);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32608)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[194] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63438));
               TeShuHuoDong.AddLiBaoNum(194);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32609)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[195] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63439));
               TeShuHuoDong.AddLiBaoNum(195);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32610)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[196] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63440));
               TeShuHuoDong.AddLiBaoNum(196);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32643)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[198] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63441));
               TeShuHuoDong.AddLiBaoNum(198);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32642)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[199] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63442));
               TeShuHuoDong.AddLiBaoNum(199);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32645)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[200] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63443));
               TeShuHuoDong.AddLiBaoNum(200);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32646)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[201] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63444));
               TeShuHuoDong.AddLiBaoNum(201);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32647)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[202] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63445));
               TeShuHuoDong.AddLiBaoNum(202);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32715)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[203] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63447));
               TeShuHuoDong.AddLiBaoNum(203);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32716)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[204] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63448));
               TeShuHuoDong.AddLiBaoNum(204);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32717)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[205] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63449));
               TeShuHuoDong.AddLiBaoNum(205);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32718)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[206] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63450));
               TeShuHuoDong.AddLiBaoNum(206);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32719)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[207] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63451));
               TeShuHuoDong.AddLiBaoNum(207);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32764)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[208] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63447));
               TeShuHuoDong.AddLiBaoNum(208);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32765)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[209] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63448));
               TeShuHuoDong.AddLiBaoNum(209);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32766)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[210] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63449));
               TeShuHuoDong.AddLiBaoNum(210);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32767)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[211] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63450));
               TeShuHuoDong.AddLiBaoNum(211);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32768)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[212] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63451));
               TeShuHuoDong.AddLiBaoNum(212);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32846)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[213] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63447));
               TeShuHuoDong.AddLiBaoNum(213);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32847)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[214] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63448));
               TeShuHuoDong.AddLiBaoNum(214);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32848)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[215] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63449));
               TeShuHuoDong.AddLiBaoNum(215);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32849)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[216] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63450));
               TeShuHuoDong.AddLiBaoNum(216);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32850)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[217] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63451));
               TeShuHuoDong.AddLiBaoNum(217);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32871)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[218] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63447));
               TeShuHuoDong.AddLiBaoNum(218);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32872)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[219] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63448));
               TeShuHuoDong.AddLiBaoNum(219);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32873)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[220] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63449));
               TeShuHuoDong.AddLiBaoNum(220);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32874)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[221] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63450));
               TeShuHuoDong.AddLiBaoNum(221);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32875)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[222] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63451));
               TeShuHuoDong.AddLiBaoNum(222);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32876)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[223] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63447));
               TeShuHuoDong.AddLiBaoNum(223);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32877)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[224] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63448));
               TeShuHuoDong.AddLiBaoNum(224);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32878)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[225] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63449));
               TeShuHuoDong.AddLiBaoNum(225);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32879)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[226] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63450));
               TeShuHuoDong.AddLiBaoNum(226);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32880)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[227] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63451));
               TeShuHuoDong.AddLiBaoNum(227);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32881)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[228] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63447));
               TeShuHuoDong.AddLiBaoNum(228);
               str = "1天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32882)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[229] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63448));
               TeShuHuoDong.AddLiBaoNum(229);
               str = "5天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32883)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[230] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63449));
               TeShuHuoDong.AddLiBaoNum(230);
               str = "14天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32884)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[231] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63450));
               TeShuHuoDong.AddLiBaoNum(231);
               str = "25天签到礼包 领取成功!!";
               Main.Save();
            }
            else if(objX.result == 32885)
            {
               if(TeShuHuoDong.TeShuHuoDongArr[232] >= 1)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取1次");
                  return;
               }
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63451));
               TeShuHuoDong.AddLiBaoNum(232);
               str = "满勤签到礼包 领取成功!!";
               Main.Save();
            }
            else
            {
               str = "验证码错误!!";
            }
         }
         else if(objX.code == 101)
         {
            str = "参数错误";
         }
         else if(objX.code == 102)
         {
            str = "验证码不存在";
         }
         else if(objX.code == 103)
         {
            str = "验证码还没被兑换";
         }
         else if(objX.code == 104)
         {
            str = "验证码被使用过";
         }
         else if(objX.code == 105)
         {
            str = "验证码只能被领取者使用";
         }
         else if(objX.code == 106)
         {
            str = "您的账号已经使用过此礼包的激活码";
         }
         else if(objX.code == 107)
         {
            str = "token无效";
         }
         else if(objX.code == 108)
         {
            str = "激活码失效了";
         }
         else if(objX.code == 109)
         {
            str = "激活失败";
         }
         else if(objX.code == 110)
         {
            str = "您的账号今天已使用过激活码";
         }
         else
         {
            str = "未知错误";
         }
         NewMC.Open("文字提示",Main._stage,480,450,60,0,true,1,str);
      }
      
      private function WangZhiLianJie2(e:MouseEvent) : *
      {
         if(Main.serverTime.getValue() < this.liBao2Time && !Main.tiaoShiYN)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,this.liBao2Str);
            return;
         }
         var request:URLRequest = new URLRequest("http://huodong.4399.com/2016/djyx/");
         navigateToURL(request,"_blank");
      }
      
      private function onTXT2(e:*) : *
      {
         this._2_txt.text = "";
      }
      
      private function onLingQue2(e:*) : *
      {
         if(Main.serverTime.getValue() < this.liBao2Time && !Main.tiaoShiYN)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,this.liBao2Str);
            return;
         }
         this._2_btn.visible = false;
         if(TeShuHuoDong.TeShuHuoDongArr[this.liBao2ID_X] > 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
            return;
         }
         if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
            this._2_btn.visible = true;
            return;
         }
         this.phpUrl2.method = URLRequestMethod.GET;
         var str:String = this._2_txt.text;
         var tempKeyStr:String = this.liBao2key + Main.userId + this.liBao2ID + str;
         var keyStr:String = MD5contrast.GetScoreMD5(MD5contrast.GetScoreMD5(tempKeyStr));
         var tempStr:String = "uid=" + Main.userId + "&type=" + this.liBao2ID + "&code=" + str + "&key=" + keyStr;
         this.phpUrl2.data = tempStr;
         TiaoShi.txtShow("独家礼包: " + tempStr);
         this.urlLoader2.load(this.phpUrl2);
         this.urlLoader2.addEventListener(Event.COMPLETE,this.completeHandler2);
      }
      
      private function completeHandler2(e:Event) : void
      {
         this._2_btn.visible = true;
         var str:String = (e.currentTarget as URLLoader).data;
         TiaoShi.txtShow("str = " + str);
         if(str == "20000")
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63446));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"独家礼包 领取成功!");
            TeShuHuoDong.AddLiBaoNum(this.liBao2ID_X);
            Main.Save();
         }
         else
         {
            if(str == "10000")
            {
               str = "参数不完整";
            }
            else if(str == "10001")
            {
               str = "礼包id不正确";
            }
            else if(str == "10002")
            {
               str = "校验码不正确";
            }
            else if(str == "10003")
            {
               str = "用户没有领取过相关类型的礼包";
            }
            else if(str == "10004")
            {
               str = "礼包的激活码没有和用户绑定,或者激活码有误";
            }
            else
            {
               str = "未知错误";
            }
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,str);
         }
      }
      
      private function WangZhiLianJie3(e:MouseEvent) : *
      {
         if(Main.serverTime.getValue() < this.liBao3Time && !Main.tiaoShiYN)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,this.liBao3Str);
            return;
         }
         var request:URLRequest = new URLRequest("http://huodong.4399.com/2017/ndyx/");
         navigateToURL(request,"_blank");
      }
      
      private function onTXT3(e:*) : *
      {
         this._3_txt.text = "";
      }
      
      private function onLingQue3(e:*) : *
      {
         if(Main.serverTime.getValue() < this.liBao3Time && !Main.tiaoShiYN)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,this.liBao3Str);
            return;
         }
         this._3_btn.visible = false;
         if(TeShuHuoDong.TeShuHuoDongArr[this.liBao3ID_X] > 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"每个存档只能领取一次");
            return;
         }
         if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包已满");
            this._3_btn.visible = true;
            return;
         }
         this.phpUrl3.method = URLRequestMethod.GET;
         var str:String = this._3_txt.text;
         var tempKeyStr:String = this.liBao3key + Main.userId + this.liBao3ID + str;
         var keyStr:String = MD5contrast.GetScoreMD5(MD5contrast.GetScoreMD5(tempKeyStr));
         var tempStr:String = "uid=" + Main.userId + "&type=" + this.liBao3ID + "&code=" + str + "&key=" + keyStr;
         this.phpUrl3.data = tempStr;
         TiaoShi.txtShow("独家礼包: " + tempStr);
         this.urlLoader3.load(this.phpUrl3);
         this.urlLoader3.addEventListener(Event.COMPLETE,this.completeHandler3);
      }
      
      private function completeHandler3(e:Event) : void
      {
         this._3_btn.visible = true;
         var str:String = (e.currentTarget as URLLoader).data;
         TiaoShi.txtShow("str = " + str);
         if(str == "20000")
         {
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63434));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"夏日激情礼包 领取成功!");
            TeShuHuoDong.AddLiBaoNum(this.liBao3ID_X);
            Main.Save();
         }
         else
         {
            if(str == "10000")
            {
               str = "参数不完整";
            }
            else if(str == "10001")
            {
               str = "礼包id不正确";
            }
            else if(str == "10002")
            {
               str = "校验码不正确";
            }
            else if(str == "10003")
            {
               str = "激活码有误";
            }
            else
            {
               str = "未知错误";
            }
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,str);
         }
      }
   }
}

