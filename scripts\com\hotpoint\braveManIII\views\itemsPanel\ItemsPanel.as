package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.views.elvesPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.stampPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.titelPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.ui.*;
   import src.*;
   import src.tool.*;
   
   public class ItemsPanel extends MovieClip
   {
      
      public static var itemsPanel:MovieClip;
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var menuTooltip:MenuTooltip;
      
      public static var dragObj:MovieClip;
      
      public static var clickObj:MovieClip;
      
      private static var pointx:Number;
      
      private static var pointy:Number;
      
      private static var allBagHandle:AllBagHandle;
      
      public static var myplayer:Player;
      
      public static var ip:ItemsPanel;
      
      public static var loadData:ClassLoader;
      
      public static var kuozhanOK:Boolean = false;
      
      public static var yeshu:int = 0;
      
      public static var isPOne:Boolean = true;
      
      public static var boolFlag:Boolean = false;
      
      public static var isDown:Boolean = false;
      
      public static var returnx:int = 0;
      
      public static var returny:int = 0;
      
      private static var oldNum:uint = 0;
      
      public static var itemsType:uint = 1;
      
      private static var openFlag:Boolean = false;
      
      private static var closeTimes:uint = 0;
      
      private static var myMenu:ContextMenu = new ContextMenu();
      
      private static var fenjie_bool:Boolean = false;
      
      public static var OpenFrist:Boolean = false;
      
      public static var loadName:String = "newbag_v1840.swf";
      
      private static var OpenYN:Boolean = false;
      
      private static var OpenFristJL:Boolean = true;
      
      public static var jiangliNum:int = 0;
      
      public static var jiangliNum3:int = 0;
      
      public static var jiangliNum2:int = 0;
      
      public function ItemsPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.mouseEnabled = false;
         menuTooltip = new MenuTooltip();
         allBagHandle = new AllBagHandle();
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = itemsPanel.getChildIndex(itemsPanel["s1_" + i]);
            mm.x = itemsPanel["s1_" + i].x;
            mm.y = itemsPanel["s1_" + i].y;
            mm.name = "s1_" + i;
            itemsPanel.removeChild(itemsPanel["s1_" + i]);
            itemsPanel["s1_" + i] = mm;
            itemsPanel.addChild(mm);
            itemsPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = itemsPanel.getChildIndex(itemsPanel["z1_" + i]);
            mm.x = itemsPanel["z1_" + i].x;
            mm.y = itemsPanel["z1_" + i].y;
            mm.name = "z1_" + i;
            itemsPanel.removeChild(itemsPanel["z1_" + i]);
            itemsPanel["z1_" + i] = mm;
            itemsPanel.addChild(mm);
            itemsPanel.setChildIndex(mm,num);
         }
         var mm2:MovieClip = new Shop_picNEW();
         mm2.x = itemsPanel["getFJ"]["showPic"].x;
         mm2.y = itemsPanel["getFJ"]["showPic"].y;
         mm2.name = "showPic";
         itemsPanel["getFJ"].removeChild(itemsPanel["getFJ"]["showPic"]);
         itemsPanel["getFJ"]["showPic"] = mm2;
         itemsPanel["getFJ"].addChild(mm2);
         var mm3:MovieClip = new Shop_picNEW();
         mm3.x = itemsPanel["sellMenu"]["showPic"].x;
         mm3.y = itemsPanel["sellMenu"]["showPic"].y;
         mm3.name = "showPic";
         itemsPanel["sellMenu"].removeChild(itemsPanel["sellMenu"]["showPic"]);
         itemsPanel["sellMenu"]["showPic"] = mm3;
         itemsPanel["sellMenu"].addChild(mm3);
      }
      
      private static function LoadSkin() : *
      {
         if(!itemsPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
         }
         if(OpenFrist)
         {
            LoadInGame.Open(loadData);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("NewBagShow") as Class;
         itemsPanel = new classRef();
         ip.addChild(itemsPanel);
         InitIcon();
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         ip = new ItemsPanel();
         LoadSkin();
         Main._stage.addChild(ip);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         ip = new ItemsPanel();
         Main._stage.addChild(ip);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         timeXiuZheng();
         Main.allClosePanel();
         if(itemsPanel)
         {
            boolFlag = true;
            yeshu = 0;
            Main.stopXX = true;
            ip.x = 0;
            ip.y = 0;
            Player.getEquipDataXX();
            Main.player1.getBag().cheatTesting();
            Main.player1.getBag().cheatGem();
            Main.player1.getBag().cheatOther();
            myplayer = Main.player_1;
            NvShenRenWu();
            addListenerP1();
            if(Main.P1P2)
            {
               itemsPanel["bagOne"].visible = true;
               itemsPanel["bagTwo"].visible = true;
               Main.player2.getBag().cheatTesting();
               Main.player2.getBag().cheatGem();
               Main.player2.getBag().cheatOther();
            }
            else
            {
               itemsPanel["bagOne"].visible = false;
               itemsPanel["bagTwo"].visible = false;
            }
            ip.visible = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(itemsPanel)
         {
            close_fenjie();
            boolFlag = false;
            Main.player1.getBag().cheatGem();
            Main.player1.getBag().cheatOther();
            if(Main.P1P2)
            {
               Main.player2.getBag().cheatGem();
               Main.player2.getBag().cheatOther();
            }
            Main.stopXX = false;
            if(menuTooltip)
            {
               menuTooltip.visible = false;
            }
            removeListenerP1();
            ip.visible = false;
         }
         else
         {
            InitClose();
         }
      }
      
      private static function closeppp(e:*) : void
      {
         close();
         twoFalse();
         isPOne = true;
      }
      
      public static function addListenerP1() : *
      {
         var z:uint = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            itemsPanel["s1_" + i].mouseChildren = false;
            itemsPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            itemsPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            itemsPanel["s1_" + i].addEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            itemsPanel["s1_" + i].addEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(var s:uint = 3; s < 11; s++)
         {
            itemsPanel["str_" + s].addEventListener(MouseEvent.MOUSE_OVER,strengthenOpen);
            itemsPanel["str_" + s].addEventListener(MouseEvent.MOUSE_OUT,strengthenClose);
         }
         for(var k:uint = 0; k < 6; k++)
         {
            itemsPanel["hz" + k].stop();
            itemsPanel["hz" + k].addEventListener(MouseEvent.MOUSE_OVER,hzOpen);
            itemsPanel["hz" + k].addEventListener(MouseEvent.MOUSE_OUT,hzClose);
         }
         for(var j:uint = 0; j < 8; j++)
         {
            itemsPanel["k1_" + j].mouseChildren = false;
            itemsPanel["z1_" + j].mouseChildren = false;
            itemsPanel["z1_" + j].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            itemsPanel["z1_" + j].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            itemsPanel["z1_" + j].addEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            itemsPanel["z1_" + j].addEventListener(MouseEvent.CLICK,menuOpen);
         }
         itemsPanel["jingling"].stop();
         itemsPanel["closePanel"].addEventListener(MouseEvent.CLICK,closeppp);
         itemsPanel["chenghao_btn"].addEventListener(MouseEvent.CLICK,chenghaoOpen);
         itemsPanel["jingling_btn"].addEventListener(MouseEvent.CLICK,jinglingOpen);
         itemsPanel["qianneng_btn"].addEventListener(MouseEvent.CLICK,qiannengOpen);
         itemsPanel.addEventListener(BtnEvent.DO_CHANGE,bagListen);
         itemsPanel.addEventListener(Event.DEACTIVATE,loseFocus);
         myMenu.addEventListener(ContextMenuEvent.MENU_SELECT,loseFocus);
         Main._stage.addEventListener(Event.MOUSE_LEAVE,loseFocus);
         Main._stage.addEventListener(MouseEvent.MOUSE_UP,loseFocus);
         itemsPanel.contextMenu = myMenu;
         for(var n:uint = 0; n < 2; n++)
         {
            itemsPanel["g1_" + n].addEventListener(MouseEvent.CLICK,menuOpen);
            itemsPanel["g1_" + n].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            itemsPanel["g1_" + n].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         }
         for(z = 0; z < 5; z++)
         {
            itemsPanel["fudai"]["xz" + z].addEventListener(MouseEvent.CLICK,xuanzejiangli);
            itemsPanel["fudai"]["xz" + z].addEventListener(MouseEvent.MOUSE_OVER,fudaiOpen);
            itemsPanel["fudai"]["xz" + z].addEventListener(MouseEvent.MOUSE_OUT,fudaiClose);
         }
         for(z = 0; z < 5; z++)
         {
            itemsPanel["fudai2"]["xz" + z].addEventListener(MouseEvent.CLICK,xuanzejiangli2);
            itemsPanel["fudai2"]["xz" + z].addEventListener(MouseEvent.MOUSE_OVER,fudaiOpen2);
            itemsPanel["fudai2"]["xz" + z].addEventListener(MouseEvent.MOUSE_OUT,fudaiClose2);
         }
         for(z = 0; z < 5; z++)
         {
            itemsPanel["fudai3"]["xz" + z].addEventListener(MouseEvent.CLICK,xuanzejiangli3);
            itemsPanel["fudai3"]["xz" + z].addEventListener(MouseEvent.MOUSE_OVER,fudaiOpen3);
            itemsPanel["fudai3"]["xz" + z].addEventListener(MouseEvent.MOUSE_OUT,fudaiClose3);
         }
         for(var p:* = 0; p < 6; p++)
         {
            ItemsPanel.itemsPanel["bagLock" + p].addEventListener(MouseEvent.CLICK,kuozhanDo);
         }
         itemsPanel["NoMoney_mc"]["no_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         itemsPanel["NoMoney_mc"]["yes_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         itemsPanel["NoMoney_mc"]["addMoney_btn"].addEventListener(MouseEvent.CLICK,addMoney_btn);
         itemsPanel["yeshu_1"].addEventListener(MouseEvent.CLICK,oneGo);
         itemsPanel["yeshu_2"].addEventListener(MouseEvent.CLICK,twoGo);
         itemsPanel["fudai"]["select"].mouseEnabled = false;
         itemsPanel["fudai2"]["select"].mouseEnabled = false;
         itemsPanel["fudai3"]["select"].mouseEnabled = false;
         itemsPanel["fudai"]["lingqu"].addEventListener(MouseEvent.CLICK,lingqujiangli);
         itemsPanel["fudai2"]["lingqu"].addEventListener(MouseEvent.CLICK,lingqujiangli2);
         itemsPanel["fudai3"]["lingqu"].addEventListener(MouseEvent.CLICK,lingqujiangli3);
         itemsPanel["all_fenjie"].addEventListener(MouseEvent.CLICK,all_fenjie);
         itemsPanel.addEventListener(MouseEvent.MOUSE_MOVE,DragHandler);
         itemsPanel.addEventListener(MouseEvent.MOUSE_UP,UpHandler);
         itemsPanel.addEventListener(MouseEvent.CLICK,menuClose);
         menuTooltip.addListener();
         BagItemsShow.allHide();
         BagItemsShow.equipShow();
         BagItemsShow.badgeSlotShow();
         BagItemsShow.slotShow();
         BagItemsShow.skillSlotShow();
         BagItemsShow.informationShow();
         allFalse();
         itemsPanel["NoMoney_mc"].visible = false;
         itemsPanel["touming"].visible = false;
         itemsPanel["closePanel"].addEventListener(MouseEvent.CLICK,closeppp);
         itemsPanel["getFJ"].mouseEnabled = false;
         itemsPanel["all_fenjie"].visible = true;
         itemsPanel["bagOne"].isClick = true;
         itemsPanel["bg1_1"].isClick = true;
         itemsPanel["getFJ"].visible = false;
         itemsPanel["isFenJie"].visible = false;
         itemsPanel["isSell"].visible = false;
         itemsPanel["isAllSell"].visible = false;
         itemsPanel["sellMenu"].visible = false;
         itemsPanel["strengTip"].visible = false;
         itemsPanel["hzTip"].visible = false;
         itemsPanel["fudai"].visible = false;
         itemsPanel["fudai2"].visible = false;
         itemsPanel["fudai3"].visible = false;
         itemsPanel["renwutishi"].visible = false;
         itemsPanel["renwutishi"].mouseChildren = false;
         itemsPanel["renwutishi"].mouseEnabled = false;
         itemsPanel["xinshou"].visible = false;
         if(OpenFristJL)
         {
            OpenFristJL = false;
            itemsPanel["jinglingOPEN"].visible = false;
         }
         if(Main.newPlay == 3)
         {
            itemsPanel["xinshou"].visible = true;
         }
         itemsType = 1;
      }
      
      public static function removeListenerP1() : *
      {
         for(var i:uint = 0; i < 24; i++)
         {
            itemsPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            itemsPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            itemsPanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            itemsPanel["s1_" + i].removeEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(var j:uint = 0; j < 8; j++)
         {
            itemsPanel["z1_" + j].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            itemsPanel["z1_" + j].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            itemsPanel["z1_" + j].removeEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            itemsPanel["z1_" + j].removeEventListener(MouseEvent.CLICK,menuOpen);
         }
         for(var s:uint = 3; s < 11; s++)
         {
            itemsPanel["str_" + s].removeEventListener(MouseEvent.MOUSE_OVER,strengthenOpen);
            itemsPanel["str_" + s].removeEventListener(MouseEvent.MOUSE_OUT,strengthenClose);
         }
         for(var k:uint = 0; k < 6; k++)
         {
            itemsPanel["hz" + k].removeEventListener(MouseEvent.MOUSE_OVER,hzOpen);
            itemsPanel["hz" + k].removeEventListener(MouseEvent.MOUSE_OUT,hzClose);
         }
         for(var n:uint = 0; n < 2; n++)
         {
            itemsPanel["g1_" + n].removeEventListener(MouseEvent.CLICK,menuOpen);
            itemsPanel["g1_" + n].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            itemsPanel["g1_" + n].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         }
         for(var p:* = 0; p < 6; p++)
         {
            ItemsPanel.itemsPanel["bagLock" + p].removeEventListener(MouseEvent.CLICK,kuozhanDo);
         }
         itemsPanel["NoMoney_mc"]["no_btn"].removeEventListener(MouseEvent.CLICK,closeNORMB);
         itemsPanel["NoMoney_mc"]["yes_btn"].removeEventListener(MouseEvent.CLICK,closeNORMB);
         itemsPanel["NoMoney_mc"]["addMoney_btn"].removeEventListener(MouseEvent.CLICK,addMoney_btn);
         itemsPanel["yeshu_1"].removeEventListener(MouseEvent.CLICK,oneGo);
         itemsPanel["yeshu_2"].removeEventListener(MouseEvent.CLICK,twoGo);
         itemsPanel.addEventListener(Event.DEACTIVATE,loseFocus);
         myMenu.addEventListener(ContextMenuEvent.MENU_SELECT,loseFocus);
         Main._stage.addEventListener(Event.MOUSE_LEAVE,loseFocus);
         Main._stage.removeEventListener(MouseEvent.MOUSE_UP,loseFocus);
         itemsPanel.contextMenu = myMenu;
         itemsPanel.removeEventListener(BtnEvent.DO_CHANGE,bagListen);
         itemsPanel.removeEventListener(MouseEvent.MOUSE_MOVE,DragHandler);
         itemsPanel.removeEventListener(MouseEvent.MOUSE_UP,UpHandler);
         itemsPanel.removeEventListener(MouseEvent.CLICK,menuClose);
         menuTooltip.removeListener();
      }
      
      private static function closeNORMB(e:*) : void
      {
         itemsPanel["NoMoney_mc"].visible = false;
      }
      
      private static function addMoney_btn(e:*) : void
      {
         Main.ChongZhi();
      }
      
      public static function kuoBagRMBOK() : *
      {
         if(kuozhanOK)
         {
            if(!Main.P1P2)
            {
               if(itemsType == 1)
               {
                  myplayer.data.getBag().addLimitE();
                  BagItemsShow.equipShow();
               }
               else if(itemsType == 2)
               {
                  myplayer.data.getBag().addLimitS();
                  BagItemsShow.suppliesShow();
               }
               else if(itemsType == 3)
               {
                  myplayer.data.getBag().addLimitG();
                  BagItemsShow.gemShow();
               }
               else if(itemsType == 4)
               {
                  myplayer.data.getBag().addLimitO();
                  BagItemsShow.otherobjShow();
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
               kuozhanOK = false;
               itemsPanel["touming"].visible = false;
            }
            else
            {
               if(itemsType == 1)
               {
                  Main.player1.getBag().addLimitE();
                  Main.player2.getBag().addLimitE();
                  BagItemsShow.equipShow();
               }
               else if(itemsType == 2)
               {
                  Main.player1.getBag().addLimitS();
                  Main.player2.getBag().addLimitS();
                  BagItemsShow.suppliesShow();
               }
               else if(itemsType == 3)
               {
                  Main.player1.getBag().addLimitG();
                  Main.player2.getBag().addLimitG();
                  BagItemsShow.gemShow();
               }
               else if(itemsType == 4)
               {
                  Main.player1.getBag().addLimitO();
                  Main.player2.getBag().addLimitO();
                  BagItemsShow.otherobjShow();
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
               kuozhanOK = false;
               itemsPanel["touming"].visible = false;
            }
            BagItemsShow.informationShow();
         }
      }
      
      private static function kuozhanDo(e:*) : *
      {
         if(!Main.P1P2)
         {
            if(itemsType == 1)
            {
               if(myplayer.data.getKillPoint() >= 250)
               {
                  myplayer.data.getBag().addLimitE();
                  myplayer.data.AddKillPoint(-250);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
               }
               else if(Shop4399.moneyAll.getValue() >= 20)
               {
                  Api_4399_All.BuyObj(InitData.kuoBag.getValue());
                  kuozhanOK = true;
                  itemsPanel["touming"].visible = true;
               }
               else
               {
                  itemsPanel["NoMoney_mc"].visible = true;
               }
               BagItemsShow.equipShow();
            }
            else if(itemsType == 2)
            {
               if(myplayer.data.getKillPoint() >= 250)
               {
                  myplayer.data.getBag().addLimitS();
                  myplayer.data.AddKillPoint(-250);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
               }
               else if(Shop4399.moneyAll.getValue() >= 20)
               {
                  Api_4399_All.BuyObj(InitData.kuoBag.getValue());
                  kuozhanOK = true;
                  itemsPanel["touming"].visible = true;
               }
               else
               {
                  itemsPanel["NoMoney_mc"].visible = true;
               }
               BagItemsShow.suppliesShow();
            }
            else if(itemsType == 3)
            {
               if(myplayer.data.getKillPoint() >= 250)
               {
                  myplayer.data.getBag().addLimitG();
                  myplayer.data.AddKillPoint(-250);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
               }
               else if(Shop4399.moneyAll.getValue() >= 20)
               {
                  Api_4399_All.BuyObj(InitData.kuoBag.getValue());
                  kuozhanOK = true;
                  itemsPanel["touming"].visible = true;
               }
               else
               {
                  itemsPanel["NoMoney_mc"].visible = true;
               }
               BagItemsShow.gemShow();
            }
            else if(itemsType == 4)
            {
               if(myplayer.data.getKillPoint() >= 250)
               {
                  myplayer.data.getBag().addLimitO();
                  myplayer.data.AddKillPoint(-250);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
               }
               else if(Shop4399.moneyAll.getValue() >= 20)
               {
                  Api_4399_All.BuyObj(InitData.kuoBag.getValue());
                  kuozhanOK = true;
                  itemsPanel["touming"].visible = true;
               }
               else
               {
                  itemsPanel["NoMoney_mc"].visible = true;
               }
               BagItemsShow.otherobjShow();
            }
         }
         else if(itemsType == 1)
         {
            if(Main.player1.getKillPoint() >= 250 && Main.player2.getKillPoint() >= 250)
            {
               Main.player1.getBag().addLimitE();
               Main.player1.AddKillPoint(-250);
               Main.player2.getBag().addLimitE();
               Main.player2.AddKillPoint(-250);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
            }
            else if(Shop4399.moneyAll.getValue() >= 20)
            {
               Api_4399_All.BuyObj(InitData.kuoBag.getValue());
               kuozhanOK = true;
               itemsPanel["touming"].visible = true;
            }
            else
            {
               itemsPanel["NoMoney_mc"].visible = true;
            }
            BagItemsShow.equipShow();
         }
         else if(itemsType == 2)
         {
            if(Main.player1.getKillPoint() >= 250 && Main.player2.getKillPoint() >= 250)
            {
               Main.player1.getBag().addLimitS();
               Main.player1.AddKillPoint(-250);
               Main.player2.getBag().addLimitS();
               Main.player2.AddKillPoint(-250);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
            }
            else if(Shop4399.moneyAll.getValue() >= 20)
            {
               Api_4399_All.BuyObj(InitData.kuoBag.getValue());
               kuozhanOK = true;
               itemsPanel["touming"].visible = true;
            }
            else
            {
               itemsPanel["NoMoney_mc"].visible = true;
            }
            BagItemsShow.suppliesShow();
         }
         else if(itemsType == 3)
         {
            if(Main.player1.getKillPoint() >= 250 && Main.player2.getKillPoint() >= 250)
            {
               Main.player1.getBag().addLimitG();
               Main.player1.AddKillPoint(-250);
               Main.player2.getBag().addLimitG();
               Main.player2.AddKillPoint(-250);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
            }
            else if(Shop4399.moneyAll.getValue() >= 20)
            {
               Api_4399_All.BuyObj(InitData.kuoBag.getValue());
               kuozhanOK = true;
               itemsPanel["touming"].visible = true;
            }
            else
            {
               itemsPanel["NoMoney_mc"].visible = true;
            }
            BagItemsShow.gemShow();
         }
         else if(itemsType == 4)
         {
            if(Main.player1.getKillPoint() >= 250 && Main.player2.getKillPoint() >= 250)
            {
               Main.player1.getBag().addLimitO();
               Main.player1.AddKillPoint(-250);
               Main.player2.getBag().addLimitO();
               Main.player2.AddKillPoint(-250);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"成功扩展背包");
            }
            else if(Shop4399.moneyAll.getValue() >= 20)
            {
               Api_4399_All.BuyObj(InitData.kuoBag.getValue());
               kuozhanOK = true;
               itemsPanel["touming"].visible = true;
            }
            else
            {
               itemsPanel["NoMoney_mc"].visible = true;
            }
            BagItemsShow.otherobjShow();
         }
         BagItemsShow.informationShow();
      }
      
      private static function oneGo(e:*) : *
      {
         yeshu = 0;
         if(itemsType == 1)
         {
            BagItemsShow.equipShow();
         }
         else if(itemsType == 2)
         {
            BagItemsShow.suppliesShow();
         }
         else if(itemsType == 3)
         {
            BagItemsShow.gemShow();
         }
         else if(itemsType == 4)
         {
            BagItemsShow.otherobjShow();
         }
         else if(itemsType == 5)
         {
            BagItemsShow.questShow();
         }
         BagItemsShow.informationShow();
      }
      
      private static function twoGo(e:*) : *
      {
         yeshu = 1;
         if(itemsType == 1)
         {
            BagItemsShow.equipShow();
         }
         else if(itemsType == 2)
         {
            BagItemsShow.suppliesShow();
         }
         else if(itemsType == 3)
         {
            BagItemsShow.gemShow();
         }
         else if(itemsType == 4)
         {
            BagItemsShow.otherobjShow();
         }
         else if(itemsType == 5)
         {
            BagItemsShow.questShow();
         }
         BagItemsShow.informationShow();
      }
      
      private static function chenghaoOpen(e:*) : *
      {
         TitelPanel.open(isPOne);
      }
      
      private static function jinglingOpen(e:*) : *
      {
         ElvesPanel.open(myplayer.data);
         itemsPanel["jinglingOPEN"].visible = false;
      }
      
      private static function qiannengOpen(e:*) : *
      {
         StampPanel.open(myplayer.data);
      }
      
      private static function loseFocus(e:Event = null) : *
      {
         if(dragObj)
         {
            UpHandler();
         }
      }
      
      private static function all_fenjie(e:MouseEvent) : void
      {
         if(fenjie_bool)
         {
            itemsPanel["mouseFJ"].x = 20000;
            Mouse.show();
            fenjie_bool = false;
            itemsPanel.removeEventListener(MouseEvent.MOUSE_MOVE,gensui);
         }
         else
         {
            fenjie_bool = true;
            itemsPanel.addChild(itemsPanel["mouseFJ"]);
            itemsPanel.addEventListener(MouseEvent.MOUSE_MOVE,gensui);
         }
      }
      
      private static function close_fenjie() : void
      {
         fenjie_bool = false;
         Mouse.show();
         itemsPanel["mouseFJ"].x = 20000;
         itemsPanel.removeEventListener(MouseEvent.MOUSE_MOVE,gensui);
      }
      
      private static function gensui(e:MouseEvent) : *
      {
         Mouse.hide();
         itemsPanel["mouseFJ"].x = itemsPanel.mouseX;
         itemsPanel["mouseFJ"].y = itemsPanel.mouseY;
         e.updateAfterEvent();
      }
      
      private static function menuClose(e:MouseEvent) : void
      {
         ++closeTimes;
         if(closeTimes > 1)
         {
            menuTooltip.visible = false;
            closeTimes = 0;
         }
      }
      
      private static function menuTest() : Boolean
      {
         if(dragObj.x > returnx + 1 || dragObj.y > returny + 1 || dragObj.x < returnx - 1 || dragObj.y < returny - 1)
         {
            return false;
         }
         return true;
      }
      
      private static function hzClose(e:MouseEvent) : *
      {
         itemsPanel["hzTip"].visible = false;
      }
      
      private static function hzOpen(e:MouseEvent) : *
      {
         var overNum:uint = uint((e.target as MovieClip).name.substr(2,1));
         if(myplayer.data.getBadgeSlot().getBadgeFromSlot(overNum))
         {
            itemsPanel["hzTip"]["str_name"].text = myplayer.data.getBadgeSlot().getBadgeFromSlot(overNum).getName();
            itemsPanel["hzTip"]["str_text"].text = myplayer.data.getBadgeSlot().getBadgeFromSlot(overNum).getIntroduction();
         }
         itemsPanel["hzTip"].visible = true;
         itemsPanel.addChild(itemsPanel["hzTip"]);
         itemsPanel["hzTip"].x = itemsPanel.mouseX + 10;
         itemsPanel["hzTip"].y = itemsPanel.mouseY;
      }
      
      private static function strengthenClose(e:MouseEvent) : *
      {
         itemsPanel["strengTip"].visible = false;
      }
      
      private static function strengthenOpen(e:MouseEvent) : *
      {
         var str:String = (e.target as MovieClip).name.toString();
         switch(str)
         {
            case "str_3":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉加成";
               itemsPanel["strengTip"]["str_text"].text = "除时装，翅膀以外的其他装备全部强化+4以上时，可获得额外属性加成";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_4":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+4";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值10%";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_5":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+5";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值15% 提高暴击值10%";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_6":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+6";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值15% 提高暴击值15% 提升1点移动力";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_7":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+7";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值15% 提高暴击值20% 提高防御力7% 提升1点移动力";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_8":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+8";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值20% 提高暴击值25% 提高防御力7% 提升1点移动力";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_9":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+9";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值20% 提高暴击值25% 提高防御力10% 提高攻击力5% 提升1点移动力";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
               break;
            case "str_10":
               itemsPanel["strengTip"]["str_name"].text = "强化荣誉+10";
               itemsPanel["strengTip"]["str_text"].text = "提高生命值27% 提高暴击值25% 提高防御力15% 提高攻击力32% 提升2点移动力";
               itemsPanel["strengTip"].x = itemsPanel.mouseX + 10;
         }
         itemsPanel["strengTip"].visible = true;
         itemsPanel.addChild(itemsPanel["strengTip"]);
      }
      
      private static function menuOpen(e:MouseEvent) : void
      {
         itemsTooltip.visible = false;
         clickObj = e.target as MovieClip;
         var str:String = clickObj.name.substr(0,2);
         oldNum = int(clickObj.name.substr(3,2));
         if(fenjie_bool)
         {
            if(str == "s1")
            {
               if(isPOne)
               {
                  MenuTooltip.whichDo = 1;
               }
               else
               {
                  MenuTooltip.whichDo = 2;
               }
               MenuTooltip.selectType = 1;
               MenuTooltip.oldNum = int(clickObj.name.substr(3,2));
               MenuTooltip.oldNum += yeshu * 24;
               MenuTooltip.isFJ(e);
            }
         }
         else
         {
            if(openFlag == true)
            {
               if(str == "s1")
               {
                  oldNum += yeshu * 24;
                  if(isPOne)
                  {
                     menuTooltip.setItemsMenu(itemsType,oldNum,1);
                     itemsPanel.addChild(menuTooltip);
                     menuTooltip.x = clickObj.x + 40;
                     menuTooltip.y = clickObj.y + 30;
                     if(menuTooltip.y + menuTooltip.height > 580 && itemsType <= 2)
                     {
                        menuTooltip.y = 580 - menuTooltip.height;
                     }
                     menuTooltip.visible = true;
                     closeTimes = 0;
                  }
                  else
                  {
                     menuTooltip.setItemsMenu(itemsType,oldNum,2);
                     itemsPanel.addChild(menuTooltip);
                     menuTooltip.x = clickObj.x + 40;
                     menuTooltip.y = clickObj.y + 30;
                     if(menuTooltip.y + menuTooltip.height > 580 && itemsType <= 2)
                     {
                        menuTooltip.y = 580 - menuTooltip.height;
                     }
                     menuTooltip.visible = true;
                     closeTimes = 0;
                  }
               }
               else if(str == "z1")
               {
                  if(isPOne)
                  {
                     menuTooltip.setSlotMenu(oldNum,1);
                     itemsPanel.addChild(menuTooltip);
                     menuTooltip.x = clickObj.x + 30;
                     menuTooltip.y = clickObj.y + 30;
                     menuTooltip.visible = true;
                     closeTimes = 0;
                  }
                  else
                  {
                     menuTooltip.setSlotMenu(oldNum,2);
                     itemsPanel.addChild(menuTooltip);
                     menuTooltip.x = clickObj.x + 30;
                     menuTooltip.y = clickObj.y + 30;
                     menuTooltip.visible = true;
                     closeTimes = 0;
                  }
               }
            }
            if(str == "g1")
            {
               if(isPOne)
               {
                  menuTooltip.setSkillSlotMenu(oldNum,1);
                  itemsPanel.addChild(menuTooltip);
                  menuTooltip.x = clickObj.x + 20;
                  menuTooltip.y = clickObj.y + 20;
                  menuTooltip.visible = true;
                  closeTimes = 0;
               }
               else
               {
                  menuTooltip.setSkillSlotMenu(oldNum,2);
                  itemsPanel.addChild(menuTooltip);
                  menuTooltip.x = clickObj.x + 20;
                  menuTooltip.y = clickObj.y + 20;
                  menuTooltip.visible = true;
                  closeTimes = 0;
               }
            }
            openFlag = false;
            clickObj = null;
         }
      }
      
      public static function allFalse() : void
      {
         itemsPanel["bg1_1"].isClick = false;
         itemsPanel["bg1_2"].isClick = false;
         itemsPanel["bg1_3"].isClick = false;
         itemsPanel["bg1_4"].isClick = false;
         itemsPanel["bg1_5"].isClick = false;
      }
      
      public static function twoFalse() : void
      {
         itemsPanel["bagTwo"].isClick = false;
         itemsPanel["bagOne"].isClick = false;
      }
      
      private static function bagListen(e:BtnEvent) : void
      {
         var btn:MovieClip = e.target as MovieClip;
         close_fenjie();
         switch(btn.name)
         {
            case "bg1_1":
               itemsType = 1;
               allFalse();
               itemsPanel["all_fenjie"].visible = true;
               itemsPanel["bg1_1"].isClick = true;
               BagItemsShow.equipShow();
               break;
            case "bg1_2":
               itemsType = 2;
               allFalse();
               itemsPanel["all_fenjie"].visible = false;
               itemsPanel["bg1_2"].isClick = true;
               BagItemsShow.suppliesShow();
               break;
            case "bg1_3":
               itemsType = 3;
               allFalse();
               itemsPanel["all_fenjie"].visible = false;
               itemsPanel["bg1_3"].isClick = true;
               BagItemsShow.gemShow();
               break;
            case "bg1_4":
               itemsType = 4;
               allFalse();
               itemsPanel["all_fenjie"].visible = false;
               itemsPanel["bg1_4"].isClick = true;
               BagItemsShow.otherobjShow();
               break;
            case "bg1_5":
               itemsType = 5;
               allFalse();
               itemsPanel["all_fenjie"].visible = false;
               itemsPanel["bg1_5"].isClick = true;
               BagItemsShow.questShow();
               break;
            case "bagOne":
               isPOne = true;
               myplayer = Main.player_1;
               twoFalse();
               itemsPanel["bagOne"].isClick = true;
               switch(itemsType)
               {
                  case 1:
                     BagItemsShow.equipShow();
                     break;
                  case 2:
                     BagItemsShow.suppliesShow();
                     break;
                  case 3:
                     BagItemsShow.gemShow();
                     break;
                  case 4:
                     BagItemsShow.otherobjShow();
                     break;
                  case 5:
                     BagItemsShow.questShow();
               }
               BagItemsShow.skillSlotShow();
               BagItemsShow.slotShow();
               BagItemsShow.badgeSlotShow();
               BagItemsShow.informationShow();
               break;
            case "bagTwo":
               isPOne = false;
               myplayer = Main.player_2;
               twoFalse();
               itemsPanel["bagTwo"].isClick = true;
               switch(itemsType)
               {
                  case 1:
                     BagItemsShow.equipShow();
                     break;
                  case 2:
                     BagItemsShow.suppliesShow();
                     break;
                  case 3:
                     BagItemsShow.gemShow();
                     break;
                  case 4:
                     BagItemsShow.otherobjShow();
                     break;
                  case 5:
                     BagItemsShow.questShow();
               }
               BagItemsShow.skillSlotShow();
               BagItemsShow.slotShow();
               BagItemsShow.badgeSlotShow();
               BagItemsShow.informationShow();
         }
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = null;
         var overNum:uint = 0;
         var str:String = null;
         var overNum2:int = 0;
         menuTooltip.visible = false;
         itemsPanel.addChild(itemsTooltip);
         if(isDown == false)
         {
            overobj = e.target as MovieClip;
            overNum = uint(overobj.name.substr(3,2));
            str = overobj.name.substr(0,2);
            overNum2 = int(uint(overobj.name.substr(3,2)));
            itemsTooltip.x = itemsPanel.mouseX;
            itemsTooltip.y = itemsPanel.mouseY;
            if(str == "s1")
            {
               overNum += yeshu * 24;
               switch(itemsType)
               {
                  case 1:
                     if(myplayer.data.getBag().getEquipFromBag(overNum) != null)
                     {
                        itemsTooltip.equipTooltip(myplayer.data.getBag().getEquipFromBag(overNum),1);
                     }
                     break;
                  case 2:
                     if(myplayer.data.getBag().getSuppliesFromBag(overNum) != null)
                     {
                        itemsTooltip.suppliesTooltip(myplayer.data.getBag().getSuppliesFromBag(overNum),1);
                     }
                     break;
                  case 3:
                     if(myplayer.data.getBag().getGemFromBag(overNum) != null)
                     {
                        itemsTooltip.gemTooltip(myplayer.data.getBag().getGemFromBag(overNum),1);
                     }
                     break;
                  case 4:
                     if(myplayer.data.getBag().getOtherobjFromBag(overNum) != null)
                     {
                        itemsTooltip.otherTooltip(myplayer.data.getBag().getOtherobjFromBag(overNum));
                     }
                     break;
                  case 5:
                     if(myplayer.data.getBag().getQuestFromBag(overNum2) != null)
                     {
                        itemsTooltip.questTooltip(myplayer.data.getBag().getQuestFromBag(overNum2));
                     }
               }
            }
            else if(str == "g1")
            {
               if(myplayer.data.getEquipSkillSlot().getGemFromSkillSlot(overNum) != null)
               {
                  itemsTooltip.gemTooltip(myplayer.data.getEquipSkillSlot().getGemFromSkillSlot(overNum));
               }
            }
            else if(str == "z1")
            {
               itemsTooltip.slotTooltip(overNum,myplayer.data.getEquipSlot());
            }
            itemsTooltip.setTooltipPoint();
            itemsTooltip.visible = true;
         }
      }
      
      private static function tooltipClose(e:MouseEvent) : void
      {
         if(isDown == false)
         {
            itemsTooltip.visible = false;
         }
      }
      
      private static function getPart(num:Number) : uint
      {
         switch(num)
         {
            case 0:
               return 2;
            case 1:
               return 0;
            case 2:
               return 1;
            case 3:
               return 3;
            case 4:
               return 4;
            case 5:
               return 2;
            case 6:
               return 2;
            case 7:
               return 2;
            case 8:
               return 6;
            case 9:
               return 7;
            case 10:
               return 0;
            case 11:
               return 1;
            case 12:
               return 3;
            case 13:
               return 4;
            default:
               return null;
         }
      }
      
      private static function closeLight() : void
      {
         for(i = 0; i < 8; ++i)
         {
            itemsPanel["k1_" + i].visible = false;
         }
      }
      
      private static function dragEquip() : void
      {
         var id:uint = 0;
         if(itemsType != 1)
         {
            return;
         }
         var num:int = int(oldNum);
         if(Main.water.getValue() != 1 && (num == 0 || num == 1 || num == 3 || num == 4))
         {
            num += 8;
         }
         var str:String = dragObj.name.substr(0,2);
         if(str == "s1")
         {
            id = uint(getPart(myplayer.data.getBag().getEquipFromBag(oldNum).getPosition()));
         }
         else if(str == "z1")
         {
            id = uint(getPart(myplayer.data.getEquipSlot().getEquipFromSlot(num).getPosition()));
         }
         ItemsPanel.itemsPanel["k1_" + id].visible = true;
         if(id == 2)
         {
            ItemsPanel.itemsPanel["k1_5"].visible = true;
         }
      }
      
      private static function DragHandler(e:MouseEvent) : void
      {
         if(isDown == true)
         {
            if(isDrag(pointx,pointy) == true)
            {
               itemsPanel.addChild(dragObj);
               dragObj.x = itemsPanel.mouseX - 20;
               dragObj.y = itemsPanel.mouseY - 20;
               menuTooltip.visible = false;
               dragObj.startDrag();
               dragEquip();
            }
         }
      }
      
      private static function DownHandler(e:MouseEvent) : void
      {
         if(!fenjie_bool)
         {
            menuTooltip.visible = false;
            itemsTooltip.visible = false;
            isDown = true;
            dragObj = e.target as MovieClip;
            returnx = dragObj.x;
            returny = dragObj.y;
            pointx = itemsPanel.mouseX;
            pointy = itemsPanel.mouseY;
            oldNum = uint(dragObj.name.substr(3,2));
         }
      }
      
      private static function isDrag(xx:Number, yy:Number) : Boolean
      {
         dragObj["diKuang"].visible = false;
         if(isDown == true)
         {
            if(itemsPanel.mouseX > xx + 1 || itemsPanel.mouseX < xx - 1 || itemsPanel.mouseY > yy + 1 || itemsPanel.mouseY < yy - 1)
            {
               return true;
            }
         }
         return false;
      }
      
      private static function UpHandler(e:MouseEvent = null) : void
      {
         if(dragObj)
         {
            dragObj.stopDrag();
            closeLight();
            if(menuTest() == true)
            {
               openFlag = true;
            }
            else
            {
               openFlag = false;
            }
            if(inItemsRange() == false && inEquipSlotRange() == false)
            {
               dragObj.x = returnx;
               dragObj.y = returny;
            }
         }
         isDown = false;
         dragObj = null;
      }
      
      private static function inEquipSlotRange() : Boolean
      {
         var bool:Boolean = false;
         var slotNum:uint = 0;
         var xflag:int = 0;
         var NewTemp:int = 0;
         var j:uint = 0;
         var yflag:int = 0;
         var i:uint = 0;
         if(itemsPanel.mouseX < 480)
         {
            if(dragObj.name.substr(0,2) == "z1")
            {
               return false;
            }
            if(itemsType != 1)
            {
               return false;
            }
            xflag = itemsPanel.x + 55;
            NewTemp = 6;
            for(j = 0; j < 2; j++)
            {
               slotNum = 3 * j;
               yflag = itemsPanel.y + 198;
               if(itemsPanel.mouseX > xflag && itemsPanel.mouseX < xflag + 66)
               {
                  for(i = 0; i < 3; i++)
                  {
                     if(itemsPanel.mouseY > 122 && itemsPanel.mouseY < 188)
                     {
                        bool = Boolean(allBagHandle.slotHandle(dragObj,oldNum,NewTemp));
                        dragObj.x = returnx;
                        dragObj.y = returny;
                        return bool;
                     }
                     if(itemsPanel.mouseY > yflag && itemsPanel.mouseY < yflag + 63)
                     {
                        bool = Boolean(allBagHandle.slotHandle(dragObj,oldNum,slotNum));
                        dragObj.x = returnx;
                        dragObj.y = returny;
                        return bool;
                     }
                     slotNum++;
                     yflag += 76;
                  }
               }
               NewTemp++;
               xflag += 345;
            }
            return false;
         }
         return false;
      }
      
      private static function inItemsRange() : Boolean
      {
         var bool:Boolean = false;
         var xflag:int = 0;
         var i:uint = 0;
         var bagNum:uint = 0;
         var yflag:int = itemsPanel.y + 104;
         for(var j:uint = 0; j < 8; j++)
         {
            bagNum = 4 * j;
            xflag = itemsPanel.x + 505;
            if(itemsPanel.mouseY > yflag && itemsPanel.mouseY < yflag + 63)
            {
               for(i = 0; i < 4; i++)
               {
                  if(itemsPanel.mouseX > xflag && itemsPanel.mouseX < xflag + 66)
                  {
                     switch(itemsType)
                     {
                        case 1:
                           bool = Boolean(allBagHandle.equipHandle(dragObj,oldNum,bagNum));
                           break;
                        case 2:
                           bool = Boolean(allBagHandle.suppliesHandle(dragObj,oldNum,bagNum));
                           break;
                        case 3:
                           bool = Boolean(allBagHandle.gemHandle(dragObj,oldNum,bagNum));
                           break;
                        case 4:
                           bool = Boolean(allBagHandle.otherobjHandle(dragObj,oldNum,bagNum));
                           break;
                        case 5:
                           trace("c");
                     }
                     dragObj.x = returnx;
                     dragObj.y = returny;
                     return bool;
                  }
                  bagNum++;
                  xflag += 77;
               }
            }
            yflag += 75;
         }
         return false;
      }
      
      public static function showjiangli() : *
      {
         itemsPanel["fudai"]["select"].x = itemsPanel["fudai"]["xz" + jiangliNum].x;
         itemsPanel["fudai"]["select"].y = itemsPanel["fudai"]["xz" + jiangliNum].y;
      }
      
      public static function xuanzejiangli(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,1));
         TiaoShi.txtShow(overNum);
         jiangliNum = overNum;
         showjiangli();
      }
      
      public static function lingqujiangli(e:*) : *
      {
         var playX:PlayerData = null;
         if(isPOne)
         {
            playX = Main.player1;
         }
         else
         {
            playX = Main.player2;
         }
         if(playX.getBag().backequipBagNum() >= 1)
         {
            if(jiangliNum == 0)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13630));
            }
            else if(jiangliNum == 1)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13631));
            }
            else if(jiangliNum == 2)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13632));
            }
            else if(jiangliNum == 3)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13633));
            }
            else if(jiangliNum == 4)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13634));
            }
            playX.getBag().delOtherById(63234);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         itemsPanel["fudai"].visible = false;
         BagItemsShow.otherobjShow();
      }
      
      public static function showjiangli3() : *
      {
         itemsPanel["fudai3"]["select"].x = itemsPanel["fudai3"]["xz" + jiangliNum3].x;
         itemsPanel["fudai3"]["select"].y = itemsPanel["fudai3"]["xz" + jiangliNum3].y;
      }
      
      public static function xuanzejiangli3(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,1));
         jiangliNum3 = overNum;
         showjiangli3();
      }
      
      public static function lingqujiangli3(e:*) : *
      {
         var playX:PlayerData = null;
         if(isPOne)
         {
            playX = Main.player1;
         }
         else
         {
            playX = Main.player2;
         }
         if(playX.getBag().backequipBagNum() >= 1)
         {
            if(jiangliNum3 == 0)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13530));
            }
            else if(jiangliNum3 == 1)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13531));
            }
            else if(jiangliNum3 == 2)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13532));
            }
            else if(jiangliNum3 == 3)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13533));
            }
            else if(jiangliNum3 == 4)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13534));
            }
            playX.getBag().delOtherById(63323);
            Main.Save();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         itemsPanel["fudai3"].visible = false;
         BagItemsShow.otherobjShow();
      }
      
      public static function fudaiOpen3(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,1));
         itemsPanel.addChild(itemsTooltip);
         itemsTooltip.x = itemsPanel.mouseX;
         itemsTooltip.y = itemsPanel.mouseY;
         switch(overNum)
         {
            case 0:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13530),1);
               break;
            case 1:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13531),1);
               break;
            case 2:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13532),1);
               break;
            case 3:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13533),1);
               break;
            case 4:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13534),1);
         }
         itemsTooltip.visible = true;
      }
      
      public static function fudaiClose3(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      public static function showjiangli2() : *
      {
         itemsPanel["fudai2"]["select"].x = itemsPanel["fudai2"]["xz" + jiangliNum2].x;
         itemsPanel["fudai2"]["select"].y = itemsPanel["fudai2"]["xz" + jiangliNum2].y;
      }
      
      public static function xuanzejiangli2(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,1));
         jiangliNum2 = overNum;
         showjiangli2();
      }
      
      public static function lingqujiangli2(e:*) : *
      {
         var playX:PlayerData = null;
         if(isPOne)
         {
            playX = Main.player1;
         }
         else
         {
            playX = Main.player2;
         }
         if(playX.getBag().backequipBagNum() >= 1)
         {
            if(jiangliNum2 == 0)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13730));
            }
            else if(jiangliNum2 == 1)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13731));
            }
            else if(jiangliNum2 == 2)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13732));
            }
            else if(jiangliNum2 == 3)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13733));
            }
            else if(jiangliNum2 == 4)
            {
               playX.getBag().addEquipBag(EquipFactory.createEquipByID(13734));
            }
            playX.getBag().delOtherById(63242);
            Main.Save();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
         }
         itemsPanel["fudai2"].visible = false;
         BagItemsShow.otherobjShow();
      }
      
      public static function fudaiOpen2(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,1));
         itemsPanel.addChild(itemsTooltip);
         itemsTooltip.x = itemsPanel.mouseX;
         itemsTooltip.y = itemsPanel.mouseY;
         switch(overNum)
         {
            case 0:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13730),1);
               break;
            case 1:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13731),1);
               break;
            case 2:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13732),1);
               break;
            case 3:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13733),1);
               break;
            case 4:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13734),1);
         }
         itemsTooltip.visible = true;
      }
      
      public static function fudaiClose2(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      public static function fudaiOpen(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,1));
         itemsPanel.addChild(itemsTooltip);
         itemsTooltip.x = itemsPanel.mouseX;
         itemsTooltip.y = itemsPanel.mouseY;
         switch(overNum)
         {
            case 0:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13630),1);
               break;
            case 1:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13631),1);
               break;
            case 2:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13632),1);
               break;
            case 3:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13633),1);
               break;
            case 4:
               itemsTooltip.equipTooltip(EquipFactory.createEquipByID(13634),1);
         }
         itemsTooltip.visible = true;
      }
      
      public static function fudaiClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      public static function changeModel(pp:PlayerData) : void
      {
         if(pp.getEquipSlot().getEquipFromSlot(2).getPosition() == 5)
         {
            pp.skinArr[0] = 0;
         }
         else if(pp.getEquipSlot().getEquipFromSlot(2).getPosition() == 6)
         {
            pp.skinArr[0] = 1;
         }
         else if(pp.getEquipSlot().getEquipFromSlot(2).getPosition() == 7)
         {
            pp.skinArr[0] = 2;
         }
         else if(pp.getEquipSlot().getEquipFromSlot(2).getPosition() == 0)
         {
            pp.skinArr[0] = 3;
         }
         if(pp.getEquipSlot().getEquipFromSlot(5).getPosition() == 5)
         {
            pp.skinArr[1] = 0;
         }
         else if(pp.getEquipSlot().getEquipFromSlot(5).getPosition() == 6)
         {
            pp.skinArr[1] = 1;
         }
         else if(pp.getEquipSlot().getEquipFromSlot(5).getPosition() == 7)
         {
            pp.skinArr[1] = 2;
         }
         else if(pp.getEquipSlot().getEquipFromSlot(5).getPosition() == 0)
         {
            pp.skinArr[1] = 3;
         }
         if(pp == Main.player1)
         {
            Main.player_1.newSkin();
         }
         else if(pp == Main.player2)
         {
            Main.player_2.newSkin();
         }
      }
      
      public static function szcbTime() : void
      {
         for(var i:int = 0; i < 24; i++)
         {
            if(Main.player1.getBag().getEquipFromBag(i) && Main.player1.getBag().getEquipFromBag(i).getPosition() > 7 && Main.player1.getBag().getEquipFromBag(i).getPosition() < 10)
            {
               Main.player1.getBag().getEquipFromBag(i).setRemainingTime(Main.serverTime);
            }
         }
         for(var j:int = 6; j < 8; j++)
         {
            if(Main.player1.getEquipSlot().getEquipFromSlot(j))
            {
               Main.player1.getEquipSlot().getEquipFromSlot(j).setRemainingTime(Main.serverTime);
            }
         }
         for(var k:int = 0; k < 35; k++)
         {
            if(StoragePanel.storage.getEquipFromStorage(k) && StoragePanel.storage.getEquipFromStorage(k).getPosition() > 7 && StoragePanel.storage.getEquipFromStorage(k).getPosition() < 10)
            {
               StoragePanel.storage.getEquipFromStorage(k).setRemainingTime(Main.serverTime);
            }
         }
         Main.player_1.newSkin();
         if(Main.P1P2)
         {
            for(i = 0; i < 24; i++)
            {
               if(Main.player2.getBag().getEquipFromBag(i) && Main.player2.getBag().getEquipFromBag(i).getPosition() > 7 && Main.player2.getBag().getEquipFromBag(i).getPosition() < 10)
               {
                  Main.player2.getBag().getEquipFromBag(i).setRemainingTime(Main.serverTime);
               }
            }
            for(j = 6; j < 8; j++)
            {
               if(Main.player2.getEquipSlot().getEquipFromSlot(j))
               {
                  Main.player2.getEquipSlot().getEquipFromSlot(j).setRemainingTime(Main.serverTime);
               }
            }
            Main.player_2.newSkin();
         }
      }
      
      public static function timeXiuZheng() : *
      {
         for(var i:int = 8; i < 13; i++)
         {
            if(Main.player1.getEquipSlot().getEquipFromSlot(i))
            {
               if(Main.player1.getEquipSlot().getEquipFromSlot(i).getRemainingTime() < 1)
               {
                  Main.player1.getEquipSlot().getEquipFromSlot(i).setRemainingTimeNow();
               }
            }
            if(Main.P1P2)
            {
               if(Main.player2.getEquipSlot().getEquipFromSlot(i))
               {
                  if(Main.player2.getEquipSlot().getEquipFromSlot(i).getRemainingTime() < 1)
                  {
                     Main.player2.getEquipSlot().getEquipFromSlot(i).setRemainingTimeNow();
                  }
               }
            }
         }
         for(i in Fly.All)
         {
            if((Fly.All[i] as Fly)._name == "高能喷射")
            {
               (Fly.All[i] as Fly).life = 0;
            }
         }
      }
      
      public static function NvShenRenWu() : *
      {
         if(myplayer.data.getBag().isHavesQuest(84110) || myplayer.data.getBag().isHavesQuest(84111) || myplayer.data.getBag().isHavesQuest(84112) || myplayer.data.getBag().isHavesQuest(84113) || myplayer.data.getBag().isHavesQuest(84114))
         {
            itemsPanel["renwutishi"].visible = true;
         }
         else
         {
            itemsPanel["renwutishi"].visible = false;
         }
      }
   }
}

