package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class DuWu_Interface extends MovieClip
   {
      
      public static var _this:DuWu_Interface;
      
      public var skin:MovieClip;
      
      public function DuWu_Interface()
      {
         super();
      }
      
      public static function Open() : *
      {
         var classRef:Class = null;
         if(!_this)
         {
            _this = new DuWu_Interface();
            classRef = NewLoad.OtherData.getClass("DuWuMC") as Class;
            _this.skin = new classRef();
            _this.addChild(_this.skin);
            _this.skin.addEventListener(Event.ADDED_TO_STAGE,onADDED_TO_STAGE);
         }
         Main._stage.addChild(_this);
         _this.visible = true;
      }
      
      private static function onADDED_TO_STAGE(e:*) : *
      {
         _this.skin.red_btn.addEventListener(MouseEvent.CLICK,Red);
         _this.skin.blue_btn.addEventListener(MouseEvent.CLICK,Blue);
         _this.skin.x_btn.addEventListener(MouseEvent.CLICK,XX);
         _this.skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
      }
      
      private static function Red(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-46684894.html");
         navigateToURL(request,"_blank");
      }
      
      private static function Blue(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-45287598.html");
         navigateToURL(request,"_blank");
      }
      
      private static function XX(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-45467169.html");
         navigateToURL(request,"_blank");
      }
      
      private static function Close(e:*) : *
      {
         _this.visible = false;
      }
   }
}

