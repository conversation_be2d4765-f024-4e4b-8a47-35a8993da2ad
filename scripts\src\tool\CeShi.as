package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   import src._data.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol7078")]
   public class <PERSON><PERSON><PERSON> extends MovieClip
   {
      
      public static var _this:<PERSON><PERSON>hi;
      
      public var _btn:SimpleButton;
      
      public var _txt:TextField;
      
      public var p1_btn:SimpleButton;
      
      public var p2_btn:SimpleButton;
      
      public function CeShi()
      {
         super();
         _this = this;
         this._txt.text = "开启调试";
         Main.tiaoShiYN = true;
         this._btn.addEventListener(MouseEvent.CLICK,this.onCLICK);
         this.p1_btn.addEventListener(MouseEvent.CLICK,this.P1Go);
         this.p2_btn.addEventListener(MouseEvent.CLICK,this.P2Go);
      }
      
      private function onCLICK(e:*) : *
      {
         if(Main.tiaoShiYN)
         {
            this._txt.text = "关闭调试";
            Main.tiaoShiYN = false;
         }
         else
         {
            this._txt.text = "开启调试";
            Main.tiaoShiYN = true;
         }
      }
      
      private function P1Go(e:*) : *
      {
         Strat.stratX.NewGame1();
         Main.noSave = 43994396;
         Main.tiaoShiYN2 = true;
      }
      
      private function P2Go(e:*) : *
      {
         Strat.stratX.NewGame2();
         Main.noSave = 43994377;
         Main.tiaoShiYN2 = true;
      }
   }
}

