package com.hotpoint.braveManIII.views.caiyaoPanel
{
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class CaiYaoPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var cyp:CaiYaoPanel;
      
      public static var ccc:ClassLoader;
      
      public static var caiPanel:MovieClip;
      
      private static var loadData:ClassLoader;
      
      public static var arr:Array = [[[4,2],[1,2],[2,2]],[[2,1],[1,2],[3,3]],[[1,1],[4,2],[2,3]],[[2,4],[1,2],[4,4]],[[1,3],[3,4],[4,3]],[[3,3],[2,5],[1,2]],[[1,5],[4,5],[2,5]],[[3,6],[4,5],[2,4]],[[4,3],[3,5],[1,7]],[[4,5],[1,9],[2,10]],[[1,8],[3,8],[4,8]],[[1,8],[2,6],[3,8]],[[1,13],[2,12],[4,11]],[[3,14],[4,12],[2,10]],[[1,12],[3,12],[2,12]]];
      
      public static var lvChange:int = 0;
      
      public static var selectNUM:int = 0;
      
      public static var addArr:Array = [1,0.5,1,2,1,2,3,1.5,3,4,2,4,5,2.5,5];
      
      public static var saveArr:Array = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
      
      public static var saveArr2:Array = [1,0];
      
      private static var loadName:String = "CaiYao.swf";
      
      private static var OpenYN:Boolean = false;
      
      public function CaiYaoPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!caiPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("CaiyaoShow") as Class;
         caiPanel = new classRef();
         cyp.addChild(caiPanel);
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         cyp = new CaiYaoPanel();
         LoadSkin();
         Main._stage.addChild(cyp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         cyp = new CaiYaoPanel();
         if(jifenTime <= 0)
         {
         }
         Main._stage.addChild(cyp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(caiPanel)
         {
            Main.stopXX = true;
            cyp.x = 0;
            cyp.y = 0;
            addListenerP1();
            cyp.visible = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(caiPanel)
         {
            cyp.visible = false;
            Main.stopXX = false;
            removeListenerP1();
         }
         else
         {
            InitClose();
         }
      }
      
      public static function closeCP(e:*) : *
      {
         close();
      }
      
      public static function initCaiYao() : *
      {
         if(Main.isVip())
         {
            saveArr2[1] = 5;
         }
         else
         {
            saveArr2[1] = 3;
         }
      }
      
      public static function showCaiYao() : *
      {
         showTimes();
         showCL();
         showLV();
         showLock();
         showADD();
         caiPanel["times"].text = saveArr2[1];
      }
      
      public static function showCL() : *
      {
         for(var i:uint = 0; i < 4; i++)
         {
            caiPanel["cy" + i].text = YaoYuan.yaoArr[i].getValue();
         }
      }
      
      public static function showTimes() : *
      {
         caiPanel["times"].text = saveArr2[1];
         if(saveArr2[1] >= 3)
         {
            caiPanel["gold"].text = 10000;
         }
         else if(saveArr2[1] == 2)
         {
            caiPanel["gold"].text = 20000;
         }
         else if(saveArr2[1] == 1)
         {
            caiPanel["gold"].text = 30000;
         }
      }
      
      public static function showADD() : *
      {
         caiPanel["att"].text = saveArr[0] * addArr[0] + saveArr[3] * addArr[3] + saveArr[6] * addArr[6] + saveArr[9] * addArr[9] + saveArr[12] * addArr[12] + "%";
         caiPanel["def"].text = saveArr[1] * addArr[1] + saveArr[4] * addArr[4] + saveArr[7] * addArr[7] + saveArr[10] * addArr[10] + saveArr[13] * addArr[13] + "%";
         caiPanel["life"].text = saveArr[2] * addArr[2] + saveArr[5] * addArr[5] + saveArr[8] * addArr[8] + saveArr[11] * addArr[11] + saveArr[14] * addArr[14] + "%";
      }
      
      public static function showLV() : *
      {
         caiPanel["lv0"].visible = true;
         caiPanel["lv1"].visible = true;
         caiPanel["lv2"].visible = true;
         caiPanel["lv3"].visible = true;
         caiPanel["lv4"].visible = true;
         caiPanel["lv" + lvChange].visible = false;
      }
      
      public static function addListenerP1() : *
      {
         var i:uint = 0;
         if(saveArr2[0] < Main.serverTime.getValue())
         {
            initCaiYao();
            saveArr2[0] = Main.serverTime.getValue();
         }
         showCaiYao();
         for(i = 0; i < 3; i++)
         {
            caiPanel["hecheng"]["cl" + i].stop();
            caiPanel["bb" + i].addEventListener(MouseEvent.CLICK,hechengOpen);
            caiPanel["bb" + i].addEventListener(MouseEvent.MOUSE_OVER,tipOpen);
            caiPanel["bb" + i].addEventListener(MouseEvent.MOUSE_OUT,tipClose);
            caiPanel["bg" + i].addEventListener(MouseEvent.MOUSE_OVER,tipOpen2);
            caiPanel["bg" + i].addEventListener(MouseEvent.MOUSE_OUT,tipClose);
         }
         caiPanel["tishi"].visible = false;
         caiPanel["hecheng"].visible = false;
         caiPanel["close"].addEventListener(MouseEvent.CLICK,closeCP);
         caiPanel["caiyaobtn"].addEventListener(MouseEvent.CLICK,caiyaoGo);
         caiPanel["hecheng"]["hc_btn"].addEventListener(MouseEvent.CLICK,hecheng);
         caiPanel["hecheng"]["close"].addEventListener(MouseEvent.CLICK,closeHC);
         for(i = 0; i < 5; i++)
         {
            caiPanel["lv" + i].addEventListener(MouseEvent.CLICK,choseLV);
         }
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         for(i = 0; i < 3; i++)
         {
            caiPanel["bb" + i].removeEventListener(MouseEvent.CLICK,hechengOpen);
            caiPanel["bb" + i].removeEventListener(MouseEvent.MOUSE_OVER,tipOpen);
            caiPanel["bb" + i].removeEventListener(MouseEvent.MOUSE_OUT,tipClose);
            caiPanel["bg" + i].removeEventListener(MouseEvent.MOUSE_OVER,tipOpen2);
            caiPanel["bg" + i].removeEventListener(MouseEvent.MOUSE_OUT,tipClose);
         }
         caiPanel["close"].removeEventListener(MouseEvent.CLICK,closeCP);
         caiPanel["caiyaobtn"].removeEventListener(MouseEvent.CLICK,caiyaoGo);
         caiPanel["hecheng"]["hc_btn"].removeEventListener(MouseEvent.CLICK,hecheng);
         for(i = 0; i < 5; i++)
         {
            caiPanel["lv" + i].removeEventListener(MouseEvent.CLICK,choseLV);
         }
      }
      
      private static function closeHC(e:*) : void
      {
         caiPanel["hecheng"].visible = false;
      }
      
      private static function tipOpen2(e:MouseEvent) : void
      {
         caiPanel["tishi"].visible = true;
         var overobj:MovieClip = e.target as MovieClip;
         var num:uint = uint(overobj.name.substr(2,1));
         var sNUM:int = num + lvChange * 3;
         if(num == 0)
         {
            caiPanel["tishi"]["txt1"].text = "强力药剂";
            caiPanel["tishi"]["txt2"].text = "合成后，最终伤害+" + addArr[sNUM] + "%";
         }
         if(num == 1)
         {
            caiPanel["tishi"]["txt1"].text = "磐石药剂";
            caiPanel["tishi"]["txt2"].text = "合成后，最终减伤+" + addArr[sNUM] + "%";
         }
         if(num == 2)
         {
            caiPanel["tishi"]["txt1"].text = "体魄药剂";
            caiPanel["tishi"]["txt2"].text = "合成后，最大生命+" + addArr[sNUM] + "%";
         }
         caiPanel["tishi"].x = caiPanel.mouseX + 10;
         caiPanel["tishi"].y = caiPanel.mouseY;
      }
      
      private static function tipOpen(e:MouseEvent) : void
      {
         caiPanel["tishi"].visible = true;
         var overobj:SimpleButton = e.target as SimpleButton;
         var num:uint = uint(overobj.name.substr(2,1));
         var sNUM:int = num + lvChange * 3;
         if(num == 0)
         {
            caiPanel["tishi"]["txt1"].text = "强力药剂";
            caiPanel["tishi"]["txt2"].text = "合成后，最终伤害+" + addArr[sNUM] + "%";
         }
         if(num == 1)
         {
            caiPanel["tishi"]["txt1"].text = "磐石药剂";
            caiPanel["tishi"]["txt2"].text = "合成后，最终减伤+" + addArr[sNUM] + "%";
         }
         if(num == 2)
         {
            caiPanel["tishi"]["txt1"].text = "体魄药剂";
            caiPanel["tishi"]["txt2"].text = "合成后，最大生命+" + addArr[sNUM] + "%";
         }
         caiPanel["tishi"].x = caiPanel.mouseX + 10;
         caiPanel["tishi"].y = caiPanel.mouseY;
      }
      
      private static function tipClose(e:MouseEvent) : void
      {
         caiPanel["tishi"].visible = false;
      }
      
      public static function showLock() : *
      {
         var i:uint = 0;
         var count:int = 0;
         for(i in saveArr)
         {
            count += saveArr[i];
         }
         if(count >= 3)
         {
            caiPanel["lock0"].visible = false;
         }
         if(count >= 6)
         {
            caiPanel["lock1"].visible = false;
         }
         if(count >= 9)
         {
            caiPanel["lock2"].visible = false;
         }
         if(count >= 12)
         {
            caiPanel["lock3"].visible = false;
         }
         for(i = 0; i < 3; i++)
         {
            if(saveArr[i + lvChange * 3] > 0)
            {
               caiPanel["bb" + i].visible = false;
            }
            else
            {
               caiPanel["bb" + i].visible = true;
            }
         }
      }
      
      public static function choseLV(e:*) : *
      {
         var overobj:SimpleButton = e.target as SimpleButton;
         lvChange = uint(overobj.name.substr(2,1));
         showCaiYao();
      }
      
      public static function hechengOpen(e:*) : *
      {
         caiPanel["hecheng"].visible = true;
         var overobj:SimpleButton = e.target as SimpleButton;
         var num:uint = uint(overobj.name.substr(2,1));
         selectNUM = num + lvChange * 3;
         caiPanel["hecheng"]["cl0"].gotoAndStop(arr[selectNUM][0][0]);
         caiPanel["hecheng"]["cl1"].gotoAndStop(arr[selectNUM][1][0]);
         caiPanel["hecheng"]["cl2"].gotoAndStop(arr[selectNUM][2][0]);
         caiPanel["hecheng"]["txt0"].text = "需要" + arr[selectNUM][0][1] + "个";
         caiPanel["hecheng"]["txt1"].text = "需要" + arr[selectNUM][1][1] + "个";
         caiPanel["hecheng"]["txt2"].text = "需要" + arr[selectNUM][2][1] + "个";
      }
      
      public static function hecheng(e:*) : *
      {
         if(YaoYuan.yaoArr[arr[selectNUM][0][0] - 1].getValue() >= arr[selectNUM][0][1] && YaoYuan.yaoArr[arr[selectNUM][1][0] - 1].getValue() >= arr[selectNUM][1][1] && YaoYuan.yaoArr[arr[selectNUM][2][0] - 1].getValue() >= arr[selectNUM][2][1])
         {
            YaoYuan.yaoArr[arr[selectNUM][0][0] - 1].setValue(YaoYuan.yaoArr[arr[selectNUM][0][0] - 1].getValue() - arr[selectNUM][0][1]);
            YaoYuan.yaoArr[arr[selectNUM][1][0] - 1].setValue(YaoYuan.yaoArr[arr[selectNUM][1][0] - 1].getValue() - arr[selectNUM][1][1]);
            YaoYuan.yaoArr[arr[selectNUM][2][0] - 1].setValue(YaoYuan.yaoArr[arr[selectNUM][2][0] - 1].getValue() - arr[selectNUM][2][1]);
            saveArr[selectNUM] = 1;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"合成成功");
            showCaiYao();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"材料不足");
         }
         caiPanel["hecheng"].visible = false;
      }
      
      public static function caiyaoGo(e:*) : *
      {
         if(saveArr2[1] > 0)
         {
            if(saveArr2[1] >= 3)
            {
               if(Main.player1.getGold() >= 10000)
               {
                  Main.player1.addGold(-10000);
                  --saveArr2[1];
                  GameData.winYN = false;
                  Main.gameNum.setValue(3000);
                  Main.gameNum2.setValue(1);
                  GameData.gameLV = 4;
                  Main._this.Loading();
                  close();
                  Main.Save();
                  return;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
               return;
            }
            if(saveArr2[1] == 2)
            {
               if(Main.player1.getGold() >= 20000)
               {
                  Main.player1.addGold(-20000);
                  --saveArr2[1];
                  GameData.winYN = false;
                  Main.gameNum.setValue(3000);
                  Main.gameNum2.setValue(1);
                  GameData.gameLV = 4;
                  Main._this.Loading();
                  close();
                  Main.Save();
                  return;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
               return;
            }
            if(saveArr2[1] == 1)
            {
               if(Main.player1.getGold() >= 30000)
               {
                  Main.player1.addGold(-30000);
                  --saveArr2[1];
                  GameData.winYN = false;
                  Main.gameNum.setValue(3000);
                  Main.gameNum2.setValue(1);
                  GameData.gameLV = 4;
                  Main._this.Loading();
                  close();
                  Main.Save();
                  return;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
               return;
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"今日次数用尽，明天再来");
         }
      }
   }
}

