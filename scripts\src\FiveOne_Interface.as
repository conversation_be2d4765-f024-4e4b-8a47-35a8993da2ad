package src
{
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class FiveOne_Interface extends MovieClip
   {
      
      public static var loadData:ClassLoader;
      
      private static var _this:FiveOne_Interface;
      
      public static var varXX:int = 4;
      
      public static var overTime:int = 20220410;
      
      public static var arrAll:Array = [10,0,0,0,0,0,0,false,0];
      
      public static var OKZhong:Boolean = false;
      
      public static var OKJiao:Boolean = false;
      
      public static var OKZhao:Boolean = false;
      
      private static var chengzhangPart1:Boolean = false;
      
      private static var chengzhangPart2:Boolean = false;
      
      private static var chengzhangPart3:Boolean = false;
      
      private static var chengzhangPart4:Boolean = false;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "HD51_v1503.swf";
      
      private static var timetemp:int = 0;
      
      private var tishi:MovieClip;
      
      private var skin:MovieClip;
      
      public function FiveOne_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var temp:FiveOne_Interface = new FiveOne_Interface();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.skin.visible = true;
            _this.tishi.visible = false;
            _this.x = _this.y = 0;
            if(!arrAll[8] || arrAll[8] != varXX)
            {
               arrAll = [10,Main.serverTime.getValue(),0,0,0,0,0,false,varXX];
               TiaoShi.txtShow("~~~~~~~~~~~ 生命之树活动 劳动最光荣 重置");
            }
            if(arrAll[1] < Main.serverTime.getValue())
            {
               arrAll[1] = Main.serverTime.getValue();
               arrAll[0] = 3;
               arrAll[2] = 0;
               arrAll[3] = 0;
               arrAll[4] = 0;
            }
            addListenerALL();
         }
         else
         {
            InitOpen();
         }
      }
      
      private static function Close(e:*) : *
      {
         _this.visible = false;
      }
      
      private static function addListenerALL() : *
      {
         _this.skin["close_btn"].addEventListener(MouseEvent.CLICK,Close);
         _this.skin["zhongxia"].addEventListener(MouseEvent.CLICK,ZhongXia);
         _this.skin["jiaoguan"].addEventListener(MouseEvent.CLICK,JiaoGuan);
         _this.skin["zhaoyao"].addEventListener(MouseEvent.CLICK,ZhaoYao);
         _this.skin["shouhuo"].addEventListener(MouseEvent.CLICK,ShouHuo);
         _this.skin["zhongxiaRMB"].addEventListener(MouseEvent.CLICK,ZhongXiaRMB);
         _this.skin["jiaoguanRMB"].addEventListener(MouseEvent.CLICK,JiaoGuanRMB);
         _this.skin["zhaoyaoRMB"].addEventListener(MouseEvent.CLICK,ZhaoYaoRMB);
         _this.tishi["jl0"].stop();
         _this.tishi["jl1"].stop();
         _this.tishi["jl2"].stop();
         _this.tishi["jl3"].stop();
         allVisibleFalse();
         if(arrAll[7] == true && arrAll[5] == 0)
         {
            _this.skin["shuMC1"].visible = true;
         }
         if(arrAll[5] < 20 && arrAll[5] > 0)
         {
            _this.skin["shuMC1"].visible = true;
         }
         if(arrAll[5] > 20 && arrAll[5] <= 40)
         {
            _this.skin["shuMC2"].visible = true;
         }
         if(arrAll[5] > 40 && arrAll[5] <= 60)
         {
            _this.skin["shuMC3"].visible = true;
         }
         if(arrAll[5] > 60 && arrAll[5] <= 80)
         {
            _this.skin["shuMC4"].visible = true;
         }
         if(arrAll[5] > 80)
         {
            _this.skin["shuMC5"].visible = true;
         }
         _this.tishi.visible = false;
         showAll();
      }
      
      private static function jianbian(e:*) : *
      {
         ++timetemp;
         _this.tishi.alpha -= 0.01;
         if(timetemp > 100)
         {
            timetemp = 0;
            _this.tishi.visible = false;
            _this.skin.removeEventListener(Event.ENTER_FRAME,jianbian);
         }
      }
      
      private static function ZhongXiaRMB(e:*) : *
      {
         if(arrAll[0] > 0 && arrAll[7] == false)
         {
            if(Shop4399.moneyAll.getValue() >= 30)
            {
               Api_4399_All.BuyObj(InitData.fiveone1.getValue());
               OKZhong = true;
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
            }
         }
         if(arrAll[0] <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"今日已经播种10次，明天再来吧");
         }
         if(arrAll[7] == true)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已播种！");
         }
         showAll();
      }
      
      public static function zhongxiaOK() : *
      {
         if(OKZhong)
         {
            arrAll[7] = true;
            arrAll[5] = 0;
            arrAll[6] = 0;
            --arrAll[0];
            chengzhangPart1 = false;
            chengzhangPart2 = false;
            chengzhangPart3 = false;
            chengzhangPart4 = false;
            allVisibleFalse();
            _this.skin["shuMC1"].visible = true;
            _this.skin["shuMC1"].gotoAndPlay("成长");
            showAll();
            OKZhong = false;
         }
      }
      
      private static function JiaoGuanRMB(e:*) : *
      {
         if(arrAll[7] == true && arrAll[5] < 100)
         {
            if(Shop4399.moneyAll.getValue() >= 5)
            {
               Api_4399_All.BuyObj(InitData.fiveone2.getValue());
               OKJiao = true;
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
            }
         }
         if(arrAll[7] == false)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"尚未播种！");
         }
         showAll();
      }
      
      public static function jiaoguanOK() : *
      {
         if(OKJiao)
         {
            arrAll[5] = arrAll[5] + 20 + int(Math.random() * 30);
            if(arrAll[5] < 20)
            {
               allVisibleFalse();
               _this.skin["shuMC1"].visible = true;
               _this.skin["shuMC1"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 20 && arrAll[5] <= 40 && chengzhangPart1 == false)
            {
               chengzhangPart1 = true;
               allVisibleFalse();
               _this.skin["shuMC2"].visible = true;
               _this.skin["shuMC2"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 20 && arrAll[5] <= 40 && chengzhangPart1 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC2"].visible = true;
               _this.skin["shuMC2"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 40 && arrAll[5] <= 60 && chengzhangPart2 == false)
            {
               chengzhangPart2 = true;
               allVisibleFalse();
               _this.skin["shuMC3"].visible = true;
               _this.skin["shuMC3"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 40 && arrAll[5] <= 60 && chengzhangPart2 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC3"].visible = true;
               _this.skin["shuMC3"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 60 && arrAll[5] <= 80 && chengzhangPart3 == false)
            {
               chengzhangPart3 = true;
               allVisibleFalse();
               _this.skin["shuMC4"].visible = true;
               _this.skin["shuMC4"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 60 && arrAll[5] <= 80 && chengzhangPart3 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC4"].visible = true;
               _this.skin["shuMC4"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 80 && chengzhangPart4 == false)
            {
               chengzhangPart4 = true;
               allVisibleFalse();
               _this.skin["shuMC5"].visible = true;
               _this.skin["shuMC5"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 80 && chengzhangPart4 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC5"].visible = true;
               _this.skin["shuMC5"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 100)
            {
               arrAll[5] = 100;
            }
            OKJiao = false;
            showAll();
         }
      }
      
      private static function ZhaoYaoRMB(e:*) : *
      {
         if(arrAll[7] == true && arrAll[6] < 100)
         {
            if(Shop4399.moneyAll.getValue() >= 5)
            {
               Api_4399_All.BuyObj(InitData.fiveone3.getValue());
               OKZhao = true;
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
            }
         }
         if(arrAll[7] == false)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"尚未播种！");
         }
         showAll();
      }
      
      public static function zhaoyaoOK() : *
      {
         if(OKZhao)
         {
            arrAll[6] = arrAll[6] + 20 + int(Math.random() * 30);
            if(arrAll[6] > 100)
            {
               arrAll[6] = 100;
            }
            if(_this.skin["shuMC1"].visible == true)
            {
               _this.skin["shuMC1"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC2"].visible == true)
            {
               _this.skin["shuMC2"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC3"].visible == true)
            {
               _this.skin["shuMC3"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC4"].visible == true)
            {
               _this.skin["shuMC4"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC5"].visible == true)
            {
               _this.skin["shuMC5"].gotoAndPlay("照耀");
            }
            OKZhao = false;
            showAll();
         }
      }
      
      private static function ZhongXia(e:*) : *
      {
         if(arrAll[2] <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"缺少生命之核");
            return;
         }
         if(arrAll[0] <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"今日已经播种10次，明天再来吧");
            return;
         }
         if(arrAll[2] > 0 && arrAll[0] > 0 && arrAll[7] == false)
         {
            arrAll[7] = true;
            arrAll[5] = 0;
            arrAll[6] = 0;
            --arrAll[2];
            --arrAll[0];
            chengzhangPart1 = false;
            chengzhangPart2 = false;
            chengzhangPart3 = false;
            chengzhangPart4 = false;
            allVisibleFalse();
            _this.skin["shuMC1"].visible = true;
            _this.skin["shuMC1"].gotoAndPlay("成长");
         }
         if(arrAll[7] == true)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已播种！");
         }
         showAll();
      }
      
      private static function ZhaoYao(e:*) : *
      {
         if(arrAll[3] <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"缺少生命之光");
            return;
         }
         if(arrAll[7] == false)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"尚未播种！");
            return;
         }
         if(arrAll[7] == true && arrAll[6] < 100 && arrAll[3] > 0)
         {
            arrAll[6] = arrAll[6] + 11 + int(Math.random() * 10);
            if(arrAll[6] > 100)
            {
               arrAll[6] = 100;
            }
            --arrAll[3];
            if(_this.skin["shuMC1"].visible == true)
            {
               _this.skin["shuMC1"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC2"].visible == true)
            {
               _this.skin["shuMC2"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC3"].visible == true)
            {
               _this.skin["shuMC3"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC4"].visible == true)
            {
               _this.skin["shuMC4"].gotoAndPlay("照耀");
            }
            else if(_this.skin["shuMC5"].visible == true)
            {
               _this.skin["shuMC5"].gotoAndPlay("照耀");
            }
         }
         showAll();
      }
      
      private static function JiaoGuan(e:*) : *
      {
         if(arrAll[4] <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"缺少生命之水");
            return;
         }
         if(arrAll[7] == false)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"尚未播种！");
            return;
         }
         if(arrAll[7] == true && arrAll[5] < 100 && arrAll[4] > 0)
         {
            arrAll[5] = arrAll[5] + 6 + int(Math.random() * 5);
            if(arrAll[5] < 20)
            {
               allVisibleFalse();
               _this.skin["shuMC1"].visible = true;
               _this.skin["shuMC1"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 20 && arrAll[5] <= 40 && chengzhangPart1 == false)
            {
               chengzhangPart1 = true;
               allVisibleFalse();
               _this.skin["shuMC2"].visible = true;
               _this.skin["shuMC2"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 20 && arrAll[5] <= 40 && chengzhangPart1 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC2"].visible = true;
               _this.skin["shuMC2"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 40 && arrAll[5] <= 60 && chengzhangPart2 == false)
            {
               chengzhangPart2 = true;
               allVisibleFalse();
               _this.skin["shuMC3"].visible = true;
               _this.skin["shuMC3"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 40 && arrAll[5] <= 60 && chengzhangPart2 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC3"].visible = true;
               _this.skin["shuMC3"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 60 && arrAll[5] <= 80 && chengzhangPart3 == false)
            {
               chengzhangPart3 = true;
               allVisibleFalse();
               _this.skin["shuMC4"].visible = true;
               _this.skin["shuMC4"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 60 && arrAll[5] <= 80 && chengzhangPart3 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC4"].visible = true;
               _this.skin["shuMC4"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 80 && chengzhangPart4 == false)
            {
               chengzhangPart4 = true;
               allVisibleFalse();
               _this.skin["shuMC5"].visible = true;
               _this.skin["shuMC5"].gotoAndPlay("成长");
            }
            else if(arrAll[5] > 80 && chengzhangPart4 == true)
            {
               allVisibleFalse();
               _this.skin["shuMC5"].visible = true;
               _this.skin["shuMC5"].gotoAndPlay("浇灌");
            }
            if(arrAll[5] > 100)
            {
               arrAll[5] = 100;
            }
            --arrAll[4];
         }
         showAll();
      }
      
      private static function ShouHuo(e:*) : *
      {
         if(arrAll[5] == 100 && arrAll[6] == 100)
         {
            if(Main.player1.getBag().backSuppliesBagNum() > 3 && Main.player1.getBag().backOtherBagNum() > 2)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21234));
               _this.tishi["jl0"].gotoAndStop(SuppliesFactory.getSuppliesById(21234).getFrame());
               getRDM(Main.player_1);
               getRDM2(Main.player_1);
               arrAll[7] = false;
               arrAll[5] = 0;
               arrAll[6] = 0;
               _this.tishi.visible = true;
               _this.tishi.alpha = 1;
               _this.skin.addEventListener(Event.ENTER_FRAME,jianbian);
               _this.skin["shuMC5"].gotoAndPlay("果实摘取");
               _this.skin["shouhuo"].visible = false;
               Main.Save();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足！");
            }
         }
         if(arrAll[5] == 100)
         {
            if(Main.player1.getBag().backSuppliesBagNum() > 2 && Main.player1.getBag().backOtherBagNum() > 1)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21234));
               _this.tishi["jl0"].gotoAndStop(SuppliesFactory.getSuppliesById(21234).getFrame());
               _this.tishi["jl2"].gotoAndStop(1);
               getRDM(Main.player_1);
               arrAll[7] = false;
               arrAll[5] = 0;
               arrAll[6] = 0;
               _this.tishi.visible = true;
               _this.tishi.alpha = 1;
               _this.skin.addEventListener(Event.ENTER_FRAME,jianbian);
               _this.skin["shuMC5"].gotoAndPlay("果实摘取");
               _this.skin["shouhuo"].visible = false;
               Main.Save();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足！");
            }
         }
         showAll();
      }
      
      private static function getRDM(ppp:Player) : *
      {
         var randomValue:Number = Math.random() * 100;
         if(randomValue > 0 && randomValue < 3)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63335));
            _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63335).getFrame());
         }
         else if(randomValue > 3 && randomValue < 23)
         {
            ppp.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21115));
            _this.tishi["jl1"].gotoAndStop(SuppliesFactory.getSuppliesById(21115).getFrame());
         }
         else if(randomValue > 23 && randomValue < 30)
         {
            ppp.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21235));
            _this.tishi["jl1"].gotoAndStop(SuppliesFactory.getSuppliesById(21235).getFrame());
         }
         else if(randomValue > 30 && randomValue < 35)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63326));
            _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63326).getFrame());
         }
         else if(randomValue > 35 && randomValue < 55)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63290));
            _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63290).getFrame());
         }
         else if(randomValue > 55 && randomValue < 75)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
            _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63181).getFrame());
         }
         else if(randomValue > 75 && randomValue < 95)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
            _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63203).getFrame());
         }
         else if(randomValue > 95 && randomValue < 100)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63271));
            _this.tishi["jl1"].gotoAndStop(OtherFactory.creatOther(63271).getFrame());
         }
      }
      
      private static function getRDM2(ppp:Player) : *
      {
         var randomValue:Number = Math.random() * 100;
         if(randomValue > 0 && randomValue < 3)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63335));
            _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63335).getFrame());
         }
         else if(randomValue > 3 && randomValue < 23)
         {
            ppp.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21115));
            _this.tishi["jl2"].gotoAndStop(SuppliesFactory.getSuppliesById(21115).getFrame());
         }
         else if(randomValue > 23 && randomValue < 30)
         {
            ppp.data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21235));
            _this.tishi["jl2"].gotoAndStop(SuppliesFactory.getSuppliesById(21235).getFrame());
         }
         else if(randomValue > 30 && randomValue < 35)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63326));
            _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63326).getFrame());
         }
         else if(randomValue > 35 && randomValue < 55)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63290));
            _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63290).getFrame());
         }
         else if(randomValue > 55 && randomValue < 75)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63181));
            _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63181).getFrame());
         }
         else if(randomValue > 75 && randomValue < 95)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63203));
            _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63203).getFrame());
         }
         else if(randomValue > 95 && randomValue < 100)
         {
            ppp.data.getBag().addOtherobjBag(OtherFactory.creatOther(63271));
            _this.tishi["jl2"].gotoAndStop(OtherFactory.creatOther(63271).getFrame());
         }
      }
      
      private static function allVisibleFalse() : *
      {
         _this.skin["shuMC1"].visible = false;
         _this.skin["shuMC2"].visible = false;
         _this.skin["shuMC3"].visible = false;
         _this.skin["shuMC4"].visible = false;
         _this.skin["shuMC5"].visible = false;
      }
      
      public static function showAll() : *
      {
         if(arrAll[7] == false)
         {
            _this.skin["zhongxia"].visible = true;
            _this.skin["zhongxiaRMB"].visible = true;
         }
         else
         {
            _this.skin["zhongxia"].visible = false;
            _this.skin["zhongxiaRMB"].visible = false;
         }
         if(arrAll[5] >= 100)
         {
            _this.skin["shouhuo"].visible = true;
         }
         else
         {
            _this.skin["shouhuo"].visible = false;
         }
         _this.skin["jindu1"]["txt"].text = arrAll[5] + "%";
         _this.skin["jindu1"]["mc"].scaleX = arrAll[5] / 100;
         _this.skin["jindu2"]["txt"].text = arrAll[6] + "%";
         _this.skin["jindu2"]["mc"].scaleX = arrAll[6] / 100;
         _this.skin["shengyu"].text = arrAll[0];
         _this.skin["smzh"].text = arrAll[2];
         _this.skin["smzg"].text = arrAll[3];
         _this.skin["smzs"].text = arrAll[4];
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("LaoDong") as Class;
         this.skin = new classRef();
         var classRef2:Class = loadData.getClass("TiShi") as Class;
         this.tishi = new classRef2();
         addChild(this.skin);
         addChild(this.tishi);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
   }
}

