package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.ItemsTooltip;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4016")]
   public class Shop4399 extends MovieClip
   {
      
      public static var shopX:Shop4399;
      
      public static var tooltip:ItemsTooltip;
      
      public static var selCZ:Boolean;
      
      public static var who_btn:int;
      
      public static var num:int = 1;
      
      public static var numTotal:int = 1;
      
      public static var numX:int = 6;
      
      public static var numXXX:int = 0;
      
      public static var ShopArr:Array = new Array();
      
      private static var ShopArr_Temp:Array = new Array();
      
      private static var SelType:String = "全部";
      
      private static var buyNum:VT = VT.createVT();
      
      private static var shop_num:VT = VT.createVT();
      
      public static var buyArr:Array = new Array();
      
      public static var tempObjArr:Array = new Array();
      
      public static var moneyAll:VT = VT.createVT(-1);
      
      public static var totalPaiedMoney:VT = VT.createVT(-1);
      
      public static var totalRecharged:VT = VT.createVT(-2);
      
      public static var p1p2X:int = 1;
      
      public var Info_mc:MovieClip;
      
      public var KillOpen_btn:SimpleButton;
      
      public var NoMoney_mc:MovieClip;
      
      public var SelType_全部_btn:MovieClip;
      
      public var SelType_其他_btn:MovieClip;
      
      public var SelType_宝石_btn:MovieClip;
      
      public var SelType_时装_btn:MovieClip;
      
      public var ZY_txt:TextField;
      
      public var _BLACK_mc:游戏初始化;
      
      public var add_Money_btn:SimpleButton;
      
      public var back_btn:SimpleButton;
      
      public var close_btn:SimpleButton;
      
      public var level_1_mc:MovieClip;
      
      public var level_2_mc:MovieClip;
      
      public var money_All_txt:TextField;
      
      public var name_txt:TextField;
      
      public var next_btn:SimpleButton;
      
      public var shop1_mc:MovieClip;
      
      public var shop2_mc:MovieClip;
      
      public var shop3_mc:MovieClip;
      
      public var shop4_mc:MovieClip;
      
      public var shop5_mc:MovieClip;
      
      public var shop6_mc:MovieClip;
      
      public var show_1:SimpleButton;
      
      public var show_2:SimpleButton;
      
      public var total_txt:TextField;
      
      public var whoBuy_mc:购买物品提示栏;
      
      public var 购买成功_mc:MovieClip;
      
      public var 道具已满_mc:MovieClip;
      
      public function Shop4399()
      {
         super();
         Main._stage.addEventListener(Event.ACTIVATE,this.onACTIVATE);
         this.close_btn.addEventListener(MouseEvent.CLICK,this.CloseX);
         this.add_Money_btn.addEventListener(MouseEvent.CLICK,this.Open_AddMoney);
         this.SelType_全部_btn.mouseChildren = false;
         this.SelType_其他_btn.mouseChildren = false;
         this.SelType_宝石_btn.mouseChildren = false;
         this.SelType_时装_btn.mouseChildren = false;
         this.SelType_全部_btn.addEventListener(MouseEvent.CLICK,this.Show全部);
         this.SelType_其他_btn.addEventListener(MouseEvent.CLICK,this.Show其他);
         this.SelType_宝石_btn.addEventListener(MouseEvent.CLICK,this.Show宝石);
         this.SelType_时装_btn.addEventListener(MouseEvent.CLICK,this.Show时装);
         this.SelType_全部_btn.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.SelType_其他_btn.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.SelType_宝石_btn.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.SelType_时装_btn.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.SelType_全部_btn.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.SelType_其他_btn.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.SelType_宝石_btn.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.SelType_时装_btn.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         this.back_btn.addEventListener(MouseEvent.CLICK,this.前页);
         this.next_btn.addEventListener(MouseEvent.CLICK,this.后页);
         this.show_1.addEventListener(MouseEvent.CLICK,this.Show_1P);
         if(Main.P1P2)
         {
            this.show_2.addEventListener(MouseEvent.CLICK,this.Show_2P);
         }
         this.KillOpen_btn.addEventListener(MouseEvent.CLICK,this.KillOpen);
         this._BLACK_mc.visible = false;
         this.allBtnXXX();
      }
      
      public static function NewShop4399() : *
      {
         if(!shopX)
         {
            shopX = new Shop4399();
         }
      }
      
      public static function Open(page:uint = 1, xx:int = 0, yy:int = 0) : *
      {
         SelType = "全部";
         Main.allClosePanel();
         NewShop4399();
         Main._stage.addChild(shopX);
         shopX.Show();
         shopX.x = xx;
         shopX.y = yy;
         shopX.visible = true;
         Shop4399.WhoBuyClose();
         shopX._BLACK_mc.visible = false;
         txtShow();
         if(page != 1)
         {
            num = page;
         }
         shopX.Show();
      }
      
      public static function Close() : *
      {
         NewShop4399();
         shopX.x = 5000;
         shopX.y = 5000;
         shopX.visible = false;
      }
      
      public static function AddBuyNum(XX:int) : *
      {
         shopX["shop" + XX + "_mc"].Buy_btn.mouseEnabled = true;
         shopX["shop" + XX + "_mc"].Buy_btn.addEventListener(MouseEvent.CLICK,BuyNUM);
         shopX["shop" + XX + "_mc"].pic_mc.gotoAndStop(int(ShopArr_Temp[XX + numXXX][3]));
         shopX["shop" + XX + "_mc"].name_txt.text = "" + ShopArr_Temp[XX + numXXX][5];
         ColorX(shopX["shop" + XX + "_mc"].name_txt,ShopArr_Temp[XX + numXXX][8]);
         shopX["shop" + XX + "_mc"].money_txt.text = "" + (ShopArr_Temp[XX + numXXX][0] as VT).getValue() + "点券";
         shopX["shop" + XX + "_mc"].info_txt.text = "" + ShopArr_Temp[XX + numXXX][4];
         shopX["shop" + XX + "_mc"].BuyNum_txt.text = "1";
         shopX["shop" + XX + "_mc"].numUP_btn.addEventListener(MouseEvent.CLICK,BuyNUM_UP);
         shopX["shop" + XX + "_mc"].numDOWN_btn.addEventListener(MouseEvent.CLICK,BuyNUM_DOWN);
      }
      
      private static function ColorX(tObj:*, colNum:int) : *
      {
         var col:String = "0xffffff";
         if(colNum == 2)
         {
            col = "0x0066ff";
         }
         else if(colNum == 3)
         {
            col = "0xFF33FF";
         }
         else if(colNum == 4)
         {
            col = "0xFF9900";
         }
         else if(colNum == 5)
         {
            col = "0xFF9900";
         }
         else if(colNum == 6)
         {
            col = "0xCC3300";
         }
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         tObj.setTextFormat(myTextFormat);
      }
      
      public static function RemoveBuyNum(XX:int) : *
      {
         shopX["shop" + XX + "_mc"].Buy_btn.mouseEnabled = false;
         shopX["shop" + XX + "_mc"].Buy_btn.removeEventListener(MouseEvent.CLICK,BuyNUM);
         shopX["shop" + XX + "_mc"].pic_mc.gotoAndStop(1);
         shopX["shop" + XX + "_mc"].name_txt.text = "";
         shopX["shop" + XX + "_mc"].money_txt.text = "";
         shopX["shop" + XX + "_mc"].info_txt.text = "";
         shopX["shop" + XX + "_mc"].BuyNum_txt.text = "";
         shopX["shop" + XX + "_mc"].numUP_btn.removeEventListener(MouseEvent.CLICK,BuyNUM_UP);
         shopX["shop" + XX + "_mc"].numDOWN_btn.removeEventListener(MouseEvent.CLICK,BuyNUM_DOWN);
      }
      
      private static function BuyNUM_UP(e:MouseEvent) : *
      {
         var X2:int = int((e.target.parent.name as String).substr(4,1));
         var xx:int = int(shopX["shop" + X2 + "_mc"].BuyNum_txt.text);
         if(xx < 20)
         {
            shopX["shop" + X2 + "_mc"].BuyNum_txt.text = "" + (xx + 1);
         }
         else
         {
            shopX["shop" + X2 + "_mc"].BuyNum_txt.text = "" + 1;
         }
      }
      
      private static function BuyNUM_DOWN(e:MouseEvent) : *
      {
         var X2:int = int((e.target.parent.name as String).substr(4,1));
         var xx:int = int(shopX["shop" + X2 + "_mc"].BuyNum_txt.text);
         if(xx > 1)
         {
            shopX["shop" + X2 + "_mc"].BuyNum_txt.text = "" + (xx - 1);
         }
         else
         {
            shopX["shop" + X2 + "_mc"].BuyNum_txt.text = "" + 20;
         }
      }
      
      private static function BuyNUM(e:MouseEvent) : *
      {
         var X2:int = int((e.target.parent.name as String).substr(4,1)) + numXXX;
         shop_num.setValue(X2);
         Shop4399.WhoBuyOpen();
      }
      
      private static function WhoBuyOpen(e:* = null) : *
      {
         if(uint(shopX["shop" + (shop_num.getValue() - numXXX) + "_mc"].BuyNum_txt.text) <= 0)
         {
            return;
         }
         buyNum.setValue(uint(shopX["shop" + (shop_num.getValue() - numXXX) + "_mc"].BuyNum_txt.text));
         shopX.whoBuy_mc.x = shopX.whoBuy_mc.y = 0;
         shopX.whoBuy_mc.close_btn.addEventListener(MouseEvent.CLICK,WhoBuyClose);
         shopX.whoBuy_mc.P1_buy_btn.addEventListener(MouseEvent.CLICK,BuyObj_DuokaiJianCe);
         shopX.whoBuy_mc.pic_mc.gotoAndStop(ShopArr_Temp[shop_num.getValue()][3]);
         shopX.whoBuy_mc.名字.text = ShopArr_Temp[shop_num.getValue()][5];
         shopX.whoBuy_mc.说明.text = ShopArr_Temp[shop_num.getValue()][4];
         shopX.whoBuy_mc.点券.text = (ShopArr_Temp[shop_num.getValue()][0] as VT).getValue() * buyNum.getValue() + "点券";
         shopX.whoBuy_mc.数量.text = buyNum.getValue();
         if(Main.P1P2)
         {
            shopX.whoBuy_mc.P2_buy_btn.addEventListener(MouseEvent.CLICK,BuyObj_DuokaiJianCe);
         }
         else
         {
            shopX.whoBuy_mc.P2_buy_btn.visible = false;
         }
      }
      
      private static function BuyObj_DuokaiJianCe(e:MouseEvent) : *
      {
         who_btn = int(e.target.name.substr(1,1));
         BuyObj();
      }
      
      private static function WhoBuyClose(e:* = null) : *
      {
         shopX.whoBuy_mc.x = shopX.whoBuy_mc.y = 5000;
      }
      
      public static function BuyObj() : *
      {
         var who:int = who_btn;
         var pX:Player = Main["player_" + who];
         if(ShopArr_Temp[shop_num.getValue()][1] == "宝石类")
         {
            if(pX.data.getBag().canPutGemNum(ShopArr_Temp[shop_num.getValue()][2].getValue()) >= buyNum.getValue())
            {
               if((Shop4399.moneyAll as VT).getValue() < (ShopArr_Temp[shop_num.getValue()][0] as VT).getValue() * buyNum.getValue())
               {
                  Shop4399.NoMoney_info_Open();
               }
               else
               {
                  Api_4399_All.BuyObj(ShopArr_Temp[shop_num.getValue()][7].getValue(),buyNum.getValue());
                  tempObjArr[0] = Main["player_" + who];
                  tempObjArr[1] = "宝石类";
                  tempObjArr[2] = ShopArr_Temp[shop_num.getValue()][2];
                  shopX._BLACK_mc.visible = true;
               }
            }
            else
            {
               道具已满();
            }
            WhoBuyClose();
         }
         else if(ShopArr_Temp[shop_num.getValue()][1] == "其他类")
         {
            if(pX.data.getBag().canPutOtherNum(ShopArr_Temp[shop_num.getValue()][2].getValue()) >= buyNum.getValue())
            {
               if((Shop4399.moneyAll as VT).getValue() < (ShopArr_Temp[shop_num.getValue()][0] as VT).getValue() * buyNum.getValue())
               {
                  Shop4399.NoMoney_info_Open();
               }
               else
               {
                  Api_4399_All.BuyObj(ShopArr_Temp[shop_num.getValue()][7].getValue(),buyNum.getValue());
                  tempObjArr[0] = Main["player_" + who];
                  tempObjArr[1] = "其他类";
                  tempObjArr[2] = ShopArr_Temp[shop_num.getValue()][2];
                  shopX._BLACK_mc.visible = true;
               }
            }
            else
            {
               道具已满();
            }
            WhoBuyClose();
         }
         else if(ShopArr_Temp[shop_num.getValue()][1] == "时装类")
         {
            if(pX.data.getBag().backequipBagNum() >= buyNum.getValue())
            {
               if((Shop4399.moneyAll as VT).getValue() < (ShopArr_Temp[shop_num.getValue()][0] as VT).getValue() * buyNum.getValue())
               {
                  Shop4399.NoMoney_info_Open();
               }
               else
               {
                  Api_4399_All.BuyObj(ShopArr_Temp[shop_num.getValue()][7].getValue(),buyNum.getValue());
                  tempObjArr[0] = Main["player_" + who];
                  tempObjArr[1] = "时装类";
                  tempObjArr[2] = ShopArr_Temp[shop_num.getValue()][2];
                  shopX._BLACK_mc.visible = true;
               }
            }
            else
            {
               道具已满();
            }
            WhoBuyClose();
         }
         else if(ShopArr_Temp[shop_num.getValue()][1] == "消耗类")
         {
            if(pX.data.getBag().backSuppliesBagNum() >= buyNum.getValue())
            {
               if((Shop4399.moneyAll as VT).getValue() < (ShopArr_Temp[shop_num.getValue()][0] as VT).getValue() * buyNum.getValue())
               {
                  Shop4399.NoMoney_info_Open();
               }
               else
               {
                  Api_4399_All.BuyObj(ShopArr_Temp[shop_num.getValue()][7].getValue(),buyNum.getValue());
                  tempObjArr[0] = Main["player_" + who];
                  tempObjArr[1] = "消耗类";
                  tempObjArr[2] = ShopArr_Temp[shop_num.getValue()][2];
                  shopX._BLACK_mc.visible = true;
               }
            }
            else
            {
               道具已满();
            }
            WhoBuyClose();
         }
      }
      
      public static function GetObj() : *
      {
         var iii:int = 0;
         var iii2:int = 0;
         var iii3:int = 0;
         var iii4:int = 0;
         shopX._BLACK_mc.visible = false;
         if(tempObjArr.length == 3)
         {
            if(tempObjArr[0] is Player)
            {
               if(tempObjArr[1] == "宝石类")
               {
                  for(iii = 0; iii < buyNum.getValue(); iii++)
                  {
                     tempObjArr[0].data.getBag().addGemBag(GemFactory.creatGemById(tempObjArr[2].getValue()));
                  }
               }
               else if(tempObjArr[1] == "其他类")
               {
                  for(iii2 = 0; iii2 < buyNum.getValue(); iii2++)
                  {
                     tempObjArr[0].data.getBag().addOtherobjBag(OtherFactory.creatOther(tempObjArr[2].getValue()));
                  }
               }
               else if(tempObjArr[1] == "时装类")
               {
                  for(iii3 = 0; iii3 < buyNum.getValue(); iii3++)
                  {
                     tempObjArr[0].data.getBag().addEquipBag(EquipFactory.createEquipByID(tempObjArr[2].getValue()));
                  }
               }
               else if(tempObjArr[1] == "消耗类")
               {
                  for(iii4 = 0; iii4 < buyNum.getValue(); iii4++)
                  {
                     tempObjArr[0].data.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(tempObjArr[2].getValue()));
                  }
               }
            }
            if(tempObjArr[2].getValue() == 63211)
            {
               Api_4399_GongHui.upNum(52);
            }
            tempObjArr = new Array();
            购买成功();
            txtShow();
         }
      }
      
      private static function txtShow() : *
      {
         if(p1p2X == 1)
         {
            showLV(Main.player_1.data.getLevel());
         }
         else
         {
            showLV(Main.player_2.data.getLevel());
         }
      }
      
      private static function showLV(level:Number) : *
      {
         var str:String = null;
         var left:int = 0;
         var right:int = 0;
         if(level < 10)
         {
            shopX["level_1_mc"].gotoAndStop(level);
            shopX["level_2_mc"].visible = false;
         }
         else
         {
            shopX["level_2_mc"].visible = true;
            str = level.toString();
            left = int(str.substring(0,1));
            right = int(str.substring(1,2));
            shopX["level_1_mc"].gotoAndStop(left);
            if(right == 0)
            {
               shopX["level_2_mc"].gotoAndStop(10);
            }
            else
            {
               shopX["level_2_mc"].gotoAndStop(right);
            }
         }
      }
      
      public static function NoMoney_info_Open(e:* = null) : *
      {
         Shop4399.shopX.NoMoney_mc.x = Shop4399.shopX.NoMoney_mc.y = 0;
         Shop4399.shopX.NoMoney_mc.yes_btn.addEventListener(MouseEvent.CLICK,NoMoney_info_Close);
         Shop4399.shopX.NoMoney_mc.addMoney_btn.addEventListener(MouseEvent.CLICK,Open_AddMoney2);
      }
      
      private static function NoMoney_info_Close(e:* = null) : *
      {
         Shop4399.shopX.NoMoney_mc.x = Shop4399.shopX.NoMoney_mc.y = 5000;
      }
      
      private static function Open_AddMoney2(e:* = null) : *
      {
         Main.ChongZhi();
      }
      
      private static function 购买成功() : *
      {
         shopX.购买成功_mc.x = shopX.购买成功_mc.y = 0;
         shopX.购买成功_mc.gotoAndPlay(1);
      }
      
      private static function 道具已满() : *
      {
         shopX.道具已满_mc.x = shopX.道具已满_mc.y = 0;
         shopX.道具已满_mc.gotoAndPlay(1);
      }
      
      private function KillOpen(e:*) : *
      {
         ShopKillPoint.Open();
      }
      
      private function Show_1P(e:*) : *
      {
         Shop4399.p1p2X = 1;
         Shop4399.txtShow();
      }
      
      private function Show_2P(e:*) : *
      {
         Shop4399.p1p2X = 2;
         Shop4399.txtShow();
      }
      
      public function 前页(e:*) : *
      {
         if(num > 1)
         {
            --num;
            shopX.Show();
         }
      }
      
      public function 后页(e:*) : *
      {
         if(num < numTotal)
         {
            ++num;
            shopX.Show();
         }
      }
      
      public function Show全部(e:*) : *
      {
         SelType = "全部";
         num = 1;
         shopX.Show();
         this.allBtnXXX();
      }
      
      public function Show其他(e:*) : *
      {
         SelType = "其他类";
         num = 1;
         shopX.Show();
         this.allBtnXXX();
      }
      
      public function Show宝石(e:*) : *
      {
         SelType = "宝石类";
         num = 1;
         shopX.Show();
         this.allBtnXXX();
      }
      
      public function Show时装(e:*) : *
      {
         SelType = "时装类";
         num = 1;
         shopX.Show();
         this.allBtnXXX();
      }
      
      public function onMOUSE_MOVE(e:MouseEvent) : *
      {
         e.target.gotoAndStop(2);
      }
      
      public function onMOUSE_OUT(e:MouseEvent) : *
      {
         e.target.gotoAndStop(1);
         this.allBtnXXX();
      }
      
      public function allBtnXXX() : *
      {
         this.SelType_全部_btn.gotoAndStop(1);
         this.SelType_其他_btn.gotoAndStop(1);
         this.SelType_宝石_btn.gotoAndStop(1);
         this.SelType_时装_btn.gotoAndStop(1);
         if(SelType == "全部")
         {
            this.SelType_全部_btn.gotoAndStop(3);
         }
         else if(SelType == "其他类")
         {
            this.SelType_其他_btn.gotoAndStop(3);
         }
         else if(SelType == "宝石类")
         {
            this.SelType_宝石_btn.gotoAndStop(3);
         }
         else if(SelType == "时装类")
         {
            this.SelType_时装_btn.gotoAndStop(3);
         }
      }
      
      private function onACTIVATE(e:*) : *
      {
         if(shopX.visible && selCZ)
         {
            Api_4399_All.Money_sel2();
            selCZ = false;
            TiaoShi.txtShow("查询勇士币余额");
         }
      }
      
      private function CloseX(e:*) : *
      {
         Close();
      }
      
      public function Show() : *
      {
         for(var i:int = 0; i < 6; i++)
         {
            RemoveBuyNum(i + 1);
         }
         Shop4399.shopX.money_All_txt.text = moneyAll.getValue();
         this.TempShow();
      }
      
      private function TempShow() : *
      {
         var i:int = 0;
         var i2:int = 0;
         this.name_txt.text = Main.logName2;
         for(i = 0; i < 5; i++)
         {
            if(i == 4)
            {
               this.ZY_txt.text = "新手";
            }
            else if(Main["player" + p1p2X]._transferArr[i])
            {
               if(i == 0)
               {
                  this.ZY_txt.text = "杀戮战神";
               }
               else if(i == 1)
               {
                  this.ZY_txt.text = "汲魂术士";
               }
               else if(i == 2)
               {
                  this.ZY_txt.text = "毁灭拳神";
               }
               else if(i == 3)
               {
                  this.ZY_txt.text = "暗影杀手";
               }
               break;
            }
         }
         ShopArr_Temp = [-1];
         for(i in ShopArr)
         {
            if(SelType != "全部" && ShopArr[i][1] == SelType || SelType == "全部" && ShopArr[i][1] != "直接消费")
            {
               ShopArr_Temp[ShopArr_Temp.length] = DeepCopyUtil.clone(ShopArr[i]);
            }
         }
         numXXX = (num - 1) * numX;
         numTotal = int(ShopArr_Temp.length / numX) + 1;
         this.total_txt.text = num + "/" + numTotal;
         for(i2 = 1; i2 < numX + 1; i2++)
         {
            if(Boolean(ShopArr_Temp[i2 + numXXX]) && ShopArr_Temp[i2 + numXXX] != null)
            {
               AddBuyNum(i2);
            }
         }
      }
      
      private function Open_AddMoney(e:* = null) : *
      {
         selCZ = true;
         var g:GameStop = new GameStop();
         Main.ChongZhi();
      }
   }
}

