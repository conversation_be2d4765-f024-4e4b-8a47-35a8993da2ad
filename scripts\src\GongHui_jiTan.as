package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class GongHui_jiTan extends MovieClip
   {
      
      public static var jiTan:MovieClip;
      
      public static var jiTanLv_arr:Array = [false,0,0,0,0];
      
      public static var sel_num:int = 1;
      
      public static var sel_max:int = 4;
      
      public static var page:int = 1;
      
      public static var page_max:int = 2;
      
      public static var jiTanExp_arr:Array = [false,[850,3000,6000,12000,17000],[1700,8500,13000,18000,24000],[4500,10000,15000,25000,32000],[7000,11500,18000,24900,35000]];
      
      public static var jiTanNum_arr:Array = [false,[0,10,15,20,25,30],[0,10,15,20,25,30],[0,4,8,12,16,20],[0,2,4,6,8,10]];
      
      public static var sj_arr:Array = [false,[1000,2000,4000,6000,8000],[1000,3000,6000,9000,12000],[4000,6000,9000,12000,15000],[5000,8000,11000,14000,18000]];
      
      public function GongHui_jiTan()
      {
         super();
      }
      
      private static function onClose(e:MouseEvent) : *
      {
         var tempArr:Array = null;
         var tempMc:MovieClip = e.target.parent;
         tempMc.visible = false;
         if(tempMc == jiTan)
         {
            tempArr = [157,158,159,160,161,162,163];
            Api_4399_GongHui.getNum(tempArr);
         }
      }
      
      public static function Open(e:MouseEvent) : *
      {
         GongHui_Interface._this.addChild(jiTan);
         jiTan.visible = true;
         jiTan.x = jiTan.y = 0;
         jiTan._info_mc.y = -5000;
         jiTan._info_mc.mouseChildren = jiTan._info_mc.mouseEnabled = false;
         jiTan.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         jiTan.back_btn.addEventListener(MouseEvent.CLICK,Back_Fun);
         jiTan.next_btn.addEventListener(MouseEvent.CLICK,Next_Fun);
         jiTan.x1_mc.sj_btn.addEventListener(MouseEvent.CLICK,SengJi);
         jiTan.x2_mc.sj_btn.addEventListener(MouseEvent.CLICK,SengJi);
         jiTan.x3_mc.sj_btn.addEventListener(MouseEvent.CLICK,SengJi);
         jiTan.x1_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_MOVE,onMOUSE_MOVE);
         jiTan.x2_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_MOVE,onMOUSE_MOVE);
         jiTan.x3_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_MOVE,onMOUSE_MOVE);
         jiTan.x1_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_OUT,onMOUSE_OUT);
         jiTan.x2_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_OUT,onMOUSE_OUT);
         jiTan.x3_mc.xxx_mc.addEventListener(MouseEvent.MOUSE_OUT,onMOUSE_OUT);
         Show();
      }
      
      public static function Show() : *
      {
         var mcX:MovieClip = null;
         var mcX2:MovieClip = null;
         var numX:int = 0;
         var gxXX:int = 0;
         var expXXX:int = 0;
         var lvX:int = 0;
         var expXX:int = 0;
         var gx:int = 0;
         jiTan._info_mc.y = -5000;
         for(var i:int = 1; i < 4; i++)
         {
            mcX = jiTan["x" + i + "_mc"];
            mcX2 = jiTan["xx" + i + "_mc"];
            numX = (page - 1) * 3 + i;
            gxXX = GongHui_Interface.playerInfo.unionInfo.contribution / 10;
            expXXX = GongHui_Interface.playerInfo.unionInfo.experience / 10;
            if(numX > sel_max)
            {
               mcX.visible = mcX2.visible = false;
            }
            else
            {
               lvX = int(jiTanLv_arr[numX]);
               mcX.sj_btn.visible = true;
               if(lvX >= 5)
               {
                  mcX.sj_btn.visible = false;
               }
               expXX = jiTanExp_arr[numX][lvX] / 10;
               mcX.visible = mcX2.visible = true;
               mcX.gotoAndStop(numX);
               mcX.lv_txt.text = "lv." + lvX;
               if(lvX < 5)
               {
                  gx = sj_arr[numX][lvX] / 10;
                  mcX.t1_txt.text = mcX2.t1_txt.text = "公会经验达到" + expXX + "开启";
                  mcX.t2_txt.text = "升级消耗贡献值:" + gx;
                  if(lvX == 0)
                  {
                     mcX.lv_txt.text = "未学习";
                     if(expXX > expXXX)
                     {
                        mcX.visible = false;
                     }
                  }
               }
               else
               {
                  mcX.t1_txt.text = "该技能已升至最高级";
                  mcX.t2_txt.text = "";
               }
            }
         }
         jiTan.gx_txt.text = GongHui_Interface.playerInfo.unionInfo.contribution / 10;
      }
      
      public static function SengJi(e:MouseEvent) : *
      {
         if(GongHui_Interface.playerInfo.unionInfo.uId != GongHui_Interface.playerInfo.member.uId && GongHui_Interface.playerInfo.member.roleId != "10")
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"只有会长及副会长拥有该权限!");
            return;
         }
         var num:int = int((e.target.parent.name as String).substr(1,1));
         var numX:int = (page - 1) * 3 + num;
         var lvX:int = int(jiTanLv_arr[numX]);
         var gx:int = int(sj_arr[numX][lvX]);
         var expXX:int = int(jiTanExp_arr[numX][lvX]);
         var gxXX:int = int(GongHui_Interface.playerInfo.unionInfo.contribution);
         var expXXX:int = int(GongHui_Interface.playerInfo.unionInfo.experience);
         var id:int = 159 + numX;
         TiaoShi.txtShow("升级所需贡献 = " + gx);
         if(lvX >= 5)
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"该技能已升至最高级!");
            return;
         }
         if(gxXX >= gx && expXXX >= expXX)
         {
            Api_4399_GongHui.XiaoHaoBanghui_GX(gx);
            Api_4399_GongHui.upNum(id);
            GongHui_Interface.playerInfo.unionInfo.contribution -= gx;
            TiaoShi.txtShow("剩余贡献点:" + GongHui_Interface.playerInfo.unionInfo.contribution);
            TiaoShi.txtShow("jiTanLv_arr[numX] = " + jiTanLv_arr[numX]);
            jiTanLv_arr[numX] = int(jiTanLv_arr[numX]) + 1;
            TiaoShi.txtShow("公会技能" + numX + " 提升到" + jiTanLv_arr[numX]);
            TiaoShi.txtShow("祭坛技能等级jiTanLv_arr = " + jiTanLv_arr);
            Show();
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"技能升级成功!");
         }
         else if(gxXX >= gx)
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"公会经验不足!");
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,300,30,0,true,2,"公会贡献点不足!");
         }
      }
      
      public static function onMOUSE_MOVE(e:MouseEvent) : *
      {
         var mcX:MovieClip = e.target.parent;
         var num:int = int(mcX.name.substr(1,1));
         var numX:int = (page - 1) * 3 + num;
         if(numX > sel_max)
         {
            return;
         }
         var lvX:int = int(jiTanLv_arr[numX]);
         var num1:int = int(jiTanNum_arr[numX][lvX]);
         var num2:int = int(jiTanNum_arr[numX][lvX + 1]);
         if(lvX <= 5)
         {
            if(numX == 1)
            {
               jiTan._info_mc._txt.text = "怪物精通\n公会玩家击杀关卡怪物获得经验提升" + num1 + "%";
               if(lvX != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n公会玩家击杀关卡怪物获得经验提升" + num2 + "%";
               }
            }
            else if(numX == 2)
            {
               jiTan._info_mc._txt.text = "关卡精通\n公会玩家挑战关卡每日成就点和击杀点消耗减少" + num1 + "%";
               if(lvX != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n公会玩家挑战关卡每日成就点和击杀点消耗减少" + num2 + "%";
               }
            }
            else if(numX == 3)
            {
               jiTan._info_mc._txt.text = "宠物精通\n出战宠物造成的伤害提升" + num1 + "%";
               if(lvX != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n出战宠物造成的伤害提升" + num2 + "%";
               }
            }
            else if(numX == 4)
            {
               jiTan._info_mc._txt.text = "技能精通\n公会玩家技能冷却时间削减" + num1 + "%";
               if(lvX != 5)
               {
                  jiTan._info_mc._txt.text += "\n\n下一等级\n公会玩家技能冷却时间削减" + num2 + "%";
               }
            }
            jiTan._info_mc.x = mcX.x - 30;
            jiTan._info_mc.y = 230;
         }
      }
      
      public static function onMOUSE_OUT(e:MouseEvent) : *
      {
         jiTan._info_mc._txt.text = "";
         jiTan._info_mc.y = -5000;
      }
      
      public static function Back_Fun(e:*) : *
      {
         if(page > 1)
         {
            --page;
         }
         Show();
      }
      
      public static function Next_Fun(e:*) : *
      {
         if(page < page_max)
         {
            ++page;
         }
         Show();
      }
      
      public static function expUP(num:int) : int
      {
         var lvX:int = int(jiTanLv_arr[1]);
         if(lvX > 0)
         {
            num = num * (100 + jiTanNum_arr[1][lvX]) / 100;
         }
         return num;
      }
      
      public static function killPointXX(num:int) : int
      {
         var lvX:int = int(jiTanLv_arr[2]);
         if(lvX > 0)
         {
            num = num * (100 - jiTanNum_arr[2][lvX]) / 100;
         }
         return num;
      }
      
      public static function CW_XX(num:int) : int
      {
         var lvX:int = int(jiTanLv_arr[3]);
         if(lvX > 0)
         {
            num = num * (100 + jiTanNum_arr[3][lvX]) / 100;
         }
         return num;
      }
      
      public static function CD_XX(num:int) : int
      {
         var lvX:int = int(jiTanLv_arr[4]);
         if(lvX > 0)
         {
            num = num * (100 - jiTanNum_arr[4][lvX]) / 100;
         }
         return num;
      }
   }
}

