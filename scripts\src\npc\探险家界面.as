package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.strengthenPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class 探险家界面 extends MovieClip
   {
      
      private static var only:探险家界面;
      
      public var close1:SimpleButton;
      
      public var close2:SimpleButton;
      
      public var ck_btn:SimpleButton;
      
      public function 探险家界面()
      {
         super();
         this.close1.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.close2.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.ck_btn.addEventListener(MouseEvent.CLICK,CK_Open);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         MusicBox.MusicPlay2("m3");
         if(!only)
         {
            classRef = All_Npc.loadData.getClass("src.npc.探险家界面") as Class;
            xxMov = new classRef();
            only = xxMov;
         }
         Main._stage.addChild(only);
         only.x = xx;
         only.y = yy;
         only.visible = true;
      }
      
      public static function Close() : *
      {
         if(only)
         {
            only.x = 5000;
            only.y = 5000;
            only.visible = false;
         }
      }
      
      public static function CK_Open(e:*) : *
      {
         Main.allClosePanel();
         StoragePanel.open();
      }
      
      private function CloseXX(e:*) : *
      {
         Close();
      }
   }
}

