package com.hotpoint.braveManIII.repository.tuijianShop
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   
   public class tuijianFactory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function tuijianFactory()
      {
         super();
      }
      
      public static function creatTuiJianFactory() : *
      {
         var es:tuijianFactory = new tuijianFactory();
         myXml = XMLAsset.createXML(Data2.tjShop);
         es.creatFactory();
      }
      
      private function creatFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var lv:Number = NaN;
         var suit:Number = NaN;
         var price:Number = NaN;
         var rmbID:Number = NaN;
         var rmbPrice:Number = NaN;
         var type:Number = NaN;
         var zhiye:Number = NaN;
         var miaoshu:String = null;
         var data:Array = null;
         for each(property in myXml.推荐商店)
         {
            id = Number(property.编号);
            lv = Number(property.级别);
            suit = Number(property.套装号);
            price = Number(property.价格);
            rmbID = Number(property.点卷编号);
            rmbPrice = Number(property.点卷价格);
            type = Number(property.类型);
            zhiye = Number(property.职业);
            miaoshu = String(property.描述);
            data = [id,lv,suit,price,rmbID,rmbPrice,type,zhiye,miaoshu];
            allData.push(data);
         }
      }
   }
}

