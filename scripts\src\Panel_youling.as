package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class Panel_youling extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      private static var only:Panel_youling;
      
      private static var skin:MovieClip;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "YouLingBaoZhu.swf";
      
      private static var sel_Num:int = 1;
      
      private static var dianQuanYn:Boolean = false;
      
      public static var lvArr:Array = [0,0,0,0,0];
      
      public static var bzNumArr:Array = [0,0,0,0,0];
      
      private static var lvMax:int = 100;
      
      private static var saveNum:int = 0;
      
      private static var zhuHunOk:Boolean = false;
      
      private static var gouMaiYN:Boolean = false;
      
      public function Panel_youling()
      {
         super();
         this.LoadSkin();
         only = this;
      }
      
      public static function Open() : *
      {
         Main.DuoKai_Fun();
         if(only)
         {
            only.visible = true;
            only.x = only.y = 0;
            Init();
            Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function Init() : *
      {
         var strArr:Array = null;
         skin["zezao_mc"].visible = false;
         skin["gold_txt"].text = "9999";
         skin["dianQun_txt"].text = "8888";
         skin["a1_txt1"].text = "攻击+0";
         skin["a1_txt2"].text = "生命+0";
         skin["a2_txt1"].text = "攻击+0%";
         skin["a2_txt2"].text = "生命+0%";
         skin["a3_txt1"].text = "破魔+0";
         skin["a3_txt2"].text = "魔抗+0";
         skin["a4_txt"].text = "最终伤害+0%";
         skin["sel_mc"].mouseChildren = skin["sel_mc"].mouseEnabled = false;
         skin.zhuHun_btn.addEventListener(MouseEvent.CLICK,ZhuHun);
         skin._mc1.addEventListener(MouseEvent.CLICK,Sel);
         skin._mc2.addEventListener(MouseEvent.CLICK,Sel);
         skin._mc3.addEventListener(MouseEvent.CLICK,Sel);
         skin._mc4.addEventListener(MouseEvent.CLICK,Sel);
         skin.close_btn.addEventListener(MouseEvent.CLICK,CloseX);
         for(var i:int = 1; i <= 4; i++)
         {
            strArr = [0,"蓝色灵魂","紫色灵魂","金色灵魂","红色灵魂"];
            skin["x_txt" + i].text = strArr[i];
            skin["dh_mc" + i].visible = false;
            skin["_txt" + i].text = bzNumArr[i] + "/" + getNum(i);
         }
         skin["A_mc"].addEventListener(MouseEvent.CLICK,Sel_Money_A);
      }
      
      private static function Show() : *
      {
         var s1:String = null;
         var s2:String = null;
         var s3:String = null;
         var s4:String = null;
         var s5:String = null;
         var s6:String = null;
         var s7:String = null;
         skin["sel_mc"].x = 159 + (sel_Num - 1) * 77;
         var strArr:Array = [0,"蓝色灵魂","紫色灵魂","金色灵魂","红色灵魂"];
         for(var i:int = 1; i <= 4; i++)
         {
            if(lvArr[i] > 0)
            {
               skin["x_txt" + i].text = strArr[i] + "+" + lvArr[i];
            }
            skin["_txt" + i].text = bzNumArr[i] + "/" + getNum(i);
         }
         skin.cgl_txt.text = getCglStr();
         skin["A_mc"]["yes_mc"].visible = false;
         if(dianQuanYn)
         {
            skin["A_mc"]["yes_mc"].visible = true;
         }
         skin["_txt"].text = "使用" + getXuQiudianQuan() + "点券注魂";
         if(lvArr[1] > 0)
         {
            s1 = lvArr[1] * 20;
            s2 = lvArr[1] * 200;
            skin["a1_txt1"].text = "攻击+" + s1;
            skin["a1_txt2"].text = "生命+" + s2;
            trace("1 >>>>>>>>>>>>>>>>>>>>>",s1,s2);
         }
         if(lvArr[2] > 0)
         {
            s3 = lvArr[2] * 2 / 10;
            s4 = lvArr[2] * 2 / 10;
            skin["a2_txt1"].text = "攻击+" + s3 + "%";
            skin["a2_txt2"].text = "生命+" + s4 + "%";
            trace("2 >>>>>>>>>>>>>>>>>>>>>",s3,s4);
         }
         if(lvArr[3] > 0)
         {
            s5 = lvArr[3] * 2;
            s6 = lvArr[3] * 2;
            skin["a3_txt1"].text = "破魔+" + s5;
            skin["a3_txt2"].text = "魔抗+" + s6;
            trace("3 >>>>>>>>>>>>>>>>>>>>>",s5,s6);
         }
         if(lvArr[4] > 0)
         {
            s7 = lvArr[4] * 2 / 10;
            skin["a4_txt"].text = "最终伤害+" + s7 + "%";
            trace("4 >>>>>>>>>>>>>>>>>>>>>",s7);
         }
         if(Main.player2)
         {
            skin.gold_txt.text = String(Main.player2.getGold());
         }
         if(Main.player1)
         {
            skin.gold_txt.text = String(Main.player1.getGold());
         }
         if(Shop4399.moneyAll.getValue() > -1)
         {
            skin.dianQun_txt.text = Shop4399.moneyAll.getValue();
         }
      }
      
      private static function ZhuHun(e:MouseEvent) : *
      {
         var shopID:int = 0;
         if(lvArr[sel_Num] >= lvMax)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已满级");
            return;
         }
         trace("注魂>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",sel_Num,"?",bzNumArr[sel_Num],getNum(sel_Num));
         if(dianQuanYn)
         {
            trace("使用点券购买",Shop4399.moneyAll.getValue(),getXuQiudianQuan());
            if(Shop4399.moneyAll.getValue() >= getXuQiudianQuan())
            {
               gouMaiYN = true;
               shopID = 301 + sel_Num;
               DongHua();
               Api_4399_All.BuyObj(shopID,getNum(sel_Num));
               skin.addEventListener(Event.ENTER_FRAME,ZhuHunGo);
               trace("使用点券购买????????????????",getXuQiudianQuan(),shopID,getNum(sel_Num));
            }
            else
            {
               Shop4399.NoMoney_info_Open();
            }
            return;
         }
         if(bzNumArr[sel_Num] < getNum(sel_Num))
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"灵魂数量不足");
            return;
         }
         bzNumArr[sel_Num] -= getNum(sel_Num);
         ZhuHunYN();
         DongHua();
         saveNum = 1;
         skin.addEventListener(Event.ENTER_FRAME,ZhuHunGo);
         Main.Save();
      }
      
      public static function DianQuanOk() : *
      {
         if(gouMaiYN)
         {
            gouMaiYN = false;
            ZhuHunYN();
            if(zhuHunOk)
            {
               NewMC.Open("文字提示",Main._stage,300,265,30,0,true,1,"注魂成功");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,300,265,30,0,true,1,"注魂失败");
            }
            skin["zezao_mc"].visible = false;
            Show();
            Main.Save();
         }
      }
      
      public static function ZhuHunYN() : *
      {
         var rX:int = 0;
         var r:Number = NaN;
         if(lvArr[sel_Num] < lvMax)
         {
            rX = int(getCgl());
            r = Math.random() * 100;
            zhuHunOk = false;
            if(r < rX)
            {
               ++lvArr[sel_Num];
               zhuHunOk = true;
               trace("注魂 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 成功!",rX,r);
            }
            else
            {
               trace("注魂 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 失败!",rX,r);
            }
         }
      }
      
      private static function DongHua() : *
      {
         skin["dh_mc" + sel_Num].visible = true;
         skin["dh_mc" + sel_Num].gotoAndPlay(1);
         skin["zezao_mc"].visible = true;
      }
      
      private static function ZhuHunGo(e:*) : *
      {
         if(skin["dh_mc" + sel_Num].currentFrame == skin["dh_mc" + sel_Num].totalFrames)
         {
            skin["dh_mc" + sel_Num].visible = false;
         }
         if(!skin["dh_mc" + sel_Num].visible && saveNum == 2)
         {
            skin["zezao_mc"].visible = false;
            saveNum = 0;
            Show();
            if(zhuHunOk)
            {
               NewMC.Open("文字提示",Main._stage,300,265,30,0,true,1,"注魂成功");
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,300,265,30,0,true,1,"注魂失败");
            }
            trace("注魂结束!");
            skin.removeEventListener(Event.ENTER_FRAME,ZhuHunGo);
         }
      }
      
      public static function SaveOk() : *
      {
         if(saveNum == 1)
         {
            saveNum = 2;
            trace("幽灵宝珠 保存成功");
         }
      }
      
      private static function Sel(e:MouseEvent) : *
      {
         var str:String = (e.target as SimpleButton).name;
         sel_Num = str.substr(3,1);
         Show();
         trace("选择>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>",sel_Num);
      }
      
      private static function getCgl() : int
      {
         var lv:int = int(lvArr[sel_Num]);
         var numX:int = 100 - lv * 3;
         if(numX < 10)
         {
            numX = 10;
         }
         if(dianQuanYn)
         {
            numX += 50;
         }
         if(numX > 100)
         {
            numX = 100;
         }
         return numX;
      }
      
      private static function getCglStr() : String
      {
         var str:* = "成功率:" + getCgl() + "%";
         if(dianQuanYn)
         {
            str += " (+50%)";
         }
         return str;
      }
      
      private static function getNum(i:*) : int
      {
         var lv:int = int(lvArr[i]);
         var lv2:int = lv / 5;
         return 10 + lv2 * 5;
      }
      
      private static function InitOpen() : *
      {
         var temp:Panel_youling = new Panel_youling();
         Main._stage.addChild(temp);
         OpenYN = true;
      }
      
      public static function CloseX(e:* = null) : *
      {
         if(only)
         {
            only.visible = false;
            only.x = only.y = 5000;
            OpenYN = false;
         }
      }
      
      private static function Sel_Money_A(e:* = null) : *
      {
         if(!dianQuanYn)
         {
            dianQuanYn = true;
            skin["A_mc"]["yes_mc"].visible = true;
         }
         else
         {
            dianQuanYn = false;
            skin["A_mc"]["yes_mc"].visible = false;
         }
         Show();
      }
      
      private static function getXuQiudianQuan() : int
      {
         var num:int = int(getNum(sel_Num));
         return int(num * (sel_Num + 1));
      }
      
      private function LoadSkin() : *
      {
         if(!skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         Play_Interface.interfaceX["load_mc"].visible = false;
         var classRef:Class = loadData.getClass("youLingBaoZhu") as Class;
         skin = new classRef();
         only.addChild(skin);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
   }
}

