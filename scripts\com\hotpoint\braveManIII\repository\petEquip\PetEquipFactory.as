package com.hotpoint.braveManIII.repository.petEquip
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.petEquip.PetEquip;
   import flash.events.*;
   import src.*;
   
   public class PetEquipFactory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function PetEquipFactory()
      {
         super();
      }
      
      public static function creatpetEquipFactory() : *
      {
         var pef:PetEquipFactory = new PetEquipFactory();
         myXml = XMLAsset.createXML(Data2.petEquip);
         pef.creatpetEquipData();
      }
      
      public static function getPetEquipById(id:Number) : PetEquipBasicData
      {
         var pData:PetEquipBasicData = null;
         var data:PetEquipBasicData = null;
         for each(data in allData)
         {
            if(data.getId() == id)
            {
               pData = data;
            }
         }
         if(pData == null)
         {
            trace("找不到");
         }
         return pData;
      }
      
      public static function findFrame(id:Number) : Number
      {
         return getPetEquipById(id).getFrame();
      }
      
      public static function findName(id:Number) : String
      {
         return getPetEquipById(id).getName();
      }
      
      public static function findType(id:Number) : Number
      {
         return getPetEquipById(id).getType();
      }
      
      public static function findPrice(id:Number) : Number
      {
         return getPetEquipById(id).getPrice();
      }
      
      public static function findColor(id:Number) : Number
      {
         return getPetEquipById(id).getColor();
      }
      
      public static function findSkillDescript(id:Number) : String
      {
         return getPetEquipById(id).getSkillDescript();
      }
      
      public static function findDescript(id:Number) : String
      {
         return getPetEquipById(id).getDescript();
      }
      
      public static function findXingge(id:Number) : Array
      {
         return getPetEquipById(id).getXingge();
      }
      
      public static function findAffect(id:Number) : Array
      {
         return getPetEquipById(id).getAffect();
      }
      
      public static function creatPetEquip(id:Number) : PetEquip
      {
         return getPetEquipById(id).createPetEquip();
      }
      
      private function creatpetEquipData() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var frame:Number = NaN;
         var name:String = null;
         var type:Number = NaN;
         var price:Number = NaN;
         var color:Number = NaN;
         var skilldescript:String = null;
         var descript:String = null;
         var xinggeList:XMLList = null;
         var xingge:Array = null;
         var affectList:XMLList = null;
         var affect:Array = null;
         var xg:XML = null;
         var aff:XML = null;
         var data:PetEquipBasicData = null;
         for each(property in myXml.宠物装备)
         {
            id = Number(property.编号);
            frame = Number(property.帧数);
            name = String(property.名字);
            type = Number(property.类型);
            price = Number(property.价格);
            color = Number(property.品质);
            skilldescript = String(property.技能描述);
            descript = String(property.描述);
            xinggeList = property.性格数组;
            xingge = [];
            affectList = property.数值数组;
            affect = [];
            for each(xg in xinggeList)
            {
               if(xg.热血 != "null")
               {
                  xingge.push(1);
               }
               if(xg.坚韧 != "null")
               {
                  xingge.push(2);
               }
               if(xg.领袖 != "null")
               {
                  xingge.push(3);
               }
               if(xg.狂傲 != "null")
               {
                  xingge.push(4);
               }
               if(xg.倔强 != "null")
               {
                  xingge.push(5);
               }
               if(xg.敏锐 != "null")
               {
                  xingge.push(6);
               }
               if(xg.激进 != "null")
               {
                  xingge.push(7);
               }
               if(xg.聪慧 != "null")
               {
                  xingge.push(8);
               }
               if(xg.暴躁 != "null")
               {
                  xingge.push(9);
               }
               if(xg.稳重 != "null")
               {
                  xingge.push(10);
               }
               if(xg.邪恶 != "null")
               {
                  xingge.push(11);
               }
               if(xg.睿智 != "null")
               {
                  xingge.push(12);
               }
            }
            for each(aff in affectList)
            {
               if(aff.预留1 != "null")
               {
                  affect.push(Number(aff.预留1));
               }
               if(aff.预留2 != "null")
               {
                  affect.push(Number(aff.预留2));
               }
               if(aff.预留3 != "null")
               {
                  affect.push(Number(aff.预留3));
               }
               if(aff.预留4 != "null")
               {
                  affect.push(Number(aff.预留4));
               }
               if(aff.预留5 != "null")
               {
                  affect.push(Number(aff.预留5));
               }
            }
            data = PetEquipBasicData.createPetEquipBasicData(id,frame,name,type,price,color,skilldescript,descript,xingge,affect);
            allData.push(data);
         }
      }
   }
}

