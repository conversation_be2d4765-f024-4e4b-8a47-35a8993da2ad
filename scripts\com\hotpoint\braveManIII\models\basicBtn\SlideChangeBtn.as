package com.hotpoint.braveManIII.models.basicBtn
{
   import com.hotpoint.braveManIII.events.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.text.TextField;
   
   public class SlideChangeBtn extends BasicBtn
   {
      
      public var n_text:TextField;
      
      public var name_text:TextField;
      
      public var num_text:TextField;
      
      public var ox:TextField;
      
      public var sbtn:MovieClip;
      
      public var tx:TextField;
      
      public var type:MovieClip;
      
      private var _isClick:Boolean = false;
      
      public function SlideChangeBtn()
      {
         super();
      }
      
      override protected function mouseUp(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.MOUSE_UP)
         {
            this.gotoAndStop(3);
         }
      }
      
      override protected function mouseDown(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.MOUSE_DOWN)
         {
            this.gotoAndStop(3);
         }
      }
      
      override protected function rollOut(event:MouseEvent) : void
      {
         if(event.type == MouseEvent.ROLL_OUT && this._isClick == false)
         {
            this.gotoAndStop(1);
         }
         else
         {
            this.gotoAndStop(3);
         }
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_OUT,this.id));
      }
      
      override protected function clickFn(event:MouseEvent) : void
      {
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_CHANGE,this.id));
         this._isClick = true;
      }
      
      public function set isClick(isClick:Boolean) : void
      {
         this._isClick = isClick;
         if(!this._isClick)
         {
            if(this.currentFrame != 4)
            {
               return this.gotoAndStop(1);
            }
            return this.gotoAndStop(4);
         }
         if(this._isClick)
         {
            return this.gotoAndStop(3);
         }
      }
   }
}

