package com.hotpoint.braveManIII.repository.equipShop
{
   import com.hotpoint.braveManIII.Tool.*;
   import src.*;
   
   public class equipShopFactory
   {
      
      public static var allData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public function equipShopFactory()
      {
         super();
      }
      
      public static function creatEquipShopFactory() : *
      {
         var es:equipShopFactory = new equipShopFactory();
         myXml = XMLAsset.createXML(Data2.eShop);
         es.creatFactory();
      }
      
      private function creatFactory() : *
      {
         this.xmlLoaded();
      }
      
      private function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var frame:Number = NaN;
         var price:Number = NaN;
         var rmbID:Number = NaN;
         var rmbPrice:Number = NaN;
         var data:Array = null;
         for each(property in myXml.装备商店)
         {
            id = Number(property.编号);
            frame = Number(property.帧数);
            price = Number(property.价格);
            rmbID = Number(property.点卷编号);
            rmbPrice = Number(property.点卷价格);
            data = [id,frame,price,rmbID,rmbPrice];
            allData.push(data);
         }
      }
   }
}

