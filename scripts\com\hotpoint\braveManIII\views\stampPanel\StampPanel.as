package com.hotpoint.braveManIII.views.stampPanel
{
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class StampPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var qnPanel:MovieClip;
      
      public static var sp:StampPanel;
      
      public static var myplayer:PlayerData;
      
      public static var clickNum:Number;
      
      private static var loadData:ClassLoader;
      
      public static var TYOK:Boolean = false;
      
      public static var yeNum:Number = 1;
      
      public static var slotNum:Number = 1;
      
      private static var mygem:Boolean = false;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "panel_qn_v1840.swf";
      
      public static var yeNumXX2:Number = 0;
      
      public function StampPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      public static function upGo(e:*) : *
      {
         yeNumXX2 = 0;
         if(slotNum == 11)
         {
            showAllGem();
         }
         else
         {
            showGem(slotNum + 6);
         }
      }
      
      public static function downGo(e:*) : *
      {
         yeNumXX2 = 1;
         if(slotNum == 11)
         {
            showAllGem();
         }
         else
         {
            showGem(slotNum + 6);
         }
      }
      
      private static function LoadSkin() : *
      {
         if(!qnPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = qnPanel.getChildIndex(qnPanel["e" + i]);
            mm.x = qnPanel["e" + i].x;
            mm.y = qnPanel["e" + i].y;
            mm.name = "e" + i;
            qnPanel.removeChild(qnPanel["e" + i]);
            qnPanel["e" + i] = mm;
            qnPanel.addChild(mm);
            qnPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 4; i++)
         {
            mm = new Shop_picNEW();
            num = qnPanel.getChildIndex(qnPanel["s1_" + i]);
            mm.x = qnPanel["s1_" + i].x;
            mm.y = qnPanel["s1_" + i].y;
            mm.name = "s1_" + i;
            qnPanel.removeChild(qnPanel["s1_" + i]);
            qnPanel["s1_" + i] = mm;
            qnPanel.addChild(mm);
            qnPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 4; i++)
         {
            mm = new Shop_picNEW();
            num = qnPanel.getChildIndex(qnPanel["s2_" + i]);
            mm.x = qnPanel["s2_" + i].x;
            mm.y = qnPanel["s2_" + i].y;
            mm.name = "s2_" + i;
            qnPanel.removeChild(qnPanel["s2_" + i]);
            qnPanel["s2_" + i] = mm;
            qnPanel.addChild(mm);
            qnPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 4; i++)
         {
            mm = new Shop_picNEW();
            num = qnPanel.getChildIndex(qnPanel["s3_" + i]);
            mm.x = qnPanel["s3_" + i].x;
            mm.y = qnPanel["s3_" + i].y;
            mm.name = "s3_" + i;
            qnPanel.removeChild(qnPanel["s3_" + i]);
            qnPanel["s3_" + i] = mm;
            qnPanel.addChild(mm);
            qnPanel.setChildIndex(mm,num);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("QNShow") as Class;
         qnPanel = new classRef();
         sp.addChild(qnPanel);
         InitIcon();
         if(OpenYN)
         {
            open(myplayer);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         sp = new StampPanel();
         LoadSkin();
         Main._stage.addChild(sp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         sp = new StampPanel();
         Main._stage.addChild(sp);
         OpenYN = false;
      }
      
      public static function open(pp:PlayerData) : void
      {
         Main.allClosePanel();
         Main.DuoKai_Fun();
         if(qnPanel)
         {
            Main.stopXX = true;
            sp.x = 0;
            sp.y = 0;
            myplayer = pp;
            yeNum = 1;
            addListenerP1();
            Main._stage.addChild(sp);
            sp.visible = true;
         }
         else
         {
            myplayer = pp;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(qnPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            sp.visible = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         qnPanel["djPoint"].visible = true;
         qnPanel["qnRMB"].visible = false;
         qnPanel["touming"].visible = false;
         qnPanel["qnRMB"]["yes_btn"].addEventListener(MouseEvent.CLICK,yes_btn);
         qnPanel["qnRMB"]["no_btn"].addEventListener(MouseEvent.CLICK,no_btn);
         qnPanel["qnRMB"]["no_btn2"].addEventListener(MouseEvent.CLICK,no_btn);
         qnPanel["NoMoney_mc"].visible = false;
         qnPanel["NoMoney_mc"]["yes_btn"].addEventListener(MouseEvent.CLICK,closeNORMB);
         qnPanel["NoMoney_mc"]["addMoney_btn"].addEventListener(MouseEvent.CLICK,addMoney_btn);
         for(var i:uint = 0; i < 4; i++)
         {
            qnPanel["yz" + i].buttonMode = true;
            qnPanel["zy" + i].buttonMode = true;
            qnPanel["yz" + i].addEventListener(MouseEvent.MOUSE_OVER,upShow);
            qnPanel["zy" + i].addEventListener(MouseEvent.MOUSE_OVER,downShow);
            qnPanel["yz" + i].addEventListener(MouseEvent.MOUSE_OUT,closeShow);
            qnPanel["zy" + i].addEventListener(MouseEvent.MOUSE_OUT,closeShow);
            qnPanel["yz" + i].addEventListener(MouseEvent.CLICK,upDo);
            qnPanel["zy" + i].addEventListener(MouseEvent.CLICK,downDo);
            qnPanel["zd" + i].addEventListener(MouseEvent.CLICK,rmbDo);
            qnPanel["ty" + i].addEventListener(MouseEvent.CLICK,tongyongDo);
            qnPanel["s1_" + i].mouseChildren = qnPanel["s1_" + i].mouseEnabled = false;
            qnPanel["s2_" + i].mouseChildren = qnPanel["s2_" + i].mouseEnabled = false;
            qnPanel["s3_" + i].mouseChildren = qnPanel["s3_" + i].mouseEnabled = false;
         }
         qnPanel["shang_btn"].addEventListener(MouseEvent.CLICK,shangDo);
         qnPanel["xia_btn"].addEventListener(MouseEvent.CLICK,xiaDo);
         qnPanel["closePanel"].addEventListener(MouseEvent.CLICK,closePanel);
         for(var j:uint = 0; j < 24; j++)
         {
            qnPanel["e" + j].mouseChildren = false;
            qnPanel["e" + j].stop();
            qnPanel["e" + j].addEventListener(MouseEvent.CLICK,choseDo);
            qnPanel["e" + j].addEventListener(MouseEvent.MOUSE_MOVE,tipOpen);
            qnPanel["e" + j].addEventListener(MouseEvent.MOUSE_OUT,tipClose);
         }
         qnPanel["strengTip"].visible = false;
         showRMB();
         showPanel();
         showGem();
         qnPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         qnPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
      }
      
      private static function tipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         qnPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         if(myplayer.getBag().getGemFromBag(overNum + yeNumXX2 * 24) != null)
         {
            itemsTooltip.gemTooltip(myplayer.getBag().getGemFromBag(overNum + yeNumXX2 * 24),1);
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = qnPanel.mouseX + 10;
         itemsTooltip.y = qnPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function tipClose(e:MouseEvent) : void
      {
         itemsTooltip.visible = false;
      }
      
      public static function closePanel(e:*) : *
      {
         close();
      }
      
      public static function tongyongDo(e:*) : *
      {
         clickNum = e.target.name.substr(2,1);
         if(myplayer.getStampSlot().getSlot11(clickNum))
         {
            mygem = true;
            if(myplayer.getBag().backGemBagNum() > 0)
            {
               myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot11(clickNum));
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
            }
         }
         else
         {
            mygem = false;
         }
         showRMB();
         showPanel();
         qnPanel["k3_" + clickNum].visible = true;
         slotNum = 11;
         showAllGem();
      }
      
      public static function yes_btn(e:*) : *
      {
         if(Shop4399.moneyAll.getValue() >= 20)
         {
            Api_4399_All.BuyObj(InitData.tongyongjiesuo.getValue());
            TYOK = true;
            qnPanel["qnRMB"].visible = false;
            qnPanel["touming"].visible = true;
         }
         else
         {
            qnPanel["NoMoney_mc"].visible = true;
         }
      }
      
      public static function no_btn(e:*) : *
      {
         qnPanel["qnRMB"].visible = false;
      }
      
      private static function closeNORMB(e:*) : void
      {
         qnPanel["NoMoney_mc"].visible = false;
      }
      
      private static function addMoney_btn(e:*) : void
      {
         Main.ChongZhi();
      }
      
      public static function rmbDo(e:*) : *
      {
         qnPanel["qnRMB"].visible = true;
      }
      
      public static function tyKeyOK() : *
      {
         if(TYOK)
         {
            myplayer.getStampSlot().setKeySlot11();
            showRMB();
            TYOK = false;
            qnPanel["touming"].visible = false;
         }
      }
      
      public static function showRMB() : *
      {
         var i:uint = 0;
         for(i = 0; i < 4; i++)
         {
            qnPanel["s3_" + i].visible = false;
            qnPanel["k3_" + i].visible = false;
         }
         for(i = 0; i < 4; i++)
         {
            if(myplayer.getStampSlot().getSlot11_key(i) == 1)
            {
               qnPanel["zd" + i].visible = false;
               if(myplayer.getStampSlot().getSlot11(i))
               {
                  qnPanel["s3_" + i].gotoAndStop(myplayer.getStampSlot().getSlot11(i).getFrame());
                  qnPanel["s3_" + i].visible = true;
               }
            }
            else
            {
               qnPanel["zd" + i].visible = true;
            }
         }
         qnPanel["djPoint"].text = Shop4399.moneyAll.getValue();
      }
      
      public static function showAllGem() : *
      {
         var i:uint = 0;
         var typeXX:int = 0;
         var yeshuNum:int = yeNumXX2 + 1;
         qnPanel["yeshu_txt"].text = yeshuNum + "/2";
         if(myplayer.getBag().backGemBagNum() > 0 || mygem == false)
         {
            qnPanel["zhedang"].gotoAndStop(2);
            qnPanel["zhedang"].visible = true;
            if(myplayer.getBag().getQN_YN())
            {
               qnPanel["zhedang"].visible = false;
            }
            for(i = 0; i < 24; i++)
            {
               qnPanel["e" + i].visible = false;
               if(myplayer.getBag().getGemFromBag(i + yeNumXX2 * 24))
               {
                  typeXX = myplayer.getBag().getGemFromBag(i + yeNumXX2 * 24).getType();
                  if(typeXX >= 7 && typeXX <= 20)
                  {
                     qnPanel["e" + i].visible = true;
                     qnPanel["e" + i].gotoAndStop(myplayer.getBag().getGemFromBag(i + yeNumXX2 * 24).getFrame());
                  }
               }
            }
         }
         else
         {
            qnPanel["zhedang"].gotoAndStop(3);
            qnPanel["zhedang"].visible = true;
         }
      }
      
      public static function showGem(type:int = -1) : *
      {
         var i:uint = 0;
         var yeshuNum:int = yeNumXX2 + 1;
         qnPanel["yeshu_txt"].text = yeshuNum + "/2";
         if(type == -1)
         {
            qnPanel["zhedang"].gotoAndStop(1);
         }
         if(type > -1)
         {
            qnPanel["zhedang"].gotoAndStop(2);
         }
         if(type > 16)
         {
            type--;
         }
         if(myplayer.getBag().backGemBagNum() > 0 || mygem == false)
         {
            qnPanel["zhedang"].visible = true;
            if(myplayer.getBag().getQN_YN(type))
            {
               qnPanel["zhedang"].visible = false;
            }
            for(i = 0; i < 24; i++)
            {
               qnPanel["e" + i].visible = false;
               if(Boolean(myplayer.getBag().getGemFromBag(i + yeNumXX2 * 24)) && myplayer.getBag().getGemFromBag(i + yeNumXX2 * 24).getType() == type)
               {
                  qnPanel["e" + i].visible = true;
                  qnPanel["e" + i].gotoAndStop(myplayer.getBag().getGemFromBag(i + yeNumXX2 * 24).getFrame());
               }
            }
         }
         else
         {
            qnPanel["zhedang"].gotoAndStop(3);
            qnPanel["zhedang"].visible = true;
         }
      }
      
      public static function showPanel() : *
      {
         var i:uint = 0;
         var arrX14:Array = null;
         var arrX15:Array = null;
         qnPanel["yeshu"].text = yeNum + "/7";
         qnPanel["color_mc"].gotoAndStop(yeNum);
         for(i = 0; i < 4; i++)
         {
            qnPanel["yz" + i].gotoAndStop(yeNum * 2 - 1);
            qnPanel["zy" + i].gotoAndStop(yeNum * 2);
            qnPanel["k1_" + i].visible = false;
            qnPanel["k2_" + i].visible = false;
            qnPanel["s1_" + i].visible = false;
            qnPanel["s2_" + i].visible = false;
         }
         if(yeNum == 1)
         {
            qnPanel["txt_1"].text = "魔力印章(使用魔法药效果提高+" + Math.round(myplayer.getStampSlot().getValueSlot1() * 100) + "%)";
            qnPanel["txt_2"].text = "体力印章(使用体力药效果提高+" + Math.round(myplayer.getStampSlot().getValueSlot2() * 100) + "%)";
         }
         else if(yeNum == 2)
         {
            qnPanel["txt_1"].text = "攻击印章(攻击力提高+" + Number(myplayer.getStampSlot().getValueSlot3() * 100).toFixed(1) + "%)";
            qnPanel["txt_2"].text = "防御印章(防御力提高+" + Number(myplayer.getStampSlot().getValueSlot4() * 100).toFixed(1) + "%)";
         }
         else if(yeNum == 3)
         {
            qnPanel["txt_1"].text = "生命印章(提高固定生命值+" + Math.round(myplayer.getStampSlot().getValueSlot5()) + ")";
            qnPanel["txt_2"].text = "力量印章(提高固定攻击力+" + Math.round(myplayer.getStampSlot().getValueSlot6()) + ")";
         }
         else if(yeNum == 4)
         {
            qnPanel["txt_1"].text = "速度印章(提高固定移动力+" + Math.round(myplayer.getStampSlot().getValueSlot7()) + ")";
            qnPanel["txt_2"].text = "石肤印章(角色所受伤害减少+" + Number(myplayer.getStampSlot().getValueSlot8() * 100).toFixed(1) + "%)";
         }
         else if(yeNum == 5)
         {
            qnPanel["txt_1"].text = "灵介印章(消耗魔法减少+" + Number(myplayer.getStampSlot().getValueSlot9() * 100).toFixed(1) + "%)";
            qnPanel["txt_2"].text = "恢复印章(释放技能时在3s内回复已损失生命值" + Number(myplayer.getStampSlot().getValueSlot10() * 100).toFixed(1) + "%)";
         }
         else if(yeNum == 6)
         {
            qnPanel["txt_1"].text = "破魔印章(破魔+" + Number(myplayer.getStampSlot().getValueSlot12() * 100).toFixed(1) + "%)";
            qnPanel["txt_2"].text = "魔抗印章(魔抗+" + Number(myplayer.getStampSlot().getValueSlot13() * 100).toFixed(1) + "%)";
         }
         else if(yeNum == 7)
         {
            arrX14 = myplayer.getStampSlot().getValueSlot14();
            arrX15 = myplayer.getStampSlot().getValueSlot15();
            qnPanel["txt_1"].text = "神王印章(技能额外消耗" + (arrX14[0] * 100).toFixed(2) + "%,技能伤害+" + (arrX14[1] * 100).toFixed(1) + "%)";
            qnPanel["txt_2"].text = "黑暗印章(受到的伤害+" + (arrX15[0] * 100).toFixed(1) + "%,对领主伤害+" + (arrX15[1] * 100).toFixed(1) + "%)";
         }
         for(i = 0; i < 4; i++)
         {
            if(yeNum == 1 && Boolean(myplayer.getStampSlot().getSlot1(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot1(i).getFrame());
            }
            if(yeNum == 1 && Boolean(myplayer.getStampSlot().getSlot2(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot2(i).getFrame());
            }
            if(yeNum == 2 && Boolean(myplayer.getStampSlot().getSlot3(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot3(i).getFrame());
            }
            if(yeNum == 2 && Boolean(myplayer.getStampSlot().getSlot4(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot4(i).getFrame());
            }
            if(yeNum == 3 && Boolean(myplayer.getStampSlot().getSlot5(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot5(i).getFrame());
            }
            if(yeNum == 3 && Boolean(myplayer.getStampSlot().getSlot6(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot6(i).getFrame());
            }
            if(yeNum == 4 && Boolean(myplayer.getStampSlot().getSlot7(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot7(i).getFrame());
            }
            if(yeNum == 4 && Boolean(myplayer.getStampSlot().getSlot8(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot8(i).getFrame());
            }
            if(yeNum == 5 && Boolean(myplayer.getStampSlot().getSlot9(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot9(i).getFrame());
            }
            if(yeNum == 5 && Boolean(myplayer.getStampSlot().getSlot10(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot10(i).getFrame());
            }
            if(yeNum == 6 && Boolean(myplayer.getStampSlot().getSlot12(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot12(i).getFrame());
            }
            if(yeNum == 6 && Boolean(myplayer.getStampSlot().getSlot13(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot13(i).getFrame());
            }
            if(yeNum == 7 && Boolean(myplayer.getStampSlot().getSlot14(i)))
            {
               qnPanel["s1_" + i].visible = true;
               qnPanel["s1_" + i].gotoAndStop(myplayer.getStampSlot().getSlot14(i).getFrame());
            }
            if(yeNum == 7 && Boolean(myplayer.getStampSlot().getSlot15(i)))
            {
               qnPanel["s2_" + i].visible = true;
               qnPanel["s2_" + i].gotoAndStop(myplayer.getStampSlot().getSlot15(i).getFrame());
            }
         }
      }
      
      public static function removeListenerP1() : *
      {
         qnPanel["qnRMB"]["yes_btn"].removeEventListener(MouseEvent.CLICK,yes_btn);
         qnPanel["qnRMB"]["no_btn"].removeEventListener(MouseEvent.CLICK,no_btn);
         qnPanel["NoMoney_mc"]["yes_btn"].removeEventListener(MouseEvent.CLICK,closeNORMB);
         qnPanel["NoMoney_mc"]["addMoney_btn"].removeEventListener(MouseEvent.CLICK,addMoney_btn);
         for(var i:uint = 0; i < 4; i++)
         {
            qnPanel["yz" + i].removeEventListener(MouseEvent.CLICK,upDo);
            qnPanel["zy" + i].removeEventListener(MouseEvent.CLICK,downDo);
            qnPanel["zd" + i].removeEventListener(MouseEvent.CLICK,rmbDo);
            qnPanel["ty" + i].removeEventListener(MouseEvent.CLICK,tongyongDo);
         }
         qnPanel["shang_btn"].addEventListener(MouseEvent.CLICK,shangDo);
         qnPanel["xia_btn"].addEventListener(MouseEvent.CLICK,xiaDo);
         qnPanel["closePanel"].addEventListener(MouseEvent.CLICK,closePanel);
         for(var j:uint = 0; j < 24; j++)
         {
            qnPanel["e" + j].removeEventListener(MouseEvent.CLICK,choseDo);
            qnPanel["e" + j].removeEventListener(MouseEvent.MOUSE_MOVE,tipOpen);
            qnPanel["e" + j].removeEventListener(MouseEvent.MOUSE_OUT,tipClose);
         }
      }
      
      public static function choseDo(e:*) : *
      {
         var choseNum:int = uint(e.target.name.substr(1,2)) + yeNumXX2 * 24;
         trace("放入潜能印章 >>",choseNum,slotNum,yeNumXX2);
         if(slotNum == 1)
         {
            myplayer.getStampSlot().setSlot1(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 2)
         {
            myplayer.getStampSlot().setSlot2(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 3)
         {
            myplayer.getStampSlot().setSlot3(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 4)
         {
            myplayer.getStampSlot().setSlot4(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 5)
         {
            myplayer.getStampSlot().setSlot5(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 6)
         {
            myplayer.getStampSlot().setSlot6(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 7)
         {
            myplayer.getStampSlot().setSlot7(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 8)
         {
            myplayer.getStampSlot().setSlot8(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 9)
         {
            myplayer.getStampSlot().setSlot9(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 10)
         {
            myplayer.getStampSlot().setSlot10(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 11)
         {
            myplayer.getStampSlot().setSlot11(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 12)
         {
            myplayer.getStampSlot().setSlot12(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 13)
         {
            myplayer.getStampSlot().setSlot13(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 14)
         {
            myplayer.getStampSlot().setSlot14(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         else if(slotNum == 15)
         {
            myplayer.getStampSlot().setSlot15(myplayer.getBag().delGem(choseNum,1),clickNum);
         }
         showGem();
         showPanel();
         showRMB();
      }
      
      public static function xiaDo(e:*) : *
      {
         if(yeNum < 7)
         {
            ++yeNum;
         }
         showGem();
         showPanel();
      }
      
      public static function shangDo(e:*) : *
      {
         if(yeNum > 1)
         {
            --yeNum;
         }
         showGem();
         showPanel();
      }
      
      public static function upShow(e:*) : *
      {
      }
      
      public static function downShow(e:*) : *
      {
      }
      
      public static function closeShow(e:*) : *
      {
      }
      
      public static function upDo(e:*) : *
      {
         clickNum = e.target.name.substr(2,1);
         if(yeNum == 1)
         {
            if(myplayer.getStampSlot().getSlot1(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot1(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 2)
         {
            if(myplayer.getStampSlot().getSlot3(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot3(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 3)
         {
            if(myplayer.getStampSlot().getSlot5(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot5(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 4)
         {
            if(myplayer.getStampSlot().getSlot7(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot7(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 5)
         {
            if(myplayer.getStampSlot().getSlot9(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot9(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 6)
         {
            if(myplayer.getStampSlot().getSlot12(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot12(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 7)
         {
            if(myplayer.getStampSlot().getSlot14(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot14(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         showPanel();
         showRMB();
         qnPanel["k1_" + clickNum].visible = true;
         slotNum = yeNum * 2 - 1;
         if(slotNum > 10)
         {
            slotNum += 1;
         }
         showGem(slotNum + 6);
      }
      
      public static function downDo(e:*) : *
      {
         clickNum = e.target.name.substr(2,1);
         if(yeNum == 1)
         {
            if(myplayer.getStampSlot().getSlot2(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot2(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 2)
         {
            if(myplayer.getStampSlot().getSlot4(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot4(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 3)
         {
            if(myplayer.getStampSlot().getSlot6(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot6(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 4)
         {
            if(myplayer.getStampSlot().getSlot8(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot8(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 5)
         {
            if(myplayer.getStampSlot().getSlot10(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot10(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 6)
         {
            if(myplayer.getStampSlot().getSlot13(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot13(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         else if(yeNum == 7)
         {
            if(myplayer.getStampSlot().getSlot15(clickNum))
            {
               mygem = true;
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(myplayer.getStampSlot().delSlot15(clickNum));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宝石栏空间不足，无法卸下");
               }
            }
            else
            {
               mygem = false;
            }
         }
         showPanel();
         showRMB();
         qnPanel["k2_" + clickNum].visible = true;
         slotNum = yeNum * 2;
         if(slotNum > 10)
         {
            slotNum += 1;
         }
         showGem(slotNum + 6);
      }
   }
}

