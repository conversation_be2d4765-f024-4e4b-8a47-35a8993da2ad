package src.npc
{
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.strengthenPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   
   public class 塔丽莎界面 extends MovieClip
   {
      
      private static var only:塔丽莎界面;
      
      public var close2:SimpleButton;
      
      public var buy_btn:SimpleButton;
      
      public function 塔丽莎界面()
      {
         super();
         this.close2.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.buy_btn.addEventListener(MouseEvent.CLICK,this.BUY);
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         MusicBox.MusicPlay2("m13");
         if(!only)
         {
            classRef = All_Npc.loadData.getClass("src.npc.塔丽莎界面") as Class;
            xxMov = new classRef();
            only = xxMov;
         }
         Main._stage.addChild(only);
         only.x = xx;
         only.y = yy;
         only.visible = true;
      }
      
      public static function Close() : *
      {
         if(only)
         {
            only.x = 5000;
            only.y = 5000;
            only.visible = false;
         }
      }
      
      private function CloseXX(e:*) : *
      {
         Close();
      }
      
      private function BUY(e:*) : *
      {
         SuppliesShopPanel.open();
         塔丽莎界面.Close();
      }
   }
}

