package src
{
   import com.hotpoint.braveManIII.models.player.*;
   import com.hotpoint.braveManIII.models.task.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.setProfession.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import src._data.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol7425")]
   public class Strat extends MovieClip
   {
      
      public static var stratX:Strat;
      
      public static var isGetData:Boolean = false;
      
      public static var SAVE_LIST:Array = ["空存档","空存档","空存档","空存档","空存档","空存档","空存档","空存档"];
      
      public static var 说明1:String = "123";
      
      public static var 说明2:String = "456";
      
      public static var 说明3:String = "789";
      
      public var KC_mc:MovieClip;
      
      public var _btn:MovieClip;
      
      public var _btn2:MovieClip;
      
      public var btn_mc:MovieClip;
      
      public var save_mc:MovieClip;
      
      public var 年龄提示按钮:SimpleButton;
      
      public var 年龄提示框:MovieClip;
      
      public var 操作说明:MovieClip;
      
      public var 游戏更新:MovieClip;
      
      public var 初始化mc:MovieClip;
      
      private var 初始化YN:Boolean = false;
      
      private var 初始化等待:int = 1;
      
      private var kqTime:int = 0;
      
      private var saveN:int = 1;
      
      public function Strat()
      {
         super();
         this._btn2.p1_btn.addEventListener(MouseEvent.CLICK,this.NewGame1);
         this._btn2.p2_btn.addEventListener(MouseEvent.CLICK,this.NewGame2);
         this._btn2.back_btn.addEventListener(MouseEvent.CLICK,this.NewGameBtnClose);
         this._btn.load_btn.addEventListener(MouseEvent.CLICK,this.loadXX);
         this._btn.newGame_btn.addEventListener(MouseEvent.CLICK,this.NewGameBtnOpen);
         this._btn.操作说明_btn.addEventListener(MouseEvent.CLICK,this.操作说明Open);
         this._btn.游戏更新_btn.addEventListener(MouseEvent.CLICK,this.游戏更新Open);
         this._btn.论坛_btn.addEventListener(MouseEvent.CLICK,this.论坛);
         this.操作说明.back_btn.addEventListener(MouseEvent.CLICK,this.操作说明Open);
         this.游戏更新.back_btn.addEventListener(MouseEvent.CLICK,this.游戏更新Open);
         this.save_mc.close_btn1.addEventListener(MouseEvent.CLICK,this.CloseLoad);
         this.save_mc.close_btn2.addEventListener(MouseEvent.CLICK,this.CloseLoad);
         this.游戏更新.http_btn.addEventListener(MouseEvent.CLICK,this.http_Fun);
         this.年龄提示按钮.addEventListener(MouseEvent.CLICK,this.年龄提示Open);
         this.年龄提示框.back_btn.addEventListener(MouseEvent.CLICK,this.年龄提示Open);
         this.操作说明.visible = this._btn.visible = this._btn2.visible = this.KC_mc.visible = this.save_mc.visible = false;
         this.游戏更新.visible = true;
         this._btn.visible = true;
         this.年龄提示框.visible = false;
         this.btn_mc.gotoAndStop("弹出stop");
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         if(!stratX)
         {
            stratX = new Strat();
         }
         Main._this.addChild(stratX);
         stratX.x = xx;
         stratX.y = yy;
         stratX.visible = true;
      }
      
      public static function Close() : *
      {
         if(!stratX)
         {
            stratX = new Strat();
         }
         Main._this.addChild(stratX);
         stratX._btn.visible = false;
         stratX.x = stratX.y = 5000;
         stratX.visible = false;
      }
      
      public static function startGame(e:* = null) : void
      {
         Api_4399_All.Money_sel();
         stratX.KC_mc.visible = false;
         Main._this.Loading();
         Strat.Close();
      }
      
      public static function VarXX() : *
      {
         var str:String = null;
         if(stratX)
         {
            str = Main.varX;
            str = str.substr(str.length - 2,2);
            stratX.游戏更新._txt.text = "正式版v" + int(Main.varX / 100) + "." + str;
            stratX.游戏更新.说明1_txt.text = Strat.说明1;
            stratX.游戏更新.说明2_txt.text = Strat.说明2;
            stratX.游戏更新.说明3_txt.text = Strat.说明3;
         }
      }
      
      private function on_MC_ENTER_FRAME(e:*) : *
      {
         if(this.btn_mc.currentLabel == "收进stop")
         {
            this._btn.visible = false;
            this._btn2.visible = true;
         }
         else if(this.btn_mc.currentLabel == "弹出stop")
         {
            this._btn.visible = true;
            this._btn2.visible = false;
         }
         else
         {
            this._btn.visible = this._btn2.visible = false;
         }
      }
      
      private function http_Fun(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/forums/thread-33880763");
         navigateToURL(request,"_blank");
      }
      
      private function 论坛(e:*) : *
      {
         var request:URLRequest = new URLRequest("http://my.4399.com/space-mtag-tagid-81127.html");
         navigateToURL(request,"_blank");
      }
      
      private function 初始化() : Boolean
      {
         if(Boolean(this.初始化YN) && Boolean(Main.tempDataEnd))
         {
            return true;
         }
         this.初始化mc = new 游戏初始化();
         addChild(this.初始化mc);
         this.onLog();
         this._btn2.visible = false;
         return false;
      }
      
      private function onLog(e:* = null) : *
      {
         Main._this.showLogUi();
         this.初始化YN = true;
         Api_4399_All.Init(Main._stage);
         Api_4399_GongHui.Init(Main._stage);
         this.调用等待弹出();
      }
      
      private function loadXX(e:* = null) : *
      {
         this._btn.visible = false;
         this.初始化等待 = 2;
         if(this.初始化())
         {
            this.LoadSave();
         }
      }
      
      private function NewGameBtnOpen(e:* = null) : *
      {
         this.btn_mc.gotoAndPlay("收进");
         this._btn.visible = false;
         this.初始化等待 = 1;
         if(this.初始化())
         {
            this._btn.visible = false;
            this._btn2.visible = true;
         }
      }
      
      public function NewGame1(e:* = null) : *
      {
         Main.saveNum = -1;
         Main.P1P2 = false;
         this.NewSave();
      }
      
      public function NewGame2(e:* = null) : *
      {
         Main.saveNum = -1;
         Main.P1P2 = true;
         this.NewSave();
      }
      
      public function NewSave() : *
      {
         var i:int = 0;
         Main.newPlay = 1;
         this.save_mc.visible = true;
         this.save_mc.save_load_mc.gotoAndStop(1);
         this.save_mc["Load_Info_mc"].visible = false;
         StoragePanel.storage = null;
         Main.player1 = null;
         Main.player2 = null;
         Main.questArr = [[false,84110],[false,84111],[false,84112],[false,84113],[false,84114]];
         for(i = 0; i < 8; i++)
         {
            this.save_mc["save" + i].removeEventListener(MouseEvent.CLICK,this.LoadNum);
            this.save_mc["save" + i].addEventListener(MouseEvent.CLICK,this.saveNum);
            this.save_mc["txt_" + i].text = "" + SAVE_LIST[i];
         }
         for(i = 0; i < 151; i++)
         {
            Main.guanKa[i] = 0;
         }
         Main.guanKa[1] = 1;
         Main.GetServerTime();
         MakeData.clearXXX();
         Main.NoLog = 0;
         TeShuHuoDong.Init();
      }
      
      public function LoadSave() : *
      {
         Main.newPlay = 0;
         this.save_mc.visible = true;
         this.save_mc["Load_Info_mc"].visible = false;
         this.save_mc.save_load_mc.gotoAndStop(2);
         for(var i:int = 0; i < 8; i++)
         {
            this.save_mc["save" + i].removeEventListener(MouseEvent.CLICK,this.saveNum);
            this.save_mc["save" + i].addEventListener(MouseEvent.CLICK,this.LoadNum);
            this.save_mc["txt_" + i].text = "" + SAVE_LIST[i];
         }
         Main.GetServerTime();
         TeShuHuoDong.Init();
      }
      
      public function KCplay() : *
      {
         this.KC_mc.visible = true;
         this.KC_mc._mc.gotoAndPlay(1);
         this.KC_mc.st_btn.addEventListener(MouseEvent.CLICK,this.SelZY);
         addEventListener(Event.ENTER_FRAME,this.ZhiMuGunDong);
      }
      
      private function ZhiMuGunDong(e:*) : *
      {
         ++this.kqTime;
         if(this.kqTime < 800)
         {
            this.KC_mc.zhiMu_mc.y -= 1.3;
         }
         else
         {
            removeEventListener(Event.ENTER_FRAME,this.ZhiMuGunDong);
            Close();
            SetProfession.open();
         }
      }
      
      private function SelZY(e:*) : *
      {
         Close();
         SetProfession.open();
         removeEventListener(Event.ENTER_FRAME,this.ZhiMuGunDong);
      }
      
      private function saveNum(e:*) : *
      {
         this.saveN = (e.target.name as String).substr(4,1);
         this.覆盖确认面板();
      }
      
      private function LoadNum(e:*) : *
      {
         var i:int = 0;
         this.saveN = (e.target.name as String).substr(4,1);
         if(SAVE_LIST[this.saveN] != "空存档")
         {
            for(i = 0; i < 8; i++)
            {
               this.save_mc["save" + i].removeEventListener(MouseEvent.CLICK,this.LoadNum);
            }
            this.save_mc.visible = false;
            Main.serviceHold.getData(false,this.saveN);
         }
      }
      
      private function 覆盖确认面板() : *
      {
         this.save_mc["Load_Info_mc"].Info_mc.gotoAndStop(1);
         this.save_mc["Load_Info_mc"].visible = true;
         this.save_mc["Load_Info_mc"].yes_btn.removeEventListener(MouseEvent.CLICK,this.信息关闭);
         this.save_mc["Load_Info_mc"].no_btn.removeEventListener(MouseEvent.CLICK,this.信息关闭);
         this.save_mc["Load_Info_mc"].no_btn2.removeEventListener(MouseEvent.CLICK,this.信息关闭);
         this.save_mc["Load_Info_mc"].yes_btn.addEventListener(MouseEvent.CLICK,this.覆盖Yse);
         this.save_mc["Load_Info_mc"].no_btn.addEventListener(MouseEvent.CLICK,this.覆盖No);
         this.save_mc["Load_Info_mc"].no_btn2.addEventListener(MouseEvent.CLICK,this.覆盖No);
      }
      
      private function 覆盖Yse(e:*) : *
      {
         Main.saveNum = this.saveN;
         this.KCplay();
      }
      
      private function 覆盖No(e:*) : *
      {
         this.save_mc["Load_Info_mc"].visible = false;
      }
      
      private function CloseLoad(e:*) : *
      {
         this.save_mc.visible = false;
         this._btn2.visible = false;
         this._btn.visible = true;
      }
      
      public function 游戏盒信息() : *
      {
         this.save_mc.visible = true;
         this.save_mc["Load_Info_mc"].visible = true;
         this.save_mc["Load_Info_mc"].Info_mc.gotoAndStop(2);
         this.save_mc["Load_Info_mc"].yes_btn.removeEventListener(MouseEvent.CLICK,this.覆盖Yse);
         this.save_mc["Load_Info_mc"].no_btn.removeEventListener(MouseEvent.CLICK,this.覆盖No);
         this.save_mc["Load_Info_mc"].no_btn2.removeEventListener(MouseEvent.CLICK,this.覆盖No);
         this.save_mc["Load_Info_mc"].yes_btn.addEventListener(MouseEvent.CLICK,this.信息关闭);
         this.save_mc["Load_Info_mc"].no_btn.addEventListener(MouseEvent.CLICK,this.信息关闭);
         this.save_mc["Load_Info_mc"].no_btn2.addEventListener(MouseEvent.CLICK,this.信息关闭);
      }
      
      public function 复制存档信息() : *
      {
         this.save_mc.visible = true;
         this.save_mc["Load_Info_mc"].visible = true;
         this.save_mc["Load_Info_mc"].Info_mc.gotoAndStop(3);
         this.save_mc["Load_Info_mc"].yes_btn.removeEventListener(MouseEvent.CLICK,this.覆盖Yse);
         this.save_mc["Load_Info_mc"].no_btn.removeEventListener(MouseEvent.CLICK,this.覆盖No);
         this.save_mc["Load_Info_mc"].no_btn2.removeEventListener(MouseEvent.CLICK,this.覆盖No);
         this.save_mc["Load_Info_mc"].yes_btn.addEventListener(MouseEvent.CLICK,this.信息关闭2);
         this.save_mc["Load_Info_mc"].no_btn.addEventListener(MouseEvent.CLICK,this.信息关闭2);
         this.save_mc["Load_Info_mc"].no_btn2.addEventListener(MouseEvent.CLICK,this.信息关闭2);
      }
      
      private function 信息关闭(e:*) : *
      {
         this.save_mc["Load_Info_mc"].visible = false;
      }
      
      private function 信息关闭2(e:*) : *
      {
         navigateToURL(new URLRequest("javascript:location.reload(); "),"_self");
      }
      
      private function NewGameBtnClose(e:* = null) : *
      {
         this.btn_mc.gotoAndPlay("弹出");
         this._btn.visible = true;
         this._btn2.visible = false;
      }
      
      private function 年龄提示Open(e:* = null) : *
      {
         if(this.年龄提示框.visible)
         {
            this.年龄提示框.visible = false;
            this.游戏更新.visible = true;
         }
         else
         {
            this.年龄提示框.visible = true;
            this.游戏更新.visible = false;
         }
      }
      
      private function 游戏更新Open(e:* = null) : *
      {
         if(this.游戏更新.visible)
         {
            this.游戏更新.visible = false;
         }
         else
         {
            this.游戏更新.visible = true;
         }
         this.操作说明.visible = false;
      }
      
      private function 操作说明Open(e:* = null) : *
      {
         if(this.操作说明.visible)
         {
            this.操作说明.visible = false;
         }
         else
         {
            this.操作说明.visible = true;
         }
         this.游戏更新.visible = false;
      }
      
      private function 游戏更新Close(e:* = null) : *
      {
         this.游戏更新.visible = false;
      }
      
      private function 操作说明Close(e:* = null) : *
      {
         this.操作说明.visible = false;
      }
      
      private function 调用等待弹出() : *
      {
         addEventListener(Event.ENTER_FRAME,this.等待弹出);
      }
      
      private function 等待弹出(e:*) : *
      {
         if(Main.loadSaveOver)
         {
            if(this.初始化等待 == 1)
            {
               this.NewGameBtnOpen();
            }
            else if(this.初始化等待 == 2)
            {
               this.LoadSave();
            }
            this.初始化mc.parent.removeChild(this.初始化mc);
            removeEventListener(Event.ENTER_FRAME,this.等待弹出);
            removeEventListener(Event.ENTER_FRAME,this.onLog);
         }
      }
   }
}

