package com.hotpoint.braveManIII.models.basicBtn
{
   import com.hotpoint.braveManIII.events.*;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   
   public class BasicClickBtn extends BasicBtn
   {
      
      public var h2:MovieClip;
      
      public var sells:Shop_picOLD;
      
      public function BasicClickBtn()
      {
         super();
      }
      
      override protected function clickFn(event:MouseEvent) : void
      {
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_CLICK,this.id));
      }
   }
}

