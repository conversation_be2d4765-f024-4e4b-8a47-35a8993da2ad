package
{
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4645")]
   public dynamic class XSRWShow extends MovieClip
   {
      
      public var NoMoney_mc:MovieClip;
      
      public var _kill_txt:TextField;
      
      public var _moneyP_txt:TextField;
      
      public var _money_txt:TextField;
      
      public var allTimes:TextField;
      
      public var bjq0:MovieClip;
      
      public var bjq1:MovieClip;
      
      public var bjq2:MovieClip;
      
      public var close:SimpleButton;
      
      public var jl1:Shop_picNEW;
      
      public var jl2:Shop_picNEW;
      
      public var jq0:SimpleButton;
      
      public var jq1:SimpleButton;
      
      public var jq2:SimpleButton;
      
      public var kaiqiXS:TextField;
      
      public var lingqu_btn:SimpleButton;
      
      public var listXS:TextField;
      
      public var lq0:MovieClip;
      
      public var lq1:MovieClip;
      
      public var lq2:MovieClip;
      
      public var msg:MovieClip;
      
      public var nowTimes:TextField;
      
      public var rwFQ:SimpleButton;
      
      public var rwOK:SimpleButton;
      
      public var shang_btn:SimpleButton;
      
      public var tq0:MovieClip;
      
      public var tq1:MovieClip;
      
      public var tq2:MovieClip;
      
      public var xia_btn:SimpleButton;
      
      public var xsRMB:MovieClip;
      
      public var xz0:MovieClip;
      
      public var xz1:MovieClip;
      
      public var xz2:MovieClip;
      
      public var yeshu_txt:TextField;
      
      public var yjq0:MovieClip;
      
      public var yjq1:MovieClip;
      
      public var yjq2:MovieClip;
      
      public function XSRWShow()
      {
         super();
      }
   }
}

