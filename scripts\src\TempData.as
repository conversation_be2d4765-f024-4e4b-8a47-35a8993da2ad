package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   
   public class TempData extends MovieClip
   {
      
      private static var infoArr:Array = ["1.新增【魔神系统】：宠物觉醒，与主人合体变身魔神，当前开放【冥王龙觉醒】","  觉醒道具可在三个炼狱中击败boss有小几率获得","",""," 勇者之刃信仰篇近期版本 更新内容:","  1.新增【狂牛战士】新宠特惠活动","  2.提升了宠物狂牛战士的技能释放频率"];
      
      public function TempData()
      {
         super();
      }
      
      public static function Init() : *
      {
         var str:String = null;
         var xx:int = 0;
         var tempStr:* = null;
         var j:int = 0;
         Main.varX = 1920;
         Strat.说明1 = "勇者之刃【v19.20】版本更新内容:";
         Strat.说明2 = "";
         for(var i:int = 0; i < infoArr.length; i++)
         {
            str = infoArr[i];
            xx = str.length / 45;
            if(xx > 0)
            {
               tempStr = "";
               for(j = 0; j <= xx; j++)
               {
                  if(j > 0)
                  {
                     tempStr += "  ";
                  }
                  tempStr += str.substr(j * 45,45) + "\n";
               }
               Strat.说明2 += tempStr;
            }
            else
            {
               Strat.说明2 += str + "\n";
            }
         }
         Strat.说明3 = "勇者之刃团队 2025.1.24";
         Strat.VarXX();
         Load.otherArr = ["DataX_v1920.swf","PlayerMc_X0_v940.swf","PlayerMc_X1_v930.swf","PlayerMc_X2_v930.swf","ZhuangBei_X0_v6.swf","ZhuangBei_X1_v6.swf","ZhuangBei_X2_v6.swf","WuQi_X0_v1905.swf","WuQi_X1_v1904.swf","WuQi_X2_v1902.swf","Data2X_v19.swf","Music_v5.swf","Jingling_v1700.swf","ShiZhuang_X0_v5.swf","ShiZhuang_X1_v5.swf","ShiZhuang_X2_v5.swf"];
         Load.otherArr[16] = "ChongWu2.swf";
         Load.otherArr[17] = "SelMap_v1840.swf";
         Load.otherArr[18] = "Npc_v1910.swf";
         Load.otherArr[19] = "PlayerMc_X3.swf";
         Load.otherArr[20] = "WuQi_X3_v1902.swf";
         Load.mapArr = ["Map0_v1910.swf","Map1_v2.swf","Map2_v2.swf","Map3_v2.swf","Map4.swf","Map5.swf","Map6.swf","Map7.swf","Map8.swf","Map9_v4.swf","Map10.swf","Map11_v2.swf","Map12_v1332.swf","Map13.swf","Map14.swf","Map15.swf","Map16.swf","Map17.swf","Map18_v2.swf"];
         Load.mapArr[101] = "Map101.swf";
         Load.mapArr[102] = "Map102.swf";
         Load.mapArr[103] = "Map103.swf";
         Load.mapArr[104] = "Map104.swf";
         Load.mapArr[105] = "Map105.swf";
         Load.mapArr[888] = "Map888.swf";
         Load.mapArr[999] = "Map999.swf";
         Load.mapArr[1000] = "Map1000.swf";
         Load.mapArr[2000] = "Map2000.swf";
         Load.mapArr[2015] = "Map2015.swf";
         Load.mapArr[3000] = "Map2015.swf";
         Load.mapArr[51] = "Map51.swf";
         Load.mapArr[52] = "Map52.swf";
         Load.mapArr[53] = "Map53.swf";
         Load.mapArr[54] = "Map54.swf";
         Load.mapArr[55] = "Map55.swf";
         Load.mapArr[56] = "Map56.swf";
         Load.mapArr[57] = "Map57.swf";
         Load.mapArr[58] = "Map58.swf";
         Load.mapArr[59] = "Map59.swf";
         Load.mapArr[60] = "Map60_v2.swf";
         Load.mapArr[61] = "Map61.swf";
         Load.mapArr[62] = "Map62.swf";
         Load.mapArr[81] = "Map81_v2.swf";
         Load.mapArr[82] = "Map82.swf";
         Load.mapArr[83] = "Map83.swf";
         Load.mapArr[84] = "Map84.swf";
         Load.mapArr[4001] = "Map4001_v1052.swf";
         Load.mapArr[4002] = "Map4002.swf";
         Load.mapArr[4003] = "Map4003.swf";
         Load.mapArr[4004] = "Map4004.swf";
         Load.mapArr[4005] = "Map4005.swf";
         Load.mapArr[7000] = "Map7000.swf";
         Load.mapArr[7001] = "Map7000.swf";
         Load.mapArr[7002] = "Map7000.swf";
         Load.mapArr[7003] = "Map7000.swf";
         Load.mapArr[99999] = "Map19_v1320.swf";
         Load.enemyArr = ["","Enemy1.swf","Enemy2_v2.swf","Enemy3.swf","Enemy4_v1070.swf","Enemy5_v2.swf","Enemy6_v1211.swf","Enemy7_v2.swf","Enemy8_v1070.swf","Enemy9.swf","Enemy10_v7.swf","Enemy11_v2.swf","Enemy12v1910.swf","Enemy13_v3.swf","Enemy14_v2.swf","Enemy15_v910.swf","Enemy16_v1101.swf","Enemy17.swf","Enemy18_v2.swf","Enemy19_v3.swf","Enemy20.swf","Enemy21.swf","Enemy22.swf","Enemy23_v2.swf","Enemy24.swf","Enemy25_v1906.swf","Enemy26_v1021.swf","Enemy27_v1021.swf","Enemy28_v3.swf","Enemy29v1910.swf"];
         Load.enemyArr[101] = "Enemy101_v5.swf";
         Load.enemyArr[102] = "Enemy102.swf";
         Load.enemyArr[103] = "Enemy103_v5.swf";
         Load.enemyArr[104] = "Enemy104_v1212.swf";
         Load.enemyArr[105] = "Enemy105_v1212.swf";
         Load.enemyArr[201] = "Enemy201_v3.swf";
         Load.enemyArr[202] = "Enemy202.swf";
         Load.enemyArr[203] = "Enemy203_v3.swf";
         Load.enemyArr[204] = "Enemy204_v2.swf";
         Load.enemyArr[205] = "Enemy205_v2.swf";
         Load.enemyArr[206] = "Enemy206_v2.swf";
         Load.enemyArr[207] = "Enemy207_v2.swf";
         Load.enemyArr[208] = "Enemy207_v2.swf";
         Load.enemyArr[209] = "Enemy207_v2.swf";
         Load.enemyArr[210] = "Enemy210.swf";
         Load.enemyArr[211] = "Enemy210.swf";
         Load.enemyArr[212] = "Enemy210.swf";
         Load.enemyArr[213] = "Enemy213.swf";
         Load.enemyArr[214] = "Enemy213.swf";
         Load.enemyArr[215] = "Enemy213.swf";
         Load.enemyArr[216] = "Enemy216.swf";
         Load.enemyArr[217] = "Enemy216.swf";
         Load.enemyArr[218] = "Enemy216.swf";
         Load.enemyArr[222] = "Enemy222v1910.swf";
         Load.enemyArr[223] = "Enemy222v1910.swf";
         Load.enemyArr[224] = "Enemy222v1910.swf";
         Load.enemyArr[234] = "Enemy225.swf";
         Load.enemyArr[235] = "Enemy225.swf";
         Load.enemyArr[236] = "Enemy225.swf";
         Load.enemyArr[237] = "Enemy237_v941.swf";
         Load.enemyArr[238] = "Enemy237_v941.swf";
         Load.enemyArr[239] = "Enemy237_v941.swf";
         Load.enemyArr[240] = "Enemy242.swf";
         Load.enemyArr[241] = "Enemy242.swf";
         Load.enemyArr[242] = "Enemy242.swf";
         Load.enemyArr[51] = "Enemy51_v1051.swf";
         Load.enemyArr[52] = "Enemy52_v1220.swf";
         Load.enemyArr[53] = "Enemy53_v2.swf";
         Load.enemyArr[54] = "Enemy54_v3.swf";
         Load.enemyArr[55] = "Enemy55_v3.swf";
         Load.enemyArr[56] = "Enemy56_v2.swf";
         Load.enemyArr[57] = "Enemy57.swf";
         Load.enemyArr[58] = "Enemy58.swf";
         Load.enemyArr[59] = "Enemy59_v2.swf";
         Load.enemyArr[60] = "Enemy60_v900.swf";
         Load.enemyArr[61] = "Enemy61.swf";
         Load.enemyArr[62] = "Enemy62.swf";
         Load.enemyArr[81] = "Enemy81_v4.swf";
         Load.enemyArr[82] = "Enemy82.swf";
         Load.enemyArr[83] = "Enemy83.swf";
         Load.enemyArr[84] = "Enemy84_v1212.swf";
         Load.enemyArr[888] = "Enemy888.swf";
         Load.enemyArr[1000] = "Enemy1000_v921.swf";
         Load.enemyArr[1001] = "Enemy1001_v1906.swf";
         Load.enemyArr[2000] = "Enemy2000.swf";
         Load.enemyArr[2015] = "CW26.swf";
         Load.enemyArr[3000] = "Npc3000.swf";
         Load.enemyArr[4001] = "Enemy4001.swf";
         Load.enemyArr[4002] = "Enemy4002.swf";
         Load.enemyArr[4003] = "Enemy4003.swf";
         Load.enemyArr[4004] = "Enemy4004.swf";
         Load.enemyArr[4005] = "Enemy4005v1900.swf";
         Load.enemyArr[99999] = "Enemy99999_v1321.swf";
         Load.enemyArr[5001] = Load.enemyArr[3];
         Load.enemyArr[5002] = Load.enemyArr[4];
         Load.enemyArr[5003] = Load.enemyArr[8];
      }
   }
}

