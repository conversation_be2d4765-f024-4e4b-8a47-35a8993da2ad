package src
{
   import com.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class SelMap extends MovieClip
   {
      
      public static var skin:MovieClip;
      
      public static var loadData:ClassLoader;
      
      public static var selMapX:SelMap;
      
      public static var pageNum:int = 1;
      
      public static var tiaoZanType:Boolean = false;
      
      private static var tiaoZhanYN:Boolean = false;
      
      public var tiao<PERSON>han_mc:MovieClip;
      
      public var M0:SimpleButton;
      
      public var M1:MovieClip;
      
      public var M2:MovieClip;
      
      public var M3:MovieClip;
      
      public var M4:MovieClip;
      
      public var M5:MovieClip;
      
      public var M6:MovieClip;
      
      public var M7:MovieClip;
      
      public var M8:MovieClip;
      
      public var M9:MovieClip;
      
      public var M10:MovieClip;
      
      public var M11:MovieClip;
      
      public var M12:MovieClip;
      
      public var M13:MovieClip;
      
      public var M14:MovieClip;
      
      public var M15:MovieClip;
      
      public var M16:MovieClip;
      
      public var M17:MovieClip;
      
      public var M18:MovieClip;
      
      public var M19:MovieClip;
      
      public var M20:MovieClip;
      
      public var M51:MovieClip;
      
      public var M52:MovieClip;
      
      public var M53:MovieClip;
      
      public var M54:MovieClip;
      
      public var M55:MovieClip;
      
      public var M56:MovieClip;
      
      public var M57:MovieClip;
      
      public var M58:MovieClip;
      
      public var M59:MovieClip;
      
      public var M60:MovieClip;
      
      public var M61:MovieClip;
      
      public var M62:MovieClip;
      
      public var M81:MovieClip;
      
      public var M4001:MovieClip;
      
      public var M4002:MovieClip;
      
      public var M4003:MovieClip;
      
      public var M4004:MovieClip;
      
      public var M4005:MovieClip;
      
      public var next_提示_mc:MovieClip;
      
      public var tiaozhan_loading:MovieClip;
      
      public var NanDu_mc_1:MovieClip;
      
      public var NanDu_mc_2:MovieClip;
      
      public var NanDu_mc_3:MovieClip;
      
      public var NanDu_mc_4:MovieClip;
      
      public var btn:SimpleButton;
      
      public var btn2:SimpleButton;
      
      public var btn3:SimpleButton;
      
      public var tiaoZhan_btn:SimpleButton;
      
      public var back_btn:SimpleButton;
      
      public var next_btn:SimpleButton;
      
      public var Sel_nanDu_mc:MovieClip;
      
      public var Sel_nanDu_mc2:MovieClip;
      
      public var Sel_nanDu_mc3:MovieClip;
      
      public var Sel_nanDu_mc4:MovieClip;
      
      public var Sel_nanDu_mc5:MovieClip;
      
      public var guanKaMax:int = 62;
      
      public function SelMap()
      {
         super();
         selMapX = this;
         this.MapYN();
      }
      
      private static function TiaoZhan(e:*) : *
      {
         if(tiaoZanType)
         {
            tiaoZanType = false;
         }
         else
         {
            tiaoZanType = true;
         }
         TiaoZhan_mc_Show();
         setTimeout(selMapX.OpenMapNum,500);
      }
      
      private static function TiaoZhan_mc_Show() : *
      {
         if(!selMapX.tiaoZhan_mc)
         {
            return;
         }
         if(tiaoZanType && Boolean(selMapX.tiaoZhan_mc))
         {
            selMapX.tiaoZhan_mc.gotoAndStop(pageNum);
            selMapX.tiaoZhan_mc.visible = true;
         }
         else if(selMapX.tiaoZhan_mc)
         {
            selMapX.tiaoZhan_mc.visible = false;
         }
      }
      
      public static function Open(xx:int = 0, yy:int = 0, type:uint = 1, goto:int = 1) : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         if(!selMapX)
         {
            classRef = loadData.getClass("src.SelMap") as Class;
            xxMov = new classRef();
         }
         Main._this.addChild(selMapX);
         selMapX.x = xx;
         selMapX.y = yy;
         selMapX.visible = true;
         Main.allClosePanel();
         selMapX.MapYN();
         pageNum = goto;
         if(type == 2)
         {
            selMapX.x = selMapX.y = -2000;
            (selMapX["Sel_nanDu_mc"] as Sel_NanDu).Open(2000,2000,2);
         }
         else if(type == 5)
         {
            selMapX.x = selMapX.y = -2000;
            (selMapX["Sel_nanDu_mc5"] as Sel_nanDu_AnHei).Open();
         }
         if(Main.gameNum2.getValue() == 4 && goto != 4)
         {
            selMapX.gotoAndStop(3);
            pageNum = 3;
         }
         else
         {
            selMapX.gotoAndStop(pageNum);
         }
         selMapX.tiaozhan_loading.visible = false;
         selMapX.tiaoZhan_btn.addEventListener(MouseEvent.CLICK,TiaoZhan);
         if(TiaoZhan_Interface.loadingOK == 2 && selMapX.tiaoZhan_btn.visible)
         {
            TiaoZhan_Interface.Close();
         }
         else if(selMapX.tiaoZhan_btn.visible)
         {
            selMapX.tiaozhan_loading.visible = true;
         }
         TiaoZhan_mc_Show();
         Play_Interface.Close();
      }
      
      public static function Close() : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         if(!selMapX)
         {
            classRef = loadData.getClass("src.SelMap") as Class;
            xxMov = new classRef();
         }
         Main._this.addChild(selMapX);
         selMapX.x = 5000;
         selMapX.y = 5000;
         selMapX.visible = false;
         TiaoZhan_Interface.Close();
      }
      
      private function MapYN() : *
      {
         setTimeout(this.OpenMapNum,500);
      }
      
      private function Next_map(e:*) : *
      {
         if(pageNum < 2)
         {
            ++pageNum;
            gotoAndStop(pageNum);
            this.MapYN();
            TiaoZhan_mc_Show();
         }
      }
      
      private function Back_map(e:*) : *
      {
         if(pageNum > 1)
         {
            --pageNum;
            gotoAndStop(pageNum);
            this.MapYN();
            TiaoZhan_mc_Show();
         }
      }
      
      private function OpenMapNum() : *
      {
         if(this["M0"])
         {
            this.M0.addEventListener(MouseEvent.CLICK,this.MouseIn2);
         }
         this.btn.addEventListener(MouseEvent.CLICK,this.MouseIn2);
         this.btn2.addEventListener(MouseEvent.CLICK,this.MouseIn3);
         this.btn3.addEventListener(MouseEvent.CLICK,this.MouseIn3);
         this["next_btn"].addEventListener(MouseEvent.CLICK,this.Next_map);
         this["back_btn"].addEventListener(MouseEvent.CLICK,this.Back_map);
         this["next_btn"].visible = true;
         this["back_btn"].visible = true;
         if(currentFrame == 1)
         {
            this["back_btn"].visible = false;
            if(!Main.guanKa[9] || Main.guanKa[9] == 0)
            {
               this["next_btn"].visible = false;
            }
         }
         else if(currentFrame == 2)
         {
            this["next_btn"].visible = false;
         }
         if(Main.guanKa[1] == null || Main.guanKa[1] == false || Main.guanKa[1] == 0)
         {
            Main.guanKa[1] = 2;
         }
         for(var i:int = 1; i < 17; i++)
         {
            if(Boolean(this["M" + i]) && (Main.guanKa[i] != 0 || i == 1))
            {
               this["M" + i].gotoAndStop(2);
               this["M" + i].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
               this["M" + i].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
               this["M" + i].addEventListener(MouseEvent.CLICK,this.sel_NanDu_Open);
            }
         }
         for(i = 51; i <= this.guanKaMax; i++)
         {
            if(Boolean(this["M" + i]) && (Main.guanKa[i] != 0 || i == 51))
            {
               this["M" + i].gotoAndStop(2);
               this["M" + i].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
               this["M" + i].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
               this["M" + i].addEventListener(MouseEvent.CLICK,this.sel_NanDu_Open);
            }
         }
         if(this["M17"])
         {
            this["M17"].gotoAndStop(2);
            this["M17"].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
            this["M17"].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
            this["M17"].addEventListener(MouseEvent.CLICK,this.GameGo);
         }
         if(this["M18"])
         {
            this["M18"].gotoAndStop(2);
            this["M18"].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
            this["M18"].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
            this["M18"].addEventListener(MouseEvent.CLICK,this.sel_GuanKaXX_Open);
         }
         if(this["M19"])
         {
            this["M19"].gotoAndStop(2);
            this["M19"].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
            this["M19"].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
            this["M19"].addEventListener(MouseEvent.CLICK,this.GameGo2);
         }
         if(this["M81"])
         {
            this["M81"].gotoAndStop(2);
            this["M81"].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
            this["M81"].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
            this["M81"].addEventListener(MouseEvent.CLICK,this.sel_GuanKaXX_Open2);
         }
         for(var xx:int = 4001; xx <= 4005; xx++)
         {
            if(this["M" + xx])
            {
               this["M" + xx].gotoAndStop(2);
               this["M" + xx].addEventListener(MouseEvent.MOUSE_OVER,this.MouseIn);
               this["M" + xx].addEventListener(MouseEvent.ROLL_OUT,this.MouseOut);
               this["M" + xx].addEventListener(MouseEvent.CLICK,this.go_AnHeiGame);
            }
         }
         if(this["M17"])
         {
            if(tiaoZanType)
            {
               this["M17"].visible = false;
            }
            else
            {
               this["M17"].visible = true;
            }
         }
         if(this["M81"])
         {
            if(tiaoZanType)
            {
               this["M81"].visible = false;
            }
            else
            {
               this["M81"].visible = true;
            }
         }
      }
      
      private function go_AnHeiGame(e:*) : *
      {
         var num:int = int((e.currentTarget.name as String).substr(1,4));
         Main.gameNum.setValue(num);
         Main.gameNum2.setValue(1);
         GameData.gameLV = 4;
         Main._this.Loading();
         Close();
      }
      
      private function MouseIn2(e:*) : *
      {
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.Loading();
         Close();
      }
      
      private function MouseIn3(e:*) : *
      {
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(4);
         Main._this.Loading();
         Close();
      }
      
      private function sel_NanDu_Open(e:MouseEvent = null) : *
      {
         var num:int = int((e.currentTarget.name as String).substr(1,2));
         Main.gameNum.setValue(num);
         if(tiaoZanType)
         {
            TiaoZhan_Interface.Open();
         }
         else
         {
            (this.Sel_nanDu_mc as Sel_NanDu).Open();
         }
      }
      
      public function sel_GuanKaXX_Open(e:MouseEvent = null) : *
      {
         (this.Sel_nanDu_mc2 as Sel_NanDu2).Open();
      }
      
      public function sel_GuanKaXX_Open2(e:MouseEvent = null) : *
      {
         (this.Sel_nanDu_mc3 as Sel_NanDu3).Open();
      }
      
      public function GameGo(e:MouseEvent = null) : *
      {
         (this.Sel_nanDu_mc4 as Sel_NanDu4).Open();
      }
      
      public function GameGo2(e:MouseEvent = null) : *
      {
         for(var i:* = 18; i <= 29; i++)
         {
            if(Main.guanKa[i] <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"需要打败12个星灵才可进入");
               return;
            }
         }
         Main.gameNum.setValue(99999);
         Main.gameNum2.setValue(1);
         GameData.gameLV = 4;
         Main._this.Loading();
      }
      
      private function MouseIn(e:*) : *
      {
         e.currentTarget.gotoAndStop(3);
      }
      
      private function MouseOut(e:*) : *
      {
         e.currentTarget.gotoAndStop(2);
      }
   }
}

