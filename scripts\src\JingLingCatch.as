package src
{
   import com.hotpoint.braveManIII.models.elves.Elves;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.elves.*;
   import com.hotpoint.braveManIII.repository.jingLingCatch.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   
   public class JingLingCatch extends MovieClip
   {
      
      public static var skin:MovieClip;
      
      public static var chuxianMC:MovieClip;
      
      public static var zhuaMC:MovieClip;
      
      public static var chenggongMC:MovieClip;
      
      public static var shibaiMC:MovieClip;
      
      public static var classStr:String;
      
      public static var gailv:int = 15;
      
      public static var myplayer:PlayerData = Main.player1;
      
      public static var only:Boolean = true;
      
      public static var count:int = 0;
      
      public static var jinglingID:int = 1;
      
      public static var isMove:Boolean = true;
      
      public static var arr:Array = [];
      
      private static var xxxx:int = 0;
      
      private static var xxxx2:int = 0;
      
      private static var rushNum2:int = 0;
      
      private static var rushNum3:int = 0;
      
      private static var temp:int = 0;
      
      private static var temp2:int = 0;
      
      private static var alltime:int = 500;
      
      public var who:Player;
      
      public var data:Elves;
      
      public var RL:Boolean = true;
      
      public function JingLingCatch()
      {
         super();
      }
      
      public static function catchJL() : *
      {
         isMove = false;
         var classRef2:Class = NewLoad.OtherData.getClass("ZhuaJL") as Class;
         zhuaMC = new classRef2();
         Main.world.moveChild_Other.addChild(zhuaMC);
         zhuaMC.x = skin.x;
         zhuaMC.y = skin.y;
         zhuaMC.addEventListener(Event.ENTER_FRAME,panDuan);
      }
      
      public static function addSkin() : *
      {
         var i:* = undefined;
         var gailv:int = 0;
         var numXXX:int = 0;
         var classRef:Class = null;
         var classRef2:Class = null;
         arr = [];
         for(i in JLCFactory.allData)
         {
            if(JLCFactory.allData[i][0] == GameData.BOSSid.getValue() && JLCFactory.allData[i][3] == GameData.gameLV)
            {
               arr.push(JLCFactory.allData[i]);
            }
         }
         for(i in arr)
         {
            gailv = int(arr[i][2]);
            if(Main.isVip())
            {
               gailv += arr[i][4];
            }
            numXXX = int(Math.random() * 1000);
            trace("精灵概率 >>>>",numXXX,gailv);
            if(numXXX < gailv)
            {
               jinglingID = arr[i][1];
               classStr = ElvesFactory.getClassName(arr[i][1]);
               classRef = ChongWu.loadData.getClass(classStr) as Class;
               skin = new classRef();
               Main.world.moveChild_Other.addChild(skin);
               if(GameData.BossIS)
               {
                  skin.x = GameData.BossIS.x - int(Math.random() * 100);
                  skin.y = GameData.BossIS.y - int(Math.random() * 200);
                  skin.visible = false;
               }
               count = 1;
               classRef2 = NewLoad.OtherData.getClass("ChuXian") as Class;
               chuxianMC = new classRef2();
               Main.world.moveChild_Other.addChild(chuxianMC);
               chuxianMC.gotoAndPlay(1);
               chuxianMC.x = skin.x;
               chuxianMC.y = skin.y;
               chuxianMC.addEventListener(Event.ENTER_FRAME,playChuXian);
               trace("精灵概率 >>>> 出现");
               break;
            }
         }
      }
      
      private static function panDuan(e:*) : *
      {
         var classRef2:Class = null;
         if(zhuaMC.currentLabel == "结束")
         {
            zhuaMC.visible = false;
            if(int(Math.random() * 100) < gailv)
            {
               classRef2 = NewLoad.OtherData.getClass("zhuaTrue") as Class;
               chenggongMC = new classRef2();
               Main.world.moveChild_Other.addChild(chenggongMC);
               chenggongMC.gotoAndPlay(1);
               chenggongMC.x = skin.x;
               chenggongMC.y = skin.y;
               isMove = true;
               count = 0;
               alltime = 0;
               if(myplayer.getElvesSlot().backElvesSlotNum() > 0)
               {
                  myplayer.getElvesSlot().addElvesSlot(ElvesFactory.creatElves(jinglingID));
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵栏已满，请清理扩充");
               }
               skin.visible = false;
               ItemsPanel.itemsPanel["jinglingOPEN"].visible = true;
               chenggongMC.addEventListener(Event.ENTER_FRAME,playChengGong);
            }
            else
            {
               classRef2 = NewLoad.OtherData.getClass("zhuaFalse") as Class;
               shibaiMC = new classRef2();
               Main.world.moveChild_Other.addChild(shibaiMC);
               shibaiMC.gotoAndPlay(1);
               shibaiMC.x = skin.x;
               shibaiMC.y = skin.y;
               skin.visible = true;
               isMove = true;
               shibaiMC.addEventListener(Event.ENTER_FRAME,playShiBai);
            }
            zhuaMC.removeEventListener(Event.ENTER_FRAME,panDuan);
         }
      }
      
      private static function playChengGong(e:*) : *
      {
         if(chenggongMC.currentLabel == "结束")
         {
            chenggongMC.visible = false;
            chenggongMC.removeEventListener(Event.ENTER_FRAME,playChengGong);
         }
      }
      
      private static function playShiBai(e:*) : *
      {
         if(shibaiMC.currentLabel == "结束")
         {
            shibaiMC.visible = false;
            shibaiMC.removeEventListener(Event.ENTER_FRAME,playShiBai);
         }
      }
      
      private static function playChuXian(e:*) : *
      {
         if(chuxianMC.currentLabel == "结束")
         {
            chuxianMC.visible = false;
            temp = int(Math.random() * 30) + 30;
            temp2 = int(Math.random() * 20) + 20;
            alltime = 99999;
            xxxx = 0;
            xxxx2 = 0;
            rushNum2 = 60;
            rushNum3 = 0;
            skin.addEventListener(Event.ENTER_FRAME,playJingLing);
            chuxianMC.removeEventListener(Event.ENTER_FRAME,playChuXian);
         }
      }
      
      private static function playJingLing(e:*) : *
      {
         if(skin.currentLabel != "移动")
         {
            skin.gotoAndPlay("移动");
         }
         skin.visible = true;
         --alltime;
         if(isMove)
         {
            if(alltime > 0)
            {
               if(rushNum2 < 0)
               {
                  xxxx = 1;
                  skin.scaleX = -1;
               }
               if(rushNum2 > temp)
               {
                  xxxx = 0;
                  skin.scaleX = 1;
               }
               if(rushNum3 < 0)
               {
                  xxxx2 = 1;
               }
               if(rushNum3 > temp2)
               {
                  xxxx2 = 0;
               }
               if(xxxx == 0)
               {
                  --rushNum2;
                  skin.x -= 3;
               }
               else
               {
                  ++rushNum2;
                  skin.x += 3;
               }
               if(xxxx2 == 0)
               {
                  --rushNum3;
                  skin.y -= 3;
               }
               else
               {
                  ++rushNum3;
                  skin.y += 3;
               }
            }
            else
            {
               count = 0;
               skin.visible = false;
               skin.removeEventListener(Event.ENTER_FRAME,playJingLing);
            }
         }
      }
      
      public function Close() : *
      {
      }
   }
}

