package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.strPanel.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   import src.tool.*;
   
   public class QiangHuaPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var qhPanel:MovieClip;
      
      public static var qhp:QiangHuaPanel;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var starNum:Number;
      
      public static var oldNum:Number;
      
      private static var loadData:ClassLoader;
      
      public static var selbool:Boolean = false;
      
      public static var isPOne:Boolean = true;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_QHQC_v1.swf";
      
      public function QiangHuaPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!qhPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = qhPanel.getChildIndex(qhPanel["e" + i]);
            mm.x = qhPanel["e" + i].x;
            mm.y = qhPanel["e" + i].y;
            mm.name = "e" + i;
            qhPanel.removeChild(qhPanel["e" + i]);
            qhPanel["e" + i] = mm;
            qhPanel.addChild(mm);
            qhPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = qhPanel.getChildIndex(qhPanel["s" + i]);
            mm.x = qhPanel["s" + i].x;
            mm.y = qhPanel["s" + i].y;
            mm.name = "s" + i;
            qhPanel.removeChild(qhPanel["s" + i]);
            qhPanel["s" + i] = mm;
            qhPanel.addChild(mm);
            qhPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = qhPanel.getChildIndex(qhPanel["select"]);
         mm.x = qhPanel["select"].x;
         mm.y = qhPanel["select"].y;
         mm.name = "select";
         qhPanel.removeChild(qhPanel["select"]);
         qhPanel["select"] = mm;
         qhPanel.addChild(mm);
         qhPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("QHShow") as Class;
         qhPanel = new classRef();
         qhp.addChild(qhPanel);
         InitIcon();
         if(OpenYN)
         {
            open(isPOne,oldNum);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         qhp = new QiangHuaPanel();
         LoadSkin();
         Main._stage.addChild(qhp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         qhp = new QiangHuaPanel();
         Main._stage.addChild(qhp);
         OpenYN = false;
      }
      
      public static function open(pp:Boolean, num:int) : void
      {
         Main.allClosePanel();
         if(qhPanel)
         {
            oldNum = num;
            Main.stopXX = true;
            qhp.x = 0;
            qhp.y = 0;
            isPOne = pp;
            addListenerP1();
            Main._stage.addChild(qhp);
            qhp.visible = true;
         }
         else
         {
            oldNum = num;
            isPOne = pp;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(qhPanel)
         {
            selbool = false;
            Main.stopXX = false;
            removeListenerP1();
            qhp.visible = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         var i:uint = 0;
         var xx:uint = 0;
         var equip:Equip = null;
         var xx2:uint = 0;
         var equip2:Equip = null;
         qhPanel["isQC"].visible = false;
         qhPanel["isQC"]["yesQC"].addEventListener(MouseEvent.CLICK,yesQC);
         qhPanel["isQC"]["noQC"].addEventListener(MouseEvent.CLICK,noQC);
         qhPanel["isQC"]["noQC2"].addEventListener(MouseEvent.CLICK,noQC);
         qhPanel["qc_btn"].addEventListener(MouseEvent.CLICK,doQH);
         qhPanel["close"].addEventListener(MouseEvent.CLICK,closeQH);
         for(i = 0; i < 24; i++)
         {
            qhPanel["e" + i].mouseChildren = false;
            qhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            qhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            qhPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            qhPanel["s" + i].mouseChildren = false;
            qhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            qhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            qhPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         qhPanel["select"].gotoAndStop(1);
         qhPanel["select"].visible = false;
         if(isPOne)
         {
            for(i = 0; i < 24; i++)
            {
               qhPanel["e" + i].t_txt.text = "";
               if(Main.player1.getBag().getEquipFromBag(i) != null)
               {
                  if(Main.player1.getBag().getEquipFromBag(i).getReinforceLevel() > 0)
                  {
                     qhPanel["e" + i].gotoAndStop(Main.player1.getBag().getEquipFromBag(i).getFrame());
                     qhPanel["e" + i].visible = true;
                  }
                  else
                  {
                     qhPanel["e" + i].visible = false;
                  }
               }
               else
               {
                  qhPanel["e" + i].visible = false;
               }
            }
            for(i = 0; i < 8; i++)
            {
               qhPanel["s" + i].t_txt.text = "";
               xx = i;
               if((i == 0 || i == 1 || i == 3 || i == 4) && Main.water.getValue() != 1)
               {
                  xx += 8;
               }
               equip = Main.player1.getEquipSlot().getEquipFromSlot(xx);
               if(equip != null)
               {
                  if(equip.getReinforceLevel() > 0)
                  {
                     qhPanel["s" + i].gotoAndStop(equip.getFrame());
                     qhPanel["s" + i].visible = true;
                  }
                  else
                  {
                     qhPanel["s" + i].visible = false;
                  }
               }
               else
               {
                  qhPanel["s" + i].visible = false;
               }
            }
         }
         else
         {
            for(i = 0; i < 24; i++)
            {
               qhPanel["e" + i].t_txt.text = "";
               if(Main.player2.getBag().getEquipFromBag(i) != null)
               {
                  if(Main.player2.getBag().getEquipFromBag(i).getReinforceLevel() > 0)
                  {
                     qhPanel["e" + i].gotoAndStop(Main.player2.getBag().getEquipFromBag(i).getFrame());
                     qhPanel["e" + i].visible = true;
                  }
                  else
                  {
                     qhPanel["e" + i].visible = false;
                  }
               }
               else
               {
                  qhPanel["e" + i].visible = false;
               }
            }
            for(i = 0; i < 8; i++)
            {
               qhPanel["s" + i].t_txt.text = "";
               xx2 = i;
               if((i == 0 || i == 1 || i == 3 || i == 4) && Main.water.getValue() != 1)
               {
                  xx2 += 8;
               }
               equip2 = Main.player1.getEquipSlot().getEquipFromSlot(xx2);
               if(equip2 != null)
               {
                  if(equip2.getReinforceLevel() > 0)
                  {
                     qhPanel["s" + i].gotoAndStop(equip2.getFrame());
                     qhPanel["s" + i].visible = true;
                  }
                  else
                  {
                     qhPanel["s" + i].visible = false;
                  }
               }
               else
               {
                  qhPanel["s" + i].visible = false;
               }
            }
         }
         qhPanel["chose"].visible = false;
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         qhPanel["close"].removeEventListener(MouseEvent.CLICK,closeQH);
         qhPanel["qc_btn"].removeEventListener(MouseEvent.CLICK,doQH);
         for(i = 0; i < 24; i++)
         {
            qhPanel["e" + i].mouseChildren = false;
            qhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            qhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            qhPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            qhPanel["s" + i].mouseChildren = false;
            qhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            qhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            qhPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
      }
      
      public static function closeQH(e:*) : *
      {
         close();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         qhPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         TiaoShi.txtShow(overobj.name);
         if(isPOne)
         {
            if(str == "e")
            {
               if(Main.player1.getBag().getEquipFromBag(overNum) != null)
               {
                  itemsTooltip.equipTooltip(Main.player1.getBag().getEquipFromBag(overNum),1);
               }
            }
            else
            {
               itemsTooltip.slotTooltip(overNum,Main.player1.getEquipSlot(),true);
            }
         }
         else if(str == "e")
         {
            if(Main.player2.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(Main.player2.getBag().getEquipFromBag(overNum),2);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(overNum,Main.player2.getEquipSlot(),true);
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = qhPanel.mouseX + 10;
         itemsTooltip.y = qhPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function yesQC(e:*) : *
      {
         if(selbool == false)
         {
            return;
         }
         if(isPOne)
         {
            if(nameStr == "e")
            {
               Main.player1.getBag().getEquipFromBag(clickNum).reinforceClear();
            }
            else
            {
               if((clickNum == 0 || clickNum == 1 || clickNum == 3 || clickNum == 4) && Main.water.getValue() != 1)
               {
                  clickNum += 8;
               }
               Main.player1.getEquipSlot().getEquipFromSlot(clickNum).reinforceClear();
            }
            Main.player1.getBag().delOtherobj(oldNum,1);
         }
         else
         {
            if(nameStr == "e")
            {
               Main.player2.getBag().getEquipFromBag(clickNum).reinforceClear();
            }
            else
            {
               if((clickNum == 0 || clickNum == 1 || clickNum == 3 || clickNum == 4) && Main.water.getValue() != 1)
               {
                  clickNum += 8;
               }
               Main.player2.getEquipSlot().getEquipFromSlot(clickNum).reinforceClear();
            }
            Main.player2.getBag().delOtherobj(oldNum,1);
         }
         qhPanel["isQC"].visible = false;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功");
         close();
      }
      
      private static function noQC(e:*) : *
      {
         qhPanel["isQC"].visible = false;
      }
      
      private static function doQH(e:*) : *
      {
         qhPanel["isQC"].visible = true;
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         selbool = true;
         clickObj = e.target as MovieClip;
         qhPanel["chose"].visible = true;
         qhPanel["chose"].x = clickObj.x - 2;
         qhPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         qhPanel["select"].gotoAndStop(clickObj.currentFrame);
         qhPanel["select"].visible = true;
      }
   }
}

