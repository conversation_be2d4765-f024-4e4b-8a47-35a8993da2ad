package src.tool
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   import src._data.*;
   
   public class Sel_nanDu_An<PERSON>ei extends MovieClip
   {
      
      public var close_btn:SimpleButton;
      
      public var anHei_1_btn:SimpleButton;
      
      public function Sel_nanDu_AnHei()
      {
         super();
         this.close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.anHei_1_btn.addEventListener(MouseEvent.CLICK,this.NanDu1);
      }
      
      public function Open(xx:uint = 2000, yy:uint = 2000) : *
      {
         this.x = xx;
         this.y = yy;
         this.visible = true;
      }
      
      public function Close(e:* = null) : *
      {
         this.x = this.y = -15000;
         this.visible = false;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(4);
         Main._this.Loading();
         SelMap.Close();
      }
      
      private function NanDu1(e:*) : *
      {
         SelMap.Open(0,0,1,4);
         this.x = this.y = -15000;
         this.visible = false;
      }
      
      private function NanDu2(e:*) : *
      {
      }
      
      private function NanDu3(e:*) : *
      {
      }
   }
}

