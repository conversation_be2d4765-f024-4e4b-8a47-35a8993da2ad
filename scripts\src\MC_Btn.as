package src
{
   import flash.display.MovieClip;
   import flash.events.*;
   
   public class MC_Btn extends MovieClip
   {
      
      public var skin:MovieClip;
      
      public var onCLICK_fun:Function;
      
      public var lingQuNum:int = 0;
      
      public function MC_Btn(mc:MovieClip, onCLICKFun:String = "")
      {
         super();
         this.skin = mc;
         this.skin.gotoAndStop(1);
         this.skin.addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         this.skin.addEventListener(MouseEvent.MOUSE_OUT,this.onMOUSE_OUT);
         if(onCLICKFun != "")
         {
            this.skin.addEventListener(MouseEvent.CLICK,this[onCLICKFun]);
         }
         mc.mouseChildren = false;
         mc.buttonMode = true;
      }
      
      public function onMOUSE_MOVE(e:MouseEvent) : *
      {
         if(this.skin.currentFrame == 2)
         {
            this.skin.gotoAndStop(3);
         }
      }
      
      public function onMOUSE_OUT(e:MouseEvent) : *
      {
         if(this.skin.currentFrame == 3)
         {
            this.skin.gotoAndStop(2);
         }
      }
      
      public function onCLICK_CZ(e:MouseEvent = null) : *
      {
         if(this.skin.currentFrame == 2 || this.skin.currentFrame == 3)
         {
            trace("充值领取",e.target.name,this.lingQuNum);
            ChongZhi_Interface4.LingQu_CZ(this.lingQuNum);
         }
      }
      
      public function goTo(num:int) : *
      {
         this.skin.gotoAndStop(num);
      }
   }
}

