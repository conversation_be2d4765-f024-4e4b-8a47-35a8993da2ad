package src._data
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import src.*;
   
   public class XingLingFactory
   {
      
      private static var selNumMax:uint;
      
      private static var sel_LV_NumMax:uint;
      
      public static var xingLingData:Array = [];
      
      public static var myXml:XML = new XML();
      
      public static var AllData:Array = new Array();
      
      private static var getTolal_Data_Arr:Array = new Array();
      
      private static var getTolal_Data_Arr2:Array = new Array();
      
      public static var dianLiangNum:uint = 0;
      
      public static var dianLiangNumX:Number = 0;
      
      public static var qiangHuaArr:Array = [90100,90080,90050,90030,90012,90010];
      
      public function XingLingFactory()
      {
         super();
      }
      
      public static function creatFactory() : *
      {
         selNumMax = XingLing_Interface.selNumMax;
         sel_LV_NumMax = XingLing_Interface.sel_LV_NumMax;
         myXml = XMLAsset.createXML(Data2.xingLing);
         InitDataX();
         Init_xingLingData();
      }
      
      public static function Init_xingLingData() : *
      {
         var j:uint = 0;
         for(var i:uint = 0; i <= selNumMax; i++)
         {
            if(!xingLingData[i])
            {
               xingLingData[i] = [VT.createVT()];
               for(j = 1; j <= sel_LV_NumMax; j++)
               {
                  if(j == 1)
                  {
                     (xingLingData[i] as Array).push(VT.createVT(1));
                  }
                  else
                  {
                     (xingLingData[i] as Array).push(VT.createVT());
                  }
               }
            }
         }
      }
      
      public static function Trace() : *
      {
         var x1:* = undefined;
         var x2:* = undefined;
         var x3:* = undefined;
         var x4:* = undefined;
         var x5:* = undefined;
         for(var i:uint = 0; i <= selNumMax; i++)
         {
            x1 = (xingLingData[i][0] as VT).getValue();
            x2 = (xingLingData[i][1] as VT).getValue();
            x3 = (xingLingData[i][2] as VT).getValue();
            x4 = (xingLingData[i][3] as VT).getValue();
            x5 = (xingLingData[i][4] as VT).getValue();
         }
      }
      
      private static function InitDataX() : *
      {
         var property:XML = null;
         var Xid:uint = 0;
         var id:uint = 0;
         var tempObj:Object = null;
         for each(property in myXml.星灵数据)
         {
            Xid = Number(property.星座ID);
            if(AllData[Xid] == null)
            {
               AllData[Xid] = new Array();
            }
            id = Number(property.等级);
            tempObj = new Object();
            tempObj.xh1_1 = VT.createVT(Number(property.消耗能源));
            tempObj.xh1_2 = VT.createVT(Number(property.消耗击杀点));
            tempObj.xh1_3 = VT.createVT(Number(property.消耗点券));
            tempObj.xh1_4 = VT.createVT(Number(property.商城ID));
            tempObj.xh2_1 = VT.createVT(Number(property.进阶能源));
            tempObj.xh2_2 = VT.createVT(Number(property.进阶水晶));
            tempObj.xh2_3 = VT.createVT(Number(property.进阶点券));
            tempObj.xh2_4 = VT.createVT(Number(property.进阶商城ID));
            tempObj.sx1 = VT.createVT(Number(property.生命));
            tempObj.sx2 = VT.createVT(Number(property.魔法));
            tempObj.sx3 = VT.createVT(Number(property.攻击));
            tempObj.sx4 = VT.createVT(Number(property.防御));
            tempObj.sx5 = VT.createVT(Number(property.暴击));
            tempObj.sx6 = VT.createVT(Number(property.闪避));
            tempObj.sx7 = VT.createVT(Number(property.破魔));
            tempObj.sx8 = VT.createVT(Number(property.魔抗));
            tempObj.sxA1 = VT.createVT(Number(property.sum生命));
            tempObj.sxA2 = VT.createVT(Number(property.sum魔法));
            tempObj.sxA3 = VT.createVT(Number(property.sum攻击));
            tempObj.sxA4 = VT.createVT(Number(property.sum防御));
            tempObj.sxA5 = VT.createVT(Number(property.sum暴击));
            tempObj.sxA6 = VT.createVT(Number(property.sum闪避));
            tempObj.sxA7 = VT.createVT(Number(property.sum破魔));
            tempObj.sxA8 = VT.createVT(Number(property.sum魔抗));
            AllData[Xid][id] = tempObj;
         }
      }
      
      public static function Get_LV_Data_Str(num:uint = 1, num2:uint = 1) : Array
      {
         var i2:uint = 0;
         var numX2:uint = 0;
         var str:String = null;
         var xxx:uint = 0;
         var tempArr:Array = null;
         var temp:Array = new Array();
         for(var i:uint = 1; i <= 10; i++)
         {
            for(i2 = 1; i2 <= 8; i2++)
            {
               numX2 = num2 * 10 + i - 10;
               if((AllData[num][numX2]["sx" + i2] as VT).getValue() != 0)
               {
                  str = "";
                  if(i2 == 1)
                  {
                     str = "生命+";
                  }
                  else if(i2 == 2)
                  {
                     str = "魔法+";
                  }
                  else if(i2 == 3)
                  {
                     str = "攻击+";
                  }
                  else if(i2 == 4)
                  {
                     str = "防御+";
                  }
                  else if(i2 == 5)
                  {
                     str = "暴击+";
                  }
                  else if(i2 == 6)
                  {
                     str = "闪避+";
                  }
                  else if(i2 == 7)
                  {
                     str = "破魔+";
                  }
                  else if(i2 == 8)
                  {
                     str = "魔抗+";
                  }
                  xxx = uint(xingLingData[num][num2].getValue());
                  tempArr = [1,1,1.25,1.5,1.75,2,3];
                  str += uint((AllData[num][numX2]["sx" + i2] as VT).getValue() * tempArr[xxx]);
                  temp[i] = str;
                  break;
               }
            }
         }
         return temp;
      }
      
      public static function Get_LV(num:uint, num2:uint) : Array
      {
         var lv:uint = uint((xingLingData[num][num2] as VT).getValue());
         var numXX:int = 0;
         var yn:Boolean = false;
         if((xingLingData[num][0] as VT).getValue() > num2 * 10)
         {
            numXX = 10;
         }
         else
         {
            numXX = (xingLingData[num][0] as VT).getValue() - num2 * 10 + 10;
            if(numXX < 0)
            {
               numXX = 0;
            }
         }
         return [lv,numXX];
      }
      
      public static function Get_LV2(num:uint) : uint
      {
         var numX2:uint = 0;
         var numX:uint = 0;
         for(var x:int = 1; x < xingLingData[num].length; x++)
         {
            numX2 = uint(xingLingData[num][x].getValue());
            if(numX2 > 0)
            {
               numX++;
            }
         }
         return numX;
      }
      
      public static function Get_LV_color(num:uint) : Array
      {
         var numX2:uint = 0;
         var tempArr:Array = new Array();
         for(var x:int = 1; x < xingLingData[num].length; x++)
         {
            numX2 = uint(xingLingData[num][x].getValue());
            tempArr[x] = numX2 + 1;
         }
         return tempArr;
      }
      
      public static function Get_one_color(num:uint, num2:uint) : uint
      {
         return uint(xingLingData[num][num2].getValue());
      }
      
      public static function GetLvNum(num:uint) : uint
      {
         return (xingLingData[num][0] as VT).getValue();
      }
      
      public static function Sel_XuQiu_up1(num:uint) : Array
      {
         var tempArr:Array = new Array();
         var xx:uint = uint(GetLvNum(num) + 1);
         tempArr[0] = (AllData[num][xx].xh1_1 as VT).getValue();
         tempArr[1] = (AllData[num][xx].xh1_2 as VT).getValue();
         tempArr[2] = (AllData[num][xx].xh1_3 as VT).getValue();
         tempArr[3] = (AllData[num][xx].xh1_4 as VT).getValue();
         return tempArr;
      }
      
      public static function Sel_XuQiu_up2(num:uint) : Array
      {
         var tempArr:Array = new Array();
         var xx:uint = GetLvNum(num);
         tempArr[0] = (AllData[num][xx].xh2_1 as VT).getValue();
         tempArr[1] = (AllData[num][xx].xh2_2 as VT).getValue();
         tempArr[2] = (AllData[num][xx].xh2_3 as VT).getValue();
         tempArr[3] = (AllData[num][xx].xh2_4 as VT).getValue();
         return tempArr;
      }
      
      public static function GetTolal_Data() : *
      {
         var num:uint = 0;
         var xx:uint = 0;
         var i:uint = 0;
         var xxx:uint = 0;
         var sxA1:uint = uint(InitData.BuyNum_0.getValue());
         var sxA2:uint = uint(InitData.BuyNum_0.getValue());
         var sxA3:uint = uint(InitData.BuyNum_0.getValue());
         var sxA4:uint = uint(InitData.BuyNum_0.getValue());
         var sxA5:uint = uint(InitData.BuyNum_0.getValue());
         var sxA6:uint = uint(InitData.BuyNum_0.getValue());
         var sxA7:uint = uint(InitData.BuyNum_0.getValue());
         var sxA8:uint = uint(InitData.BuyNum_0.getValue());
         for(var x:int = 1; x < xingLingData.length; x++)
         {
            num = uint(xingLingData[x][0].getValue());
            if(num > 0)
            {
               sxA1 += (AllData[x][num].sxA1 as VT).getValue();
               sxA2 += (AllData[x][num].sxA2 as VT).getValue();
               sxA3 += (AllData[x][num].sxA3 as VT).getValue();
               sxA4 += (AllData[x][num].sxA4 as VT).getValue();
               sxA5 += (AllData[x][num].sxA5 as VT).getValue();
               sxA6 += (AllData[x][num].sxA6 as VT).getValue();
               sxA7 += (AllData[x][num].sxA7 as VT).getValue();
               sxA8 += (AllData[x][num].sxA8 as VT).getValue();
               if(num >= 10)
               {
                  xx = num / 10;
                  for(i = 1; i <= xx; i++)
                  {
                     xxx = uint(xingLingData[x][i].getValue());
                     if(i == 1)
                     {
                        sxA1 += (AllData[x][10].sxA1 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA2 += (AllData[x][10].sxA2 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA3 += (AllData[x][10].sxA3 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA4 += (AllData[x][10].sxA4 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA5 += (AllData[x][10].sxA5 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA6 += (AllData[x][10].sxA6 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA7 += (AllData[x][10].sxA7 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA8 += (AllData[x][10].sxA8 as VT).getValue() * InitData.xingLingXiShu_Arr[xxx].getValue();
                     }
                     else
                     {
                        sxA1 += ((AllData[x][i * 10].sxA1 as VT).getValue() - (AllData[x][i * 10 - 10].sxA1 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA2 += ((AllData[x][i * 10].sxA2 as VT).getValue() - (AllData[x][i * 10 - 10].sxA2 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA3 += ((AllData[x][i * 10].sxA3 as VT).getValue() - (AllData[x][i * 10 - 10].sxA3 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA4 += ((AllData[x][i * 10].sxA4 as VT).getValue() - (AllData[x][i * 10 - 10].sxA4 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA5 += ((AllData[x][i * 10].sxA5 as VT).getValue() - (AllData[x][i * 10 - 10].sxA5 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA6 += ((AllData[x][i * 10].sxA6 as VT).getValue() - (AllData[x][i * 10 - 10].sxA6 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA7 += ((AllData[x][i * 10].sxA7 as VT).getValue() - (AllData[x][i * 10 - 10].sxA7 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                        sxA8 += ((AllData[x][i * 10].sxA8 as VT).getValue() - (AllData[x][i * 10 - 10].sxA8 as VT).getValue()) * InitData.xingLingXiShu_Arr[xxx].getValue();
                     }
                  }
               }
            }
         }
         var VsxA1:VT = VT.createVT(sxA1);
         var VsxA2:VT = VT.createVT(sxA2);
         var VsxA3:VT = VT.createVT(sxA3);
         var VsxA4:VT = VT.createVT(sxA4);
         var VsxA5:VT = VT.createVT(sxA5);
         var VsxA6:VT = VT.createVT(sxA6);
         var VsxA7:VT = VT.createVT(sxA7);
         var VsxA8:VT = VT.createVT(sxA8);
         getTolal_Data_Arr = [0,VsxA1,VsxA2,VsxA3,VsxA4,VsxA5,VsxA6,VsxA7,VsxA8];
         GetTolal_NUM_UP();
      }
      
      public static function GetTolal_NUM_UP() : *
      {
         var j:uint = 0;
         var x2:uint = 0;
         var num:uint = uint(InitData.Temp0.getValue());
         for(var x:int = 1; x < xingLingData.length; x++)
         {
            for(j = 1; j <= sel_LV_NumMax; j++)
            {
               if(!xingLingData[x][j])
               {
                  xingLingData[x][j] = VT.createVT();
               }
               x2 = uint(xingLingData[x][j].getValue());
               if(x2 >= InitData.Temp5.getValue())
               {
                  num++;
               }
            }
         }
         dianLiangNum = num;
         var lv:uint = 0;
         for(var i2:uint = 1; i2 <= 8; i2++)
         {
            getTolal_Data_Arr2[i2] = VT.createVT();
         }
         if(num >= InitData.xL_Arr[0].getValue())
         {
            getTolal_Data_Arr2[2] = InitData.Temp3000;
         }
         if(num >= InitData.xL_Arr[1].getValue())
         {
            getTolal_Data_Arr2[5] = InitData.Temp1000;
         }
         if(num >= InitData.xL_Arr[2].getValue())
         {
            getTolal_Data_Arr2[1] = InitData.Temp8000;
         }
         if(num >= InitData.xL_Arr[3].getValue())
         {
            getTolal_Data_Arr2[3] = InitData.Temp500;
         }
         if(num >= InitData.xL_Arr[4].getValue())
         {
            getTolal_Data_Arr2[8] = InitData.Temp50;
         }
         if(num >= InitData.xL_Arr[5].getValue())
         {
            getTolal_Data_Arr2[7] = InitData.Temp50;
         }
         if(num >= InitData.xL_Arr[6].getValue())
         {
            getTolal_Data_Arr2[4] = InitData.Temp500;
         }
         if(num >= InitData.xL_Arr[7].getValue())
         {
            getTolal_Data_Arr2[6] = InitData.Temp1000;
         }
         if(num < InitData.xL_Arr[0].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[0].getValue();
         }
         else if(num < InitData.xL_Arr[1].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[1].getValue();
         }
         else if(num < InitData.xL_Arr[2].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[2].getValue();
         }
         else if(num < InitData.xL_Arr[3].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[3].getValue();
         }
         else if(num < InitData.xL_Arr[4].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[4].getValue();
         }
         else if(num < InitData.xL_Arr[5].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[5].getValue();
         }
         else if(num < InitData.xL_Arr[6].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[6].getValue();
         }
         else if(num < InitData.xL_Arr[7].getValue())
         {
            dianLiangNumX = num / InitData.xL_Arr[7].getValue();
         }
      }
      
      public static function GetTolal_DataStr() : Array
      {
         if(getTolal_Data_Arr.length <= 0)
         {
            GetTolal_Data();
         }
         var StrArr:Array = [0,"生命+","魔法+","攻击+","防御+","暴击+","闪避+","破魔+","魔抗+"];
         var tempArr:Array = new Array();
         for(var i:uint = 1; i <= 8; i++)
         {
            tempArr[i] = StrArr[i] + getTolal_Data_Arr[i].getValue();
            tempArr[i + 8] = "(+" + getTolal_Data_Arr2[i].getValue() + ")";
         }
         return tempArr;
      }
      
      public static function GetTolal_Data_VT() : Array
      {
         var tempArr:Array = new Array();
         if(getTolal_Data_Arr.length <= 0)
         {
            GetTolal_Data();
         }
         for(var i:uint = 1; i <= 8; i++)
         {
            tempArr[i] = VT.createVT(getTolal_Data_Arr[i].getValue() + getTolal_Data_Arr2[i].getValue());
         }
         return tempArr;
      }
      
      public static function UP(num:uint, num2:uint) : uint
      {
         var numXX:Number = (xingLingData[num][0] as VT).getValue() + 1;
         if(numXX <= sel_LV_NumMax * 10)
         {
            (xingLingData[num][0] as VT).setValue(numXX);
            GetTolal_Data();
            if(numXX % 10 == 0)
            {
               return 10;
            }
            return numXX % 10;
         }
         return 10;
      }
      
      public static function UP2(num:uint, num2:uint) : *
      {
         if(num2 <= sel_LV_NumMax && (xingLingData[num][num2 + 1] as VT).getValue() == 0)
         {
            (xingLingData[num][num2 + 1] as VT).setValue(1);
            GetTolal_Data();
         }
      }
      
      public static function QiangHua(num:uint, num2:uint) : Boolean
      {
         var r:uint = Math.random() * 100 + 90000;
         if(xingLingData[num][num2].getValue() == 1 && r < qiangHuaArr[1])
         {
            xingLingData[num][num2].setValue(2);
            GetTolal_Data();
            return true;
         }
         if(xingLingData[num][num2].getValue() == 2 && r < qiangHuaArr[2])
         {
            xingLingData[num][num2].setValue(3);
            GetTolal_Data();
            return true;
         }
         if(xingLingData[num][num2].getValue() == 3 && r < qiangHuaArr[3])
         {
            xingLingData[num][num2].setValue(4);
            GetTolal_Data();
            return true;
         }
         if(xingLingData[num][num2].getValue() == 4 && r < qiangHuaArr[4])
         {
            xingLingData[num][num2].setValue(5);
            GetTolal_Data();
            return true;
         }
         if(xingLingData[num][num2].getValue() == 5 && r < qiangHuaArr[5])
         {
            xingLingData[num][num2].setValue(6);
            GetTolal_Data();
            return true;
         }
         return false;
      }
   }
}

