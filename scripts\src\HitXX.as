package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import flash.display.*;
   import flash.events.*;
   import src.other.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4148")]
   public class HitXX extends MovieClip
   {
      
      public static var AllHitXX:Array = [];
      
      public static var baoJiUP:VT = VT.createVT();
      
      public var objArr:Array = [];
      
      public var gongJi_hp:Number;
      
      public var gongJi_hp_MAX:Number;
      
      public var gongJi_Ctr:Number;
      
      public var runArr:Array = [];
      
      public var cross:Boolean;
      
      public var 硬直:int;
      
      public var who:Object;
      
      public var getWho:Object;
      
      public var isCW:Boolean = false;
      
      public var _stage:MovieClip;
      
      public var RL:Boolean;
      
      public var times:int = 0;
      
      public var type:int = 0;
      
      public var space:int = 0;
      
      public var totalTime:int = 0;
      
      public var numValue:Number = 0;
      
      public var speedValue:int = 0;
      
      public var nameXX:String = "";
      
      public var flyName:String = "";
      
      public var jnGoYn:Boolean;
      
      public var isFly:Boolean;
      
      public var jiNengStr:String = "";
      
      public var onlyHit:Boolean = false;
      
      public function HitXX()
      {
         super();
         this._stage = Main._this;
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public static function GongjiNum(fly:Fly) : int
      {
         if(fly._name == "Cwbs14_Fly1")
         {
            return 16;
         }
         if(fly._name == "Cwbs14_Fly3")
         {
            return 14;
         }
         if(fly._name == "Cwbs14_Fly4")
         {
            return 8;
         }
         return 1;
      }
      
      public static function HitEnemy() : *
      {
         var i:int = 0;
         var hitXX:HitXX = null;
         var AA:Boolean = false;
         var j:uint = 0;
         var map:* = undefined;
         var i2:int = 0;
         var bool:Boolean = false;
         var i3:int = 0;
         var flyXX:Fly = null;
         var enX:Enemy = null;
         var playerX:Player = null;
         var xxx:int = 0;
         var yyy:int = 0;
         var bj:Boolean = false;
         var rdm:int = 0;
         var hxx:HitXX = null;
         var rdm2:int = 0;
         var hxx2:HitXX = null;
         var hxx3:HitXX = null;
         var hxx6:HitXX = null;
         var hxx4:HitXX = null;
         var hxx5:HitXX = null;
         var xx:int = 0;
         for(i = 0; i < HitXX.AllHitXX.length; i++)
         {
            hitXX = HitXX.AllHitXX[i] as HitXX;
            if(hitXX.parent is Fly)
            {
               (hitXX.parent as Fly).bingDong = false;
               if((hitXX.parent as Fly)._name == "Cw9_Fly3")
               {
                  (hitXX.parent as Fly).bingDong = true;
               }
               if(!hitXX.cross && hitXX.parent.life != 0)
               {
                  AA = Boolean(JhitTestPoint.hitTestPoint(hitXX,Main.world.MapData));
                  if(!AA)
                  {
                     for(j = 0; j < Main.world.numChildren; j++)
                     {
                        map = Main.world.getChildAt(j);
                        if(map is Map && Boolean(JhitTestPoint.hitTestPoint(hitXX,map.MapData)))
                        {
                           AA = true;
                           break;
                        }
                     }
                  }
                  if(AA)
                  {
                     (hitXX.parent as Fly).life = 0;
                     continue;
                  }
               }
            }
            if(hitXX.who is Player)
            {
               if(Main.gameNum.getValue() == 0 && Boolean(Main.P1P2))
               {
                  for(i2 = 0; i2 < Player.All.length; i2++)
                  {
                     if(hitXX.who != Player.All[i2])
                     {
                        bool = false;
                        for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                        {
                           if(Player.All[i2] == hitXX.objArr[i3])
                           {
                              bool = true;
                              break;
                           }
                        }
                        if(!bool && Player.All[i2].hit && Player.All[i2].hp.getValue() > 0 && hitXX.hitTestObject(Player.All[i2].hit))
                        {
                           if(BaoJi(hitXX.who))
                           {
                              (Player.All[i2] as Player).HpXX(hitXX,true);
                           }
                           else
                           {
                              (Player.All[i2] as Player).HpXX(hitXX);
                           }
                           if((hitXX.who as Player).skin.type >= 100)
                           {
                              new BuffEnemy(hitXX,Player.All[i2] as Player);
                           }
                           hitXX.who.连击计数();
                           hitXX.objArr[hitXX.objArr.length] = Player.All[i2];
                           MusicBox.ActMusicPlayX(hitXX.who.data.skinArr[hitXX.who.data.skinNum],hitXX.who.skin.runType);
                           ACT.Play(hitXX.who,Player.All[i2]);
                           if(hitXX.parent is Fly)
                           {
                              if((hitXX.parent as Fly).life != -1 && (hitXX.parent as Fly).life > 0)
                              {
                                 --(hitXX.parent as Fly).life;
                              }
                           }
                        }
                     }
                  }
               }
               for(i2 = 0; i2 < Enemy.All.length; i2++)
               {
                  bool = false;
                  for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                  {
                     if(Enemy.All[i2] == hitXX.objArr[i3])
                     {
                        bool = true;
                        break;
                     }
                  }
                  if(Enemy.All[i2].hit && Enemy.All[i2].life.getValue() > 0 && !bool && hitXX.hitTestObject(Enemy.All[i2].hit))
                  {
                     if(hitXX.parent is Fly)
                     {
                        flyXX = hitXX.parent;
                        if(flyXX.over2)
                        {
                           continue;
                        }
                        if((hitXX.parent as Fly).life != -1 && (hitXX.parent as Fly).life > 0)
                        {
                           --(hitXX.parent as Fly).life;
                        }
                     }
                     if(BaoJi(hitXX.who))
                     {
                        (Enemy.All[i2] as Enemy).HpXX(hitXX,true);
                     }
                     else
                     {
                        (Enemy.All[i2] as Enemy).HpXX(hitXX);
                     }
                     if((hitXX.who as Player).skin.type >= 100)
                     {
                        new BuffEnemy(hitXX,Enemy.All[i2] as Enemy);
                     }
                     if(hitXX.type >= 100 && hitXX.parent is Fly)
                     {
                        new BuffEnemy(hitXX,Enemy.All[i2] as Enemy);
                     }
                     hitXX.who.连击计数();
                     hitXX.objArr.push(Enemy.All[i2]);
                     if(hitXX.parent is Fly && (hitXX.parent as Fly)._name == "高能喷射")
                     {
                        ACT.Play(hitXX.who,Enemy.All[i2],"光束爆炸");
                     }
                     else
                     {
                        MusicBox.ActMusicPlayX(hitXX.who.data.skinArr[hitXX.who.data.skinNum],hitXX.who.skin.runType);
                        ACT.Play(hitXX.who,Enemy.All[i2]);
                     }
                  }
               }
               for(i2 = 0; i2 < Player2.All.length; i2++)
               {
                  bool = false;
                  for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                  {
                     if(Player2.All[i2] == hitXX.objArr[i3])
                     {
                        bool = true;
                        break;
                     }
                  }
                  if(!bool && Player2.All[i2].hit && Player2.All[i2].hp.getValue() > 0 && hitXX.hitTestObject(Player2.All[i2].hit))
                  {
                     if(BaoJi(hitXX.who))
                     {
                        (Player2.All[i2] as Player2).HpXX(hitXX,true);
                     }
                     else
                     {
                        (Player2.All[i2] as Player2).HpXX(hitXX);
                     }
                     if((hitXX.who as Player).skin.type >= 100)
                     {
                        new BuffEnemy(hitXX,Player2.All[i2] as Player2);
                     }
                     hitXX.who.连击计数();
                     hitXX.objArr[hitXX.objArr.length] = Player2.All[i2];
                     MusicBox.ActMusicPlayX(hitXX.who.data.skinArr[hitXX.who.data.skinNum],hitXX.who.skin.runType);
                     ACT.Play(hitXX.who,Player2.All[i2]);
                     if(hitXX.parent is Fly)
                     {
                        if((hitXX.parent as Fly).life != -1 && (hitXX.parent as Fly).life > 0)
                        {
                           --(hitXX.parent as Fly).life;
                        }
                     }
                  }
               }
               for(i2 = 0; i2 < OtherSkin.All.length; i2++)
               {
                  bool = false;
                  for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                  {
                     if(OtherSkin.All[i2] == hitXX.objArr[i3])
                     {
                        bool = true;
                        break;
                     }
                  }
                  if(OtherSkin.All[i2].hit && !bool && hitXX.hitTestObject(OtherSkin.All[i2].hit) && (MapJG.step == 4 || MapJG.step == 13 || MapJG.step == 22))
                  {
                     if(MapJG.arr[OtherSkin.All[i2].id - 1] - MapJG.attTimes == 1)
                     {
                        oskin = OtherSkin.All[i2];
                        MapJG.attTimes = MapJG.arr[OtherSkin.All[i2].id - 1];
                        OtherSkin.All[i2].skin.gotoAndPlay(2);
                     }
                     if(MapJG.arr[OtherSkin.All[i2].id - 1] - MapJG.attTimes > 1)
                     {
                        if(MapJG.step == 4)
                        {
                           MapJG.step = 6;
                           MapJG.timetemp = 0;
                        }
                        if(MapJG.step == 13)
                        {
                           MapJG.step = 15;
                           MapJG.timetemp = 0;
                        }
                        if(MapJG.step == 22)
                        {
                           MapJG.step = 24;
                           MapJG.timetemp = 0;
                        }
                        MapJG.attTimes = 0;
                     }
                  }
               }
            }
            else if(hitXX.who is Enemy)
            {
               enX = hitXX.who;
               if(Main.gameNum.getValue() == 3000)
               {
                  if(NpcYY._this && NpcYY._this.skin && hitXX.hitTestObject(NpcYY._this.skin.hit))
                  {
                     NpcYY._this.Down();
                  }
               }
               for(i2 = 0; i2 < Player.All.length; i2++)
               {
                  playerX = Player.All[i2];
                  if(playerX.hp.getValue() > 0 && playerX.newLifeTime.getValue() <= InitData.BuyNum_0.getValue() && playerX.hit && hitXX.hitTestObject(playerX.hit))
                  {
                     bool = false;
                     for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                     {
                        if(playerX == hitXX.objArr[i3])
                        {
                           bool = true;
                           break;
                        }
                     }
                     if(!bool)
                     {
                        if(Main.gameNum.getValue() == 3000)
                        {
                           playerX.Hit3000(enX.att_temp);
                        }
                        else if(!SanBi(playerX))
                        {
                           playerX.HpXX(hitXX);
                           enX.skin.MingZong(playerX);
                           enX.skin.MingZong2(playerX,hitXX);
                           if(hitXX.type != 0)
                           {
                              new BuffEffect(hitXX,playerX);
                           }
                        }
                        else
                        {
                           xxx = playerX.x + Math.random() * 100 - 50;
                           yyy = playerX.y + Math.random() * 100 - 150;
                           NewMC.Open("闪避",Main.world.moveChild_Other,xxx,yyy,15,0,true,2);
                        }
                        hitXX.objArr[hitXX.objArr.length] = playerX;
                        if(hitXX.parent is Fly)
                        {
                           if((hitXX.parent as Fly).life != -1 && (hitXX.parent as Fly).life > 0)
                           {
                              --(hitXX.parent as Fly).life;
                           }
                        }
                     }
                  }
               }
            }
            else if((hitXX.who is ChongWu || hitXX.who is ChongWu2) && hitXX.getWho is Player && !hitXX.onlyHit)
            {
               for(i2 = 0; i2 < Enemy.All.length; i2++)
               {
                  bool = false;
                  for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                  {
                     if(Enemy.All[i2] == hitXX.objArr[i3])
                     {
                        bool = true;
                        break;
                     }
                  }
                  if(!bool && Enemy.All[i2].hit && Enemy.All[i2].life.getValue() > 0 && hitXX.hitTestObject(Enemy.All[i2].hit))
                  {
                     bj = Boolean(BaoJi(hitXX.getWho));
                     (Enemy.All[i2] as Enemy).HpXX(hitXX,bj);
                     hitXX.objArr[hitXX.objArr.length] = Enemy.All[i2];
                     ACT.Play2(0,Enemy.All[i2]);
                     if(hitXX.parent is Fly)
                     {
                        hitXX.times = GongjiNum(hitXX.parent);
                        if((hitXX.parent as Fly).bingDong)
                        {
                           if((hitXX.parent as Fly)._name == "Cw9_Fly3")
                           {
                              rdm = Math.random() * 100;
                              if(rdm < 10)
                              {
                                 hxx = new HitXX();
                                 hxx.type = 103;
                                 hxx.space = 27 * 2;
                                 hxx.totalTime = 27 * 2;
                                 hxx.numValue = 0;
                                 new BuffEnemy(hxx,Enemy.All[i2] as Enemy);
                              }
                           }
                        }
                        if((hitXX.parent as Fly)._name == "Cw33_Fly3")
                        {
                           ACT.Play(hitXX.who,Enemy.All[i2],"火眼金睛效果");
                        }
                        if((hitXX.parent as Fly)._name == "Cw31_Fly1")
                        {
                           rdm2 = Math.random() * 100;
                           if(rdm2 < 50)
                           {
                              hxx2 = new HitXX();
                              hxx2.type = 507;
                              hxx2.totalTime = 3 * 27;
                              hxx2.space = 3 * 27;
                              hxx2.numValue = 2;
                              new BuffEnemy(hxx2,Enemy.All[i2]);
                           }
                        }
                        if((hitXX.parent as Fly)._name == "Cw31_Fly2")
                        {
                           hxx3 = new HitXX();
                           hxx3.type = 507;
                           hxx3.totalTime = 3 * 27;
                           hxx3.space = 3 * 27;
                           hxx3.numValue = 2;
                           new BuffEnemy(hxx3,Enemy.All[i2]);
                        }
                        if((hitXX.parent as Fly).life != -1 && (hitXX.parent as Fly).life > 0)
                        {
                           --(hitXX.parent as Fly).life;
                        }
                        if((hitXX.parent as Fly)._name == "Cw34_Fly4")
                        {
                           (Enemy.All[i2] as Enemy).noGongJiTime = 27 * 5;
                        }
                        if((hitXX.parent as Fly)._name == "Cw34_Fly3")
                        {
                           hxx6 = new HitXX();
                           hxx6.type = 511;
                           hxx6.totalTime = 8 * 27;
                           hxx6.space = 8 * 27;
                           hxx6.numValue = 1.12;
                           new BuffEnemy(hxx6,Enemy.All[i2]);
                        }
                        if((hitXX.parent as Fly)._name == "Cw34_Fly1")
                        {
                           hxx4 = new HitXX();
                           hxx4.type = 509;
                           hxx4.totalTime = 5 * 27;
                           hxx4.space = 5 * 27;
                           hxx4.numValue = 0.6;
                           new BuffEnemy(hxx4,Enemy.All[i2]);
                           hxx5 = new HitXX();
                           hxx5.type = 510;
                           hxx5.totalTime = 5 * 27;
                           hxx5.space = 5 * 27;
                           hxx5.numValue = 0.85;
                           new BuffEnemy(hxx5,Enemy.All[i2]);
                           hitXX.onlyHit = true;
                           break;
                        }
                     }
                  }
               }
            }
            else if(hitXX.who is Player2)
            {
               for(i2 = 0; i2 < Player.All.length; i2++)
               {
                  bool = false;
                  for(i3 = 0; i3 < hitXX.objArr.length; i3++)
                  {
                     if(Player.All[i2] == hitXX.objArr[i3])
                     {
                        bool = true;
                        break;
                     }
                  }
                  if(Player.All[i2].hit && (Player.All[i2] as Player).hp.getValue() > 0 && !bool && hitXX.hitTestObject(Player.All[i2].hit))
                  {
                     if(!SanBi(Player.All[i2]))
                     {
                        if(BaoJi(hitXX.who))
                        {
                           (Player.All[i2] as Player).HpXX(hitXX,true);
                        }
                        else
                        {
                           (Player.All[i2] as Player).HpXX(hitXX);
                        }
                     }
                     else
                     {
                        xxx = (Player.All[i2] as Player).x + Math.random() * 100 - 50;
                        yyy = (Player.All[i2] as Player).y + Math.random() * 100 - 150;
                        NewMC.Open("闪避",Main.world.moveChild_Other,xxx,yyy,15,0,true,2);
                     }
                     hitXX.objArr[hitXX.objArr.length] = Player.All[i2];
                     if(hitXX.parent is Fly)
                     {
                        if((hitXX.parent as Fly).life != -1 && (hitXX.parent as Fly).life > 0)
                        {
                           --(hitXX.parent as Fly).life;
                        }
                     }
                  }
               }
            }
         }
         for(var i4:int = Enemy.All.length - 1; i4 >= 0; i4--)
         {
            if(Enemy.All[i4] is Enemy && (Enemy.All[i4] as Enemy).life.getValue() <= 0)
            {
               Enemy.All.splice(i4,1);
            }
         }
         for(i = Player.All.length - 1; i >= 0; i--)
         {
            xx = int((Player.All[i] as Player).hp.getValue());
            if(Boolean(Player.All[i]) && xx <= 0)
            {
               Player.All.splice(i,1);
            }
         }
      }
      
      private static function SanBi(p:*) : Boolean
      {
         var sb:Number = Number(InitData.Temp0.getValue());
         if(PK_UI.PK_ing)
         {
            sb = int(p.use_sanbi.getValue() * InitData.Temp01.getValue() * InitData.Temp01.getValue() * InitData.Temp01.getValue());
         }
         else
         {
            sb = int(p.use_sanbi.getValue() * InitData.Temp01.getValue() * InitData.Temp01.getValue());
         }
         sb = Math.pow(sb,InitData.Temp8.getValue());
         var xx:int = Math.random() * InitData.Temp100.getValue();
         if(sb > xx)
         {
            return true;
         }
         return false;
      }
      
      private static function BaoJi(p:*) : Boolean
      {
         var bj:int = int(p.use_baoji.getValue() / InitData.Temp7.getValue() * InitData.Temp01.getValue()) + baoJiUP.getValue();
         bj = Math.pow(bj,InitData.Temp8.getValue());
         var xx:int = Math.random() * InitData.Temp100.getValue();
         if(bj > xx)
         {
            return true;
         }
         return false;
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         var i:int = 0;
         if(AllHitXX)
         {
            for(i = 0; i < AllHitXX.length; i++)
            {
               if(AllHitXX[i] == this)
               {
                  return;
               }
            }
         }
         var parentMC:MovieClip = this.parent as MovieClip;
         while(parentMC != this._stage)
         {
            if(parentMC is FlyPerson)
            {
               this.硬直 = parentMC.硬直;
               this.times = parentMC.attTimes;
               this.who = this.getWho = parentMC.who;
               if(this.who is ChongWu)
               {
                  this.isCW = true;
                  this.getWho = (parentMC.who as ChongWu).who;
               }
               this.RL = parentMC.RL;
               this.cross = parentMC.cross;
               this.gongJi_hp = parentMC.gongJi_hp;
               this.gongJi_hp_MAX = parentMC.gongJi_hp_MAX;
               this.runArr = [parentMC.runX,parentMC.runY,parentMC.runTime];
               this.type = parentMC.type;
               this.space = parentMC.space;
               this.totalTime = parentMC.totalTime;
               this.numValue = parentMC.numValue;
               AllHitXX.push(this);
               return;
            }
            if(parentMC is Fly)
            {
               if((parentMC as Fly).jnGoYn)
               {
                  this.jnGoYn = true;
               }
               this.nameXX = parentMC.nameXX;
               this.硬直 = parentMC.硬直;
               this.times = parentMC.attTimes;
               this.who = this.getWho = parentMC.who;
               if(this.who is ChongWu || this.who is ChongWu2)
               {
                  this.isCW = true;
                  this.getWho = parentMC.who.who;
               }
               this.RL = parentMC.RL;
               this.cross = parentMC.cross;
               this.gongJi_hp = parentMC.gongJi_hp;
               this.gongJi_hp_MAX = parentMC.gongJi_hp_MAX;
               this.gongJi_Ctr = this.gongJi_hp;
               this.runArr = [parentMC.runX,parentMC.runY,parentMC.runTime];
               this.type = (parentMC as Fly).type;
               this.space = (parentMC as Fly).space;
               this.totalTime = (parentMC as Fly).totalTime;
               this.numValue = (parentMC as Fly).numValue;
               AllHitXX.push(this);
               this.isFly = true;
               this.jiNengStr = (parentMC as Fly).jiNengStr;
               return;
            }
            if(parentMC is Skin_WuQi && parentMC.parent is Player)
            {
               this.who = parentMC.parent;
               this.RL = (this.who as Player).RL;
               if((this.who as Player).jnGoYn)
               {
                  this.jnGoYn = true;
               }
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.gongJi_hp_MAX = this.who.skin.hpMax;
               if(this.who.skin.hpX >= this.who.skin.hpXX.getValue() + 1)
               {
                  SaveXX.Save(2,this.who.skin.hpX,true,false,false);
                  return;
               }
               this.硬直 = this.who.skin.硬直;
               this.times = this.who.skin.attTimes;
               this.runArr = [this.who.skin.runX,this.who.skin.runY,this.who.skin.runTime];
               AllHitXX.push(this);
               this.jiNengStr = parentMC.currentLabel;
               return;
            }
            if(parentMC is Skin_WuQi && parentMC.parent is Player2)
            {
               this.who = parentMC.parent;
               this.RL = (this.who as Player2).RL;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.gongJi_hp_MAX = this.who.skin.hpMax;
               this.硬直 = this.who.skin.硬直;
               this.times = this.who.skin.attTimes;
               this.runArr = [this.who.skin.runX,this.who.skin.runY,this.who.skin.runTime];
               AllHitXX.push(this);
               this.jiNengStr = parentMC.currentLabel;
               return;
            }
            if(parentMC is EnemySkin)
            {
               this.gongJi_hp = parentMC.hpX;
               this.硬直 = parentMC.硬直;
               this.runArr = [parentMC.runX,parentMC.runY,parentMC.runTime];
               this.times = (parentMC as EnemySkin).attTimes;
               this.type = (parentMC as EnemySkin).type;
               this.space = (parentMC as EnemySkin).space;
               this.totalTime = (parentMC as EnemySkin).totalTime;
               this.numValue = (parentMC as EnemySkin).numValue;
               this.jiNengStr = parentMC.currentLabel;
            }
            else if(parentMC is Enemy)
            {
               this.gongJi_hp *= (parentMC as Enemy).hitHpX;
               this.who = parentMC;
               this.RL = (this.who as Enemy).RL;
               AllHitXX.push(this);
               return;
            }
            parentMC = parentMC.parent as MovieClip;
         }
      }
      
      private function onREMOVED_FROM_STAGE(e:*) : *
      {
         var i:int = 0;
         if(AllHitXX)
         {
            for(i = 0; i < AllHitXX.length; i++)
            {
               if(AllHitXX[i] == this)
               {
                  AllHitXX.splice(i,1);
               }
            }
         }
      }
   }
}

