package com.hotpoint.braveManIII.models.equip
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import flash.utils.*;
   import src.*;
   
   public class Equip
   {
      
      public var _base:VT;
      
      public var _baseAttrib:Array = [];
      
      public var _blessAttrib:EquipBaseAttrib = null;
      
      private var _skillAttrib:VT;
      
      private var _newSkill:VT = VT.createVT(0);
      
      private var _reinforceAttrib:EquipReinforce;
      
      private var _gemSlot:Gem;
      
      private var _gemGrid:VT;
      
      private var _createTime:Date = null;
      
      private var _remainingTime:VT = VT.createVT(1);
      
      private var _huanhua:VT = VT.createVT(0);
      
      private var _huanhuaVisible:VT = VT.createVT(0);
      
      private var _explain:String;
      
      private var _classNameHH:String;
      
      public function Equip()
      {
         super();
      }
      
      public static function creatEquip(baseId:Number, baseAttrib:Array, skillAttrib:Number, grid:Number) : Equip
      {
         var equip:Equip = new Equip();
         equip._base = VT.createVT(baseId);
         equip._baseAttrib = baseAttrib;
         equip._skillAttrib = VT.createVT(skillAttrib);
         equip._gemGrid = VT.createVT(grid);
         equip._reinforceAttrib = EquipReinforce.createEquipReinforce(0,0,0);
         equip.setSysTime(Main.serverTime);
         equip.setRemainingTimeNow();
         return equip;
      }
      
      public function get gemGrid() : VT
      {
         trace("禁止get1:",getQualifiedClassName(this));
         return this._gemGrid;
      }
      
      public function set gemGrid(value:VT) : void
      {
         trace("禁止get2:",getQualifiedClassName(this));
         this._gemGrid = value;
      }
      
      public function get base() : VT
      {
         trace("禁止get3:",getQualifiedClassName(this));
         return this._base;
      }
      
      public function set base(value:VT) : void
      {
         trace("禁止get4:",getQualifiedClassName(this));
         this._base = value;
      }
      
      public function get baseAttrib() : Array
      {
         return this._baseAttrib;
      }
      
      public function set baseAttrib(value:Array) : void
      {
         this._baseAttrib = value;
      }
      
      public function get skillAttrib() : VT
      {
         trace("禁止get7:",getQualifiedClassName(this));
         return this._skillAttrib;
      }
      
      public function set skillAttrib(value:VT) : void
      {
         trace("禁止get8:",getQualifiedClassName(this));
         this._skillAttrib = value;
      }
      
      public function get reinforceAttrib() : EquipReinforce
      {
         trace("禁止get9:",getQualifiedClassName(this));
         return this._reinforceAttrib;
      }
      
      public function set reinforceAttrib(value:EquipReinforce) : void
      {
         trace("禁止get10:",getQualifiedClassName(this));
         this._reinforceAttrib = value;
      }
      
      public function get gemSlot() : Gem
      {
         trace("禁止get11:",getQualifiedClassName(this));
         return this._gemSlot;
      }
      
      public function set gemSlot(value:Gem) : void
      {
         trace("禁止get12:",getQualifiedClassName(this));
         this._gemSlot = value;
      }
      
      public function get newSkill() : VT
      {
         trace("禁止get13:",getQualifiedClassName(this));
         return this._newSkill;
      }
      
      public function set newSkill(value:VT) : void
      {
         trace("禁止get14:",getQualifiedClassName(this));
         this._newSkill = value;
      }
      
      public function get createTime() : Date
      {
         trace("禁止get15:",getQualifiedClassName(this));
         return this._createTime;
      }
      
      public function set createTime(value:Date) : void
      {
         trace("禁止get16:",getQualifiedClassName(this));
         this._createTime = value;
      }
      
      public function get remainingTime() : VT
      {
         trace("禁止get17:",getQualifiedClassName(this));
         return this._remainingTime;
      }
      
      public function set remainingTime(value:VT) : void
      {
         trace("禁止get18:",getQualifiedClassName(this));
         this._remainingTime = value;
      }
      
      public function get blessAttrib() : EquipBaseAttrib
      {
         return this._blessAttrib;
      }
      
      public function set blessAttrib(value:EquipBaseAttrib) : void
      {
         this._blessAttrib = value;
      }
      
      public function get huanhua() : VT
      {
         return this._huanhua;
      }
      
      public function set huanhua(value:VT) : void
      {
         this._huanhua = value;
      }
      
      public function get classNameHH() : String
      {
         return this._classNameHH;
      }
      
      public function set classNameHH(value:String) : void
      {
         this._classNameHH = value;
      }
      
      public function get explain() : String
      {
         return this._explain;
      }
      
      public function set explain(value:String) : void
      {
         this._explain = value;
      }
      
      public function get huanhuaVisible() : VT
      {
         return this._huanhuaVisible;
      }
      
      public function set huanhuaVisible(value:VT) : void
      {
         this._huanhuaVisible = value;
      }
      
      public function getId() : Number
      {
         return this._base.getValue();
      }
      
      public function getPosition() : Number
      {
         return EquipFactory.findPosition(this._base.getValue());
      }
      
      public function getFrame() : Number
      {
         return EquipFactory.findFrame(this._base.getValue());
      }
      
      public function getHuanHua() : Number
      {
         return this._huanhua.getValue();
      }
      
      public function getDropLevel() : Number
      {
         return EquipFactory.findDropLevel(this._base.getValue());
      }
      
      public function getDressLevel() : Number
      {
         return EquipFactory.findDressLevel(this._base.getValue());
      }
      
      public function getName() : String
      {
         return EquipFactory.findName(this._base.getValue());
      }
      
      public function getClassName() : String
      {
         if(this._huanhuaVisible.getValue() == 0)
         {
            if(this._huanhua.getValue() == 0)
            {
               return EquipFactory.findClassName(this._base.getValue());
            }
            return this._classNameHH;
         }
         return EquipFactory.findClassName(this._base.getValue());
      }
      
      public function getClassName2() : String
      {
         return EquipFactory.findClassName2(this._base.getValue());
      }
      
      public function getClassName3() : int
      {
         return EquipFactory.findClassName3(this._base.getValue());
      }
      
      public function getClassName4() : String
      {
         return EquipFactory.findClassName4(this._base.getValue());
      }
      
      public function getDescript() : String
      {
         return EquipFactory.findDescript(this._base.getValue());
      }
      
      public function getPrice() : Number
      {
         return Math.round(EquipFactory.findPrice(this._base.getValue()));
      }
      
      public function getReincarnationLimit() : Number
      {
         trace("升星:",this._base.getValue());
         return EquipFactory.findReincarnationLimit(this._base.getValue());
      }
      
      public function getColor() : Number
      {
         return EquipFactory.findColor(this._base.getValue());
      }
      
      public function getIsStrengthen() : Boolean
      {
         return EquipFactory.findIsStrengthen(this._base.getValue());
      }
      
      public function getGrid() : Number
      {
         return this._gemGrid.getValue();
      }
      
      public function getSuitId() : Number
      {
         return EquipFactory.findSuitId(this._base.getValue());
      }
      
      public function getStar() : Number
      {
         return EquipFactory.findStar(this._base.getValue());
      }
      
      public function getJinHua() : Number
      {
         return EquipFactory.findJinHua(this._base.getValue());
      }
      
      public function getQianghuaMAX() : Number
      {
         return EquipFactory.findQianghuaMAX(this._base.getValue());
      }
      
      public function changeHH(hhyc:int) : *
      {
         this._huanhuaVisible.setValue(hhyc);
      }
      
      public function getHHVisible() : int
      {
         return this._huanhuaVisible.getValue();
      }
      
      public function setHuanHua(hhtype:int, cn:String, miaoshu:String) : *
      {
         this._huanhua.setValue(hhtype);
         this._classNameHH = cn;
         this._explain = miaoshu;
      }
      
      public function getHHExplain() : String
      {
         return this._explain;
      }
      
      public function getHP() : Number
      {
         return Number(this.countEquipAllAttrib(EquipBaseAttribTypeConst.HP));
      }
      
      public function getMP() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.MP);
      }
      
      public function getAttack() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.ATTACK);
      }
      
      public function getAttackIgnoreDefense() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.ATTACKIGNOREDEFENSE);
      }
      
      public function getDefense() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.DEFENSE);
      }
      
      public function getMoveSpeed() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.MOVESPEED);
      }
      
      public function getCrit() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.CRIT);
      }
      
      public function getDuck() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.DUCK);
      }
      
      public function getMOKANG() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.MOKANG);
      }
      
      public function getPOMO() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.POMO);
      }
      
      public function getHardValue() : Number
      {
         return this.countEquipAllAttrib(EquipBaseAttribTypeConst.HARDVALUE);
      }
      
      public function getReinforceLevel() : Number
      {
         return this._reinforceAttrib.getLevel();
      }
      
      public function reinforceClear() : *
      {
         this._reinforceAttrib.setAttribType(0);
         this._reinforceAttrib.setAttribValues(0);
         this._reinforceAttrib.setLevel(0);
      }
      
      public function getDefaultTime() : Number
      {
         trace(EquipFactory.findDefaultTime(this._base.getValue()));
         return EquipFactory.findDefaultTime(this._base.getValue());
      }
      
      public function getRemainingTime() : Number
      {
         if(this.getPosition() < 8 && this._remainingTime.getValue() <= 0)
         {
            this._remainingTime.setValue(1);
         }
         return this._remainingTime.getValue();
      }
      
      public function setRemainingTimeNow() : *
      {
         this._remainingTime.setValue(this.getDefaultTime());
      }
      
      public function setRemainingTime(time:VT) : *
      {
         var tt:int = 0;
         var mytime:int = time.getValue();
         var str:String = String(mytime);
         var yea:String = str.substr(0,4);
         var mun:String = str.substr(4,2);
         var day:String = str.substr(6,2);
         var yeanum:int = new int(yea);
         var munnum:int = new int(mun);
         var daynum:int = new int(day);
         var dd:Date = this.getSysTime();
         var nd:Date = new Date(yeanum,munnum - 1,daynum);
         if(nd.getTime() >= dd.getTime())
         {
            tt = Math.floor((nd.getTime() - dd.getTime()) / (1000 * 24 * 60 * 60));
            tt = this.getDefaultTime() - tt;
            if(tt < 0)
            {
               tt = 0;
            }
            this._remainingTime.setValue(tt);
         }
      }
      
      public function setSysTime(time:VT) : *
      {
         var mytime:int = time.getValue();
         if(mytime <= 18991231)
         {
            this._createTime = new Date();
            return;
         }
         var str:String = String(mytime);
         var yea:String = str.substr(0,4);
         var mun:String = str.substr(4,2);
         var day:String = str.substr(6,2);
         var yeanum:int = new int(yea);
         var munnum:int = new int(mun);
         var daynum:int = new int(day);
         this._createTime = new Date(yeanum,munnum - 1,daynum);
      }
      
      public function getSysTime() : Date
      {
         if(this._createTime == null)
         {
            this.setSysTime(Main.serverTime);
         }
         return this._createTime;
      }
      
      public function changeReinforce(level:Number, attribType:uint, value:uint) : void
      {
         this._reinforceAttrib.setLevel(level);
         this._reinforceAttrib.setAttribType(attribType);
         this._reinforceAttrib.setAttribValues(this._reinforceAttrib.getAttribValues() + value);
      }
      
      public function changeReinforceTest() : void
      {
         if(Boolean(this._reinforceAttrib) && this._reinforceAttrib.getAttribType() == EquipBaseAttribTypeConst.DUCK)
         {
            this._reinforceAttrib.setAttribType(EquipBaseAttribTypeConst.DEFENSE);
            this._reinforceAttrib.setAttribValues(int(this._reinforceAttrib.getAttribValues() / 4.4));
         }
      }
      
      public function zhufu3Num() : Number
      {
         var lv:int = this._blessAttrib.getBeishu() - 1;
         var data:Array = Zhufu2Factory.allData[lv];
         if(this.getPosition() == 0 || this.getPosition() == 5 || this.getPosition() == 6 || this.getPosition() == 7)
         {
            return data[6];
         }
         if(this.getPosition() == 2)
         {
            return data[7];
         }
         if(this.getPosition() == 1)
         {
            return data[8];
         }
         if(this.getPosition() == 4)
         {
            return data[9];
         }
         if(this.getPosition() == 3)
         {
            return data[10];
         }
         if(this.getPosition() == 8 || this.getPosition() == 9)
         {
            return data[11];
         }
      }
      
      public function zhufu3tex() : String
      {
         var lv:int = this._blessAttrib.getBeishu() - 1;
         var data:Array = Zhufu2Factory.allData[lv];
         var vipXX:Number = 1.5;
         if(Main.isVip())
         {
            vipXX = 1;
         }
         if(this.getPosition() == 0 || this.getPosition() == 5 || this.getPosition() == 6 || this.getPosition() == 7)
         {
            return "攻击+" + int(data[6] / vipXX);
         }
         if(this.getPosition() == 2)
         {
            return "防御+" + int(data[7] / vipXX);
         }
         if(this.getPosition() == 1)
         {
            return "暴击+" + int(data[8] / vipXX);
         }
         if(this.getPosition() == 4)
         {
            return "魔抗+" + int(data[9] / vipXX);
         }
         if(this.getPosition() == 3)
         {
            return "破魔+" + int(data[10] / vipXX);
         }
         if(this.getPosition() == 8)
         {
            return "生命+" + int(data[11] / vipXX);
         }
         if(this.getPosition() == 9)
         {
            return "魔力+" + int(data[12] / vipXX);
         }
      }
      
      public function showBlessAttrib() : String
      {
         var lv:int = 0;
         var num:* = this._blessAttrib.getBeishu();
         if(num >= 2)
         {
            lv = num - 1;
            return "星灵王祝福" + lv + "级:" + this.zhufu3tex();
         }
         if(num > 0)
         {
            return "星灵祝福二阶:" + EquipBaseAttribTypeConst.getDescription(this._blessAttrib.getAttribType(),this._blessAttrib.getValue() + this._blessAttrib.getBeishuValue());
         }
         return "星灵祝福一阶:" + EquipBaseAttribTypeConst.getDescription(this._blessAttrib.getAttribType(),this._blessAttrib.getValue());
      }
      
      public function getBlessAttrib() : Boolean
      {
         if(this._blessAttrib)
         {
            return true;
         }
         return false;
      }
      
      public function setBlessAttrib() : *
      {
         var arr:Array = EquipFactory.findBlessAttrib(this._base.getValue());
         var rdm:int = int(this.getRandomType());
         this._blessAttrib = DeepCopyUtil.clone(arr[rdm]);
         this._blessAttrib.setBeishuValue(this._blessAttrib.getValue() * this._blessAttrib.getBeishu());
      }
      
      public function setBlessAttribLV2() : *
      {
         this.setRandomBeishu();
         this._blessAttrib.setBeishuValue(this._blessAttrib.getValue() * this._blessAttrib.getBeishu());
      }
      
      public function setBlessAttribLV3() : *
      {
         trace("设置祝福属性 3阶:",this.getPosition(),this._blessAttrib.getAttribType(),this._blessAttrib.getBeishu(),this._blessAttrib.getBeishuValue(),this._blessAttrib.getValue());
         var lv:* = this._blessAttrib.getBeishu() < 2 ? 2 : this._blessAttrib.getBeishu() + 1;
         this._blessAttrib.setBeishu(lv);
         this._blessAttrib.setBeishuValue(0);
         this._blessAttrib.setValue(0);
      }
      
      private function getRandomType() : uint
      {
         var rdm:* = Math.random() * 100;
         if(rdm >= 0 && rdm < 25)
         {
            return 0;
         }
         if(rdm >= 25 && rdm < 50)
         {
            return 1;
         }
         if(rdm >= 50 && rdm < 75)
         {
            return 2;
         }
         return 3;
      }
      
      private function setRandomBeishu() : *
      {
         var rdm:* = Math.random() * 100;
         if(rdm >= 0 && rdm < 20)
         {
            this._blessAttrib.setBeishu(0.2);
         }
         else if(rdm >= 20 && rdm < 40)
         {
            this._blessAttrib.setBeishu(0.4);
         }
         else if(rdm >= 40 && rdm < 60)
         {
            this._blessAttrib.setBeishu(0.6);
         }
         else if(rdm >= 60 && rdm < 80)
         {
            this._blessAttrib.setBeishu(0.8);
         }
         else
         {
            this._blessAttrib.setBeishu(1);
         }
         if(Main.isVip())
         {
            if(Math.random() * 100 > 80)
            {
               this._blessAttrib.setBeishu(1.5);
            }
         }
      }
      
      public function showBaseAttrib() : Array
      {
         var baseAttrib:EquipBaseAttrib = null;
         var str:String = null;
         var allAttrib:Array = [];
         for each(baseAttrib in this._baseAttrib)
         {
            str = EquipBaseAttribTypeConst.getDescription(baseAttrib.getAttribType(),baseAttrib.getValue());
            allAttrib.push([baseAttrib.getColorType(),str]);
         }
         return allAttrib;
      }
      
      public function showReinforceAttrib() : String
      {
         return EquipBaseAttribTypeConst.getDescription(this._reinforceAttrib.getAttribType(),this._reinforceAttrib.getAttribValues());
      }
      
      public function getEquipSkillAttrib() : String
      {
         return SkillFactory.getSkillById(this.getSkillAttrib()).getIntroduction();
      }
      
      public function getEquipNewSkill() : String
      {
         return SkillFactory.getSkillById(this.getNewSkill()).getIntroduction();
      }
      
      public function setInGem(gg:Gem) : Boolean
      {
         if(this._gemGrid.getValue() == 1 || this.gemGrid.getValue() == 0)
         {
            this._gemSlot = gg;
            this._gemGrid.setValue(0);
            return true;
         }
         return false;
      }
      
      public function getOutGem() : Gem
      {
         if(this._gemGrid.getValue() == 0)
         {
            this._gemGrid.setValue(1);
            return this._gemSlot;
         }
         return null;
      }
      
      public function getGemAttrib() : Array
      {
         var attrib:Attribute = null;
         var arr:Array = [];
         if(this._gemSlot.getType() == GemTypeConst.ATTRIBSTONE)
         {
            for each(attrib in this._gemSlot.getGemAttrib())
            {
               arr.push(EquipBaseAttribTypeConst.getDescription(attrib.getAttribType(),attrib.getValue()));
            }
         }
         return arr;
      }
      
      private function countEquipAllAttrib(attribType:Number) : Number
      {
         var baseAttrib:EquipBaseAttrib = null;
         var num:int = 0;
         var attrib:Attribute = null;
         var count:Number = 0;
         for each(baseAttrib in this._baseAttrib)
         {
            if(baseAttrib.getAttribType() == attribType)
            {
               count += baseAttrib.getValue();
            }
         }
         if(this._reinforceAttrib.getLevel() > 0 && this._reinforceAttrib.getAttribType() == attribType)
         {
            num = int(this._reinforceAttrib.getAttribValues());
            count += int(num);
         }
         if(this._gemGrid.getValue() == 0 && this._gemSlot.getType() == GemTypeConst.ATTRIBSTONE)
         {
            for each(attrib in this._gemSlot.getGemAttrib())
            {
               if(attrib.getAttribType() == attribType)
               {
                  count += attrib.getValue();
               }
            }
         }
         if(this._blessAttrib)
         {
            if(this._blessAttrib.getAttribType() == attribType)
            {
               count += this._blessAttrib.getValue();
               if(this._blessAttrib.getBeishuValue() > 0)
               {
                  count += this._blessAttrib.getBeishuValue();
               }
            }
         }
         return count;
      }
      
      public function getSkillAttrib() : Number
      {
         return this._skillAttrib.getValue();
      }
      
      public function getNewSkill() : Number
      {
         return this._newSkill.getValue();
      }
      
      public function setNewSkill(fmId:Number) : *
      {
         this._newSkill.setValue(fmId);
      }
      
      public function getGemSlot() : Gem
      {
         return this._gemSlot;
      }
      
      public function jinhuaEquip() : Equip
      {
         var e:Equip = null;
         e = EquipFactory.createEquipByID(this.getJinHua());
         e._reinforceAttrib = this._reinforceAttrib;
         e._gemGrid = this._gemGrid;
         e._gemSlot = this._gemSlot;
         e._blessAttrib = this._blessAttrib;
         e._newSkill = this._newSkill;
         e._huanhua = this._huanhua;
         e._classNameHH = this._classNameHH;
         e._explain = this._explain;
         return e;
      }
      
      public function reCreatEquip() : Equip
      {
         var e:Equip = null;
         var i:* = undefined;
         var dataXX:EquipBaseAttrib = null;
         var numMax:int = 0;
         trace("装备洗炼 原先装备 >>>",this.baseAttrib.length);
         e = EquipFactory.createEquipByID(this._base.getValue());
         e._gemGrid = this._gemGrid;
         e._gemSlot = this._gemSlot;
         e._newSkill = this._newSkill;
         e._reinforceAttrib = this._reinforceAttrib;
         e._skillAttrib = this._skillAttrib;
         e._blessAttrib = this._blessAttrib;
         e._huanhua = this._huanhua;
         e._classNameHH = this._classNameHH;
         e._explain = this._explain;
         for(i in this.baseAttrib)
         {
            dataXX = this.baseAttrib[i];
            numMax = int(EquipFactory.getValueMax(this._base.getValue(),i));
            if(dataXX.getColorType() == 1)
            {
               trace("洗炼>>",i,dataXX.getColorType(),dataXX.getAttribType(),"?",e.baseAttrib[i].getValue(),dataXX.getValue(),numMax);
               if(dataXX.getValue() > e.baseAttrib[i].getValue())
               {
                  e.baseAttrib[i] = this.baseAttrib[i];
               }
            }
         }
         return e;
      }
      
      public function upStarEquip() : Equip
      {
         var idXXX:int = 0;
         var rr:int = 0;
         var numR:int = 0;
         var arr:Array = null;
         var xxx:* = undefined;
         var i:int = 0;
         var numXX:Number = this.getReincarnationLimit();
         trace("升星装备:",numXX);
         if((this.getFrame() >= 524 && this.getFrame() <= 527 || this.getFrame() >= 529 && this.getFrame() <= 532) && this.getStar() >= 5 && this.getStar() < 10)
         {
            idXXX = numXX / 10;
            rr = Math.random() * 100;
            numR = 10;
            arr = [40,70,85,95];
            for(xxx in arr)
            {
               if(rr < arr[xxx])
               {
                  numR = xxx + 6;
                  break;
               }
            }
            numXX = idXXX * 10 + numR;
            trace("升星装备(随机6~10):",numXX);
         }
         var e:Equip = EquipFactory.createEquipByID(numXX);
         if(e._baseAttrib)
         {
            for(i = 0; i < e._baseAttrib.length; i++)
            {
               if((e._baseAttrib[i] as EquipBaseAttrib).getColorType() == 3)
               {
                  e._baseAttrib.splice(i,1);
                  i--;
               }
            }
            for(i = 0; i < this._baseAttrib.length; i++)
            {
               if((this._baseAttrib[i] as EquipBaseAttrib).getColorType() == 3)
               {
                  e._baseAttrib.push(this._baseAttrib[i]);
               }
            }
         }
         e._gemGrid = this._gemGrid;
         e._gemSlot = this._gemSlot;
         e._newSkill = this._newSkill;
         e._reinforceAttrib = this._reinforceAttrib;
         e._blessAttrib = this._blessAttrib;
         e._huanhua = this._huanhua;
         e._classNameHH = this._classNameHH;
         e._explain = this._explain;
         return e;
      }
      
      public function getClone() : Equip
      {
         return Equip.creatEquip(this._base.getValue(),this._baseAttrib,this._skillAttrib.getValue(),this._gemGrid.getValue());
      }
      
      public function compareById(id:Number) : Boolean
      {
         if(this._base.getValue() == id)
         {
            return true;
         }
         return false;
      }
      
      public function testEquip(ee:Equip) : Boolean
      {
         if(this == ee)
         {
            return true;
         }
         return false;
      }
   }
}

