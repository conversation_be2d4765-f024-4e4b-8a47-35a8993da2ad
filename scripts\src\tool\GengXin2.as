package src.tool
{
   import com.adobe.serialization.json.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol1631")]
   public class GengXin2 extends MovieClip
   {
      
      public var no_btn:SimpleButton;
      
      public var yes_btn:SimpleButton;
      
      public function GengXin2()
      {
         super();
         this.yes_btn.addEventListener(MouseEvent.CLICK,this.OK);
         this.no_btn.addEventListener(MouseEvent.CLICK,this.OK);
      }
      
      private function OK(e:*) : *
      {
         GengXin.reGame();
      }
   }
}

