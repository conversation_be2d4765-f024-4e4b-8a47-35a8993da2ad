package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.repository.probability.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src.*;
   
   public class XiangQianPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var xqPanel:MovieClip;
      
      public static var xqp:XiangQianPanel;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var equip:Equip;
      
      public static var gem:Gem;
      
      public static var oldNum:Number;
      
      private static var loadData:ClassLoader;
      
      private static var goldXX:int;
      
      public static var bagType:Number = 1;
      
      public static var selbool:Boolean = false;
      
      public static var isPOne:Boolean = true;
      
      public static var yeshu:Number = 0;
      
      public static var state:Number = 1;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_XQ_v1903.swf";
      
      public function XiangQianPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!xqPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = xqPanel.getChildIndex(xqPanel["e" + i]);
            mm.x = xqPanel["e" + i].x;
            mm.y = xqPanel["e" + i].y;
            mm.name = "e" + i;
            xqPanel.removeChild(xqPanel["e" + i]);
            xqPanel["e" + i] = mm;
            xqPanel.addChild(mm);
            xqPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = xqPanel.getChildIndex(xqPanel["s" + i]);
            mm.x = xqPanel["s" + i].x;
            mm.y = xqPanel["s" + i].y;
            mm.name = "s" + i;
            xqPanel.removeChild(xqPanel["s" + i]);
            xqPanel["s" + i] = mm;
            xqPanel.addChild(mm);
            xqPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = xqPanel.getChildIndex(xqPanel["c1"]);
         mm.x = xqPanel["c1"].x;
         mm.y = xqPanel["c1"].y;
         mm.name = "c1";
         xqPanel.removeChild(xqPanel["c1"]);
         xqPanel["c1"] = mm;
         xqPanel.addChild(mm);
         xqPanel.setChildIndex(mm,num);
         var mm2:MovieClip = new Shop_picNEW();
         num = xqPanel.getChildIndex(xqPanel["g1"]);
         mm2.x = xqPanel["g1"].x;
         mm2.y = xqPanel["g1"].y;
         mm2.name = "g1";
         xqPanel.removeChild(xqPanel["g1"]);
         xqPanel["g1"] = mm2;
         xqPanel.addChild(mm2);
         xqPanel.setChildIndex(mm2,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("XQShow") as Class;
         xqPanel = new classRef();
         xqp.addChild(xqPanel);
         InitIcon();
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         xqp = new XiangQianPanel();
         LoadSkin();
         Main._stage.addChild(xqp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         xqp = new XiangQianPanel();
         Main._stage.addChild(xqp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(xqPanel)
         {
            Main.stopXX = true;
            xqp.x = 0;
            xqp.y = 0;
            isPOne = true;
            addListenerP1();
            Main._stage.addChild(xqp);
            xqp.visible = true;
            Main.player1.getBag().cheatGem();
            if(Main.P1P2)
            {
               xqPanel["bagOne"].visible = true;
               xqPanel["bagTwo"].visible = true;
            }
         }
         else
         {
            isPOne = true;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(xqPanel)
         {
            xqPanel["xuanze"].gotoAndStop(1);
            isPOne = true;
            bagType = 1;
            gem = null;
            equip = null;
            twoFalse();
            Main.stopXX = false;
            removeListenerP1();
            xqp.visible = false;
            clickObj = null;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         isxqOK();
         xqPanel["bagOne"].isClick = true;
         xqPanel["s1_mc"].stop();
         xqPanel["s2_mc"].stop();
         xqPanel["gold"].text = Main.player1.getGold();
         xqPanel["usegold"].text = "";
         xqPanel["gemName"].text = "";
         xqPanel["equipName"].text = "";
         xqPanel["bagOne"].visible = false;
         xqPanel["bagTwo"].visible = false;
         xqPanel["xuanze"].mouseEnabled = false;
         xqPanel["isXQ"].visible = false;
         xqPanel["isXQ"]["yes_btn"].addEventListener(MouseEvent.CLICK,yesXQ);
         xqPanel["isXQ"]["no_btn"].addEventListener(MouseEvent.CLICK,noXQ);
         xqPanel["isXQ"]["no2_btn"].addEventListener(MouseEvent.CLICK,noXQ);
         xqPanel.addEventListener(BtnEvent.DO_CHANGE,bagListen);
         xqPanel["c1"].addEventListener(MouseEvent.CLICK,changeEquip);
         xqPanel["g1"].addEventListener(MouseEvent.CLICK,changeGem);
         xqPanel["xq_btn"].addEventListener(MouseEvent.CLICK,doXQ);
         xqPanel["close"].addEventListener(MouseEvent.CLICK,closexXQ);
         xqPanel["c1"].mouseChildren = false;
         xqPanel["c1"].addEventListener(MouseEvent.MOUSE_OVER,showE);
         xqPanel["c1"].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         xqPanel["g1"].mouseChildren = false;
         xqPanel["g1"].addEventListener(MouseEvent.MOUSE_OVER,showG);
         xqPanel["g1"].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         xqPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         xqPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         for(var i:uint = 0; i < 24; i++)
         {
            xqPanel["e" + i].mouseChildren = false;
            xqPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xqPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xqPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            xqPanel["s" + i].mouseChildren = false;
            xqPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xqPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xqPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         xqPanel["c1"].gotoAndStop(1);
         xqPanel["c1"].visible = false;
         xqPanel["g1"].visible = false;
         if(isPOne)
         {
            showEquipP1();
         }
         else
         {
            showEquipP2();
         }
         xqPanel["chose"].visible = false;
      }
      
      public static function upGo(e:*) : *
      {
         yeshu = 0;
         if(state == 1)
         {
            if(isPOne)
            {
               showEquipP1();
            }
            else
            {
               showEquipP2();
            }
         }
         else if(isPOne)
         {
            showGemP1();
         }
         else
         {
            showGemP2();
         }
      }
      
      public static function downGo(e:*) : *
      {
         yeshu = 1;
         if(state == 1)
         {
            if(isPOne)
            {
               showEquipP1();
            }
            else
            {
               showEquipP2();
            }
         }
         else if(isPOne)
         {
            showGemP1();
         }
         else
         {
            showGemP2();
         }
      }
      
      private static function showEquipP1() : *
      {
         var i:uint = 0;
         var yeshuNum:int = yeshu + 1;
         xqPanel["yeshu_txt"].text = yeshuNum + "/2";
         state = 1;
         for(i = 0; i < 24; i++)
         {
            xqPanel["e" + i].t_txt.text = "";
            if(Main.player1.getBag().getEquipFromBag(i + yeshu * 24) != null)
            {
               if(Main.player1.getBag().getEquipFromBag(i + yeshu * 24).getGrid() != -1)
               {
                  xqPanel["e" + i].gotoAndStop(Main.player1.getBag().getEquipFromBag(i + yeshu * 24).getFrame());
                  xqPanel["e" + i].visible = true;
               }
               else
               {
                  xqPanel["e" + i].visible = false;
               }
            }
            else
            {
               xqPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            xqPanel["s" + i].t_txt.text = "";
            if(Main.player1.getEquipSlot().getEquipFromSlot(i) != null)
            {
               if(Main.player1.getEquipSlot().getEquipFromSlot(i).getGrid() != -1)
               {
                  xqPanel["s" + i].gotoAndStop(Main.player1.getEquipSlot().getEquipFromSlot(i).getFrame());
                  xqPanel["s" + i].visible = true;
               }
               else
               {
                  xqPanel["s" + i].visible = false;
               }
            }
            else
            {
               xqPanel["s" + i].visible = false;
            }
         }
      }
      
      private static function showEquipP2() : *
      {
         var i:uint = 0;
         var yeshuNum:int = yeshu + 1;
         xqPanel["yeshu_txt"].text = yeshuNum + "/2";
         state = 1;
         for(i = 0; i < 24; i++)
         {
            xqPanel["e" + i].t_txt.text = "";
            if(Main.player2.getBag().getEquipFromBag(i + yeshu * 24) != null)
            {
               if(Main.player2.getBag().getEquipFromBag(i + yeshu * 24).getGrid() != -1)
               {
                  xqPanel["e" + i].gotoAndStop(Main.player2.getBag().getEquipFromBag(i + yeshu * 24).getFrame());
                  xqPanel["e" + i].visible = true;
               }
               else
               {
                  xqPanel["e" + i].visible = false;
               }
            }
            else
            {
               xqPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            xqPanel["s" + i].t_txt.text = "";
            if(Main.player2.getEquipSlot().getEquipFromSlot(i) != null)
            {
               if(Main.player2.getEquipSlot().getEquipFromSlot(i).getGrid() != -1)
               {
                  xqPanel["s" + i].gotoAndStop(Main.player2.getEquipSlot().getEquipFromSlot(i).getFrame());
                  xqPanel["s" + i].visible = true;
               }
               else
               {
                  xqPanel["s" + i].visible = false;
               }
            }
            else
            {
               xqPanel["s" + i].visible = false;
            }
         }
      }
      
      private static function showGemP1() : *
      {
         var i:uint = 0;
         var yeshuNum:int = yeshu + 1;
         xqPanel["yeshu_txt"].text = yeshuNum + "/2";
         state = 2;
         for(i = 0; i < 24; i++)
         {
            xqPanel["e" + i].t_txt.text = "";
            if(Main.player1.getBag().getGemFromBag(i + yeshu * 24) != null)
            {
               if(Main.player1.getBag().getGemFromBag(i + yeshu * 24).getType() == 3)
               {
                  xqPanel["e" + i].gotoAndStop(Main.player1.getBag().getGemFromBag(i + yeshu * 24).getFrame());
                  xqPanel["e" + i].visible = true;
                  if(Main.player1.getBag().getGemFromBag(i + yeshu * 24).getIsPile() == true)
                  {
                     xqPanel["e" + i].t_txt.text = Main.player1.getBag().getGemFromBag(i + yeshu * 24).getTimes();
                  }
               }
               else
               {
                  xqPanel["e" + i].visible = false;
               }
            }
            else
            {
               xqPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            xqPanel["s" + i].visible = false;
         }
      }
      
      private static function showGemP2() : *
      {
         var i:uint = 0;
         var yeshuNum:int = yeshu + 1;
         xqPanel["yeshu_txt"].text = yeshuNum + "/2";
         state = 2;
         for(i = 0; i < 24; i++)
         {
            xqPanel["e" + i].t_txt.text = "";
            if(Main.player2.getBag().getGemFromBag(i + yeshu * 24) != null)
            {
               if(Main.player2.getBag().getGemFromBag(i + yeshu * 24).getType() == 3)
               {
                  xqPanel["e" + i].gotoAndStop(Main.player2.getBag().getGemFromBag(i + yeshu * 24).getFrame());
                  xqPanel["e" + i].visible = true;
                  if(Main.player2.getBag().getGemFromBag(i + yeshu * 24).getIsPile() == true)
                  {
                     xqPanel["e" + i].t_txt.text = Main.player2.getBag().getGemFromBag(i + yeshu * 24).getTimes();
                  }
               }
               else
               {
                  xqPanel["e" + i].visible = false;
               }
            }
            else
            {
               xqPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            xqPanel["s" + i].visible = false;
         }
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         xqPanel["isXQ"]["yes_btn"].removeEventListener(MouseEvent.CLICK,yesXQ);
         xqPanel["isXQ"]["no_btn"].removeEventListener(MouseEvent.CLICK,noXQ);
         xqPanel["isXQ"]["no2_btn"].removeEventListener(MouseEvent.CLICK,noXQ);
         xqPanel["c1"].removeEventListener(MouseEvent.CLICK,changeEquip);
         xqPanel["g1"].removeEventListener(MouseEvent.CLICK,changeGem);
         xqPanel.removeEventListener(BtnEvent.DO_CHANGE,bagListen);
         xqPanel["close"].removeEventListener(MouseEvent.CLICK,closeXQ);
         xqPanel["c1"].removeEventListener(MouseEvent.MOUSE_OVER,showE);
         xqPanel["c1"].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         xqPanel["g1"].removeEventListener(MouseEvent.MOUSE_OVER,showG);
         xqPanel["g1"].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
         for(i = 0; i < 24; i++)
         {
            xqPanel["e" + i].mouseChildren = false;
            xqPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xqPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xqPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            xqPanel["s" + i].mouseChildren = false;
            xqPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xqPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xqPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
      }
      
      public static function closeXQ(e:*) : *
      {
         close();
      }
      
      private static function showE(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         xqPanel.addChild(itemsTooltip);
         if(overobj.name == "c1")
         {
            if(equip)
            {
               itemsTooltip.equipTooltip(equip);
            }
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = xqPanel.mouseX + 10;
         itemsTooltip.y = xqPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function showG(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         xqPanel.addChild(itemsTooltip);
         if(overobj.name == "g1")
         {
            if(gem)
            {
               itemsTooltip.gemTooltip(gem);
            }
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = xqPanel.mouseX + 10;
         itemsTooltip.y = xqPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         xqPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(isPOne)
         {
            if(bagType == 1)
            {
               if(str == "e")
               {
                  overNum += yeshu * 24;
                  if(Main.player1.getBag().getEquipFromBag(overNum) != null)
                  {
                     itemsTooltip.equipTooltip(Main.player1.getBag().getEquipFromBag(overNum),1);
                  }
               }
               else if(str == "s")
               {
                  itemsTooltip.slotTooltip(overNum,Main.player1.getEquipSlot());
               }
            }
            else if(str == "e")
            {
               overNum += yeshu * 24;
               itemsTooltip.gemTooltip(Main.player1.getBag().getGemFromBag(overNum),1);
            }
         }
         else if(bagType == 1)
         {
            if(str == "e")
            {
               overNum += yeshu * 24;
               if(Main.player2.getBag().getEquipFromBag(overNum) != null)
               {
                  itemsTooltip.equipTooltip(Main.player2.getBag().getEquipFromBag(overNum),2);
               }
            }
            else if(str == "s")
            {
               itemsTooltip.slotTooltip(overNum,Main.player2.getEquipSlot());
            }
         }
         else if(str == "e")
         {
            overNum += yeshu * 24;
            itemsTooltip.gemTooltip(Main.player2.getBag().getGemFromBag(overNum),2);
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = xqPanel.mouseX + 10;
         itemsTooltip.y = xqPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      public static function twoFalse() : void
      {
         xqPanel["bagTwo"].isClick = false;
         xqPanel["bagOne"].isClick = false;
      }
      
      private static function bagListen(e:BtnEvent) : void
      {
         var btn:MovieClip = e.target as MovieClip;
         xqPanel["chose"].visible = false;
         switch(btn.name)
         {
            case "bagOne":
               isPOne = true;
               bagType = 1;
               twoFalse();
               equip = null;
               gem = null;
               xqPanel["c1"].visible = false;
               xqPanel["g1"].visible = false;
               xqPanel["equipName"].text = "";
               xqPanel["gemName"].text = "";
               xqPanel["bagOne"].isClick = true;
               xqPanel["gold"].text = Main.player1.getGold();
               showEquipP1();
               break;
            case "bagTwo":
               twoFalse();
               bagType = 1;
               isPOne = false;
               equip = null;
               gem = null;
               xqPanel["c1"].visible = false;
               xqPanel["g1"].visible = false;
               xqPanel["equipName"].text = "";
               xqPanel["gemName"].text = "";
               xqPanel["bagTwo"].isClick = true;
               xqPanel["gold"].text = Main.player2.getGold();
               showEquipP2();
         }
      }
      
      private static function changeEquip(e:*) : *
      {
         xqPanel["chose"].visible = false;
         if(isPOne)
         {
            showEquipP1();
            xqPanel["xuanze"].gotoAndStop(1);
         }
         else
         {
            showEquipP2();
            xqPanel["xuanze"].gotoAndStop(1);
         }
         bagType = 1;
         xqPanel["c1"].visible = false;
         xqPanel["equipName"].text = "";
         xqPanel["usegold"].text = "";
         equip = null;
         isxqOK();
      }
      
      private static function changeGem(e:*) : *
      {
         xqPanel["chose"].visible = false;
         xqPanel["g1"].visible = false;
         xqPanel["gemName"].text = "";
         gem = null;
         isxqOK();
      }
      
      private static function isxqOK() : *
      {
         if(Boolean(equip) && Boolean(gem))
         {
            xqPanel["xq_btn"].visible = true;
         }
         else
         {
            xqPanel["xq_btn"].visible = false;
         }
      }
      
      private static function xiangQian() : *
      {
         xqPanel["xq_btn"].visible = true;
         if(Boolean(equip) && Boolean(gem))
         {
            if(isPOne)
            {
               if(Main.player1.getGold() < goldXX)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
                  return;
               }
               equip.setInGem(gem);
               Main.player1.getBag().delGem(oldNum,1);
               Main.player1.payGold(goldXX);
               AchData.setXqNum(1);
               xqPanel["gold"].text = Main.player1.getGold();
               showEquipP1();
            }
            else
            {
               if(Main.player2.getGold() < goldXX)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
                  return;
               }
               Main.player2.payGold(goldXX);
               equip.setInGem(gem);
               Main.player2.getBag().delGem(oldNum,1);
               AchData.setXqNum(2);
               xqPanel["gold"].text = Main.player2.getGold();
               showEquipP2();
            }
            xqPanel["usegold"].text = "";
            xqPanel["equipName"].text = "";
            xqPanel["gemName"].text = "";
            equip = null;
            gem = null;
            bagType = 1;
            xqPanel["c1"].visible = false;
            xqPanel["g1"].visible = false;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"镶嵌成功");
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"物品类型错误");
         }
      }
      
      private static function doXQ(e:*) : *
      {
         xqPanel["chose"].visible = false;
         if(equip)
         {
            if(equip.getGrid() == 0)
            {
               xqPanel["isXQ"].visible = true;
            }
            else
            {
               xqPanel["s1_mc"].gotoAndPlay(1);
               xqPanel["s2_mc"].gotoAndPlay(1);
               xqPanel["xq_btn"].visible = false;
               setTimeout(xiangQian,2800);
            }
         }
      }
      
      private static function yesXQ(e:*) : *
      {
         xqPanel["s1_mc"].gotoAndPlay(1);
         xqPanel["s2_mc"].gotoAndPlay(1);
         xqPanel["xq_btn"].visible = false;
         setTimeout(xiangQian,2800);
         xqPanel["isXQ"].visible = false;
      }
      
      private static function noXQ(e:*) : *
      {
         xqPanel["isXQ"].visible = false;
      }
      
      private static function closexXQ(e:*) : *
      {
         close();
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         selbool = true;
         clickObj = e.target as MovieClip;
         xqPanel["chose"].x = clickObj.x - 2;
         xqPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         if(isPOne)
         {
            if(bagType == 1)
            {
               if(nameStr == "e")
               {
                  clickNum += yeshu * 24;
                  equip = Main.player1.getBag().getEquipFromBag(clickNum);
               }
               else
               {
                  equip = Main.player1.getEquipSlot().getEquipFromSlot(clickNum);
               }
               goldXX = GoldFactory.getMosaicGold(equip.getDropLevel());
               xqPanel["usegold"].text = "所需金币:" + goldXX;
               xqPanel["c1"].gotoAndStop(clickObj.currentFrame);
               xqPanel["c1"].visible = true;
               xqPanel["equipName"].text = equip.getName();
               setEquipColor(equip);
               showGemP1();
               xqPanel["xuanze"].gotoAndStop(2);
               bagType = 2;
            }
            else
            {
               if(nameStr == "e")
               {
                  clickNum += yeshu * 24;
                  oldNum = clickNum;
                  gem = Main.player1.getBag().getGemFromBag(clickNum);
                  xqPanel["g1"].gotoAndStop(clickObj.currentFrame);
                  xqPanel["g1"].visible = true;
                  xqPanel["gemName"].text = gem.getName();
                  setGemColor(gem);
               }
               xqPanel["chose"].visible = true;
            }
         }
         else if(bagType == 1)
         {
            if(nameStr == "e")
            {
               clickNum += yeshu * 24;
               equip = Main.player2.getBag().getEquipFromBag(clickNum);
            }
            else
            {
               equip = Main.player2.getEquipSlot().getEquipFromSlot(clickNum);
            }
            goldXX = GoldFactory.getMosaicGold(equip.getDropLevel());
            xqPanel["usegold"].text = "所需金币:" + goldXX;
            xqPanel["c1"].gotoAndStop(clickObj.currentFrame);
            xqPanel["c1"].visible = true;
            xqPanel["equipName"].text = equip.getName();
            setEquipColor(equip);
            showGemP2();
            xqPanel["xuanze"].gotoAndStop(2);
            bagType = 2;
         }
         else
         {
            if(nameStr == "e")
            {
               clickNum += yeshu * 24;
               oldNum = clickNum;
               gem = Main.player2.getBag().getGemFromBag(clickNum);
               xqPanel["g1"].gotoAndStop(clickObj.currentFrame);
               xqPanel["g1"].visible = true;
               xqPanel["gemName"].text = gem.getName();
               setGemColor(gem);
            }
            xqPanel["chose"].visible = true;
         }
         isxqOK();
      }
      
      private static function setGemColor(gem:Gem) : *
      {
         if(gem.getColor() == 1)
         {
            ColorX(xqPanel["gemName"],"0xffffff");
         }
         if(gem.getColor() == 2)
         {
            ColorX(xqPanel["gemName"],"0x0066ff");
         }
         if(gem.getColor() == 3)
         {
            ColorX(xqPanel["gemName"],"0xFF33FF");
         }
      }
      
      private static function setEquipColor(equip:Equip) : *
      {
         if(equip.getColor() == 1)
         {
            ColorX(xqPanel["equipName"],"0xffffff");
         }
         if(equip.getColor() == 2)
         {
            ColorX(xqPanel["equipName"],"0x0066ff");
         }
         if(equip.getColor() == 3)
         {
            ColorX(xqPanel["equipName"],"0xFF33FF");
         }
         if(equip.getColor() == 4)
         {
            ColorX(xqPanel["equipName"],"0xFF9900");
         }
         if(equip.getColor() == 5)
         {
            ColorX(xqPanel["equipName"],"0xFF9900");
         }
         if(equip.getColor() == 6)
         {
            ColorX(xqPanel["equipName"],"0xCC3300");
         }
      }
      
      private static function ColorX(t:*, col:String) : *
      {
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         t.setTextFormat(myTextFormat);
      }
   }
}

