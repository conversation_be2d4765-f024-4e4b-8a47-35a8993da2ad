package src.tool
{
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol310")]
   public class CengHao extends MovieClip
   {
      
      public var who:Player;
      
      public var frame:uint = 1;
      
      public function CengHao()
      {
         super();
         addFrameScript(0,this.frame1);
         gotoAndStop(1);
         this.frame = 1;
      }
      
      public function Show(num:int) : *
      {
         if(Main["player" + num].getTitleSlot().getTitleView())
         {
            this.frame = Main["player" + num].getTitleSlot().getTitleView().getFrame();
            this.gotoAndStop(this.frame);
         }
         else
         {
            this.frame = 1;
            gotoAndStop(1);
         }
      }
      
      internal function frame1() : *
      {
         stop();
      }
   }
}

