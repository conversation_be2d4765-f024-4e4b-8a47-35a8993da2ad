package src
{
   import com.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol2680")]
   public class PK_JiFen_UI extends MovieClip
   {
      
      public static var _this:PK_JiFen_UI;
      
      public var _t1_txt:TextField;
      
      public var _t1x_txt:TextField;
      
      public var _t2_txt:TextField;
      
      public var _t2x_txt:TextField;
      
      public var _t3_txt:TextField;
      
      public var _t3x_txt:TextField;
      
      public var _t4_txt:TextField;
      
      public var _t4x_txt:TextField;
      
      public var _t5_txt:TextField;
      
      public var _t5x_txt:TextField;
      
      public var _t6_txt:TextField;
      
      public var _t6x_txt:TextField;
      
      public var _t7_txt:TextField;
      
      public var submit_btn:SimpleButton;
      
      public function PK_JiFen_UI()
      {
         super();
         this.submit_btn.addEventListener(MouseEvent.CLICK,this.submit_Fun);
      }
      
      public static function Open(dead:Boolean = true, jiFen:int = 0) : *
      {
         Main.allClosePanel();
         NewThis();
         _this.Show(dead,jiFen);
         _this.x = _this.y = 0;
         _this.visible = true;
         Main._stage.addChild(_this);
      }
      
      public static function Close() : *
      {
         NewThis();
         _this.x = _this.y = 5000;
         _this.visible = false;
      }
      
      private static function NewThis() : *
      {
         if(!_this)
         {
            _this = new PK_JiFen_UI();
         }
      }
      
      private function submit_Fun(e:*) : *
      {
         Close();
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.Loading();
         Player.一起信春哥();
         PK_UI.Open();
         WinShow.All_0();
      }
      
      private function Show(dead:Boolean = true, jiFen:int = 0) : *
      {
         var _t2_str:String = null;
         var _t3_Str:String = null;
         var _t4_str:String = null;
         var _t5_str:String = null;
         var _t6_str:String = null;
         var time8:uint = 27 * 60 * 8 - PK_UI.Pk_timeNum;
         if(time8 / 27 % 60 < 10)
         {
            _t3_Str = uint(time8 / 27 / 60) + ":0" + uint(time8 / 27 % 60);
         }
         else
         {
            _t3_Str = uint(time8 / 27 / 60) + ":" + uint(time8 / 27 % 60);
         }
         if(Main.P1P2)
         {
            _t2_str = Main.player1.level.getValue() + "," + Main.player2.level.getValue();
         }
         else
         {
            _t2_str = Main.player1.level.getValue();
         }
         _t4_str = WinShow.txt_3;
         if(dead)
         {
            _t5_str = "生存";
         }
         else
         {
            _t5_str = "死亡";
         }
         var killNum:int = PK_UI.killNum70up.getValue() + PK_UI.killNum70down.getValue();
         this._t1_txt.text = killNum;
         this._t2_txt.text = _t2_str;
         this._t3_txt.text = _t3_Str;
         this._t4_txt.text = _t4_str;
         this._t5_txt.text = _t5_str;
         this._t1x_txt.text = GameData.jifenArr[2];
         this._t2x_txt.text = GameData.jifenArr[1];
         this._t3x_txt.text = int(GameData.jifenArr[3]);
         this._t4x_txt.text = GameData.jifenArr[5];
         this._t5x_txt.text = GameData.jifenArr[4];
         this._t7_txt.text = jiFen;
      }
   }
}

