package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.skill.Skill;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import src.other.*;
   import src.tool.*;
   
   public class Player2 extends MovieClip
   {
      
      public static var LV_data:XML;
      
      public static var enemySkillMC:*;
      
      public static var All:Array = [];
      
      public static var PlayerMcLoaded:Boolean = false;
      
      public static var PlayerMcArr:Array = new Array();
      
      public static var num_hp:VT = VT.createVT();
      
      public static var num_mp:VT = VT.createVT();
      
      public static var num_gj:VT = VT.createVT();
      
      public static var num_fy:VT = VT.createVT();
      
      public static var num_sb:VT = VT.createVT();
      
      public static var num_bj:VT = VT.createVT();
      
      public static var num_sd:VT = VT.createVT();
      
      public static var num_1:VT = VT.createVT();
      
      public static var num_15:VT = VT.createVT();
      
      public static var num_20:VT = VT.createVT();
      
      public static var num_25:VT = VT.createVT();
      
      public static var num_30:VT = VT.createVT();
      
      public static var num_35:VT = VT.createVT();
      
      public static var num_7:VT = VT.createVT();
      
      public static var num_27:VT = VT.createVT();
      
      public static var num_13:VT = VT.createVT();
      
      private static var Temp11:VT = VT.createVT(VT.GetTempVT("10+1/10"));
      
      public var idXX:String = "??";
      
      public var pkmc:PKmc = new PKmc();
      
      public var lifeMC:EnemyLife = new EnemyLife();
      
      public var noMove:Boolean = false;
      
      private var tempXXX2:VT = VT.createVT(VT.GetTempVT("8/4"));
      
      public var hitXX:HitXX;
      
      public var shuangShouBeiShu:Number = 1;
      
      public var energySlot:EnergySlot = new EnergySlot();
      
      public var skin:Skin;
      
      public var cengHao_mc:CengHao = new CengHao();
      
      public var skin_Z:Skin_ZhuangBei;
      
      public var skin_Z2:Skin_ZhuangBei;
      
      public var skin_Z3:Skin_ZhuangBei;
      
      public var skin_Z2_V:Boolean = true;
      
      public var skin_Z3_V:Boolean = true;
      
      public var headFrame:int = 1;
      
      public var skin_W:Skin_WuQi;
      
      public var playerCW:ChongWu;
      
      public var hit:MovieClip = null;
      
      public var deadX:Dead;
      
      public var 职业附加:Array = [1,1,1,1];
      
      public var 套装强化:Array = [0,0,0,0,0,false,false,false,false];
      
      public var 装备附加:Array = [];
      
      public var time:int = 1000;
      
      public var lianJi:VT = VT.createVT(0);
      
      public var lianJiTime:VT = VT.createVT(60);
      
      public var data:PlayerData;
      
      public var nextExp:VT = VT.createVT();
      
      public var hp:VT = VT.createVT();
      
      public var mp:VT = VT.createVT();
      
      public var hp_Max:VT = VT.createVT();
      
      public var mp_Max:VT = VT.createVT();
      
      public var gongji:VT = VT.createVT();
      
      public var fangyu:VT = VT.createVT();
      
      public var baoji:VT = VT.createVT();
      
      public var sanbi:VT = VT.createVT();
      
      public var walk_power:VT = VT.createVT();
      
      public var yingzhi:VT = VT.createVT();
      
      public var use_hp_Max:VT = VT.createVT();
      
      public var use_mp_Max:VT = VT.createVT();
      
      public var use_gongji:VT = VT.createVT();
      
      public var use_fangyu:VT = VT.createVT();
      
      public var use_baoji:VT = VT.createVT();
      
      public var use_sanbi:VT = VT.createVT();
      
      public var AllSkillCD:Array = new Array();
      
      public var AllSkillCDXX:Array = new Array();
      
      public var All_ObjCD:Array = new Array();
      
      public var All_ObjCDXX:Array = new Array();
      
      public var RL:Boolean = true;
      
      private var jump_power:* = 145;
      
      private var jump_time:* = 8;
      
      private var parabola:Number = 0.3;
      
      private var jumping:int = 0;
      
      private var jumpX2:Boolean = false;
      
      private var jumpType:int = 2;
      
      private var gravity:int = 2;
      
      private var gravityNum:* = 0;
      
      private var SwitchingTime:int;
      
      public var jnGoYn:Boolean;
      
      public var noHit:Boolean = false;
      
      public var noJiFen:Boolean = false;
      
      public var fangYuPOWER:VT = VT.createVT();
      
      private var KeyArrStr:Array = ["上","下","左","右","攻击","跳","切换","技能1","技能2","技能3","技能4","消耗1","消耗2","消耗3","转职","怪物"];
      
      public var dead_Visible:Boolean = false;
      
      public var dead_Visible_Time:uint = 0;
      
      private var getAllMoveSpeed:VT = VT.createVT(-99);
      
      private var getAllSuitSkill:Array;
      
      private var getAllEquipSkill:Array;
      
      public var noHead:Boolean = false;
      
      public var distance_X:int;
      
      public var distance_Y:int;
      
      private var RunArr:Array = ["攻击1","上挑","下斩","跑攻","技能1","技能2","技能3","技能4","转职技能1","转职技能2","转职技能3","转职技能4"];
      
      private var runXYArr:Array = [[120,120,150,200,300,200,300,500,200,160,350,120],[400,150,150,400,400,350,400,300,300,400,200,300],[60,80,200,80,200,200,220,350,150,400,200,80],[250,160,160,400,300,160,260,500,260,450,300,280]];
      
      public var flyTime:int = 0;
      
      public var fly:Boolean = false;
      
      private var runX:int = 0;
      
      private var runY:int = 0;
      
      private var runArr:Array = new Array();
      
      public function Player2()
      {
         super();
         mouseEnabled = mouseChildren = false;
         this.deadX = new Dead();
         addChild(this.deadX);
         this.deadX.visible = false;
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public static function Init() : *
      {
         num_hp = VT.createVT(1);
         num_mp = VT.createVT(1);
         num_gj = VT.createVT(1);
         num_fy = VT.createVT(1);
         num_sb = VT.createVT(1);
         num_bj = VT.createVT(1);
         num_sd = VT.createVT(0);
         num_1 = VT.createVT(0.1);
         num_15 = VT.createVT(0.15);
         num_20 = VT.createVT(0.2);
         num_25 = VT.createVT(0.25);
         num_30 = VT.createVT(0.3);
         num_35 = VT.createVT(0.35);
         num_7 = VT.createVT(0.07);
         num_13 = VT.createVT(0.13);
         num_27 = VT.createVT(0.27);
      }
      
      public static function getPlayerLvData(pd:PlayerData) : XML
      {
         var i:* = undefined;
         var lv:int = 0;
         for(i in Player.LV_data.角色)
         {
            lv = int(Player.LV_data.角色[i].等级);
            if(lv == pd.getLevel())
            {
               return Player.LV_data.角色[i];
            }
         }
         trace("找不到该等级信息");
         return null;
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         Main.world.moveChild_Enemy.addChild(this);
         All[All.length] = this;
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function 不信春哥() : *
      {
         if(this.dead_Visible)
         {
            ++this.dead_Visible_Time;
            if(this.dead_Visible_Time > 160)
            {
               this.deadX.visible = false;
               this.pkmc.visible = false;
               this.lifeMC.visible = false;
               this.x = this.y = 5000;
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
               this.skin.Over();
               this.parent.removeChild(this);
            }
            return;
         }
         if(!this.deadX.visible)
         {
            NewMC.Open("死亡倒计时2",this.parent,this.x,this.y,160);
            trace("死亡倒计时2");
            --PK_UI.PlayerNum_XX;
            this.dead_Visible = true;
            Main.player_1.MpUp(40,2);
            if(Main.P1P2)
            {
               Main.player_2.MpUp(40,2);
            }
         }
         this.deadX.frame = this.headFrame;
         this.deadX.head_mc.gotoAndStop(this.deadX.frame);
         this.deadX.visible = true;
         this.skin_Z.visible = false;
         this.skin_W.visible = false;
         if(this.skin_Z2)
         {
            this.skin_Z2.visible = false;
         }
         if(this.skin_Z3)
         {
            this.skin_Z3.visible = false;
         }
         this.energySlot.setZero();
      }
      
      public function 信春哥() : *
      {
         this.deadX.visible = false;
         this.skin.visible = true;
         this.skin_Z.visible = true;
         this.skin_W.visible = true;
         this.PK_hpMax();
         this.hp.setValue(9999999);
         this.mp.setValue(9999999);
      }
      
      private function onREMOVED_FROM_STAGE(e:*) : *
      {
         this.skin.Over();
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function ExpUP(num:int, type:int = 1) : String
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         this.data.setEXP(this.data.getEXP() + num);
         if(this.data.getEXP() >= this.nextExp.getValue())
         {
            return this.LevelUP(type);
         }
         return "ok";
      }
      
      public function LevelUP(type:int = 1) : *
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         var levelX:int = this.data.getLevel();
         if(levelX < 50 || levelX < Player.maxLevel && this.data.isRebirth())
         {
            if(type == 1)
            {
               this.data.setEXP(0);
               this.LevelUP_X();
            }
            else if(type == 2)
            {
               while(this.data.getEXP() >= this.nextExp.getValue() && (this.data.getLevel() < 50 || this.data.getLevel() < Player.maxLevel && this.data.isRebirth()))
               {
                  this.data.setEXP(this.data.getEXP() - this.nextExp.getValue());
                  this.LevelUP_X();
               }
            }
            NewMC.Open("升级效果",this);
            if(this.data.getLevel() == 50)
            {
            }
         }
      }
      
      public function LevelUP_X() : *
      {
         this.data.setLevel(this.data.getLevel() + VT.GetTempVT("8/8"));
         TiaoShi.tempVar = "data.getLevel() = " + this.data.getLevel() + " , data.getEXP() = " + this.data.getEXP();
         if(this.data.isRebirth())
         {
            this.data.addPoint(VT.GetTempVT("10/2"));
         }
         else
         {
            this.data.addPoint(VT.GetTempVT("8/2"));
         }
         this.LoadPlayerLvData();
         this.信春哥();
      }
      
      public function MoneyUP(m:int) : *
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         this.data.addGold(m);
      }
      
      public function Load_All_Player_Data() : *
      {
         this.LoadPlayerLvData();
         this.GetAllSkillCD();
         this.GetAllObjCD();
         this.newSkin();
         this.信春哥();
         this.left_Reight();
      }
      
      public function left_Reight() : *
      {
         if(this.data.getEquipSkillSlot().getGemFromSkillSlot(0) != null)
         {
            this.energySlot.energyLeftNum.setValue(0);
            this.energySlot.energyLeftMax.setValue(SkillFactory.getSkillById(this.data.getEquipSkillSlot().getGemFromSkillSlot(0).getGemSkill()).getEp());
         }
         if(this.data.getEquipSkillSlot().getGemFromSkillSlot(1) != null)
         {
            this.energySlot.energyRightNum.setValue(0);
            this.energySlot.energyRightMax.setValue(SkillFactory.getSkillById(this.data.getEquipSkillSlot().getGemFromSkillSlot(1).getGemSkill()).getEp());
         }
      }
      
      public function LoadPlayerLvData() : *
      {
         this.双手加成();
         var xml:XML = Player.getPlayerLvData(this.data);
         this.nextExp.setValue(int(xml.经验));
         var hpMaxX:int = int(xml.HP) + this.data.getEquipSlot().getAllHP();
         this.hp_Max.setValue(hpMaxX);
         var mpMaxX:int = int(xml.MP) + this.data.getEquipSlot().getAllMP();
         this.mp_Max.setValue(mpMaxX);
         var gongJiX:int = (int(xml.攻击) + this.data.getEquipSlot().getAllAttack()) * this.shuangShouBeiShu;
         this.gongji.setValue(gongJiX);
         var fangyuX:int = int(xml.防御) + this.data.getEquipSlot().getAllDefense();
         this.fangyu.setValue(fangyuX);
         var baojibaojiX:int = int(xml.暴击) + this.data.getEquipSlot().getAllCrit();
         this.baoji.setValue(baojibaojiX);
         var sanbiX:int = int(xml.闪避) + this.data.getEquipSlot().getAllDuck();
         this.sanbi.setValue(sanbiX);
         var yingzhiX:int = int(xml.硬值) + this.data.getEquipSlot().getAllHardValue();
         this.yingzhi.setValue(yingzhiX);
         this.walk_power.setValue(6 + this.data.getEquipSlot().getAllMoveSpeed());
         this.LoadAll_D_Skill();
      }
      
      private function LoadAll_D_Skill() : *
      {
         var i:int = 0;
         var s:String = null;
         var arr:Array = null;
         var num:Number = NaN;
         var num2:Number = NaN;
         for(i in this.data.getSkillArr())
         {
            s = (this.data.getSkillArr()[i][0] as String).substr(0,1);
            if(s == "d")
            {
               if(this.data.getSkillArr()[i][0] == "d1" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.jumpX2 = true;
               }
               else if(this.data.getSkillArr()[i][0] == "d2" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.hp_Max.setValue(this.hp_Max.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d3" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.mp_Max.setValue(this.mp_Max.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d4" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.baoji.setValue(this.baoji.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d5" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.sanbi.setValue(this.sanbi.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d6" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[0] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d7" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[1] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d8" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[2] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "k16" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[3] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d9" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.套装强化[0] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d10" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.套装强化[1] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d11" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.套装强化[2] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d12" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  this.套装强化[3] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d13" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  this.套装强化[4] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d14" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[5] = true;
               }
               else if(this.data.getSkillArr()[i][0] == "d15" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[6] = true;
               }
               else if(this.data.getSkillArr()[i][0] == "d16" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[7] = true;
               }
               else if(this.data.getSkillArr()[i][0] == "k16" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[8] = true;
               }
            }
         }
      }
      
      private function getEquipData(now:Boolean = false) : *
      {
         var getAllEquipSkillXX:Array = null;
         var i:int = 0;
         if(this.getAllMoveSpeed.getValue() == -99)
         {
            this.getAllMoveSpeed.setValue(this.data.getEquipSlot().getAllMoveSpeed());
         }
         if(!this.getAllSuitSkill)
         {
            this.getAllSuitSkill = this.data.equipSlot.getAllSuitSkill();
         }
         if(!this.getAllEquipSkill)
         {
            getAllEquipSkillXX = this.data.getEquipSlot().getAllEquipSkill();
            this.getAllEquipSkill = new Array();
            for(i in getAllEquipSkillXX)
            {
               this.getAllEquipSkill[i] = SkillFactory.getSkillById(getAllEquipSkillXX[i]);
            }
         }
      }
      
      private function LoadAll_ZB_Skill() : *
      {
         this.getEquipData();
         this.use_hp_Max.setValue(this.hp_Max.getValue());
         this.use_mp_Max.setValue(this.mp_Max.getValue());
         this.use_gongji.setValue(this.gongji.getValue());
         this.use_fangyu.setValue(this.fangyu.getValue());
         this.use_baoji.setValue(this.baoji.getValue());
         this.use_sanbi.setValue(this.sanbi.getValue());
         this.walk_power.setValue(6 + this.getAllMoveSpeed.getValue());
         this.reSet();
         this.装备技能();
         this.TaoZhuangXiaoGuo();
         this.zengFu();
         this.huiZhangJiaCheng();
         this.chongwujiacheng();
         this.QiangHuaJiaCheng();
         this.ALLcompute();
         this.chenghaoJiaCheng();
         this.PK_hpMax();
         this.ZB_HpMpUP();
         this.数值溢出修正();
      }
      
      private function PK_hpMax() : *
      {
         if(Main.gameNum.getValue() == 999)
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue() * InitData.pkHpMaxNum_Player.getValue());
            this.use_mp_Max.setValue(this.use_mp_Max.getValue() * InitData.pkHpMaxNum_Player.getValue());
         }
         else
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue());
            this.use_mp_Max.setValue(this.use_mp_Max.getValue());
         }
      }
      
      private function 装备技能() : *
      {
         var i:int = 0;
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         var num:Number = NaN;
         for(i in this.getAllEquipSkill)
         {
            idX = int(this.getAllEquipSkill[i].getSkillActOn());
            sk_ValueArr = this.getAllEquipSkill[i].getSkillValueArray();
            if(idX == 32)
            {
               if(this.lianJi.getValue() > sk_ValueArr[1].getValue())
               {
                  num = Number(sk_ValueArr[0].getValue());
                  num_gj.setValue(num_gj.getValue() + num);
               }
            }
            else if(idX == 35)
            {
               num = this.use_baoji.getValue() + sk_ValueArr[0].getValue();
               this.use_baoji.setValue(num);
            }
            else if(idX == 200)
            {
               if(Boolean(this.data.getEquipSlot().getEquipFromSlot(6)) && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
               {
                  num_fy.setValue(num_fy.getValue() + sk_ValueArr[0].getValue());
                  num_gj.setValue(num_gj.getValue() + sk_ValueArr[1].getValue());
               }
            }
            else if(idX == 201)
            {
               if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  num_hp.setValue(num_hp.getValue() + sk_ValueArr[0].getValue());
                  num_bj.setValue(num_bj.getValue() + sk_ValueArr[1].getValue());
               }
            }
         }
      }
      
      private function ZB_HpMpUP() : *
      {
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         for(i in this.getAllEquipSkill)
         {
            idX = int(this.getAllEquipSkill[i].getSkillActOn());
            sk_ValueArr = this.getAllEquipSkill[i].getSkillValueArray();
            if(idX == 33)
            {
               if(this.time % (sk_ValueArr[0].getValue() - this.套装强化[0]) == 0)
               {
                  num = sk_ValueArr[1].getValue() * this.use_mp_Max.getValue();
                  this.MP_UP(num);
               }
            }
            else if(idX == 34)
            {
               if(this.time % (sk_ValueArr[0].getValue() - this.套装强化[1]) == 0)
               {
                  num = sk_ValueArr[1].getValue() * this.use_hp_Max.getValue();
                  this.HP_UP(num);
               }
            }
         }
      }
      
      private function huiZhangJiaCheng() : *
      {
         num_hp.setValue(this.data.getBadgeSlot().getHP() + num_hp.getValue());
         num_bj.setValue(this.data.getBadgeSlot().getCRIT() + num_bj.getValue());
         num_gj.setValue(this.data.getBadgeSlot().getATT() + num_gj.getValue());
         num_fy.setValue(this.data.getBadgeSlot().getDEF() + num_fy.getValue());
         num_mp.setValue(this.data.getBadgeSlot().getMP() + num_mp.getValue());
         num_sd.setValue(this.data.getBadgeSlot().getSPEED() + num_sd.getValue());
      }
      
      private function zengFu() : *
      {
         var i:int = 0;
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         var arr:Array = this.data.equipSlot.getAllEquipNewSkill();
         for(i in arr)
         {
            idX = int(SkillFactory.getSkillById(arr[i]).getSkillActOn());
            sk_ValueArr = SkillFactory.getSkillById(arr[i]).getSkillValueArray();
            if(idX == 39)
            {
               num_gj.setValue(num_gj.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 40)
            {
               num_bj.setValue(num_bj.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 41)
            {
               num_sb.setValue(num_sb.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 42)
            {
               num_hp.setValue(num_hp.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 43)
            {
               num_fy.setValue(num_fy.getValue() + sk_ValueArr[0].getValue());
            }
         }
      }
      
      private function reSet() : *
      {
         num_gj.setValue(InitData.BuyNum_1.getValue());
         num_fy.setValue(InitData.BuyNum_1.getValue());
         num_hp.setValue(InitData.BuyNum_1.getValue());
         num_mp.setValue(InitData.BuyNum_1.getValue());
         num_sb.setValue(InitData.BuyNum_1.getValue());
         num_bj.setValue(InitData.BuyNum_1.getValue());
         num_sd.setValue(InitData.BuyNum_0.getValue());
      }
      
      private function ALLcompute() : *
      {
         this.use_hp_Max.setValue(int(this.use_hp_Max.getValue() * num_hp.getValue()));
         this.use_mp_Max.setValue(int(this.use_mp_Max.getValue() * num_mp.getValue()));
         this.use_gongji.setValue(this.use_gongji.getValue() * num_gj.getValue());
         this.use_fangyu.setValue(this.use_fangyu.getValue() * num_fy.getValue());
         this.use_sanbi.setValue(this.use_sanbi.getValue() * num_sb.getValue());
         this.use_baoji.setValue(this.use_baoji.getValue() * num_bj.getValue());
         this.walk_power.setValue(this.walk_power.getValue() + num_sd.getValue());
      }
      
      private function chenghaoJiaCheng() : *
      {
         if(this.data.getTitleSlot().getTitleAttrib())
         {
            this.use_hp_Max.setValue(this.data.getTitleSlot().getTitleAttrib().getHP() + this.use_hp_Max.getValue());
            this.use_baoji.setValue(this.data.getTitleSlot().getTitleAttrib().getCrit() + this.use_baoji.getValue());
            this.use_gongji.setValue(this.data.getTitleSlot().getTitleAttrib().getAttack() + this.use_gongji.getValue());
            this.use_fangyu.setValue(this.data.getTitleSlot().getTitleAttrib().getDefense() + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(this.data.getTitleSlot().getTitleAttrib().getMP() + this.use_mp_Max.getValue());
            this.walk_power.setValue(this.data.getTitleSlot().getTitleAttrib().getMoveSpeed() + this.walk_power.getValue());
         }
      }
      
      private function chongwujiacheng() : *
      {
         if(this.playerCW)
         {
            num_hp.setValue(this.playerCW.data.getLife() + num_hp.getValue());
            num_bj.setValue(this.playerCW.data.getCrit() + num_bj.getValue());
            num_gj.setValue(this.playerCW.data.getAtt() + num_gj.getValue());
            num_fy.setValue(this.playerCW.data.getDef() + num_fy.getValue());
         }
      }
      
      private function ZhuFu3_up() : *
      {
         var eX:Equip = null;
         var lv:int = 0;
         var data:Array = null;
         for(i = 0; i < 8; ++i)
         {
            eX = this.data.getEquipSlot().getEquipFromSlot(i);
            if(eX && eX._blessAttrib && eX._blessAttrib.getBeishu() >= 2)
            {
               if(!(this.data.skinNum == 0 && i == 5))
               {
                  if(!(this.data.skinNum == 1 && i == 2))
                  {
                     lv = eX._blessAttrib.getBeishu() - 1;
                     data = Zhufu2Factory.allData[lv];
                     if(eX.getPosition() == 0 || eX.getPosition() == 5 || eX.getPosition() == 6 || eX.getPosition() == 7)
                     {
                        this.use_gongji.setValue(this.use_gongji.getValue() + data[6]);
                     }
                     else if(eX.getPosition() == 2)
                     {
                        this.use_fangyu.setValue(this.use_fangyu.getValue() + data[7]);
                     }
                     else if(eX.getPosition() == 1)
                     {
                        this.use_baoji.setValue(this.use_baoji.getValue() + data[8]);
                     }
                     else if(eX.getPosition() == 4)
                     {
                        use_fangyu2.setValue(use_fangyu2.getValue() + data[9]);
                     }
                     else if(eX.getPosition() == 3)
                     {
                        use_gongji2.setValue(use_gongji2.getValue() + data[10]);
                     }
                     else if(eX.getPosition() == 8)
                     {
                        this.use_hp_Max.setValue(this.use_hp_Max.getValue() + data[11]);
                     }
                     else if(eX.getPosition() == 9)
                     {
                        this.use_mp_Max.setValue(this.use_mp_Max.getValue() + data[12]);
                     }
                  }
               }
            }
         }
      }
      
      private function QiangHuaJiaCheng() : *
      {
         var sLV:int = this.data.getEquipSlot().getSuitStrength();
         if(sLV < 4)
         {
            return;
         }
         switch(sLV)
         {
            case 4:
               num_hp.setValue(num_hp.getValue() + num_1.getValue());
               break;
            case 5:
               num_hp.setValue(num_hp.getValue() + num_15.getValue());
               num_sb.setValue(num_sb.getValue() + num_1.getValue());
               break;
            case 6:
               num_hp.setValue(num_hp.getValue() + num_15.getValue());
               num_sb.setValue(num_sb.getValue() + num_15.getValue());
               num_sd.setValue(num_sd.getValue() + 1);
               break;
            case 7:
               num_hp.setValue(num_hp.getValue() + num_15.getValue());
               num_sb.setValue(num_sb.getValue() + num_20.getValue());
               num_fy.setValue(num_fy.getValue() + num_7.getValue());
               num_sd.setValue(num_sd.getValue() + 1);
               break;
            case 8:
               num_hp.setValue(num_hp.getValue() + num_20.getValue());
               num_sb.setValue(num_sb.getValue() + num_25.getValue());
               num_fy.setValue(num_fy.getValue() + num_7.getValue());
               num_sd.setValue(num_sd.getValue() + 1);
               break;
            case 9:
               num_hp.setValue(num_hp.getValue() + num_20.getValue());
               num_sb.setValue(num_sb.getValue() + num_30.getValue());
               num_fy.setValue(num_fy.getValue() + num_1.getValue());
               num_sd.setValue(num_sd.getValue() + 1);
               break;
            case 10:
               num_hp.setValue(num_hp.getValue() + num_27.getValue());
               num_sb.setValue(num_sb.getValue() + num_35.getValue());
               num_fy.setValue(num_fy.getValue() + num_15.getValue());
               num_sd.setValue(num_sd.getValue() + 2);
               num_gj.setValue(num_gj.getValue() + num_20.getValue());
         }
      }
      
      private function TaoZhuangXiaoGuo() : *
      {
         var i:int = 0;
         var hpXX:int = 0;
         var lianjiX:int = 0;
         var attack:Number = NaN;
         for(i in this.getAllSuitSkill)
         {
            if(this.getAllSuitSkill[i] == 57200)
            {
               hpXX = this.hp_Max.getValue() * 0.3;
               if(this.hp.getValue() < hpXX)
               {
                  num_gj.setValue(num_gj.getValue() + num_13.getValue());
               }
            }
            else if(this.getAllSuitSkill[i] == 57201)
            {
               hpXX = this.hp_Max.getValue() * 0.4;
               if(this.hp.getValue() < hpXX)
               {
                  num_fy.setValue(num_fy.getValue() + num_20.getValue());
               }
            }
            else if(this.getAllSuitSkill[i] == 57210)
            {
               lianjiX = this.lianJi.getValue();
               if(lianjiX > 2000)
               {
                  lianjiX = 2000;
               }
               attack = lianjiX / 60 * 0.02;
               if(attack > 3)
               {
                  attack = 1;
               }
               num_gj.setValue(num_gj.getValue() + attack);
            }
         }
      }
      
      private function 数值溢出修正() : *
      {
         if(this.hp.getValue() > this.use_hp_Max.getValue())
         {
            this.hp.setValue(this.use_hp_Max.getValue());
         }
         if(this.mp.getValue() > this.use_mp_Max.getValue())
         {
            this.mp.setValue(this.use_mp_Max.getValue());
         }
      }
      
      private function HP_UP(num:int) : *
      {
         var hpXX:int = num + this.hp.getValue();
         if(hpXX > this.use_hp_Max.getValue())
         {
            this.hp.setValue(this.use_hp_Max.getValue());
         }
         else
         {
            this.hp.setValue(hpXX);
         }
      }
      
      private function MP_UP(num:int) : *
      {
         var mpXX:int = num + this.mp.getValue();
         if(mpXX > this.use_mp_Max.getValue())
         {
            this.mp.setValue(this.use_mp_Max.getValue());
         }
         else
         {
            this.mp.setValue(mpXX);
         }
      }
      
      public function 连击计数() : *
      {
         this.lianJiTime = VT.createVT();
         this.lianJi.setValue(this.lianJi.getValue() + 1);
      }
      
      private function 连击计时() : *
      {
         if(this.lianJiTime.getValue() < 60)
         {
            this.lianJiTime.setValue(this.lianJiTime.getValue() + 1);
         }
         else
         {
            this.lianJi.setValue(0);
         }
      }
      
      public function GetAllSkillCD() : *
      {
         var i:int = 0;
         this.AllSkillCD = this.data.skillCdArr();
         this.AllSkillCDXX = DeepCopyUtil.clone(this.AllSkillCD);
         for(i in this.AllSkillCD)
         {
            this.AllSkillCDXX[i][2] = 1;
            this.AllSkillCDXX[i][1] = 0;
            if(this.data.getSkillLevel(this.AllSkillCDXX[i][0]) > 0)
            {
               this.AllSkillCDXX[i][2] = 50;
               this.AllSkillCDXX[i][1] = this.AllSkillCD[i][1];
            }
         }
      }
      
      public function GetAllObjCD() : *
      {
         var i:int = 0;
         this.All_ObjCD = this.data.ObjCdArr();
         this.All_ObjCDXX = DeepCopyUtil.clone(this.All_ObjCD);
         for(i in this.All_ObjCD)
         {
            this.All_ObjCDXX[i][2] = 50;
         }
      }
      
      public function GetKeyArr(arr:Array) : *
      {
         var i:int = 0;
         if(arr.length == this.data._keyArr.length)
         {
            for(i = 0; i < this.data._keyArr.length; i++)
            {
               if(!(arr[i] is int))
               {
                  trace("GetKeyArr按键设置 参数错误#1 数组类型错误");
               }
            }
            this.KeyArr = this.data._keyArr;
         }
         else
         {
            trace("GetKeyArr按键设置 参数错误#2 数组长度错误");
         }
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         ++this.time;
         if(this.hp.getValue() <= 0 || !PK_UI.PK_ing)
         {
            this.hp.setValue(0);
            this.skin.visible = false;
            this.不信春哥();
            if(!PK_UI.PK_ing)
            {
               this.deadX.visible = false;
            }
            return;
         }
         this.visible = true;
         this.连击计时();
         this.LoadAll_ZB_Skill();
         if(Main.world)
         {
            this.CDtime();
            this.SkinValue();
            this.KeyControl();
            this.MoveData();
            this.MoveRun();
         }
      }
      
      public function 双手加成() : *
      {
         var arr:Array = null;
         var leftWQ:* = undefined;
         var rightWQ:* = undefined;
         var i:int = 0;
         this.shuangShouBeiShu = 1;
         if(this.data.isTransferOk())
         {
            arr = this.data.getTransferOk();
            leftWQ = this.data.getEquipSlot().getEquipFromSlot(2).getPosition();
            rightWQ = this.data.getEquipSlot().getEquipFromSlot(5).getPosition();
            for(i in arr)
            {
               if(arr[i] == 0)
               {
                  if(leftWQ == rightWQ && rightWQ == 5)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(arr[i] == 1)
               {
                  if(leftWQ == rightWQ && rightWQ == 6)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(arr[i] == 2)
               {
                  if(leftWQ == rightWQ && rightWQ == 7)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
            }
         }
      }
      
      private function HeadXX() : *
      {
         var z2Frame:int = 0;
         var newHeadFrame:int = 0;
         var headEquip:Equip = this.data.getEquipSlot().getEquipFromSlot(0);
         var equipXX:Equip = this.data.getEquipSlot().getEquipFromSlot(1);
         if(Main.water.getValue() != 1)
         {
            headEquip = this.data.getEquipSlot().getEquipFromSlot(8);
         }
         if(this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            z2Frame = int(this.data.getEquipSlot().getEquipFromSlot(6).getClassName2());
            this.headFrame = z2Frame;
            this.noHead = false;
         }
         else if(Boolean(this.data) && Boolean(headEquip))
         {
            newHeadFrame = int(headEquip.getClassName());
            this.headFrame = newHeadFrame;
            this.noHead = false;
            if(newHeadFrame == 24)
            {
               if(equipXX && equipXX.getClassName() == "甲红10" && Main.water.getValue() == 1)
               {
                  this.noHead = true;
               }
               else
               {
                  this.noHead = false;
               }
               TiaoShi.txtShow("发型0 = ???");
            }
            if(Boolean(equipXX) && equipXX.getClassName() == "甲红6")
            {
               if(Main.water.getValue() != 1)
               {
                  eadFrame = 1;
                  this.noHead = false;
               }
               else
               {
                  eadFrame = 53;
                  this.noHead = true;
               }
               TiaoShi.txtShow("发型1 = " + equipXX.getClassName());
            }
         }
         else
         {
            this.headFrame = 1;
            if(equipXX && equipXX.getClassName() == "甲红10" && Main.water.getValue() == 1)
            {
               this.noHead = true;
            }
            else
            {
               this.noHead = false;
            }
            if(Boolean(equipXX) && equipXX.getClassName() == "甲红6")
            {
               if(Main.water.getValue() != 1)
               {
                  eadFrame = 1;
                  this.noHead = false;
               }
               else
               {
                  eadFrame = 53;
                  this.noHead = true;
               }
            }
         }
      }
      
      private function CDtime() : *
      {
         var i:int = 0;
         for(i in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[i][1] < this.AllSkillCD[i][1] && this.data.getSkillLevel(this.AllSkillCDXX[i][0]) > 0)
            {
               ++this.AllSkillCDXX[i][1];
               this.AllSkillCDXX[i][2] = int(this.AllSkillCDXX[i][1] / this.AllSkillCD[i][1] * 50);
            }
         }
         for(i in this.All_ObjCDXX)
         {
            if(this.All_ObjCDXX[i][1] < this.All_ObjCD[i][1])
            {
               ++this.All_ObjCDXX[i][1];
               this.All_ObjCDXX[i][2] = int(this.All_ObjCDXX[i][1] / this.All_ObjCD[i][1] * 50);
            }
         }
      }
      
      private function selCD(str:String) : int
      {
         var i:int = 0;
         for(i in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[i][0] == str)
            {
               if(this.AllSkillCDXX[i][1] == this.AllSkillCD[i][1])
               {
                  return i;
               }
               return -1;
            }
         }
         return -1;
      }
      
      private function sel_objCD(str:String) : int
      {
         var i:int = 0;
         for(i in this.All_ObjCD)
         {
            if(this.All_ObjCDXX[i][0] == str)
            {
               if(this.All_ObjCDXX[i][1] == this.All_ObjCD[i][1])
               {
                  return i;
               }
               return -1;
            }
         }
         return -1;
      }
      
      public function HpXX(hitX:*, bj:Boolean = false) : *
      {
         var playHPxx:Number = NaN;
         var fangYU:int = 0;
         var rdm:Number = NaN;
         if(this.noHit)
         {
            this.noHit = false;
            return;
         }
         if(hitX is HitXX)
         {
            this.hitXX = hitX;
         }
         var gongJi_hpX:Number = Number(hitX.gongJi_hp);
         var attTimes:int = int(hitX.times);
         var p:Player = hitX.who as Player;
         playHPxx = p.use_gongji.getValue();
         var 职业附加:Number = 1;
         var hpX:int = 0;
         NewMC.Open("被攻击",this);
         if(hitX.who is Player)
         {
            if(p.data.skinArr[p.data.skinNum] == 0)
            {
               职业附加 = Number(p.职业附加[0]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 1)
            {
               职业附加 = Number(p.职业附加[1]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 2)
            {
               职业附加 = Number(p.职业附加[2]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 3)
            {
               职业附加 = Number(p.职业附加[3]);
            }
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  gongJi_hpX *= 0.3;
               }
            }
            fangYU = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            playHPxx = p.use_gongji.getValue() + Enemy.gongJiPOWER.getValue();
            hpX = gongJi_hpX * 职业附加 * playHPxx - fangYU / attTimes;
            if(hpX < 0)
            {
               hpX = 0;
            }
            else
            {
               {};
            }
            hpX += gongJi_hpX * (Math.random() * 3 + 2) / 100;
            if(bj)
            {
               hpX *= this.tempXXX2.getValue();
            }
         }
         if(Main.gameNum.getValue() == 999 && hpX > hitX.gongJi_hp_MAX)
         {
            hpX = int(hitX.gongJi_hp_MAX);
         }
         var hpHP:int = this.hp.getValue() - hpX;
         if(Main.tiaoShiYN)
         {
            hpHP -= Enemy.gongJiPOWER.getValue();
         }
         if(hpHP <= 0)
         {
            this.hp.setValue(0);
            if(!this.noJiFen)
            {
               if(this.data.level.getValue() > 70)
               {
                  PK_UI.killNum70up.setValue(PK_UI.killNum70up.getValue() + 1);
               }
               else
               {
                  PK_UI.killNum70down.setValue(PK_UI.killNum70down.getValue() + 1);
               }
            }
         }
         else
         {
            this.hp.setValue(hpHP);
            trace("~~~~~~~~~~~~~~~~~~~~~~~~~~剩余血量 = " + this.hp.getValue());
         }
         var lifeX:int = 100 - this.hp.getValue() / this.use_hp_Max.getValue() * 100;
         this.lifeMC.gotoAndStop(lifeX);
         var xxx:int = this.x + Math.random() * 100 - 50;
         var yyy:int = this.y + Math.random() * 100 - 50;
         if(hpX == 0)
         {
            NewMC.Open("闪避2",Main.world.moveChild_Other,xxx,yyy,15,0,true,2);
            this.hp.setValue(0);
            TiaoShi.txtShow("清除抵御玩家,重新加载~~~~");
            return;
         }
         if(!bj)
         {
            NewMC.Open("_被打数字",Main.world.moveChild_Other,xxx,yyy,15,hpX,true,2);
         }
         else
         {
            NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpX,true);
         }
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14455 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14461 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14462 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14463 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14464 || this.data.getEquipSlot().getEquipFromSlot(6).getId() == 14465)
            {
               rdm = Math.random() * 20;
               if(rdm < 1)
               {
                  if(Main.gameNum.getValue() == 999)
                  {
                     this.hp.setValue(this.hp.getValue() + Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player2.getValue()));
                     trace("huixue回血效果 = " + Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player2.getValue()));
                     NewMC.Open("回血效果",this,0,0,0,Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player2.getValue()));
                  }
                  else
                  {
                     this.hp.setValue(this.hp.getValue() + Math.round(this.use_hp_Max.getValue() / 5));
                     NewMC.Open("回血效果",this,0,0,0,Math.round(this.use_hp_Max.getValue() / 5));
                  }
               }
            }
         }
         var XX:int = (hitX.硬直 - this.skin.被攻击硬直) / 2;
         if(XX > this.skin.continuousTime && XX >= 0)
         {
            this.skin.GoTo("被打",XX);
         }
         this.runArr = new Array();
         if(hitX.RL)
         {
            this.runPower(hitX.runArr[0],hitX.runArr[1],hitX.runArr[2]);
         }
         else
         {
            this.runPower(-hitX.runArr[0],hitX.runArr[1],hitX.runArr[2]);
         }
      }
      
      private function SkinValue() : *
      {
         if(this.skin != null)
         {
            this.gravity = this.skin.gravity;
            if(this.skin.moveArr != null)
            {
               if(this.RL)
               {
                  this.runPower(this.skin.moveArr[0],this.skin.moveArr[1],this.skin.moveArr[2]);
               }
               else
               {
                  this.runPower(-this.skin.moveArr[0],this.skin.moveArr[1],this.skin.moveArr[2]);
               }
            }
         }
      }
      
      private function WhereAreYou() : *
      {
         var i:int = 0;
         var dx2:* = undefined;
         if(Player.All.length > 0)
         {
            this.distance_X = this.x - Player.All[0].x;
            this.distance_Y = this.y - Player.All[0].y;
            for(i = 1; i < Player.All.length; i++)
            {
               dx2 = this.x - Player.All[i].x;
               if(Math.abs(dx2) < Math.abs(this.distance_X))
               {
                  this.distance_X = dx2;
                  this.distance_Y = this.y - Player.All[i].y;
               }
            }
         }
         else
         {
            this.distance_X = 0;
         }
      }
      
      private function KeyControl() : *
      {
         var zhiYe:int = 0;
         var xxx:int = 0;
         var jumpNum:int = 0;
         var ii:int = 0;
         var absDistance_X:int = 0;
         if(!this.skin)
         {
            trace("skin is null !!");
            return;
         }
         if(this.skin.continuousTime > 0)
         {
            return;
         }
         this.WhereAreYou();
         if(this.skin.stopRun)
         {
            zhiYe = int(this.data.skinArr[this.data.skinNum]);
            xxx = 0;
            if(zhiYe == 0)
            {
               xxx = 150;
            }
            else if(zhiYe == 1)
            {
               xxx = 300;
            }
            else if(zhiYe == 2)
            {
               xxx = 150;
            }
            else if(zhiYe == 3)
            {
               xxx = 150;
            }
            if(this.distance_X < -xxx)
            {
               if(!this.noMove)
               {
                  this.runPower(this.walk_power.getValue() * 1.5,0,1);
                  this.getRL(true);
                  this.SkinPlay("跑");
               }
            }
            else if(this.distance_X > xxx)
            {
               if(!this.noMove)
               {
                  this.runPower(-this.walk_power.getValue() * 1.5,0,1);
                  this.getRL(false);
                  this.SkinPlay("跑");
               }
            }
            else
            {
               jumpNum = 2;
               ii = Math.random() * (this.RunArr.length + jumpNum);
               if(ii > this.RunArr.length && (this.jumping == 0 || Boolean(this.jumpX2) && this.jumping < 2))
               {
                  this.SkinPlay("跳");
                  this.runPower(0,145,8,"跳");
                  return;
               }
               if(this.distance_X > 10)
               {
                  this.getRL(false);
               }
               else if(this.distance_X < -10)
               {
                  this.getRL(true);
               }
               absDistance_X = Math.abs(this.distance_X);
               if(absDistance_X < this.runXYArr[zhiYe][ii])
               {
                  this.SkinPlay(this.RunArr[ii]);
               }
            }
         }
      }
      
      public function CanSkill(skill_ID:String, lv:int) : Boolean
      {
         var tempMP:int = 0;
         var tempCd:int = 0;
         var s:Skill = SkillFactory.getSkillByTypeIAndLevel(skill_ID,lv);
         if(s != null)
         {
            tempMP = this.mp.getValue() - s.getMp();
            tempCd = int(this.selCD(skill_ID));
            if(tempMP <= 0)
            {
               return false;
            }
            if(tempCd == -1)
            {
               return false;
            }
            this.mp.setValue(tempMP);
            this.AllSkillCDXX[tempCd][1] = 0;
            return true;
         }
         return true;
      }
      
      private function MoveData() : *
      {
         this.runX = this.runY = 0;
         for(var i:int = this.runArr.length - 1; i >= 0; i--)
         {
            if(this.runArr[i][2] > 0)
            {
               this.runX += this.runArr[i][0] + this.runArr[i][3] * this.runArr[i][2];
               this.runY -= this.runArr[i][1] + this.runArr[i][4] * this.runArr[i][2];
               --this.runArr[i][2];
            }
            else
            {
               this.runArr.splice(i,1);
            }
         }
         if(this.runY < 0)
         {
            this.jumpType = 1;
            this.gravityNum = 0;
         }
         else
         {
            this.jumpType = 2;
            if(this.gravity != 0)
            {
               this.gravityNum += 1;
            }
            this.runY += this.gravity * this.gravityNum;
         }
      }
      
      private function MoveRun() : *
      {
         var xxYN:Boolean = false;
         var AA:Boolean = false;
         var i2:uint = 0;
         var map:* = undefined;
         var BB2:Boolean = false;
         var AA2:Boolean = false;
         var CC:Boolean = false;
         var i3:uint = 0;
         var AA3:Boolean = false;
         var i4:uint = 0;
         var AA4:Boolean = false;
         var i5:uint = 0;
         var AA5:Boolean = false;
         var i6:uint = 0;
         if(this.runX > 0)
         {
            xxYN = true;
         }
         var xx:int = Math.abs(this.runX);
         var yy:int = Math.abs(this.runY);
         var forecast_x:int = this.x + Main.world.x;
         var forecast_y:int = this.y;
         var mapX:int = int(Main.world.x);
         for(var i:int = yy; i > 0; i--)
         {
            if(this.jumpType == 1)
            {
               AA = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 100,true));
               if(!AA)
               {
                  for(i2 = 0; i2 < Main.world.numChildren; i2++)
                  {
                     map = Main.world.getChildAt(i2);
                     if(map is Map && Boolean(map.MapData1.hitTestPoint(forecast_x,forecast_y - 100,true)))
                     {
                        AA = true;
                        break;
                     }
                  }
               }
               if(!AA)
               {
                  forecast_y--;
               }
            }
            else if(this.jumpType == 2)
            {
               BB2 = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 3,true));
               AA2 = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 1,true));
               CC = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y + 6,true));
               for(i3 = 0; i3 < Main.world.numChildren; i3++)
               {
                  map = Main.world.getChildAt(i3);
                  if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x,forecast_y - 3,true)))
                  {
                     BB2 = true;
                  }
                  else if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x,forecast_y - 1,true)))
                  {
                     AA2 = true;
                  }
                  else if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x,forecast_y + 6,true)))
                  {
                     CC = true;
                  }
               }
               if(BB2)
               {
                  forecast_y -= 2;
                  this.jumping = 0;
               }
               else if(AA2)
               {
                  this.runY = 0;
                  this.gravityNum = 0;
                  this.jumping = 0;
               }
               else if(CC)
               {
                  forecast_y += 3;
                  this.jumping = 0;
               }
               else
               {
                  forecast_y++;
               }
            }
            else if(this.jumpType == 3)
            {
               AA3 = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 5,true));
               for(i4 = 0; i4 < Main.world.numChildren; i4++)
               {
                  map = Main.world.getChildAt(i4);
                  if(map is Map && Boolean(map.MapData1.hitTestPoint(forecast_x,forecast_y - 5,true)))
                  {
                     AA3 = true;
                  }
               }
               if(!AA3)
               {
                  this.jumpType = 2;
                  break;
               }
               forecast_y += 2;
            }
         }
         this.y = forecast_y;
         for(var ii:int = xx; ii > 0; ii--)
         {
            if(xxYN && !Main.world.MapData1.hitTestPoint(forecast_x + 20,forecast_y - 50,true))
            {
               AA4 = false;
               for(i5 = 0; i5 < Main.world.numChildren; i5++)
               {
                  map = Main.world.getChildAt(i5);
                  if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x + 20,forecast_y - 50,true)))
                  {
                     AA4 = true;
                     break;
                  }
               }
               if(!AA4)
               {
                  forecast_x++;
               }
            }
            else if(!xxYN && !Main.world.MapData1.hitTestPoint(forecast_x - 20,forecast_y - 50,true))
            {
               AA5 = false;
               for(i6 = 0; i6 < Main.world.numChildren; i6++)
               {
                  map = Main.world.getChildAt(i6);
                  if(map is Map && Boolean(map.MapData.hitTestPoint(forecast_x - 20,forecast_y - 50,true)))
                  {
                     AA5 = true;
                     break;
                  }
               }
               if(!AA5)
               {
                  forecast_x--;
               }
            }
         }
         this.x = forecast_x - Main.world.x;
         if(this.x < -200 - Main.world.x)
         {
            this.x = -200 - Main.world.x;
         }
         else if(this.x > 940 + 200 - Main.world.x)
         {
            this.x = 940 + 200 - Main.world.x;
         }
      }
      
      public function runPower(numX:Number = 0, numY:Number = 0, time:int = 0, str:String = "") : *
      {
         var i:int = 0;
         if(str == "跳")
         {
            for(i = this.runArr.length - 1; i >= 0; i--)
            {
               if(this.runArr[i][5] == "跳")
               {
                  this.runArr.splice(i,1);
                  this.jumping += 1;
               }
            }
         }
         if(time <= 3)
         {
            this.runArr[this.runArr.length] = [numX,numY,1,0,0,str];
            return;
         }
         var numXX:* = numX / time * this.parabola;
         var numYY:* = numY / time * this.parabola;
         var gravityX:Number = numX * (1 - this.parabola) / (time * time - time * (time - 1) / 2);
         var gravityY:Number = numY * (1 - this.parabola) / (time * time - time * (time - 1) / 2);
         this.runArr[this.runArr.length] = [numXX,numYY,time,gravityX,gravityY,str];
      }
      
      public function getKeyStatus(str:*, type:int = 1) : Boolean
      {
         var i:int = 0;
         var Arr:Array = null;
         var j:int = 0;
         if(str is String)
         {
            for(i = 0; i < this.data._keyArr.length; i++)
            {
               if(this.KeyArrStr[i] == str)
               {
                  return BasicKey.getKeyState(this.data._keyArr[i],type);
               }
            }
            return false;
         }
         if(str is Array)
         {
            Arr = new Array();
            for(i = 0; i < (str as Array).length; i++)
            {
               for(j = 0; j < this.data._keyArr.length; j++)
               {
                  if(this.KeyArrStr[j] == str[i])
                  {
                     Arr[Arr.length] = this.data._keyArr[j];
                  }
               }
            }
            return BasicKey.getTargetState(Arr);
         }
         trace("只接受string & string[] 类型参数");
         return false;
      }
      
      public function newSkin() : *
      {
         this.newZhuangBei3();
         if(this.skin)
         {
            this.skin.gotoAndStop("站");
            this.skin.parent.removeChild(this.skin);
            this.skin = null;
         }
         this.AddSkin();
         this.newZhuangBei();
         this.HeadXX();
         this.newWuQi();
         this.LoadPlayerLvData();
         this.getRL(this.RL);
         addChild(this.cengHao_mc);
         addChild(this.pkmc);
      }
      
      private function newZhuangBei3() : *
      {
         if(this.skin_Z3)
         {
            this.skin_Z3.parent.removeChild(this.skin_Z3);
            this.skin_Z3 = null;
         }
         this.AddSkin_Z3();
      }
      
      public function newZhuangBei() : *
      {
         if(this.skin_Z2)
         {
            this.skin_Z2.parent.removeChild(this.skin_Z2);
            this.skin_Z2 = null;
         }
         if(this.skin_Z)
         {
            this.skin_Z.parent.removeChild(this.skin_Z);
            this.skin_Z = null;
         }
         this.AddSkin_Z();
      }
      
      public function newWuQi() : *
      {
         if(this.skin_W)
         {
            this.skin_W.parent.removeChild(this.skin_W);
            this.skin_W = null;
         }
         this.AddSkin_W();
      }
      
      private function AddSkin() : *
      {
         var skinNumX:int = int(this.data.skinArr[this.data.skinNum]);
         var classRef:Class = Player.PlayerMcArr[this.data.skinArr[this.data.skinNum]].getClass("src.Skin.Skin_player" + skinNumX) as Class;
         this.skin = new classRef();
         this.skin.skinNum = this.data.skinArr[this.data.skinNum];
         this.skin.Xml = Skin.PlayerXml[this.data.skinArr[this.data.skinNum]];
         this.skin.playX = this;
         addChild(this.skin);
         this.skin.mouseChildren = this.skin.mouseEnabled = false;
         addChild(this.lifeMC);
         this.lifeMC.stop();
         this.lifeMC.y = -this.skin.height;
      }
      
      private function AddSkin_Z3() : *
      {
         var skinNumX:int = 0;
         var className:* = undefined;
         var numID:* = undefined;
         var loadName:* = undefined;
         var classRef:Class = null;
         if(this.data.getEquipSlot().getEquipFromSlot(7) != null)
         {
            skinNumX = int(this.data.skinArr[this.data.skinNum]);
            className = this.data.getEquipSlot().getEquipFromSlot(7).getClassName();
            numID = this.data.getEquipSlot().getEquipFromSlot(7).getClassName3();
            loadName = this.data.getEquipSlot().getEquipFromSlot(7).getClassName4();
            classRef = NewLoad.zhuangBeiSkin[skinNumX][numID].getClass(className) as Class;
            this.skin_Z3 = new classRef();
            addChild(this.skin_Z3);
            if(this.skin_Z3)
            {
               if(this.skin_Z3_V && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  this.skin_Z3.visible = true;
               }
               else
               {
                  this.skin_Z3.visible = false;
               }
            }
         }
      }
      
      private function AddSkin_Z() : *
      {
         var className:String = null;
         var numID:int = 0;
         var loadName:String = null;
         var skinNumX:int = int(this.data.skinArr[this.data.skinNum]);
         var xxx:uint = 1;
         if(Main.water.getValue() != 1)
         {
            xxx = 9;
         }
         if(this.data.getEquipSlot().getEquipFromSlot(xxx) == null)
         {
            className = "甲白1";
            numID = 1;
            loadName = "1_v2";
         }
         else
         {
            className = this.data.getEquipSlot().getEquipFromSlot(xxx).getClassName();
            numID = this.data.getEquipSlot().getEquipFromSlot(xxx).getClassName3();
            loadName = this.data.getEquipSlot().getEquipFromSlot(xxx).getClassName4();
         }
         var classRef:Class = NewLoad.zhuangBeiSkin[skinNumX][numID].getClass(className) as Class;
         this.skin_Z = new classRef();
         addChild(this.skin_Z);
         this.skin_Z.mouseChildren = this.skin_Z.mouseEnabled = false;
         var tempShow:Array = new Array();
         if(this.data.getEquipSlot().getEquipFromSlot(6) != null)
         {
            className = this.data.getEquipSlot().getEquipFromSlot(6).getClassName();
            numID = this.data.getEquipSlot().getEquipFromSlot(6).getClassName3();
            loadName = this.data.getEquipSlot().getEquipFromSlot(6).getClassName4();
            classRef = NewLoad.zhuangBeiSkin[skinNumX][numID].getClass(className) as Class;
            this.skin_Z2 = new classRef();
            addChild(this.skin_Z2);
            this.skin_Z.visible = false;
            tempShow = [numID,loadName,className];
         }
         if(this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            this.skin_Z2.visible = true;
            this.skin_Z.visible = false;
            this.skin_Z2.mouseChildren = this.skin_Z2.mouseEnabled = false;
         }
         else if(!this.skin_Z2_V || this.skin_Z2 && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() <= 0)
         {
            if(this.skin_Z2)
            {
               this.skin_Z2.visible = false;
            }
            this.skin_Z.visible = true;
         }
      }
      
      private function AddSkin_W() : *
      {
         var name:String = null;
         var skinNumX:int = this.data.skinNum;
         if(skinNumX == 0)
         {
            name = this.data.getEquipSlot().getEquipFromSlot(2).getClassName();
         }
         else if(skinNumX == 1)
         {
            name = this.data.getEquipSlot().getEquipFromSlot(5).getClassName();
         }
         var classRef:Class = Skin_WuQi.PlayerMcArr[this.data.skinArr[this.data.skinNum]].getClass(name) as Class;
         this.skin_W = new classRef();
         addChild(this.skin_W);
         this.skin_W.mouseChildren = this.skin_W.mouseEnabled = false;
      }
      
      private function SkinPlay(str:String) : *
      {
         if(this.skin)
         {
            this.skin.GoTo(str);
         }
      }
      
      private function getRL(_RL:Boolean) : *
      {
         this.RL = _RL;
         if(_RL)
         {
            this.skin.scaleX = this.skin_Z.scaleX = this.skin_W.scaleX = -1;
            if(this.skin_Z2)
            {
               this.skin_Z2.scaleX = -1;
            }
            if(this.skin_Z3)
            {
               this.skin_Z3.scaleX = -1;
            }
            if(this.cengHao_mc.frame == 363)
            {
               this.cengHao_mc.scaleX = 1;
            }
         }
         else if(!_RL)
         {
            this.skin.scaleX = this.skin_Z.scaleX = this.skin_W.scaleX = 1;
            if(this.skin_Z2)
            {
               this.skin_Z2.scaleX = 1;
            }
            if(this.skin_Z3)
            {
               this.skin_Z3.scaleX = 1;
            }
            if(this.cengHao_mc.frame == 363)
            {
               this.cengHao_mc.scaleX = -1;
            }
         }
      }
      
      public function HpUp(xxx:Number, type:int = 1) : *
      {
         var hpUpNum:int = 0;
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         var max:int = this.use_hp_Max.getValue();
         if(type == 1)
         {
            hpUpNum = this.hp.getValue() + xxx;
         }
         else if(type == 2)
         {
            xxx = max * xxx / 100;
            hpUpNum = this.hp.getValue() + xxx;
         }
         else
         {
            hpUpNum = xxx = 0;
         }
         if(hpUpNum < max)
         {
            this.hp.setValue(hpUpNum);
         }
         else
         {
            this.hp.setValue(max);
         }
         NewMC.Open("回血效果",this,0,0,0,xxx);
      }
      
      public function MpUp(xxx:Number, type:int = 1) : *
      {
         var hpUpNum:int = 0;
         if(this.hp.getValue() <= 0 || xxx <= 0)
         {
            return;
         }
         var max:int = this.use_mp_Max.getValue();
         if(type == 1)
         {
            mpUpNum = this.mp.getValue() + xxx;
         }
         else if(type == 2)
         {
            xxx = max * xxx / 100;
            mpUpNum = this.mp.getValue() + xxx;
         }
         else
         {
            mpUpNum = xxx = 0;
         }
         if(mpUpNum < max)
         {
            this.mp.setValue(mpUpNum);
         }
         else
         {
            this.mp.setValue(max);
         }
         NewMC.Open("回蓝效果",this,0,0,0,xxx);
      }
   }
}

