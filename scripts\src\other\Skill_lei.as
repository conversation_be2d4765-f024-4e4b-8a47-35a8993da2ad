package src.other
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_lei extends Fly
   {
      
      public function Skill_lei()
      {
         super();
      }
      
      public static function Add_Skill_lei(player:Player) : *
      {
         var r:int = 0;
         if(player.data.getEquipSlot().getEquipFromSlot(6) && player.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 487 && player.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            r = Math.random() * 100;
            if(r < 35 && Player.szCD > 30)
            {
               <PERSON><PERSON><PERSON>(player);
               Player.szCD = 0;
            }
         }
      }
      
      public static function XiaoGuo(player:Player) : *
      {
         if(Main.gameNum.getValue() == 999 || Enemy.All.length <= 0)
         {
            return;
         }
         var rrr:int = Math.random() * Enemy.All.length;
         var en:Enemy = Enemy.All[rrr];
         Player.szCD = 0;
         var class_JG:Class = NewLoad.OtherData.getClass("雷霆打击") as Class;
         var flyX:* = new class_JG();
         player.skin_W.addChild(flyX);
         flyX.x = en.x;
         flyX.y = en.y;
         player.jianCD_lei = true;
      }
      
      public static function JianCDXXXX(player:Player) : *
      {
         var x:int = 0;
         var xxxx:Number = NaN;
         player.jianCD_lei = false;
         var arr:Array = [];
         for(x in player.AllSkillCDXX)
         {
            if(player.AllSkillCDXX[x][1] < player.AllSkillCD[x][1] && player.data.getSkillLevel(player.AllSkillCDXX[x][0]) > 0)
            {
               arr.push(x);
            }
         }
         if(arr.length <= 0)
         {
            return;
         }
         var r:int = Math.random() * arr.length;
         var i:int = int(arr[r]);
         if(player.AllSkillCDXX[i][1] < player.AllSkillCD[i][1] && player.data.getSkillLevel(player.AllSkillCDXX[i][0]) > 0)
         {
            xxxx = 0.2;
            if(player.data.getEquipSlot().getEquipFromSlot(7) && player.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 488 && player.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
            {
               xxxx = 0.3;
            }
            player.AllSkillCDXX[i][1] += int(player.AllSkillCD[i][1] * xxxx);
            if(player.AllSkillCDXX[i][1] > player.AllSkillCD[i][1])
            {
               player.AllSkillCDXX[i][1] = player.AllSkillCD[i][1];
            }
            player.AllSkillCDXX[i][2] = int(player.AllSkillCDXX[i][1] / player.AllSkillCD[i][1] * 50);
         }
      }
      
      override public function onADDED_TO_STAGE(e:* = null) : *
      {
         var player:Player = null;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         var parentMC:MovieClip = this;
         while(parentMC != _stage)
         {
            if(parentMC is Skin_WuQi && parentMC.parent is Player)
            {
               who = parentMC.parent;
               this.RL = (who as Player).RL;
               gongJi_hp_MAX = 6000;
               硬直 = 0;
               gongJi_hp = 3;
               player = parentMC.parent;
               if(player.data.getEquipSlot().getEquipFromSlot(7) && player.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 488 && player.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  gongJi_hp = 6;
               }
               attTimes = who.skin.attTimes;
               runX = who.skin.runX;
               runTime = who.skin.runTime;
               break;
            }
            parentMC = parentMC.parent as MovieClip;
         }
         Main.world.moveChild_Other.addChild(this);
      }
      
      override public function onENTER_FRAME(e:*) : *
      {
         if(over && this.currentLabel != "结束")
         {
            Dead();
            return;
         }
         if(!over && life == 0)
         {
            life = -1;
            gotoAndPlay("结束");
            continuous = false;
            over = true;
         }
         if(!over && time != -1)
         {
            --time;
            if(time == -1)
            {
               time = -1;
               gotoAndPlay("结束");
               continuous = false;
               over = true;
            }
         }
         if(continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         else if(!continuous && this.currentLabel != "运行")
         {
            over = true;
         }
         if(!over)
         {
            Move();
         }
         otherXX();
      }
   }
}

