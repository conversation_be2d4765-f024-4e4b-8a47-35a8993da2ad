package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   public class WinShow2 extends MovieClip
   {
      
      public static var skin:MovieClip;
      
      public static var _this:MovieClip;
      
      public function WinShow2()
      {
         super();
         _this = this;
         addChild(skin);
         GameData.GuanKaXX();
         this.TextShow();
         Main._stage.addChild(this);
         Main._stage.frameRate = 0;
         var gNum:int = int(Main.gameNum.getValue());
         if(gNum == 2015 || gNum == 1000 || gNum == 888 || gNum > 16 && gNum < 50 || gNum > 80 && gNum < 100 || gNum > 5000 && gNum < 5100)
         {
            skin.re_btn.visible = false;
         }
         else
         {
            skin.re_btn.visible = true;
         }
         skin.re_btn.addEventListener(MouseEvent.CLICK,重开);
         skin.back_btn.addEventListener(MouseEvent.CLICK,回村);
         AchData.setTg();
         AchData.gkOk();
         TaskData.setTg();
         TaskData.isOk();
         JingLing.QingChuLengQue();
      }
      
      private static function 回村(e:*) : *
      {
         Main._stage.frameRate = 27;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Load.loadGameNum = 1;
         Load.loadGameNum2 = 1;
         Player.一起信春哥();
         _this.removeEventListener(MouseEvent.CLICK,回村);
         _this.removeEventListener(MouseEvent.CLICK,重开);
         Main._this.Loading();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         _this.parent.removeChild(_this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
      }
      
      private static function 重开(e:*) : *
      {
         var num:int = 1;
         if(Main.gameNum.getValue() <= 9)
         {
            num = 1;
         }
         else if(Main.gameNum.getValue() <= 16)
         {
            num = 2;
         }
         else if(Main.gameNum.getValue() <= 70)
         {
            num = 3;
         }
         Player.skillYAO = 0;
         Main._stage.frameRate = 27;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Load.loadGameNum = 1;
         Load.loadGameNum2 = 1;
         Player.一起信春哥();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         _this.parent.removeChild(_this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         SelMap.Open(0,0,3,num);
      }
      
      private function TextShow() : *
      {
         var jf:int = 0;
         var arr:Array = PaiHang_Data.Show();
         skin.time1_txt.text = arr[0];
         skin.time2_txt.text = arr[1];
         skin.qiu1_txt.text = arr[2];
         skin.qiu2_txt.text = arr[3];
         skin.Bj1_txt.text = arr[4];
         skin.Bj2_txt.text = arr[5];
         skin.fenshu1.text = arr[6];
         skin.fenshu2.text = arr[7];
         if(arr[8])
         {
            skin.jiLu_mc.visible = true;
         }
         else
         {
            skin.jiLu_mc.visible = false;
         }
         NewMC.Open("文字提示",Main._stage,470,400,30,0,true,2,"成绩已提交");
         if(Main.gameNum.getValue() <= 9)
         {
            jf = int((PaiHang_Data.jiFenArr[1] as VT).getValue());
            PaiHang_Data.jiFenArr[1] = VT.createVT(jf + InitData.tiaoZhanJF_1.getValue());
            if(PaiHang_Data.jiFenArr[1].getValue() > InitData.tiaoZhanJF_500.getValue())
            {
               PaiHang_Data.jiFenArr[1] = VT.createVT(InitData.tiaoZhanJF_500.getValue());
            }
            skin.JL_mc.gotoAndStop(1);
            skin.JL_txt.text = 1;
         }
         else if(Main.gameNum.getValue() <= 16)
         {
            jf = int((PaiHang_Data.jiFenArr[2] as VT).getValue());
            PaiHang_Data.jiFenArr[2] = VT.createVT(jf + InitData.tiaoZhanJF_1.getValue());
            if(PaiHang_Data.jiFenArr[2].getValue() > InitData.tiaoZhanJF_500.getValue())
            {
               PaiHang_Data.jiFenArr[2] = VT.createVT(InitData.tiaoZhanJF_500.getValue());
            }
            skin.JL_mc.gotoAndStop(2);
            skin.JL_txt.text = 1;
         }
         else if(Main.gameNum.getValue() <= 62)
         {
            jf = int((PaiHang_Data.jiFenArr[3] as VT).getValue());
            PaiHang_Data.jiFenArr[3] = VT.createVT(jf + InitData.tiaoZhanJF_1.getValue());
            if(PaiHang_Data.jiFenArr[3].getValue() > InitData.tiaoZhanJF_500.getValue())
            {
               PaiHang_Data.jiFenArr[3] = VT.createVT(InitData.tiaoZhanJF_500.getValue());
            }
            skin.JL_mc.gotoAndStop(3);
            skin.JL_txt.text = 1;
         }
         var lv:String = " / 1P等级" + Main.player_1.data.getLevel();
         if(Main.P1P2)
         {
            lv += " / 2P等级" + Main.player_2.data.getLevel();
         }
         var GameNumXX:String = " / 关卡:" + Main.gameNum.getValue();
         if(Main.gameNum.getValue() > 100 && Main.gameNum.getValue() < 200)
         {
            GameNumXX = " / 关卡:副本" + (Main.gameNum.getValue() - 100);
         }
         else if(Main.gameNum.getValue() == 17)
         {
            GameNumXX = " / 挑关卡:" + (Main.gameNum.getValue() - 16);
         }
         else if(Main.gameNum.getValue() > 17 && Main.gameNum.getValue() < 50)
         {
            GameNumXX = " / 星灵擂台:" + (Main.gameNum.getValue() - 17);
         }
         else if(Main.gameNum.getValue() > 50 && Main.gameNum.getValue() < 81)
         {
            GameNumXX = " / 海底:" + (Main.gameNum.getValue() - 50);
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            GameNumXX = " / 海底副本:" + (Main.gameNum.getValue() - 80);
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            GameNumXX = " / 关卡:药园";
         }
         else
         {
            GameNumXX = " / 关卡:" + Main.gameNum.getValue();
         }
         var 难度:String = " / 难度:★";
         if(GameData.gameLV == 2)
         {
            难度 = " / 难度:★★";
         }
         else if(GameData.gameLV == 3)
         {
            难度 = " / 难度:★★★";
         }
         else if(GameData.gameLV == 4)
         {
            难度 = " / 难度:★★★★";
         }
         else if(GameData.gameLV == 5)
         {
            难度 = " / 难度:挑战模式";
         }
         skin.XX_txt.text = "版本" + Main.varX / 100 + lv + GameNumXX + 难度;
      }
   }
}

