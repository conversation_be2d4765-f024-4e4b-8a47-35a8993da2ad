package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class ChongZhi_Interface4 extends MovieClip
   {
      
      public static var loadData:ClassLoader;
      
      public static var skin:MovieClip;
      
      public static var box_MC:MovieClip;
      
      private static var tooltip:ItemsTooltip;
      
      private static var tooltipID:int;
      
      public static var varXX:int = 18;
      
      public static var overTime:int = 20231203;
      
      public static var time0:String = "2023-11-27|10:00:01";
      
      public static var time1:String = "2023-12-03|23:59:59";
      
      public static var timeStr:String = "活动时间: 11月27日-12月3日";
      
      public static var lingQu_new:Array = [];
      
      public static var selYN:Boolean = false;
      
      public static var HDpointNUM15:VT = VT.createVT(-1);
      
      public static var loadName:String = "ChongZhi_new_v1720.swf";
      
      public static var czArr:Array = [0,0,100,300,800,1400,2000];
      
      public static var objIdArr:Array = [[],[63181,63106,63105,21221],[63458,63106,63140,21221],[63459,33213,63140,21222],[63460,33213,63289,63463],[63461,63290,63464,63463],[63462,33615,63464,63463]];
      
      public static var objNumArr:Array = [[],[1,1,1,1],[1,2,1,3],[1,10,2,1],[1,10,15,1],[1,15,1,2],[1,1,1,3]];
      
      public static var objTypeArr:Array = [[],[3,3,3,1],[3,3,3,1],[3,2,3,1],[3,2,3,3],[3,3,3,3],[3,2,3,3]];
      
      public static var objIdArr2:Array = [];
      
      public static var objTypeArr2:Array = [];
      
      public static var id2xArr:Array = [];
      
      public function ChongZhi_Interface4()
      {
         super();
      }
      
      public static function Open() : *
      {
         if(!lingQu_new[0] || lingQu_new[0] != varXX)
         {
            lingQu_new = [varXX];
         }
         if(!selYN)
         {
            Api_4399_All.GetTotalRecharged(15);
            selYN = true;
         }
         if(!skin)
         {
            Loading();
            return;
         }
         skin.x = skin.y = 0;
         skin.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         skin.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         skin.congzhi_btn.addEventListener(MouseEvent.CLICK,Open_CongZhi);
         skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
         if(!tooltip)
         {
            tooltip = new ItemsTooltip();
         }
         skin.addChild(tooltip);
         tooltip.visible = false;
         Show();
      }
      
      public static function Open_CongZhi(e:* = null) : *
      {
         selYN = false;
         var g:GameStop = new GameStop();
         Main.ChongZhi();
      }
      
      public static function Close(e:* = null) : *
      {
         selYN = false;
         skin.x = skin.y = -5000;
         skin.removeEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         skin.removeEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         skin.close_btn.removeEventListener(MouseEvent.CLICK,Close);
      }
      
      public static function onENTER_FRAME(e:*) : *
      {
      }
      
      public static function Show() : *
      {
         var i:int = 0;
         var mcX:MovieClip = null;
         var btnXX:MC_Btn = null;
         var objIdArrX:Array = null;
         var objNumArrX:Array = null;
         var objTypeArrX:Array = null;
         var mm:MovieClip = null;
         var mcXX:MovieClip = null;
         var num:int = 0;
         var xx:Number = NaN;
         var xx2:int = 0;
         skin.time_txt.text = timeStr;
         skin.jindu_mc._mc.scaleX = 1;
         skin.jindu_txt.text = "查询中...";
         for(var x:int = 1; x <= 6; x++)
         {
            skin["txt_" + x].text = "充" + czArr[x] + "点券";
            mcX = skin["mc_" + x];
            btnXX = new MC_Btn(mcX.lingqu_btn,"onCLICK_CZ");
            btnXX.lingQuNum = x;
            if(lingQu_new[x])
            {
               btnXX.goTo(5);
            }
            objIdArrX = objIdArr[x];
            objNumArrX = objNumArr[x];
            objTypeArrX = objTypeArr[x];
            for(i = 0; i < 4; i++)
            {
               mm = new Shop_picNEW();
               if(mcX["n_" + i])
               {
                  mcXX = mcX["n_" + i];
                  num = mcXX.getChildIndex(mcXX.pic_xx);
                  mm.x = mcXX.pic_xx.x;
                  mm.y = mcXX.pic_xx.y;
                  mm.name = "pic_xx";
                  mm.gotoAndStop(getFrame(objTypeArrX[i],objIdArrX[i]));
                  mcXX.removeChild(mcXX.pic_xx);
                  mcXX.pic_xx = mm;
                  mcXX.addChild(mm);
                  mcXX.setChildIndex(mm,num);
                  mcXX.howNum.text = objNumArrX[i];
                  mcXX.idX = objIdArrX[i];
                  mcXX.typeX = objTypeArrX[i];
                  mm.scaleX = mm.scaleY = 0.9;
               }
               else
               {
                  trace("找不到图标:" + i);
               }
            }
         }
         var xNum:int = HDpointNUM15.getValue();
         if(xNum >= 0)
         {
            xx = xNum / 2000;
            if(xx > 2000)
            {
               xx = 1;
            }
            xx2 = xx * 100;
            skin.jindu_mc._mc.scaleX = xx;
            skin.jindu_txt.text = xNum + "/2000";
         }
         for(i = 1; i <= 6; i++)
         {
            mcX = skin["mc_" + i];
            mcX.lingqu_btn.visible = false;
            mcX.lingqu_btn.gotoAndStop(1);
            if(xNum >= 0)
            {
               mcX.lingqu_btn.visible = true;
            }
            if(xNum >= czArr[i])
            {
               mcX.lingqu_btn.gotoAndStop(2);
               if(lingQu_new[i])
               {
                  mcX.lingqu_btn.gotoAndStop(5);
               }
            }
         }
      }
      
      public static function LingQu_CZ(num:int) : *
      {
         var i:* = undefined;
         var idX:int = 0;
         var typeX:int = 0;
         var numX:int = 0;
         var x:int = 0;
         var num1:int = int(Main.player1.getBag().backOtherBagNum());
         var num2:int = int(Main.player1.getBag().backGemBagNum());
         var num3:int = int(Main.player1.getBag().backSuppliesBagNum());
         trace("充值领取",num1,num2,num3);
         if(num1 < 4 || num2 < 1 || num3 < 1)
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包空间不足");
            return;
         }
         for(i in objIdArr[num])
         {
            idX = int(objIdArr[num][i]);
            typeX = int(objTypeArr[num][i]);
            numX = int(objNumArr[num][i]);
            for(x = 0; x < numX; x++)
            {
               if(typeX == 1)
               {
                  Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(idX));
               }
               else if(typeX == 2)
               {
                  Main.player1.getBag().addGemBag(GemFactory.creatGemById(idX));
               }
               else if(typeX == 3)
               {
                  Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(idX));
               }
            }
         }
         lingQu_new[num] = true;
         Show();
         NewMC.Open("文字提示",Main._stage,470,400,90,0,true,1,"奖励物品已放入背包");
         Main.Save();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("Ui_ChongZhi") as Class;
         skin = new classRef();
         Main._stage.addChild(skin);
         Open();
      }
      
      public static function setObjArr() : *
      {
         id2xArr[194] = 63458;
         id2xArr[195] = 63459;
         id2xArr[196] = 63460;
         id2xArr[197] = 63461;
         id2xArr[198] = 63462;
         id2xArr[199] = 63463;
         objIdArr2[194] = [100815,100817,100819,100821,100823,100825,100827,100829,100831];
         objIdArr2[195] = [100816,100818,100820,100822,100824,100826,100828,100830,100832];
         objIdArr2[196] = [63453,63300,63309,63327,63376];
         objIdArr2[197] = [63465,63296,63369,63193,63295,63335,63294];
         objIdArr2[198] = [63209,63251,63266,63267,63280,63291,63328,63339,63375,63386,63310];
         objIdArr2[199] = [63162,63169,63171,63183,63197,63207,63246,63257,63268,63273,63337,63340,63235,63457];
         objTypeArr2[194] = [8,8,8,8,8,8,8,8,8];
         objTypeArr2[195] = [8,8,8,8,8,8,8,8,8];
         objTypeArr2[196] = [3,3,3,3,3];
         objTypeArr2[197] = [3,3,3,3,3,3,3];
         objTypeArr2[198] = [3,3,3,3,3,3,3,3,3,3,3];
         objTypeArr2[199] = [3,3,3,3,3,3,3,3,3,3,3,3,3,3];
      }
      
      public static function openBoX(type:int, where:MovieClip, pData:PlayerData) : *
      {
         var mcX:MovieClip = null;
         var mm:MovieClip = null;
         var num:int = 0;
         setObjArr();
         var classRef:Class = ItemsPanel.loadData.getClass("自选礼包") as Class;
         box_MC = new classRef();
         box_MC.addEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         box_MC.addEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         box_MC.close_btn.addEventListener(MouseEvent.CLICK,CloseBoX);
         box_MC.typeXX = type;
         box_MC.lingQu_btn.addEventListener(MouseEvent.CLICK,LingQu_BoX);
         box_MC.pData = pData;
         if(!tooltip)
         {
            tooltip = new ItemsTooltip();
         }
         box_MC.addChild(tooltip);
         tooltip.visible = false;
         where.addChild(box_MC);
         var objIdArr2X:Array = objIdArr2[type];
         var objTypeArr2X:Array = objTypeArr2[type];
         for(var i:int = 0; i <= 13; i++)
         {
            mcX = box_MC["n_" + i];
            mcX.idX = objIdArr2X[i];
            mcX.typeX = objTypeArr2X[i];
            trace("自选礼包 >>>",i,objIdArr2X[i],objTypeArr2X[i]);
            if(objIdArr2X[i])
            {
               mm = new Shop_picNEW();
               num = mcX.getChildIndex(mcX.pic_xx);
               mcX.addEventListener(MouseEvent.CLICK,xuanze);
               mm.x = mcX.pic_xx.x - 3;
               mm.y = mcX.pic_xx.y - 3;
               mm.name = "pic_xx";
               mm.gotoAndStop(getFrame(objTypeArr2X[i],objIdArr2X[i]));
               mcX.removeChild(mcX.pic_xx);
               mcX.pic_xx = mm;
               mcX.addChild(mm);
               mcX.setChildIndex(mm,num);
               mcX.howNum.text = "";
               if(type == 199)
               {
                  mcX.howNum.text = "20";
               }
            }
            else
            {
               mcX.visible = false;
            }
         }
      }
      
      public static function LingQu_BoX(e:MouseEvent) : *
      {
         var pData:PlayerData = null;
         var typeXX:int = 0;
         var selNum:int = 0;
         var idX:int = 0;
         var typeX:int = 0;
         var num1:int = 0;
         var num2:int = 0;
         var num3:int = 0;
         var num4:int = 0;
         var numX:int = 0;
         var x:int = 0;
         trace("领取自选礼包",e.target.name);
         if(e.target.name == "lingQu_btn")
         {
            pData = e.target.parent.pData;
            if(!pData)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"出错了, 请稍后再试");
               CloseBoX();
               return;
            }
            typeXX = int(e.target.parent.typeXX);
            selNum = int(e.target.parent.selNum);
            idX = int(objIdArr2[typeXX][selNum]);
            typeX = int(objTypeArr2[typeXX][selNum]);
            num1 = int(pData.getBag().backSuppliesBagNum());
            num2 = int(pData.getBag().backGemBagNum());
            num3 = int(pData.getBag().backOtherBagNum());
            num4 = int(pData.getBag().backequipBagNum());
            trace("领取自选礼包>>>>>>>>>>> 空间 >>",typeX,"?",num1,num2,num3,num4);
            if(typeX == 1 && num1 < 1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包 消耗栏空间不足");
               return;
            }
            if(typeX == 2 && num2 < 1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包 宝石栏空间不足");
               return;
            }
            if(num4 < 1 && (typeX == 0 || typeX == 4 || typeX == 5 || typeX == 6 || typeX == 8 || typeX == 9))
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"背包 武器栏空间不足");
               return;
            }
            trace("领取自选礼包>>>>>>>>>>>",typeXX,selNum,"?",typeX,id2xArr[typeXX]);
            numX = 1;
            if(typeXX == 199)
            {
               numX = 20;
            }
            pData.getBag().delOtherById(id2xArr[typeXX]);
            for(x = 0; x < numX; x++)
            {
               if(typeX == 0 || typeX == 4 || typeX == 5 || typeX == 6 || typeX == 8 || typeX == 9)
               {
                  pData.getBag().addEquipBag(EquipFactory.createEquipByID(idX));
               }
               else if(typeX == 1)
               {
                  pData.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(idX));
               }
               else if(typeX == 2)
               {
                  pData.getBag().addGemBag(GemFactory.creatGemById(idX));
               }
               else if(typeX == 3)
               {
                  pData.getBag().addOtherobjBag(OtherFactory.creatOther(idX));
               }
            }
            NewMC.Open("文字提示",Main._stage,400,400,60,0,true,1,"领取成功 物品已放入背包");
            BagItemsShow.otherobjShow();
            CloseBoX();
            Main.Save();
         }
      }
      
      public static function CloseBoX(e:* = null) : *
      {
         box_MC.removeEventListener(DaoJuEvent.DAOJU_OVER_MESSAGE,daoJuOver);
         box_MC.removeEventListener(DaoJuEvent.DAOJU_OUT_MESSAGE,daoJuOut);
         box_MC.close_btn.removeEventListener(MouseEvent.CLICK,CloseBoX);
         if(box_MC.parent)
         {
            box_MC.parent.removeChild(box_MC);
         }
         box_MC.visible = false;
      }
      
      public static function xuanze(e:*) : *
      {
         var overobj:MovieClip = e.target as MovieClip;
         var overNum:uint = uint(overobj.name.substr(2,2));
         box_MC["select"].x = overobj.x - 3;
         box_MC["select"].y = overobj.y - 3;
         box_MC.selNum = overNum;
         trace("选择 >>",e.target.name,"?",overNum);
      }
      
      public static function openBoX_200(pData:PlayerData) : *
      {
         if(NewPetPanel.bag.backPetBagNum() <= 0)
         {
            NewMC.Open("文字提示",Main._stage,480,360,30,0,true,2,"宠物背包已满!");
            return;
         }
         var arrX:Array = [17005,17010,17015,17020,17025,17030,17035,17040,17045,17050,17055,17060,17065,17070,17075,17080,17085,17090,17095,17100,17105,17110];
         var r:int = Math.random() * arrX.length;
         var idX:int = int(arrX[r]);
         NewPetPanel.bag.addPetBag(PetEquip.creatPetEquip(idX));
         pData.getBag().delOtherById(63464);
         BagItemsShow.otherobjShow();
         NewMC.Open("文字提示",Main._stage,480,360,30,0,true,2,"神权利器已放入宠物背包");
         Main.Save();
      }
      
      public static function getBoX_200() : PetEquip
      {
         var arrX:Array = [17005,17010,17015,17020,17025,17030,17035,17040,17045,17050,17055,17060,17065,17070,17075,17080,17085,17090,17095,17100,17105,17110];
         var r:int = Math.random() * arrX.length;
         var idX:int = int(arrX[r]);
         return PetEquip.creatPetEquip(idX);
      }
      
      public static function getFrame(type:Number, id:Number) : Number
      {
         var ob:Object = null;
         if(type == 0 || type == 4 || type == 5 || type == 6 || type == 8 || type == 9)
         {
            ob = EquipFactory.createEquipByID(id);
            return (ob as Equip).getFrame();
         }
         if(type == 1)
         {
            ob = SuppliesFactory.getSuppliesById(id);
            return (ob as Supplies).getFrame();
         }
         if(type == 2)
         {
            ob = GemFactory.creatGemById(id);
            return (ob as Gem).getFrame();
         }
         if(type == 3)
         {
            ob = OtherFactory.creatOther(id);
            return (ob as Otherobj).getFrame();
         }
         return 1;
      }
      
      private static function addobj(type:Number, id:Number) : Object
      {
         var ob:Object = null;
         if(type == 0 || type == 4 || type == 5 || type == 6 || type == 8 || type == 9)
         {
            ob = EquipFactory.createEquipByID(id);
         }
         else if(type == 1)
         {
            ob = SuppliesFactory.getSuppliesById(id);
         }
         else if(type == 2)
         {
            ob = GemFactory.creatGemById(id);
         }
         else if(type == 3)
         {
            ob = OtherFactory.creatOther(id);
         }
         return ob;
      }
      
      private static function daoJuOut(e:DaoJuEvent) : void
      {
         tooltip.visible = false;
      }
      
      private static function daoJuOver(e:DaoJuEvent) : void
      {
         var str:String = e.target.name.substr(0,2);
         var id:Number = Number(e.target.name.substr(2,1));
         if(str != "n_")
         {
            return;
         }
         tooltip.visible = true;
         tooltip.x = Main._stage.mouseX;
         tooltip.y = Main._stage.mouseY;
         if(tooltip.x > 600)
         {
            tooltip.x = Main._stage.mouseX - 200;
         }
         var idXX:int = int(e.target.idX);
         var typeXX:int = int(e.target.typeX);
         var ob:* = addobj(typeXX,idXX);
         if(ob is Equip)
         {
            tooltip.equipTooltip(ob);
         }
         else if(ob is Supplies)
         {
            tooltip.suppliesTooltip(ob);
         }
         else if(ob is Gem)
         {
            tooltip.gemTooltip(ob);
         }
         else if(ob is Otherobj)
         {
            tooltip.otherTooltip(ob);
         }
      }
   }
}

