package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.repository.pet.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.other.*;
   
   public class ChongWu2 extends MovieClip
   {
      
      public static var cdXArr:Array = [];
      
      public static var cw2save:Array = [];
      
      public var who:Player;
      
      public var skin:MovieClip;
      
      public var RL:Boolean = true;
      
      public var data:Pet;
      
      public var noMove:Boolean = false;
      
      public var jnMove:Boolean = false;
      
      public var runType:String = "站";
      
      public var runOver:Boolean;
      
      public var stopRun:Boolean = true;
      
      public var id:int;
      
      public var jnIDArr:Array;
      
      public var wuDi:Boolean = false;
      
      public var hp:int = 100;
      
      public var hpMax:int = 100;
      
      public var gjUP:Number = 0;
      
      public var cdArr:Array;
      
      public var cdMaxArr:Array;
      
      public var hpDownTime:int;
      
      internal var Cwbs14X:int = 0;
      
      internal var Cwbs14Y:int = 0;
      
      public function ChongWu2()
      {
         var classRef:Class = null;
         this.jnIDArr = [];
         this.cdArr = [];
         this.cdMaxArr = [];
         super();
         classRef = ChongWu.chongWu_Data2[14].getClass("CWBS14") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public static function EnenyKillDiaoluo(enID:int) : *
      {
         var r:int = Math.random() * 100;
         if(r < 10 || Boolean(Main.tiaoShiYN) && r < 50)
         {
            if(Main.gameNum.getValue() == 17 && enID == 1056)
            {
               ++cw2save[14][1];
               NewMC.Open("文字提示",Main._stage,480,380,60,0,true,1,"获得 \'黑暗炼狱秘文-冥王龙\'");
            }
            else if(Main.gameNum.getValue() == 1000 && enID == 3022)
            {
               ++cw2save[14][2];
               NewMC.Open("文字提示",Main._stage,480,380,60,0,true,1,"获得 \'极限炼狱秘文-冥王龙\'");
            }
            else if(Main.gameNum.getValue() == 2000 && enID == 4010)
            {
               ++cw2save[14][3];
               NewMC.Open("文字提示",Main._stage,480,380,60,0,true,1,"获得 \'轮回炼狱秘文-冥王龙\'");
            }
         }
      }
      
      public static function Init() : *
      {
         cdXArr[14] = [20 * 27,15 * 27,18 * 27,16 * 27];
      }
      
      public static function InitSave() : *
      {
         if(!cw2save[14])
         {
            cw2save[14] = [0,0,0,0];
         }
      }
      
      public static function getJiaCeng() : Number
      {
         var num:Number = 0;
         if(cw2save[14][0] > 0)
         {
            num += Data_mosen.data[14][6] * cw2save[14][0];
         }
         return num;
      }
      
      public static function noCW2() : Boolean
      {
         if(Main.gameNum.getValue() == 0)
         {
            return true;
         }
         return false;
      }
      
      public static function addCW2(p:Player) : *
      {
         var id:int = 0;
         var cw2:ChongWu2 = null;
         var hpNum:Number = NaN;
         var gjNum:Number = NaN;
         if(noCW2())
         {
            return;
         }
         if(!p || !p.playerCW)
         {
            return;
         }
         id = int(PetFactory.getClassName(p.playerCW.data._id.getValue()));
         if(id != 14)
         {
            return;
         }
         var lv:int = int(cw2save[id][0]);
         if(lv < 1)
         {
            return;
         }
         id = int(PetFactory.getClassName(p.playerCW.data._id.getValue()));
         if(p.CW2gameNum != Main.gameNum.getValue() && !p.playerCW2)
         {
            cw2 = new ChongWu2();
            cw2.data = p.playerCW.data;
            cw2.who = p;
            cw2.id = id;
            hpNum = 2 + Data_mosen.data[id][7] / 100 * cw2save[id][0];
            cw2.hp = cw2.hpMax = p.hp_Max.getValue() * hpNum;
            gjNum = Data_mosen.data[id][8] / 100 * cw2save[id][0];
            cw2.gjUP = gjNum;
            cw2.jnIDArr = cw2.data.getPetSkillType();
            cw2.setCD();
            p.playerCW2 = cw2;
            p.parent.addChild(cw2);
            p.playerCW.Close();
            p.CW2gameNum = Main.gameNum.getValue();
            trace("宠物id:",cw2.id,"技能id:",cw2.jnIDArr,"cd时间:",cw2.cdMaxArr,"?",p.CW2gameNum,hpNum,cw2.hp);
         }
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         if(noCW2())
         {
            this.DeadX();
            return;
         }
         this.cdGo();
         this.GoToPlay();
         this.KeyControl();
         this.Where();
         this.RL_fun();
         this.HP_fun();
         this.GongJi();
         this.Dead_fun();
      }
      
      public function HP_fun() : *
      {
         ++this.hpDownTime;
         if(this.hpDownTime >= 27)
         {
            this.hpDown_new(this.hpMax / 150,false);
            this.hpDownTime = 0;
         }
      }
      
      public function setCD() : *
      {
         var i:* = undefined;
         for(i in this.jnIDArr)
         {
            if(this.jnIDArr[i] != -1)
            {
               this.cdArr[i] = 0;
               this.cdMaxArr[i] = cdXArr[this.id][this.jnIDArr[i] - 1];
            }
         }
      }
      
      public function getCD(num:int) : int
      {
         return int((this.cdMaxArr[num] - this.cdArr[num]) / this.cdMaxArr[num] * 50);
      }
      
      public function cdOK(num:int) : Boolean
      {
         if(this.cdArr[num] <= 0)
         {
            return true;
         }
         return false;
      }
      
      public function cdGo() : *
      {
         var i:* = undefined;
         for(i in this.cdArr)
         {
            if(this.cdArr[i] > 0)
            {
               --this.cdArr[i];
            }
         }
      }
      
      public function Where() : *
      {
         if(this.who)
         {
            this.x = this.who.x;
            this.y = this.who.y;
         }
      }
      
      public function RL_fun() : *
      {
         if(this.who)
         {
            this.RL = this.who.RL;
            if(this.RL)
            {
               scaleX = -1;
            }
            else if(!this.RL)
            {
               scaleX = 1;
            }
         }
      }
      
      public function hpDown_new(hpX:int, show:Boolean = true) : *
      {
         var xxx:int = 0;
         var yyy:int = 0;
         if(hpX <= 0 || this.hp <= 0 || this.wuDi)
         {
            return;
         }
         this.hp -= hpX;
         this.Dead_fun();
         if(show)
         {
            xxx = this.x + Math.random() * 50 - 25;
            yyy = this.y + Math.random() * 50 - 25;
            NewMC.Open("_被打数字",Main.world.moveChild_Other,xxx,yyy,15,hpX,true,2);
         }
      }
      
      public function Dead_fun() : *
      {
         if(this.hp > 0 && this.who && this.who.hp.getValue() > 0)
         {
            return;
         }
         this.DeadX();
      }
      
      public function DeadX() : *
      {
         this.hp = 0;
         if(this.who)
         {
            this.who.visible = true;
            this.who.NewCW(this.data);
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         if(this.who)
         {
            this.who.playerCW2 = null;
            this.who = null;
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         trace(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> 宠物变身结束");
      }
      
      public function getKeyStatus(str:*, type:int = 1, traceX:Boolean = false) : Boolean
      {
         return this.who.getKeyStatus(str,type,traceX);
      }
      
      public function SkinPlay(str:String, numX:int = -1) : *
      {
         if((this.runOver || this.stopRun) && this.runType != str)
         {
            this.runType = str;
            this.runOver = false;
            this.runData();
            this.skin.gotoAndPlay(this.runType);
            if(numX != -1)
            {
               this.cdArr[numX] = this.cdMaxArr[numX];
            }
            if(str == "怪物技能")
            {
               this.who.skin.enemySkill();
            }
         }
      }
      
      private function GoToPlay() : *
      {
         this.isRunOver();
         if(this.runOver)
         {
            if(this.runType == "站" || this.runType == "跑" || this.runType == "走")
            {
               this.skin.gotoAndPlay(this.runType);
               this.runOver = false;
            }
            else
            {
               this.SkinPlay("站");
            }
         }
         this.runData();
      }
      
      public function runData() : *
      {
         this.stopRun = false;
         this.noMove = true;
         if(this.runType == "站" || this.runType == "跑" || this.runType == "走" || this.runType == "跳")
         {
            this.stopRun = true;
            this.noMove = false;
         }
      }
      
      private function isRunOver() : *
      {
         if(this.skin.currentLabel != this.runType)
         {
            this.runOver = true;
         }
         else
         {
            this.runOver = false;
         }
      }
      
      public function runPower(numX:Number = 0, numY:Number = 0, timeX:int = 0, str:String = "") : *
      {
         this.who.runPower(numX,numY,timeX,"宠物变身");
      }
      
      public function getRL(_RL:Boolean) : *
      {
         this.who.getRL(_RL);
      }
      
      public function KeyControl() : *
      {
         var flySpeed:int = 0;
         if(!this.who)
         {
            return;
         }
         if(Boolean(this.who.playerJL) && (this.who == Main.player_1 && Boolean(BasicKey.getKeyState(66,1)) || this.who == Main.player_2 && Boolean(BasicKey.getKeyState(106,1))))
         {
            this.who.playerJL.ZD();
         }
         if(this.jnMove)
         {
            this.who.gravity = this.who.gravityNum = 0;
            if(this.getKeyStatus("左",3) || this.getKeyStatus("左",2))
            {
               this.runPower(-this.who.walk_power.getValue() * this.who.walk_power2,0,1);
            }
            else if(this.getKeyStatus("右",3) || this.getKeyStatus("右",2))
            {
               this.runPower(this.who.walk_power.getValue() * this.who.walk_power2,0,1);
            }
            if(this.getKeyStatus("上",2))
            {
               this.runPower(0,5 * this.who.walk_power2,1);
            }
            if(this.getKeyStatus("下",2))
            {
               this.runPower(0,-5 * this.who.walk_power2,1);
            }
            return;
         }
         if(!this.stopRun)
         {
            return;
         }
         if(this.who.jumping > 0 && this.getKeyStatus("跳",1) && !PK_UI.PK_ing && Main.gameNum.getValue() != 7003)
         {
            this.who.fly = true;
         }
         if(this.getKeyStatus("跳",2) == false)
         {
            this.who.flyTime = 0;
         }
         if(Boolean(PK_UI.PK_ing) || Main.gameNum.getValue() == 7003)
         {
            this.who.fly = false;
            this.who.flyTime = 0;
         }
         if(this.who.fly)
         {
            ++this.who.flyTime;
            if(this.who.flyTime > 4)
            {
               this.who.gravity = this.who.gravityNum = 0;
               flySpeed = this.who.walk_power.getValue();
               if(this.getKeyStatus("左",2))
               {
                  this.runPower(-flySpeed * this.who.walk_power2,0,1);
                  this.getRL(false);
               }
               if(this.getKeyStatus("右",2))
               {
                  this.runPower(flySpeed * this.who.walk_power2,0,1);
                  this.getRL(true);
               }
               if(this.getKeyStatus("上",2))
               {
                  this.runPower(0,8 * this.who.walk_power2,1);
               }
               if(this.getKeyStatus("下",2))
               {
                  this.runPower(0,-8 * this.who.walk_power2,1);
               }
               this.SkinPlay("跑");
               return;
            }
         }
         if(this.who.jKey && (this.who.jumping == 0 || this.who.jumpX2 && this.who.jumping < 2))
         {
            this.who.jKey = false;
            this.runPower(0,this.who.jump_power,this.who.jump_time);
            this.SkinPlay("跳");
            this.who.jumping += 1;
            if(this.who.jumpX2 && this.who.jumping == 2)
            {
               NewMC.Open("二段跳效果",Main.world.moveChild_Other,this.who.x,this.who.y);
            }
         }
         else if(this.who.jKey)
         {
            this.who.jKey = false;
         }
         var typeStr:String = "站";
         if(!this.noMove)
         {
            if(this.getKeyStatus("左",3))
            {
               typeStr = "跑";
               this.runPower(-this.who.walk_power.getValue() * 1.5 * this.who.walk_power2,0,1);
               this.getRL(false);
            }
            else if(this.getKeyStatus("右",3))
            {
               typeStr = "跑";
               this.runPower(this.who.walk_power.getValue() * 1.5 * this.who.walk_power2,0,1);
               this.getRL(true);
            }
            else if(this.getKeyStatus("左",2))
            {
               typeStr = "走";
               this.runPower(-this.who.walk_power.getValue() * this.who.walk_power2,0,1);
               this.getRL(false);
            }
            else if(this.getKeyStatus("右",2))
            {
               typeStr = "走";
               this.runPower(this.who.walk_power.getValue() * this.who.walk_power2,0,1);
               this.getRL(true);
            }
         }
         if(this.getKeyStatus("攻击",1))
         {
            if(typeStr == "跑" && this.who.jumping == 0)
            {
               this.SkinPlay("跑攻");
            }
            else
            {
               this.SkinPlay("攻击1");
            }
         }
         else if(this.getKeyStatus("怪物",1))
         {
            this.SkinPlay("怪物技能");
         }
         else if(this.getKeyStatus("技能1",1) && this.jnIDArr[0] > 0 && this.cdOK(0))
         {
            this.SkinPlay("技能" + this.jnIDArr[0],0);
         }
         else if(this.getKeyStatus("技能2",1) && this.jnIDArr[1] > 0 && this.cdOK(1))
         {
            this.SkinPlay("技能" + this.jnIDArr[1],1);
         }
         else if(this.getKeyStatus("技能3",1) && this.jnIDArr[2] > 0 && this.cdOK(2))
         {
            this.SkinPlay("技能" + this.jnIDArr[2],2);
         }
         else if(this.getKeyStatus("技能4",1) && this.jnIDArr[3] > 0 && this.cdOK(3))
         {
            this.SkinPlay("技能" + this.jnIDArr[3],3);
         }
         else if(this.who.jumping == 0)
         {
            this.SkinPlay(typeStr);
         }
         else
         {
            this.SkinPlay("跑");
         }
      }
      
      public function GongJi() : *
      {
         if(this.id == 14)
         {
            this.Cwbs14(this);
         }
      }
      
      public function Cwbs14(cw:ChongWu2) : *
      {
         var rlXXX:int = 0;
         var frameX:int = cw.skin.currentFrame;
         var pX:Player = cw.who;
         if(pX)
         {
            pX.noMapHit = false;
            this.jnMove = false;
            this.wuDi = false;
            if(frameX >= 340 && frameX <= 403)
            {
               this.jnMove = true;
            }
            else if(frameX >= 557 && frameX <= 630)
            {
               pX.noMapHit = true;
               this.wuDi = true;
               rlXXX = -1;
               if(pX.RL)
               {
                  rlXXX = 1;
               }
               if(frameX == 557)
               {
                  this.Cwbs14X = pX.x;
                  this.Cwbs14Y = pX.y;
                  this.runPower(840 * rlXXX,0,20);
               }
               else if(frameX == 602)
               {
                  this.runPower(-840 * rlXXX,0,28);
               }
               else if(frameX == 629)
               {
                  pX.x = this.Cwbs14X;
                  pX.y = this.Cwbs14Y;
               }
            }
         }
      }
   }
}

