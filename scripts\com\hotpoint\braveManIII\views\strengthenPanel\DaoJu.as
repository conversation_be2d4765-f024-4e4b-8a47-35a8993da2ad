package com.hotpoint.braveManIII.views.strengthenPanel
{
   import com.hotpoint.braveManIII.events.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.text.*;
   
   public class <PERSON>o<PERSON>u extends MovieClip
   {
      
      public var diKuang:MovieClip;
      
      public var howNum:TextField;
      
      public var ob_mast:MovieClip;
      
      public var pic_xx:MovieClip;
      
      public function DaoJu()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         this.mouseChildren = false;
         this.howNum.text = "";
         this.addEventListener(MouseEvent.MOUSE_OVER,this.overHandle);
         this.addEventListener(MouseEvent.MOUSE_OUT,this.outHandle);
         this.addEventListener(MouseEvent.MOUSE_DOWN,this.downHandle);
         this.addEventListener(MouseEvent.MOUSE_UP,this.upHandle);
         this.addEventListener(MouseEvent.CLICK,this.clickHandle);
      }
      
      private function overHandle(e:MouseEvent) : void
      {
         this.dispatchEvent(new DaoJuEvent(DaoJuEvent.DAOJU_OVER_MESSAGE,true));
      }
      
      private function outHandle(e:MouseEvent) : void
      {
         this.dispatchEvent(new DaoJuEvent(DaoJuEvent.DAOJU_OUT_MESSAGE,true));
      }
      
      private function downHandle(e:MouseEvent) : void
      {
         this.dispatchEvent(new DaoJuEvent(DaoJuEvent.DAOJU_DOWN_MESSAGE,true));
      }
      
      private function upHandle(e:MouseEvent) : void
      {
         this.dispatchEvent(new DaoJuEvent(DaoJuEvent.DAOJU_UP_MESSAGE,true));
      }
      
      private function clickHandle(e:MouseEvent) : void
      {
         this.dispatchEvent(new DaoJuEvent(DaoJuEvent.DAOJU_CLICK_MESSAGE,true));
      }
   }
}

