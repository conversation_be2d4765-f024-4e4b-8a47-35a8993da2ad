package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class zengfuPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var zfPanel:MovieClip;
      
      public static var zfp:zengfuPanel;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var strS:String;
      
      public static var skillId:Number;
      
      public static var oldNum:Number;
      
      private static var loadData:ClassLoader;
      
      public static var yeshu:Number = 0;
      
      public static var isPOne:Boolean = true;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_ZF_v892.swf";
      
      public function zengfuPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!zfPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = zfPanel.getChildIndex(zfPanel["e" + i]);
            mm.x = zfPanel["e" + i].x;
            mm.y = zfPanel["e" + i].y;
            mm.name = "e" + i;
            zfPanel.removeChild(zfPanel["e" + i]);
            zfPanel["e" + i] = mm;
            zfPanel.addChild(mm);
            zfPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = zfPanel.getChildIndex(zfPanel["s" + i]);
            mm.x = zfPanel["s" + i].x;
            mm.y = zfPanel["s" + i].y;
            mm.name = "s" + i;
            zfPanel.removeChild(zfPanel["s" + i]);
            zfPanel["s" + i] = mm;
            zfPanel.addChild(mm);
            zfPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = zfPanel.getChildIndex(zfPanel["select"]);
         mm.x = zfPanel["select"].x;
         mm.y = zfPanel["select"].y;
         mm.name = "select";
         zfPanel.removeChild(zfPanel["select"]);
         zfPanel["select"] = mm;
         zfPanel.addChild(mm);
         zfPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("ZFShow") as Class;
         zfPanel = new classRef();
         zfp.addChild(zfPanel);
         InitIcon();
         if(OpenYN)
         {
            open(isPOne,strS,skillId,oldNum);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         zfp = new zengfuPanel();
         LoadSkin();
         Main._stage.addChild(zfp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         zfp = new zengfuPanel();
         Main._stage.addChild(zfp);
         OpenYN = false;
      }
      
      public static function open(pp:Boolean, skStr:String, skId:Number, num:int) : void
      {
         Main.allClosePanel();
         Main.DuoKai_Fun();
         if(zfPanel)
         {
            Main.stopXX = true;
            zfp.x = 0;
            zfp.y = 0;
            isPOne = pp;
            zfPanel["zftxt"].text = skStr;
            skillId = skId;
            oldNum = num;
            addListenerP1();
            Main._stage.addChild(zfp);
            zfp.visible = true;
         }
         else
         {
            skillId = skId;
            oldNum = num;
            isPOne = pp;
            strS = skStr;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(zfPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            zfp.visible = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         zfPanel["isZF"]["yesZF"].addEventListener(MouseEvent.CLICK,yesZF);
         zfPanel["isZF"]["noZF"].addEventListener(MouseEvent.CLICK,noZF);
         zfPanel["isZF"]["noZF2"].addEventListener(MouseEvent.CLICK,noZF);
         zfPanel["zf_btn"].addEventListener(MouseEvent.CLICK,doZF);
         zfPanel["close"].addEventListener(MouseEvent.CLICK,closeZF);
         for(var i:uint = 0; i < 24; i++)
         {
            zfPanel["e" + i].mouseChildren = false;
            zfPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            zfPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            zfPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            zfPanel["s" + i].mouseChildren = false;
            zfPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            zfPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            zfPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         zfPanel["select"].gotoAndStop(1);
         zfPanel["select"].visible = false;
         zfPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         zfPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         showAll();
         zfPanel["isZF"].visible = false;
         zfPanel["chose"].visible = false;
      }
      
      public static function upGo(e:*) : *
      {
         yeshu = 0;
         showAll();
      }
      
      public static function downGo(e:*) : *
      {
         yeshu = 1;
         showAll();
      }
      
      public static function showAll() : *
      {
         var i:uint = 0;
         var xxx:int = 0;
         var yeshuNum:int = yeshu + 1;
         zfPanel["yeshu_txt"].text = yeshuNum + "/2";
         if(isPOne)
         {
            for(i = 0; i < 24; i++)
            {
               zfPanel["e" + i].t_txt.text = "";
               if(Main.player1.getBag().getEquipFromBag(i + 24 * yeshu) != null)
               {
                  zfPanel["e" + i].gotoAndStop(Main.player1.getBag().getEquipFromBag(i + 24 * yeshu).getFrame());
                  zfPanel["e" + i].visible = true;
               }
               else
               {
                  zfPanel["e" + i].visible = false;
               }
            }
            for(i = 0; i < 8; i++)
            {
               xxx = int(i);
               if((i == 0 || i == 1 || i == 3 || i == 4) && Main.water.getValue() != 1)
               {
                  xxx += 8;
               }
               zfPanel["s" + i].t_txt.text = "";
               if(Main.player1.getEquipSlot().getEquipFromSlot(xxx) != null)
               {
                  zfPanel["s" + i].gotoAndStop(Main.player1.getEquipSlot().getEquipFromSlot(xxx).getFrame());
                  zfPanel["s" + i].visible = true;
               }
               else
               {
                  zfPanel["s" + i].visible = false;
               }
            }
         }
         else
         {
            for(i = 0; i < 24; i++)
            {
               zfPanel["e" + i].t_txt.text = "";
               if(Main.player2.getBag().getEquipFromBag(i + 24 * yeshu) != null)
               {
                  zfPanel["e" + i].gotoAndStop(Main.player2.getBag().getEquipFromBag(i + 24 * yeshu).getFrame());
                  zfPanel["e" + i].visible = true;
               }
               else
               {
                  zfPanel["e" + i].visible = false;
               }
            }
            for(i = 0; i < 8; i++)
            {
               xxx = int(i);
               if((i == 0 || i == 1 || i == 3 || i == 4) && Main.water.getValue() != 1)
               {
                  xxx += 8;
               }
               zfPanel["s" + i].t_txt.text = "";
               if(Main.player2.getEquipSlot().getEquipFromSlot(xxx) != null)
               {
                  zfPanel["s" + i].gotoAndStop(Main.player2.getEquipSlot().getEquipFromSlot(xxx).getFrame());
                  zfPanel["s" + i].visible = true;
               }
               else
               {
                  zfPanel["s" + i].visible = false;
               }
            }
         }
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         zfPanel["isZF"]["yesZF"].removeEventListener(MouseEvent.CLICK,yesZF);
         zfPanel["isZF"]["noZF"].removeEventListener(MouseEvent.CLICK,noZF);
         zfPanel["isZF"]["noZF2"].removeEventListener(MouseEvent.CLICK,noZF);
         zfPanel["close"].removeEventListener(MouseEvent.CLICK,closeZF);
         zfPanel["zf_btn"].removeEventListener(MouseEvent.CLICK,doZF);
         for(i = 0; i < 24; i++)
         {
            zfPanel["e" + i].mouseChildren = false;
            zfPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            zfPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            zfPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            zfPanel["s" + i].mouseChildren = false;
            zfPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            zfPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            zfPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         zfPanel["up_btn"].removeEventListener(MouseEvent.CLICK,upGo);
         zfPanel["down_btn"].removeEventListener(MouseEvent.CLICK,downGo);
      }
      
      public static function closeZF(e:*) : *
      {
         close();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         zfPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(isPOne)
         {
            if(str == "e")
            {
               overNum += 24 * yeshu;
               if(Main.player1.getBag().getEquipFromBag(overNum) != null)
               {
                  itemsTooltip.equipTooltip(Main.player1.getBag().getEquipFromBag(overNum),1);
               }
            }
            else
            {
               itemsTooltip.slotTooltip(overNum,Main.player1.getEquipSlot());
            }
         }
         else if(str == "e")
         {
            overNum += 24 * yeshu;
            if(Main.player2.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(Main.player2.getBag().getEquipFromBag(overNum),2);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(overNum,Main.player2.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = zfPanel.mouseX + 10;
         itemsTooltip.y = zfPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function yesZF(e:*) : *
      {
         if(isPOne)
         {
            if(nameStr == "e")
            {
               Main.player1.getBag().getEquipFromBag(clickNum).setNewSkill(skillId);
            }
            else
            {
               Main.player1.getEquipSlot().getEquipFromSlot(clickNum).setNewSkill(skillId);
            }
            Main.player1.getBag().delGem(oldNum,1);
            AchData.setZfNum(1);
         }
         else
         {
            if(nameStr == "e")
            {
               Main.player2.getBag().getEquipFromBag(clickNum).setNewSkill(skillId);
            }
            else
            {
               Main.player2.getEquipSlot().getEquipFromSlot(clickNum).setNewSkill(skillId);
            }
            Main.player2.getBag().delGem(oldNum,1);
            AchData.setZfNum(2);
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备增幅成功");
         close();
      }
      
      private static function noZF(e:*) : *
      {
         zfPanel["isZF"].visible = false;
      }
      
      private static function doZF(e:*) : *
      {
         if(isPOne)
         {
            if(nameStr == "e")
            {
               if(Main.player1.getBag().getEquipFromBag(clickNum).getNewSkill() == 0)
               {
                  Main.player1.getBag().getEquipFromBag(clickNum).setNewSkill(skillId);
                  Main.player1.getBag().delGem(oldNum,1);
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备增幅成功");
                  AchData.setZfNum(1);
                  close();
               }
               else
               {
                  zfPanel["isZF"].visible = true;
               }
            }
            else if(Main.player1.getEquipSlot().getEquipFromSlot(clickNum).getNewSkill() == 0)
            {
               Main.player1.getEquipSlot().getEquipFromSlot(clickNum).setNewSkill(skillId);
               Main.player1.getBag().delGem(oldNum,1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备增幅成功");
               AchData.setZfNum(1);
               close();
            }
            else
            {
               zfPanel["isZF"].visible = true;
            }
         }
         else if(nameStr == "e")
         {
            if(Main.player2.getBag().getEquipFromBag(clickNum).getNewSkill() == 0)
            {
               Main.player2.getBag().getEquipFromBag(clickNum).setNewSkill(skillId);
               Main.player2.getBag().delGem(oldNum,1);
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备增幅成功");
               AchData.setZfNum(2);
               close();
            }
            else
            {
               zfPanel["isZF"].visible = true;
            }
         }
         else if(Main.player2.getEquipSlot().getEquipFromSlot(clickNum).getNewSkill() == 0)
         {
            Main.player2.getEquipSlot().getEquipFromSlot(clickNum).setNewSkill(skillId);
            Main.player2.getBag().delGem(oldNum,1);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备增幅成功");
            AchData.setZfNum(2);
            close();
         }
         else
         {
            zfPanel["isZF"].visible = true;
         }
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         clickObj = e.target as MovieClip;
         zfPanel["chose"].visible = true;
         zfPanel["chose"].x = clickObj.x - 2;
         zfPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         if(nameStr != "e" && (clickNum == 0 || clickNum == 1 || clickNum == 3 || clickNum == 4) && Main.water.getValue() != 1)
         {
            clickNum += 8;
         }
         if(nameStr == "e")
         {
            clickNum += yeshu * 24;
         }
         zfPanel["select"].gotoAndStop(clickObj.currentFrame);
         zfPanel["select"].visible = true;
      }
   }
}

