package src
{
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class NPC_YouLing extends MovieClip
   {
      
      private static var skin:MovieClip;
      
      private static var overTime:int;
      
      private static var overMC:MovieClip;
      
      public static var timeX_YN:Boolean;
      
      public static var overNum:int = 0;
      
      private static var selDaLu:int = 0;
      
      private static var enNum:int = 5;
      
      public static var killEnNum:int = 0;
      
      public static var killEnMax:int = 50;
      
      private static var timeArr:Array = [0,50,75,120];
      
      public static var timeX:int = 0;
      
      public static var hunNum:int = 0;
      
      public static var colNum:int = 0;
      
      public static var hunNumNow:int = 0;
      
      public static var gameNumX:int = 1;
      
      public static var hpArr:Array = [0,0,0];
      
      public function NPC_YouLing()
      {
         super();
      }
      
      public static function Open() : *
      {
         var ClassLoaderX:ClassLoader = null;
         var classRef:Class = null;
         trace("幽灵船长界面 打开!!");
         Main.allClosePanel();
         if(!skin)
         {
            ClassLoaderX = Map.MapArr[7000];
            classRef = ClassLoaderX.getClass("船长对话框");
            skin = new classRef();
            Main._stage.addChild(skin);
            InitBtn();
         }
         skin.visible = true;
         InitDataX();
         skin.gotoAndStop(1);
         InitShow();
      }
      
      public static function InitDataX() : *
      {
         trace("幽灵船长界面 InitDataX!!");
         enNum = 5;
         selDaLu = 0;
         killEnNum = 0;
         timeX_YN = false;
      }
      
      public static function Close() : *
      {
         trace("幽灵船长界面 关闭!!");
         if(skin)
         {
            skin.visible = false;
         }
      }
      
      public static function InitBtn() : *
      {
         skin["jixu_btn"].addEventListener(MouseEvent.CLICK,JiXu);
         skin["jixu_btn2"].addEventListener(MouseEvent.CLICK,JiXu);
         skin["jixu_btn3"].addEventListener(MouseEvent.CLICK,JiXu);
         skin["guiZe_btn"].addEventListener(MouseEvent.CLICK,GuiZe);
         skin["guiZe_btn2"].addEventListener(MouseEvent.CLICK,GuiZe);
         skin["goHome_btn"].addEventListener(MouseEvent.CLICK,GoHome);
         skin["goHome_btn2"].addEventListener(MouseEvent.CLICK,GoHome);
         skin["goHome_btn_OK"].addEventListener(MouseEvent.CLICK,GoHomeOk);
         skin["btn_1"].addEventListener(MouseEvent.CLICK,SelDaLu);
         skin["btn_2"].addEventListener(MouseEvent.CLICK,SelDaLu);
         skin["btn_3"].addEventListener(MouseEvent.CLICK,SelDaLu);
         skin["btn_4"].addEventListener(MouseEvent.CLICK,SelDaLu);
      }
      
      public static function InitShow() : *
      {
         skin["name_txt"].visible = false;
         skin["xuqiu_txt"].visible = false;
         skin["_txt_1"].visible = false;
         skin["_txt_2"].visible = false;
         skin["_txt_3"].visible = false;
         skin["_txt_4"].visible = false;
         skin["_txt_31"].visible = false;
         skin["_txt"].visible = false;
         skin["jixu_btn"].visible = false;
         skin["jixu_btn2"].visible = false;
         skin["jixu_btn3"].visible = false;
         skin["guiZe_btn"].visible = false;
         skin["guiZe_btn2"].visible = false;
         skin["goHome_btn"].visible = false;
         skin["goHome_btn2"].visible = false;
         skin["goHome_btn_OK"].visible = false;
         skin["btn_1"].visible = false;
         skin["btn_2"].visible = false;
         skin["btn_3"].visible = false;
         skin["btn_4"].visible = false;
         skin["_txt_1"].text = "xx";
         skin["_txt_2"].text = "yy";
         skin["_txt_4"].text = "zz";
         var color:Array = ["","蓝色灵魂","紫色灵魂","金色灵魂"];
         var numX:int = int(skin.currentFrame);
         if(numX >= 1 && numX <= 4)
         {
            skin["jixu_btn"].visible = true;
         }
         else if(numX == 5 || numX == 20)
         {
            skin["xuqiu_txt"].visible = true;
            skin["btn_1"].visible = true;
            skin["btn_2"].visible = true;
            skin["btn_3"].visible = true;
            skin["btn_4"].visible = true;
            skin["guiZe_btn"].visible = true;
            skin["goHome_btn"].visible = true;
            skin["xuqiu_txt"].text = getStr();
         }
         else if(numX == 10)
         {
            skin["name_txt"].visible = true;
            skin["jixu_btn"].visible = true;
         }
         else if(numX == 11)
         {
            skin["_txt_1"].visible = true;
            skin["_txt_2"].visible = true;
            skin["_txt_3"].visible = true;
            skin["_txt_4"].visible = true;
            skin["guiZe_btn2"].visible = true;
            skin["goHome_btn2"].visible = true;
            skin["jixu_btn2"].visible = true;
            skin["_txt_1"].text = color[colNum] + "x" + hunNumNow;
            skin["_txt_2"].text = color[colNum] + "x" + hunNum;
            skin["_txt_3"].text = "" + enNum;
            skin["_txt_4"].text = color[colNum] + "x" + hunNum / 2;
         }
         else if(numX == 12)
         {
            skin["jixu_btn"].visible = true;
            skin["_txt"].visible = true;
            skin["_txt"].text = color[colNum] + "x40, 红色灵魂x10";
         }
         else if(numX == 21 || numX == 30 || numX == 31)
         {
            skin["jixu_btn3"].visible = true;
            skin["goHome_btn_OK"].visible = true;
            if(numX == 31)
            {
               skin["_txt_31"].visible = true;
               skin._txt_31.text = color[colNum] + "x" + hunNum;
            }
         }
      }
      
      public static function JiXu(e:MouseEvent) : *
      {
         var numX:int = int(skin.currentFrame);
         if(numX == 10)
         {
            Close();
            SelDaLu();
            return;
         }
         if(numX >= 1 && numX <= 3)
         {
            numX++;
         }
         else if(numX == 4)
         {
            if(killEnNum > 0)
            {
               numX = 11;
            }
            else
            {
               numX = 5;
            }
         }
         else if(numX == 11)
         {
            numX = 10;
         }
         else if(numX == 12 || numX == 30 || numX == 31)
         {
            numX = 20;
         }
         else if(numX == 21)
         {
            numX = 11;
         }
         skin.gotoAndStop(numX);
         InitShow();
      }
      
      public static function GuiZe(e:MouseEvent) : *
      {
         skin.gotoAndStop(2);
         InitShow();
      }
      
      public static function GoHome(e:MouseEvent) : *
      {
         trace("点击返回按钮:",skin.currentFrame,selDaLu,enNum);
         if(skin.currentFrame == 5 || skin.currentFrame == 20)
         {
            skin.gotoAndStop(30);
            InitShow();
         }
         else if(skin.currentFrame == 30 || skin.currentFrame == 21)
         {
            GoHomeOk();
         }
         else if(selDaLu == 0)
         {
            skin.gotoAndStop(20);
            InitShow();
         }
         else if(enNum == 0)
         {
            skin.gotoAndStop(30);
            InitShow();
         }
         else
         {
            skin.gotoAndStop(21);
            InitShow();
         }
      }
      
      public static function SelDaLu(e:MouseEvent = null) : *
      {
         var str:String = null;
         if(e)
         {
            str = (e.target as SimpleButton).name;
            selDaLu = str.substr(4,1);
            enNum = 5;
            if(!XuQiuYN())
            {
               return;
            }
            ++NPC_YouLing.gameNumX;
         }
         var numX2:int = 6 - enNum;
         Main.gameNum.setValue(7000 + selDaLu);
         Main.gameNum2.setValue(numX2);
         GameData.gameLV = 7;
         killEnNum = 0;
         timeX = timeArr[selDaLu] * 27;
         trace("选择大陆:",selDaLu,numX2);
         Main._this.Loading();
         setHpArr();
         Close();
         var xx:int = numX2 - 1;
         var xxArr:Array = [0,2,5,9,14];
         trace("xxxxxxxxxxxxxxxxxxx  进关扣魂:",selDaLu,Panel_youling.bzNumArr[selDaLu],xxArr[xx]);
         Panel_youling.bzNumArr[selDaLu] -= xxArr[xx];
         Main.Save();
      }
      
      public static function timeGo() : *
      {
         timeX = timeArr[selDaLu] * 27;
      }
      
      public static function GoHomeOk(e:MouseEvent = null) : *
      {
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Main._this.Loading();
         Close();
      }
      
      public static function getHun(yjYN:Boolean = false) : *
      {
         var xxArr:Array = null;
         var arr:Array = [0,4,6,8,10,12];
         var arr2:Array = [0,4,10,18,28,40];
         var xx:int = 5 - enNum;
         hunNum = arr2[xx];
         if(yjYN)
         {
            hunNum /= 2;
         }
         colNum = selDaLu;
         hunNumNow = arr[xx];
         if(!yjYN)
         {
            xxArr = [4,8,13,19,26];
            trace("?????????????????????????????????????  胜利给魂:",selDaLu,Panel_youling.bzNumArr[selDaLu],xxArr[xx - 1]);
            Panel_youling.bzNumArr[selDaLu] += xxArr[xx - 1];
            if(xx == 5)
            {
               Panel_youling.bzNumArr[4] += 10;
            }
            Main.Save();
         }
      }
      
      public static function McPlay(e:*) : *
      {
         if(Main.gameNum.getValue() <= 7000 || Main.gameNum.getValue() > 7200)
         {
            skin.removeEventListener(Event.ENTER_FRAME,McPlay);
            overNum = 0;
            overTime = 0;
            timeX_YN = false;
            if(Boolean(overMC) && Boolean(overMC.parent))
            {
               overMC.parent.removeChild(overMC);
            }
            return;
         }
         ++overTime;
         if(overTime >= 65)
         {
            if(Boolean(overMC) && Boolean(overMC.parent))
            {
               overMC.parent.removeChild(overMC);
            }
            if(overNum == 1)
            {
               Win2();
            }
            else if(overNum == 2)
            {
               Lose2();
            }
            overNum = 0;
            overTime = 0;
            skin.removeEventListener(Event.ENTER_FRAME,McPlay);
         }
      }
      
      public static function Win() : *
      {
         if(Main.gameNum.getValue() <= 7000 || Main.gameNum.getValue() > 7200)
         {
            return;
         }
         timeX_YN = false;
         overNum = 1;
         getHpArr();
         var ClassLoaderX1:ClassLoader = Map.MapArr[7000];
         var classRef1:Class = ClassLoaderX1.getClass("挑战成功");
         overMC = new classRef1();
         Main._stage.addChild(overMC);
         skin.addEventListener(Event.ENTER_FRAME,McPlay);
      }
      
      public static function Win2() : *
      {
         Main.gameNum.setValue(7000);
         Main.gameNum2.setValue(0);
         GameData.gameLV = 7;
         Main._this.GameStart();
         --enNum;
         getHun();
         trace("战斗胜利, 剩余:",enNum,", 魂:" + hunNum);
         if(enNum > 0)
         {
            killEnNum = 0;
            skin.gotoAndStop(11);
         }
         else
         {
            InitDataX();
            skin.gotoAndStop(12);
            getHpArr(true);
         }
         skin.visible = true;
         InitShow();
         setHpArr();
      }
      
      public static function Lose() : *
      {
         if(Main.gameNum.getValue() <= 7000 || Main.gameNum.getValue() > 7200)
         {
            return;
         }
         timeX_YN = false;
         overNum = 2;
         var ClassLoaderX1:ClassLoader = Map.MapArr[7000];
         var classRef1:Class = ClassLoaderX1.getClass("挑战失败");
         overMC = new classRef1();
         Main._stage.addChild(overMC);
         skin.addEventListener(Event.ENTER_FRAME,McPlay);
      }
      
      public static function Lose2() : *
      {
         skin.visible = true;
         getHun(true);
         InitDataX();
         Main.gameNum.setValue(7000);
         Main.gameNum2.setValue(0);
         GameData.gameLV = 7;
         Main._this.GameStart();
         trace("战斗失败, 剩余:" + enNum);
         skin.gotoAndStop(31);
         InitShow();
      }
      
      public static function getHpArr(yn:Boolean = false) : *
      {
         hpArr = [0,0,0];
         var hp:int = 0;
         if(Boolean(Main.player_1) && Main.player_1.hp.getValue() > 0)
         {
            hp = Main.player_1.hp.getValue() + Main.player_1.use_hp_Max.getValue() * 0.25;
            if(hp > Main.player_1.use_hp_Max.getValue())
            {
               hp = int(Main.player_1.use_hp_Max.getValue());
            }
            hpArr[1] = hp;
            if(yn)
            {
               hpArr[1] = Main.player_1.use_hp_Max.getValue();
            }
         }
         if(Boolean(Main.player_2) && Main.player_2.hp.getValue() > 0)
         {
            hp = Main.player_2.hp.getValue() + Main.player_2.use_hp_Max.getValue() * 0.25;
            if(hp > Main.player_2.use_hp_Max.getValue())
            {
               hp = int(Main.player_2.use_hp_Max.getValue());
            }
            hpArr[2] = hp;
            if(yn)
            {
               hpArr[1] = Main.player_2.use_hp_Max.getValue();
            }
         }
      }
      
      public static function setHpArr() : *
      {
         if(Main.gameNum.getValue() != 7000 && enNum == 5)
         {
            return;
         }
         if(Main.player_1)
         {
            Main.player_1.hp.setValue(hpArr[1]);
         }
         if(Main.player_2)
         {
            Main.player_2.hp.setValue(hpArr[2]);
         }
      }
      
      public static function KillEnemy() : *
      {
         ++killEnNum;
      }
      
      public static function TimeOver() : Boolean
      {
         if(timeX <= 0)
         {
            return true;
         }
         return false;
      }
      
      public static function ShowTime() : String
      {
         if(timeX_YN && overNum == 0)
         {
            --timeX;
         }
         if(timeX < 0)
         {
            timeX = 0;
         }
         var t:int = timeX / 27 + 0.9;
         return t + "秒";
      }
      
      public static function ShowHitEn() : String
      {
         var str:* = "击败所有怪物";
         if(selDaLu == 2)
         {
            str = "击败" + killEnNum + "/50只怪物";
         }
         else if(selDaLu == 3)
         {
            str = "击败所有怪物,禁止飞行";
         }
         return str;
      }
      
      public static function getStr() : String
      {
         var x1:int = 25 * gameNumX;
         var x2:int = 10000 * gameNumX;
         return "击杀点x" + x1 + "、金币x" + x2;
      }
      
      private static function XuQiuYN() : Boolean
      {
         var x1:int = 25 * gameNumX;
         var x2:int = 10000 * gameNumX;
         if(Main.P1P2)
         {
            if(Main.player1.getGold() < x2 || Main.player2.getGold() < x2)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"金币不足");
               return false;
            }
            if(Main.player1.killPoint.getValue() < x1 || Main.player2.killPoint.getValue() < x1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"击杀点不足");
               return false;
            }
         }
         else
         {
            if(Main.player1.getGold() < x2)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"金币不足");
               return false;
            }
            if(Main.player1.killPoint.getValue() < x1)
            {
               NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"击杀点不足");
               return false;
            }
         }
         if(Main.P1P2)
         {
            Main.player1.payGold(x2);
            Main.player2.payGold(x2);
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - x1);
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - x1);
         }
         else
         {
            Main.player1.payGold(x2);
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - x1);
         }
         return true;
      }
   }
}

