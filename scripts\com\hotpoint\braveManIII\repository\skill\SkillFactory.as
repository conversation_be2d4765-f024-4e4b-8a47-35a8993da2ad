package com.hotpoint.braveManIII.repository.skill
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.skill.*;
   import flash.utils.ByteArray;
   import src.*;
   import src.tool.*;
   
   public class SkillFactory
   {
      
      public static var isSkillDataOk:Boolean;
      
      public static var myXml:XML;
      
      public static var myXmlMD5:ByteArray;
      
      public static var skillAllDataArr:Array = [];
      
      public static var skillAllDataArr2:Object = new Object();
      
      public function SkillFactory()
      {
         super();
      }
      
      public static function creatSkillData() : void
      {
         myXml = XMLAsset.createXML(InData.SkillData);
         myXmlMD5 = Obj_Compare.getObj_ByteArray(SkillFactory.myXml);
         var sk:SkillFactory = new SkillFactory();
         sk.creatLoard();
      }
      
      public static function Add_skillAllDataArr2(typeId:String, skillLevel:int, skillData:Skill) : *
      {
         if(!skillAllDataArr2[typeId])
         {
            skillAllDataArr2[typeId] = [];
         }
         skillAllDataArr2[typeId][skillLevel] = skillData;
      }
      
      public static function getSkillById(id:Number) : Skill
      {
         var data:Skill = null;
         var skData:Skill = null;
         for each(data in skillAllDataArr)
         {
            if(data.getId() == id)
            {
               skData = data;
            }
         }
         if(skData == null)
         {
         }
         return skData;
      }
      
      public static function getSkillByTypeIAndLevel(typeId:String, level:Number = 1) : Skill
      {
         var data:Skill = null;
         var data2:Skill = null;
         var skArr:Array = [];
         var skData:Skill = null;
         for each(data in skillAllDataArr)
         {
            if(data.getTypeId() == typeId)
            {
               skArr.push(data);
            }
         }
         for each(data2 in skArr)
         {
            if(data2.getSkillLevel() == level)
            {
               skData = data2;
            }
         }
         if(skData == null)
         {
         }
         return skData;
      }
      
      public static function getSkillByPet(petId:String) : Number
      {
         var data:Skill = null;
         var skArr:Array = [];
         var skData:Skill = null;
         for each(data in skillAllDataArr)
         {
            if(data.getProfessional() == petId)
            {
               skArr.push(data.getId());
            }
         }
         return skArr[Math.round(Math.random() * (skArr.length - 1))];
      }
      
      private function creatLoard() : *
      {
         this.xmlLoaded();
      }
      
      internal function xmlLoaded() : *
      {
         var property:XML = null;
         var id:Number = NaN;
         var typeId:String = null;
         var frame:Number = NaN;
         var skillName:String = null;
         var introduction:String = null;
         var touchOff:String = null;
         var professional:String = null;
         var weapon:String = null;
         var skillLevel:Number = NaN;
         var skillLevelUp:Number = NaN;
         var mp:Number = NaN;
         var ep:Number = NaN;
         var skillCd:Number = NaN;
         var duration:Number = NaN;
         var actOn:Number = NaN;
         var valueLise:XMLList = null;
         var arrValue:Array = null;
         var va:XML = null;
         var skillData:Skill = null;
         for each(property in myXml.数据)
         {
            id = Number(property.编号);
            typeId = String(property.技能类型);
            frame = Number(property.帧数);
            skillName = String(property.技能名称);
            introduction = String(property.技能描述);
            touchOff = String(property.触发类型);
            professional = String(property.职业要求);
            weapon = String(property.武器要求);
            skillLevel = Number(property.技能等级);
            skillLevelUp = Number(property.技能等级上限);
            mp = Number(property.魔法消耗);
            ep = Number(property.能量消耗);
            skillCd = Number(property.技能冷却);
            duration = Number(property.持续时间);
            actOn = Number(property.作用类型);
            valueLise = property.具体数值;
            arrValue = [];
            for each(va in valueLise)
            {
               if(va.数值1 != "null")
               {
                  arrValue[0] = VT.createVT(Number(va.数值1));
               }
               if(va.数值2 != "null")
               {
                  arrValue[1] = VT.createVT(Number(va.数值2));
               }
               if(va.数值3 != "null")
               {
                  arrValue[2] = VT.createVT(Number(va.数值3));
               }
               if(va.数值4 != "null")
               {
                  arrValue[3] = VT.createVT(Number(va.数值4));
               }
               if(va.次数 != "null")
               {
                  arrValue[4] = VT.createVT(Number(va.次数));
               }
               if(va.硬直 != "null")
               {
                  arrValue[5] = VT.createVT(Number(va.硬直));
               }
               if(va.挑高 != "null")
               {
                  arrValue[6] = VT.createVT(Number(va.挑高));
               }
               if(va.震退 != "null")
               {
                  arrValue[7] = VT.createVT(Number(va.震退));
               }
               if(va.持续 != "null")
               {
                  arrValue[8] = VT.createVT(Number(va.持续));
               }
               if(va.伤害限定 != "null")
               {
                  arrValue[9] = VT.createVT(Number(va.伤害限定));
               }
               if(va.伤害限定2 != "null")
               {
                  arrValue[10] = VT.createVT(Number(va.伤害限定2));
               }
               if(va.伤害限定3 != "null")
               {
                  arrValue[11] = VT.createVT(Number(va.伤害限定3));
               }
               if(va.竞技伤害限定 != "null")
               {
                  arrValue[12] = VT.createVT(Number(va.竞技伤害限定));
               }
            }
            skillData = Skill.creatSkill(id,typeId,frame,skillName,introduction,touchOff,weapon,professional,skillLevel,skillLevelUp,mp,ep,skillCd,duration,actOn,arrValue);
            skillAllDataArr.push(skillData);
            Add_skillAllDataArr2(typeId,skillLevel,skillData);
         }
         isSkillDataOk = true;
      }
   }
}

