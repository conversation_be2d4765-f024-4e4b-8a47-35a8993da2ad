package com.hotpoint.braveManIII.repository.plan
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.plan.*;
   
   public class PlanBasicData
   {
      
      private var _id:VT;
      
      private var _groupid:VT;
      
      private var _group:VT;
      
      private var _introduction:String;
      
      private var _rewardtype_1:VT;
      
      private var _rewardtype_2:VT;
      
      private var _reward_1:VT;
      
      private var _reward_2:VT;
      
      private var _frame_1:VT;
      
      private var _frame_2:VT;
      
      private var _count_1:VT;
      
      private var _count_2:VT;
      
      public function PlanBasicData()
      {
         super();
      }
      
      public static function creatPlanBasicData(id:*, groupid:*, group:*, introduction:*, rewardtype_1:*, rewardtype_2:*, reward_1:*, reward_2:*, frame_1:*, frame_2:*, count_1:*, count_2:*) : *
      {
         var pbd:PlanBasicData = new PlanBasicData();
         pbd._id = VT.createVT(id);
         pbd._groupid = VT.createVT(groupid);
         pbd._group = VT.createVT(group);
         pbd._introduction = introduction;
         pbd.rewardtype_1 = VT.createVT(rewardtype_1);
         pbd.rewardtype_2 = VT.createVT(rewardtype_2);
         pbd._reward_1 = VT.createVT(reward_1);
         pbd._reward_2 = VT.createVT(reward_2);
         pbd._frame_1 = VT.createVT(frame_1);
         pbd._frame_2 = VT.createVT(frame_2);
         pbd._count_1 = VT.createVT(count_1);
         pbd._count_2 = VT.createVT(count_2);
         return pbd;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(value:VT) : void
      {
         this._id = value;
      }
      
      public function get groupid() : VT
      {
         return this._groupid;
      }
      
      public function set groupid(value:VT) : void
      {
         this._groupid = value;
      }
      
      public function get group() : VT
      {
         return this._group;
      }
      
      public function set group(value:VT) : void
      {
         this._group = value;
      }
      
      public function get introduction() : String
      {
         return this._introduction;
      }
      
      public function set introduction(value:String) : void
      {
         this._introduction = value;
      }
      
      public function get rewardtype_1() : VT
      {
         return this._rewardtype_1;
      }
      
      public function set rewardtype_1(value:VT) : void
      {
         this._rewardtype_1 = value;
      }
      
      public function get rewardtype_2() : VT
      {
         return this._rewardtype_2;
      }
      
      public function set rewardtype_2(value:VT) : void
      {
         this._rewardtype_2 = value;
      }
      
      public function get reward_1() : VT
      {
         return this._reward_1;
      }
      
      public function set reward_1(value:VT) : void
      {
         this._reward_1 = value;
      }
      
      public function get reward_2() : VT
      {
         return this._reward_2;
      }
      
      public function set reward_2(value:VT) : void
      {
         this._reward_2 = value;
      }
      
      public function get frame_1() : VT
      {
         return this._frame_1;
      }
      
      public function set frame_1(value:VT) : void
      {
         this._frame_1 = value;
      }
      
      public function get frame_2() : VT
      {
         return this._frame_2;
      }
      
      public function set frame_2(value:VT) : void
      {
         this._frame_2 = value;
      }
      
      public function get count_1() : VT
      {
         return this._count_1;
      }
      
      public function set count_1(value:VT) : void
      {
         this._count_1 = value;
      }
      
      public function get count_2() : VT
      {
         return this._count_2;
      }
      
      public function set count_2(value:VT) : void
      {
         this._count_2 = value;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getGroupId() : Number
      {
         return this._groupid.getValue();
      }
      
      public function getGroup() : Number
      {
         return this._group.getValue();
      }
      
      public function getIntroduction() : String
      {
         return this._introduction;
      }
      
      public function getRewardType_1() : Number
      {
         return this._rewardtype_1.getValue();
      }
      
      public function getRewardType_2() : Number
      {
         return this._rewardtype_2.getValue();
      }
      
      public function getReward_1() : Number
      {
         return this._reward_1.getValue();
      }
      
      public function getReward_2() : Number
      {
         return this._reward_2.getValue();
      }
      
      public function getFrame_1() : Number
      {
         return this._frame_1.getValue();
      }
      
      public function getFrame_2() : Number
      {
         return this._frame_2.getValue();
      }
      
      public function getCount_1() : Number
      {
         return this._count_1.getValue();
      }
      
      public function getCount_2() : Number
      {
         return this._count_2.getValue();
      }
      
      public function creatPlan() : Plan
      {
         return Plan.creatPlan(this._id.getValue());
      }
   }
}

