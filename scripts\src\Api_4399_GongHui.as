package src
{
   import com.ByteArrayXX.*;
   import com.adobe.serialization.json.*;
   import com.hotpoint.braveManIII.views.elvesPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.makePanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.stampPanel.*;
   import com.hotpoint.braveManIII.views.wantedPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import flash.utils.*;
   import src._data.*;
   import src.tool.*;
   import unit4399.events.*;
   
   public class Api_4399_GongHui
   {
      
      public function Api_4399_GongHui()
      {
         super();
      }
      
      public static function Init(_stage:Stage) : *
      {
         _stage.addEventListener(UnionEvent.UNION_VISITOR_SUCCESS,onVisitorSuccess);
         _stage.addEventListener(UnionEvent.UNION_MEMBER_SUCCESS,onMemberSuccess);
         _stage.addEventListener(UnionEvent.UNION_GROW_SUCCESS,onGrowSuccess);
         _stage.addEventListener(UnionEvent.UNION_MASTER_SUCCESS,onMasterSuccess);
         _stage.addEventListener(UnionEvent.UNION_VARIABLES_SUCCESS,onVariablesSuccess);
         _stage.addEventListener(UnionEvent.UNION_ROLE_SUCCESS,unionRoleSuccess);
         _stage.addEventListener(UnionEvent.UNION_ERROR,unionCreateError);
      }
      
      public static function SelUserInfo() : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.getOwnUnion(idx);
      }
      
      public static function AddGongHui(title:String, extra:String) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.unionCreate(idx,title,extra);
      }
      
      public static function getList(pageNum:int = 1) : *
      {
         var idx:int = int(Main.saveNum);
         var pageSize:int = int(GongHui_Interface.gongHuiNumMax);
         if(pageSize == 0)
         {
            pageSize = 900;
         }
         Main.serviceHold.getUnionList(idx,pageNum,pageSize);
      }
      
      public static function getBHCY_List(unionId:int) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.getUnionMembers(idx,unionId);
      }
      
      public static function upNum(id:int = 15) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.doVariable(idx,id);
      }
      
      public static function getNum(ids:Array) : *
      {
         var idx:int = int(Main.saveNum);
         if(Main.serviceHold)
         {
            Main.serviceHold.getVariables(idx,ids);
         }
      }
      
      public static function getSenHe(pageNum:int = 1, pageSize:int = 100) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.getApplyList(idx,pageNum,pageSize);
         TiaoShi.txtShow("获取待审核列表 请求\n");
      }
      
      public static function SenQing(unionId:int, extra:String = "") : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.applyUnion(idx,unionId,extra);
      }
      
      public static function DuiHuanGX(money:int) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.doExchange(idx,money);
      }
      
      public static function setRW(taskID:int) : *
      {
         var idx:int = int(Main.saveNum);
         var task:String = String(taskID);
         Main.serviceHold.doTask(idx,task);
      }
      
      public static function XiaoHaoGX(contribution:int) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.usePersonalContribution(idx,contribution);
      }
      
      public static function XiaoHaoBanghui_GX(contribution:int) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.useUnionContribution(idx,contribution);
      }
      
      public static function InfoXX(extra:String, type:int = 1, unionId:int = 0, userId:int = 0, userIndex:int = 0) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.setMemberExtra(idx,type,extra,unionId,userId,userIndex);
      }
      
      public static function GH_InfoXX(extra:String, type:int = 1) : *
      {
         var idx:int = int(Main.saveNum);
         var unionId:int = int(GongHui_Interface.gongHui_ID);
         Main.serviceHold.setUnionExtra(idx,type,extra,unionId);
      }
      
      public static function SenHe(userId:int = 0, userIndex:int = 0, auditResult:int = 1) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.auditMember(idx,userId,userIndex,auditResult);
      }
      
      public static function get_QX(pageNum:int = 1, pageSize:int = 50) : *
      {
         Main.serviceHold.getRoleList(pageNum,pageSize);
      }
      
      public static function set_QX(uid:int, index:int, roleId:int) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.setRole(idx,uid,index,roleId);
      }
      
      public static function TiRen(userId:int = 0, userIndex:int = 0) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.removeMember(idx,userId,userIndex);
      }
      
      public static function ZhuanRang(userId:int = 0, userIndex:int = 0, result:int = 0) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.transferUnion(idx,userId,userIndex,result);
      }
      
      public static function TuiChu() : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.quitUion(idx);
      }
      
      public static function JieSan(actionType:int = 1) : *
      {
         var idx:int = int(Main.saveNum);
         Main.serviceHold.dissolveUnion(idx,actionType);
      }
      
      private static function onVisitorSuccess(e:UnionEvent) : *
      {
         var data:* = undefined;
         var str:String = null;
         var objX:Object = null;
         var arr:Array = null;
         var dataObj:Object = e.data;
         TiaoShi.txtShow("游客模块apiName:" + dataObj.apiName + "\n");
         data = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHCJ:
               TiaoShi.txtShow("帮会创建结果:" + String(data) + "\n");
               if(data == true)
               {
                  Api_4399_GongHui.SelUserInfo();
               }
               break;
            case UnionEvent.UNI_API_BHLB:
               str = data;
               objX = JSONs.decode(str,true);
               arr = objX.unionList;
               TiaoShi.txtShow("公会总数:" + objX.rowCount + "\n");
               GongHui_Interface.ListOk(objX);
               break;
            case UnionEvent.UNI_API_BHSQ:
               if(Boolean(data))
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"申请提交成功, 请耐心等待审核结果");
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"申请提交失败了!");
               }
               break;
            case UnionEvent.UNI_API_SSBH:
               str = data;
               objX = JSONs.decode(str,true);
               TiaoShi.txtShow("当前用户 所属帮会信息:\n" + String(data) + "\n");
               GongHui_Interface.PlayerInfoOk(objX);
         }
      }
      
      private static function onMemberSuccess(e:UnionEvent) : *
      {
         var data:* = undefined;
         var str:String = null;
         var arr:Array = null;
         var dataObj:Object = e.data;
         TiaoShi.txtShow("成员模块apiName:" + dataObj.apiName + "\n");
         data = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHMX:
               TiaoShi.txtShow("帮会明细结果:\n" + String(data) + "\n");
               GongHui_Interface.SenQingOpen();
               break;
            case UnionEvent.UNI_API_BHCY:
               TiaoShi.txtShow("帮会成员列表:\n" + String(data) + "\n");
               str = data;
               arr = JSONs.decode(str,true);
               GongHui_Interface.CengYuanListOk(arr);
               break;
            case UnionEvent.UNI_API_CYTZBG:
               TiaoShi.txtShow("成员扩展信息变更:\n" + Boolean(data) + "\n");
               break;
            case UnionEvent.UNI_API_BHTZBG:
               TiaoShi.txtShow("帮会扩展信息变更:\n" + Boolean(data) + "\n");
               break;
            case UnionEvent.UNI_API_BHRZ:
               TiaoShi.txtShow("帮会行为记录列表:\n" + String(data) + "\n");
               break;
            case UnionEvent.UNI_API_TCBH:
               TiaoShi.txtShow("退出帮会信息:\n" + Boolean(data) + "\n");
               break;
            case UnionEvent.UNI_API_XHGRGXD:
               TiaoShi.txtShow("消耗个人贡献点信息:\n" + int(data) + "\n");
               break;
            case UnionEvent.UNI_API_SZJS:
               TiaoShi.txtShow("设置角色权限信息:\n" + Boolean(data) + "\n");
         }
      }
      
      private static function onGrowSuccess(e:UnionEvent) : *
      {
         var dataObj:Object = e.data;
         TiaoShi.txtShow("成长模块apiName:" + dataObj.apiName + "\n");
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_BHRW:
               TiaoShi.txtShow("帮会任务结果:\n" + Boolean(data) + "\n");
               GongHui_Interface.gongHuiShow = true;
               SelUserInfo();
               Main.Save(false);
               break;
            case UnionEvent.UNI_API_BHDH:
               upNum(159);
               Api_4399_All.Money_sel();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点券捐献成功, 公会经验+1, 贡献值+1");
               GongHui_Interface.gongHuiShow = true;
               SelUserInfo();
               Main.Save(false);
               TiaoShi.txtShow("帮会兑换信息:\n" + Boolean(data) + "\n");
               break;
            case UnionEvent.UNI_API_BHRWWC:
               TiaoShi.txtShow("帮会任务完成情况:\n" + String(data) + "\n");
         }
      }
      
      private static function onMasterSuccess(e:UnionEvent) : *
      {
         var data:* = undefined;
         var str:String = null;
         var objX:Object = null;
         var dataObj:Object = e.data;
         TiaoShi.txtShow("帮主模块apiName:" + dataObj.apiName + "\n");
         data = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_DSHLB:
               TiaoShi.txtShow("获取待审核列表:\n" + String(data) + "\n");
               str = data;
               objX = JSONs.decode(str,true);
               GongHui_Interface.SenHeListOk(objX);
               break;
            case UnionEvent.UNI_API_CYSH:
               TiaoShi.txtShow("成员审核信息:\n" + Boolean(data) + "\n");
               break;
            case UnionEvent.UNI_API_CYYC:
               TiaoShi.txtShow("移除成员信息:\n" + Boolean(data) + "\n");
               break;
            case UnionEvent.UNI_API_JSBH:
               TiaoShi.txtShow("解散帮会信息:\n" + String(data) + "\n");
         }
      }
      
      private static function onVariablesSuccess(e:UnionEvent) : *
      {
         var data:* = undefined;
         var str:String = null;
         var arr:Array = null;
         var x57:int = 0;
         var x58:int = 0;
         var x137:int = 0;
         var i:int = 0;
         var xxx:int = 0;
         var dataObj:Object = e.data;
         TiaoShi.txtShow("公共变量模块apiName:" + dataObj.apiName + "\n");
         data = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_HQBL:
               TiaoShi.txtShow("公共变量列表:\n" + String(data) + "\n");
               str = data;
               arr = JSONs.decode(str,true);
               if(arr != null)
               {
                  TiaoShi.txtShow("公共变量列表数组长度:" + arr.length);
                  x57 = 0;
                  x58 = 0;
                  x137 = 0;
                  for(i = 0; i < arr.length; i++)
                  {
                     TiaoShi.txtShow(i + " : " + arr[i].id + ", " + arr[i].value);
                     if(arr[i].id == 57)
                     {
                        x57 = int(arr[i].value);
                     }
                     if(arr[i].id == 58)
                     {
                        x58 = int(arr[i].value);
                     }
                     if(arr[i].id == 137)
                     {
                        x137 = int(arr[i].value);
                     }
                     if(arr[i].id == 157)
                     {
                        GongHui_Interface.xxArr[0] = arr[i].value;
                     }
                     if(arr[i].id == 158)
                     {
                        GongHui_Interface.xxArr[1] = arr[i].value;
                     }
                     if(arr[i].id == 159)
                     {
                        GongHui_Interface.xxArr[2] = arr[i].value;
                        GongHui_Interface.HuoYue_Show();
                     }
                     if(arr[i].id == 160)
                     {
                        GongHui_jiTan.jiTanLv_arr[0] = true;
                        GongHui_jiTan.jiTanLv_arr[1] = arr[i].value;
                     }
                     if(arr[i].id == 161)
                     {
                        GongHui_jiTan.jiTanLv_arr[2] = arr[i].value;
                     }
                     if(arr[i].id == 162)
                     {
                        GongHui_jiTan.jiTanLv_arr[3] = arr[i].value;
                     }
                     if(arr[i].id == 163)
                     {
                        GongHui_jiTan.jiTanLv_arr[4] = arr[i].value;
                        Main.player_1.GetAllSkillCD();
                        if(Main.P1P2)
                        {
                           Main.player_2.GetAllSkillCD();
                        }
                     }
                  }
                  if(x57 != 0)
                  {
                     GengXin.Go(x57,x58);
                  }
                  if(x137 != 0)
                  {
                     xxx = x137 % 9;
                     Main.HuiTie8Num = xxx;
                     Play_Interface.LunTan10_Show();
                  }
                  break;
               }
               TiaoShi.txtShow("公会模式为沙盒模式!!");
               return;
               break;
            case UnionEvent.UNI_API_XGBL:
               TiaoShi.txtShow("公共变量修改信息:\n" + Boolean(data));
         }
      }
      
      private static function unionRoleSuccess(e:UnionEvent) : void
      {
         var dataObj:Object = e.data;
         TiaoShi.txtShow("apiName:" + dataObj.apiName + "\n");
         var data:* = dataObj.data;
         switch(dataObj.apiName)
         {
            case UnionEvent.UNI_API_JSQXLB:
               TiaoShi.txtShow("角色权限列表:\n" + String(data) + "\n");
         }
      }
      
      private static function unionCreateError(e:UnionEvent) : *
      {
         var temeArr:Array = new Array();
         temeArr[10002] = "参数错误";
         temeArr[10003] = "游戏未开通帮会API";
         temeArr[10004] = "只有帮主有权限执行该操作";
         temeArr[10005] = "用户未登陆";
         temeArr[20001] = "当前点券不足";
         temeArr[20002] = "点券不足";
         temeArr[20003] = "扣款失败";
         temeArr[20004] = "帮会名称已存在";
         temeArr[20005] = "退出帮会后,24小时内不能申请或创建帮会";
         temeArr[20006] = "超过申请数量上限";
         temeArr[20007] = "该帮会的申请列表已满";
         temeArr[20008] = "用户已经有帮会了";
         temeArr[20009] = "已经申请过了";
         temeArr[20010] = "用户还没有加入任何帮会";
         temeArr[20011] = "不存在该帮会";
         temeArr[20012] = "移除成员失败,用户不属于该帮会";
         temeArr[20013] = "移除成员失败,帮主不能被移除";
         temeArr[20014] = "审核失败,帮会成员已满 ";
         temeArr[20015] = "编辑失败,只有帮主有该权限";
         temeArr[20016] = "超过最大贡献值";
         temeArr[20017] = "不存在该公共变量";
         temeArr[20018] = "超过最大数量";
         temeArr[20019] = "文本信息的字符数超过最大个数限制(1500)";
         temeArr[20020] = "退出帮会后,24小时内不能申请加帮会";
         temeArr[20021] = "没有兑换配置";
         temeArr[20022] = "用户的申请信息已经过期";
         temeArr[20023] = "帮会id错误";
         temeArr[20024] = "已经申请过解散帮会了";
         temeArr[20025] = "没有该任务";
         temeArr[20026] = "用户不在审核列表中";
         temeArr[20027] = "只有在加入帮会的24小时后才能进行贡献";
         temeArr[20028] = "没有解散过帮会,不能进行取消解散";
         temeArr[20029] = "公共变量未到生效时间";
         temeArr[20030] = "账号不能变换存档加入同一个帮会";
         temeArr[30001] = "数据库添加失败";
         temeArr[30002] = "数据库删除失败";
         temeArr[40001] = "特殊用户的type填写错误";
         temeArr[40002] = "没有这个用户";
         TiaoShi.txtShow("帮会错误返回 eId:" + e.data.eId + "  message:" + e.data.msg + "\n");
         if(temeArr[e.data.eId])
         {
            if(e.data.eId != 20016)
            {
               NewMC.Open("文字提示",Main._stage,480,300,40,0,true,1,temeArr[e.data.eId]);
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,300,40,0,true,1,"帮会接口未知错误:" + e.data.eId);
         }
      }
   }
}

