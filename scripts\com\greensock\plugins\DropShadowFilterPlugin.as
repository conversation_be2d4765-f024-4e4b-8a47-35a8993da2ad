package com.greensock.plugins
{
   import com.greensock.*;
   import flash.filters.*;
   
   public class DropShadowFilterPlugin extends FilterPlugin
   {
      
      public static const API:Number = 1;
      
      private static var _propNames:Array = ["distance","angle","color","alpha","blurX","blurY","strength","quality","inner","knockout","hideObject"];
      
      public function DropShadowFilterPlugin()
      {
         super();
         this.propName = "dropShadowFilter";
         this.overwriteProps = ["dropShadowFilter"];
      }
      
      override public function onInitTween(target:Object, value:*, tween:TweenLite) : <PERSON><PERSON>an
      {
         _target = target;
         _type = DropShadowFilter;
         initFilter(value,new DropShadowFilter(0,45,0,0,0,0,1,int(value.quality) || 2,value.inner,value.knockout,value.hideObject),_propNames);
         return true;
      }
   }
}

