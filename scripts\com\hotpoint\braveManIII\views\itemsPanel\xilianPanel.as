package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class xilianPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var xlPanel:MovieClip;
      
      public static var xlp:xilianPanel;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var myplayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      public static var clickNum:Number = 100;
      
      public static var tempTime:Number = 0;
      
      public static var isPOne:Boolean = true;
      
      public static var moneyOK:int = 0;
      
      public static var yeshu:Number = 0;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_XL_v1903.swf";
      
      public function xilianPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!xlPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = xlPanel.getChildIndex(xlPanel["e" + i]);
            mm.x = xlPanel["e" + i].x;
            mm.y = xlPanel["e" + i].y;
            mm.name = "e" + i;
            xlPanel.removeChild(xlPanel["e" + i]);
            xlPanel["e" + i] = mm;
            xlPanel.addChild(mm);
            xlPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 6; i++)
         {
            mm = new Shop_picNEW();
            num = xlPanel.getChildIndex(xlPanel["s" + i]);
            mm.x = xlPanel["s" + i].x;
            mm.y = xlPanel["s" + i].y;
            mm.name = "s" + i;
            xlPanel.removeChild(xlPanel["s" + i]);
            xlPanel["s" + i] = mm;
            xlPanel.addChild(mm);
            xlPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = xlPanel.getChildIndex(xlPanel["select"]);
         mm.x = xlPanel["select"].x;
         mm.y = xlPanel["select"].y;
         mm.name = "select";
         xlPanel.removeChild(xlPanel["select"]);
         xlPanel["select"] = mm;
         xlPanel.addChild(mm);
         xlPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("XLShow") as Class;
         xlPanel = new classRef();
         xlp.addChild(xlPanel);
         InitIcon();
         if(OpenYN)
         {
            open(isPOne);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         xlp = new xilianPanel();
         LoadSkin();
         Main._stage.addChild(xlp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         xlp = new xilianPanel();
         Main._stage.addChild(xlp);
         OpenYN = false;
      }
      
      public static function open(pp:Boolean) : void
      {
         Main.allClosePanel();
         Main.DuoKai_Fun();
         if(xlPanel)
         {
            noShow();
            Main.stopXX = true;
            xlp.x = 0;
            xlp.y = 0;
            isPOne = pp;
            myplayer = Main.player1;
            addListenerP1();
            Main._stage.addChild(xlp);
            xlp.visible = true;
         }
         else
         {
            isPOne = pp;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(xlPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            xlp.visible = false;
            clickObj = null;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         xlPanel["waiting"].visible = false;
         xlPanel["xl_btn"].addEventListener(MouseEvent.CLICK,doXL);
         xlPanel["xl_btn"].addEventListener(MouseEvent.MOUSE_OVER,doOVER);
         xlPanel["xl_btn"].addEventListener(MouseEvent.MOUSE_OUT,doOUT);
         xlPanel["close"].addEventListener(MouseEvent.CLICK,closeXL);
         for(var i:uint = 0; i < 24; i++)
         {
            xlPanel["e" + i].mouseChildren = false;
            xlPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xlPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xlPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 6; i++)
         {
            xlPanel["s" + i].mouseChildren = false;
            xlPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xlPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xlPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         xlPanel["s1_mc"].stop();
         xlPanel["select"].gotoAndStop(1);
         xlPanel["select"].visible = false;
         xlPanel["tishi_mc"].visible = false;
         xlPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         xlPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         if(Main.P1P2)
         {
            xlPanel["pb_0"].visible = true;
            xlPanel["pb_1"].visible = true;
            xlPanel["back_btn"].visible = true;
            xlPanel["pb_0"].addEventListener(MouseEvent.CLICK,toP1);
            xlPanel["pb_1"].addEventListener(MouseEvent.CLICK,toP2);
         }
         else
         {
            xlPanel["pb_0"].visible = false;
            xlPanel["pb_1"].visible = false;
            xlPanel["back_btn"].visible = false;
         }
         showAll();
      }
      
      public static function toP1(e:*) : *
      {
         myplayer = Main.player1;
         xlPanel["pb_0"].visible = false;
         xlPanel["pb_1"].visible = true;
         showAll();
      }
      
      public static function toP2(e:*) : *
      {
         myplayer = Main.player2;
         xlPanel["pb_0"].visible = true;
         xlPanel["pb_1"].visible = false;
         showAll();
      }
      
      public static function showAll() : *
      {
         var i:uint = 0;
         var begin:int = 0;
         var yeshuNum:int = yeshu + 1;
         xlPanel["yeshu_txt"].text = yeshuNum + "/2";
         for(i = 0; i < 24; i++)
         {
            xlPanel["e" + i].t_txt.text = "";
            if(myplayer.getBag().getEquipFromBag(i + yeshu * 24) != null && (myplayer.getBag().getEquipFromBag(i + yeshu * 24).getPosition() <= 7 || myplayer.getBag().getEquipFromBag(i + yeshu * 24).getPosition() >= 10))
            {
               xlPanel["e" + i].gotoAndStop(myplayer.getBag().getEquipFromBag(i + yeshu * 24).getFrame());
               xlPanel["e" + i].visible = true;
            }
            else
            {
               xlPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 6; i++)
         {
            begin = int(i);
            if(Main.water.getValue() != 1 && (begin == 0 || begin == 1 || begin == 3 || begin == 4))
            {
               begin += 8;
            }
            xlPanel["s" + i].t_txt.text = "";
            if(myplayer.getEquipSlot().getEquipFromSlot(begin) != null)
            {
               xlPanel["s" + i].gotoAndStop(myplayer.getEquipSlot().getEquipFromSlot(begin).getFrame());
               xlPanel["s" + i].visible = true;
            }
            else
            {
               xlPanel["s" + i].visible = false;
            }
         }
         xlPanel["chose"].visible = false;
         xlPanel.point_txt.text = Shop4399.moneyAll.getValue();
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         xlPanel["close"].removeEventListener(MouseEvent.CLICK,closeXL);
         xlPanel["xl_btn"].removeEventListener(MouseEvent.CLICK,doXL);
         for(i = 0; i < 24; i++)
         {
            xlPanel["e" + i].mouseChildren = false;
            xlPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xlPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xlPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 6; i++)
         {
            xlPanel["s" + i].mouseChildren = false;
            xlPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            xlPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            xlPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         xlPanel["up_btn"].removeEventListener(MouseEvent.CLICK,upGo);
         xlPanel["down_btn"].removeEventListener(MouseEvent.CLICK,downGo);
      }
      
      public static function upGo(e:*) : *
      {
         yeshu = 0;
         showAll();
      }
      
      public static function downGo(e:*) : *
      {
         yeshu = 1;
         showAll();
      }
      
      public static function closeXL(e:*) : *
      {
         close();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         xlPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(str == "e")
         {
            overNum += 24 * yeshu;
            if(myplayer.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(myplayer.getBag().getEquipFromBag(overNum),1);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(overNum,myplayer.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = xlPanel.mouseX + 10;
         itemsTooltip.y = xlPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function doOVER(e:*) : *
      {
         xlPanel["tishi_mc"].visible = true;
         xlPanel["tishi_mc"]["juan_num"].text = "当前拥有" + myplayer.getBag().getOtherobjNum(63210) + "张洗练卷";
      }
      
      private static function doOUT(e:*) : *
      {
         xlPanel["tishi_mc"].visible = false;
      }
      
      private static function doXL(e:*) : *
      {
         var i:uint = 0;
         if(clickObj)
         {
            xlPanel["xl_btn"].visible = false;
            if(myplayer.getBag().getOtherobjNum(63210) > 0)
            {
               myplayer.getBag().delOtherById(63210,1);
               xlPanel["waiting"].visible = true;
               moneyOK = 2;
               Main.Save();
               return;
            }
            if(Shop4399.moneyAll.getValue() >= 3)
            {
               Api_4399_All.BuyObj(InitData.XiLian_Money.getValue());
               moneyOK = 1;
               xlPanel["waiting"].visible = true;
               for(i = 0; i < 24; i++)
               {
                  xlPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
               }
               for(i = 0; i < 6; i++)
               {
                  xlPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"缺少洗炼卷或点卷");
            }
         }
      }
      
      public static function xlOK() : *
      {
         var eq:Equip = null;
         var i:uint = 0;
         if(xilianPanel.xlPanel)
         {
            xilianPanel.xlPanel["xl_btn"].visible = true;
         }
         if(moneyOK > 0)
         {
            if(nameStr == "e")
            {
               eq = myplayer.getBag().delEquip(clickNum).reCreatEquip();
               myplayer.getBag().addEquipBag(eq,clickNum);
               showAttrib2(myplayer.getBag().getEquipFromBag(clickNum));
            }
            else
            {
               eq = myplayer.getEquipSlot().delSlot(clickNum).reCreatEquip();
               myplayer.getEquipSlot().addToSlot(eq,clickNum);
               showAttrib2(myplayer.getEquipSlot().getEquipFromSlot(clickNum));
            }
            for(i = 0; i < 24; i++)
            {
               xlPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
            }
            for(i = 0; i < 6; i++)
            {
               xlPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
            }
            JiHua_Interface.ppp4_10 = true;
            if(moneyOK == 2)
            {
               xlPanel["xl_btn"].visible = true;
            }
            xlPanel["waiting"].visible = false;
            xlPanel["s1_mc"].gotoAndPlay(2);
            tempTime = 0;
            moneyOK = 0;
         }
      }
      
      private static function noShow() : *
      {
         for(var i:uint = 0; i < 8; i++)
         {
            xlPanel["txt_" + i].visible = false;
            xlPanel["txtb_" + i].visible = false;
         }
      }
      
      private static function showAttrib(equip:Equip) : *
      {
         var suoStr:String = null;
         var dataXX:EquipBaseAttrib = null;
         var numMax:int = 0;
         noShow();
         var Attribarr:Array = equip.showBaseAttrib();
         var temp1:uint = 0;
         var temp2:uint = 4;
         for(var i:uint = 0; i < Attribarr.length; i++)
         {
            suoStr = "";
            dataXX = equip.baseAttrib[i];
            numMax = int(EquipFactory.getValueMax(equip._base.getValue(),i));
            if(dataXX.getValue() == numMax)
            {
               suoStr = "(Max)";
            }
            if(Attribarr[i][0] == 1)
            {
               xlPanel["txt_" + temp1].text = Attribarr[i][1] + suoStr;
               xlPanel["txt_" + temp1].visible = true;
               temp1++;
            }
            else
            {
               xlPanel["txt_" + temp2].text = Attribarr[i][1];
               xlPanel["txt_" + temp2].visible = true;
               temp2++;
            }
         }
      }
      
      private static function showAttrib2(equip:Equip) : *
      {
         var suoStr:String = null;
         var dataXX:EquipBaseAttrib = null;
         var numMax:int = 0;
         var Attribarr:Array = equip.showBaseAttrib();
         var temp1:uint = 0;
         var temp2:uint = 4;
         for(var i:uint = 0; i < Attribarr.length; i++)
         {
            if(Attribarr[i][0] == 1)
            {
               suoStr = "";
               dataXX = equip.baseAttrib[i];
               numMax = int(EquipFactory.getValueMax(equip._base.getValue(),i));
               if(dataXX.getValue() == numMax)
               {
                  suoStr = "(Max)";
               }
               xlPanel["txtb_" + temp1].text = Attribarr[i][1] + suoStr;
               xlPanel["txtb_" + temp1].visible = true;
               temp1++;
            }
            else
            {
               xlPanel["txtb_" + temp2].text = Attribarr[i][1];
               xlPanel["txtb_" + temp2].visible = true;
               temp2++;
            }
         }
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         xlPanel["xl_btn"].visible = true;
         clickObj = e.target as MovieClip;
         xlPanel["chose"].visible = true;
         xlPanel["chose"].x = clickObj.x - 2;
         xlPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         if(nameStr != "e" && Main.water.getValue() != 1 && (clickNum == 0 || clickNum == 1 || clickNum == 3 || clickNum == 4))
         {
            clickNum += 8;
         }
         if(nameStr == "e")
         {
            clickNum += 24 * yeshu;
         }
         xlPanel["select"].gotoAndStop(clickObj.currentFrame);
         xlPanel["select"].visible = true;
         if(nameStr == "e")
         {
            showAttrib(myplayer.getBag().getEquipFromBag(clickNum));
         }
         else
         {
            showAttrib(myplayer.getEquipSlot().getEquipFromSlot(clickNum));
         }
      }
   }
}

