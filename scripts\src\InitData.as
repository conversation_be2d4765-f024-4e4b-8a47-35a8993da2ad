package src
{
   import com.hotpoint.braveManIII.models.common.*;
   import flash.events.*;
   import flash.utils.*;
   
   public class InitData
   {
      
      public static var qinMiDu_Arr:Array;
      
      public static var qinMiDu_Max_Arr:Array;
      
      public static var qinMiDu_num:Array;
      
      public static var qinMiDu_Time:Array;
      
      public static var XX1:VT;
      
      public static var zongzi:VT = VT.createVT();
      
      public static var tiaoZhanJF_0:VT = VT.createVT(0);
      
      public static var tiaoZhanJF_1:VT = VT.createVT(1);
      
      public static var tiaoZhanJF_2:VT = VT.createVT(2);
      
      public static var tiaoZhanJF_3:VT = VT.createVT(3);
      
      public static var tiaoZhanJF_10:VT = VT.createVT(10);
      
      public static var tiaoZhanJF_100:VT = VT.createVT(100);
      
      public static var tiaoZhanJF_500:VT = VT.createVT(500);
      
      public static var tiaoZhanJF_5000:VT = VT.createVT(5000);
      
      public static var tiaoZhanJF_80:VT = VT.createVT(80);
      
      public static var tiaoZhanJF_60:VT = VT.createVT(60);
      
      public static var tiaoZhanJF_40:VT = VT.createVT(40);
      
      public static var tiaoZhanJF_20:VT = VT.createVT(20);
      
      public static var tiaoZhanJF_8:VT = VT.createVT(8);
      
      public static var tiaoZhanJF_4:VT = VT.createVT(4);
      
      public static var shengdanArr:Array = [];
      
      public static var cjPoint:VT = VT.createVT(1);
      
      public static var timeNum:VT = VT.createVT();
      
      private static var timeInterval:VT = VT.createVT(1000);
      
      public static var DOWNxN:VT = VT.createVT();
      
      public static var DOWNxTime:VT = VT.createVT();
      
      public static var EXPxN:VT = VT.createVT();
      
      public static var EXPxTime:VT = VT.createVT();
      
      public static var BuyNum_Money:VT = VT.createVT();
      
      public static var BuyS_Money:VT = VT.createVT();
      
      public static var XiLian_Money:VT = VT.createVT();
      
      public static var EXP_num:VT = VT.createVT();
      
      public static var EXP_big_num:VT = VT.createVT();
      
      public static var no_jiaoYan:VT = VT.createVT();
      
      public static var jiaoYan_num:VT = VT.createVT();
      
      public static var fuHua:VT = VT.createVT();
      
      public static var cwJiNeng:VT = VT.createVT();
      
      public static var cwWuXing:VT = VT.createVT();
      
      public static var cwShengJi:VT = VT.createVT();
      
      public static var cwXingGe:VT = VT.createVT();
      
      public static var cwBag:VT = VT.createVT();
      
      public static var cwSlot:VT = VT.createVT();
      
      public static var kuoBag:VT = VT.createVT();
      
      public static var chongzhirenwu:VT = VT.createVT();
      
      public static var tongyongjiesuo:VT = VT.createVT();
      
      public static var blue:VT = VT.createVT();
      
      public static var pink:VT = VT.createVT();
      
      public static var gold:VT = VT.createVT();
      
      public static var jlexp:VT = VT.createVT();
      
      public static var chunjie143:VT = VT.createVT();
      
      public static var jifen10:VT = VT.createVT();
      
      public static var jifen30:VT = VT.createVT();
      
      public static var jifen50:VT = VT.createVT();
      
      public static var fiveone1:VT = VT.createVT();
      
      public static var fiveone2:VT = VT.createVT();
      
      public static var fiveone3:VT = VT.createVT();
      
      public static var temai159:VT = VT.createVT();
      
      public static var temai144:VT = VT.createVT();
      
      public static var temai145:VT = VT.createVT();
      
      public static var temai146:VT = VT.createVT();
      
      public static var temai147:VT = VT.createVT();
      
      public static var temai148:VT = VT.createVT();
      
      public static var temai237:VT = VT.createVT();
      
      public static var temai238:VT = VT.createVT();
      
      public static var newyear15:VT = VT.createVT();
      
      public static var temai151:VT = VT.createVT();
      
      public static var Money_init:VT = VT.createVT();
      
      public static var Money_max:VT = VT.createVT();
      
      public static var noTestVar:VT = VT.createVT();
      
      public static var pkHpMaxNum_Player:VT = VT.createVT();
      
      public static var pkHpMaxNum_Player2:VT = VT.createVT();
      
      public static var X002:VT = VT.createVT();
      
      public static var tNum_100:VT = VT.createVT(100);
      
      public static var tNum_200:VT = VT.createVT(200);
      
      public static var tNum_300:VT = VT.createVT(300);
      
      public static var BuyNum_0:VT = VT.createVT();
      
      public static var BuyNum_1:VT = VT.createVT();
      
      public static var BuyNum_3:VT = VT.createVT();
      
      public static var BuyNum_119:VT = VT.createVT();
      
      public static var BuyNum_20:VT = VT.createVT();
      
      public static var BuyNum_30:VT = VT.createVT();
      
      public static var BuyNum_40:VT = VT.createVT();
      
      public static var BuyNum_50:VT = VT.createVT();
      
      public static var BuyNum_80:VT = VT.createVT();
      
      public static var BuyNum_300:VT = VT.createVT();
      
      public static var BuyNum_2000:VT = VT.createVT();
      
      public static var BuyNum_80000:VT = VT.createVT();
      
      public static var BuyNum_18000:VT = VT.createVT();
      
      public static var Temp0:VT = VT.createVT();
      
      public static var Temp01:VT = VT.createVT();
      
      public static var Temp5:VT = VT.createVT();
      
      public static var Temp7:VT = VT.createVT();
      
      public static var Temp8:VT = VT.createVT();
      
      public static var Temp50:VT = VT.createVT();
      
      public static var Temp100:VT = VT.createVT();
      
      public static var Temp500:VT = VT.createVT();
      
      public static var Temp1000:VT = VT.createVT();
      
      public static var Temp3000:VT = VT.createVT();
      
      public static var Temp8000:VT = VT.createVT();
      
      public static var xingLing:VT = VT.createVT();
      
      public static var xingLing_num9:VT = VT.createVT();
      
      public static var xingLing_ID55:VT = VT.createVT();
      
      public static var vip2990:VT = VT.createVT();
      
      public static var vipID:VT = VT.createVT();
      
      public static var vip10:VT = VT.createVT();
      
      public static var yueKa30:VT = VT.createVT();
      
      public static var yueKaID:VT = VT.createVT();
      
      public static var PaiNumMax:VT = VT.createVT();
      
      public static var xingLingXiShu_Arr:Array = new Array();
      
      public static var xL_Arr:Array = new Array();
      
      public static var ShopId_Arr:Array = new Array();
      
      public static var BuyNum_63132:VT = VT.createVT(63132);
      
      public static var BuyNum_63133:VT = VT.createVT(63133);
      
      public static var BuyNum_63134:VT = VT.createVT(63134);
      
      public static var BuyNum_63135:VT = VT.createVT(63135);
      
      public static var BuyNum_63136:VT = VT.createVT(63136);
      
      public static var BuyNum_63137:VT = VT.createVT(63137);
      
      public static var BuyNum_63138:VT = VT.createVT(63138);
      
      public static var LingQue_1:VT = VT.createVT(5);
      
      public static var LingQue_2:VT = VT.createVT(10);
      
      public static var LingQue_3:VT = VT.createVT(15);
      
      public static var LingQue_4:VT = VT.createVT(28);
      
      public static var LingQue_10:VT = VT.createVT(10);
      
      public static var LingQue_X0:VT = VT.createVT(63203);
      
      public static var LingQue_X1:VT = VT.createVT(63235);
      
      public static var LingQue_X2:VT = VT.createVT(21222);
      
      public static var LingQue_X3:Array = [];
      
      public function InitData()
      {
         super();
      }
      
      public static function Init() : *
      {
         var i:int = 0;
         var intervalId:int = 0;
         var tempVt:VT = null;
         var tempVt2:VT = null;
         var tempVt3:VT = null;
         var tempVt4:VT = null;
         LingQue_X3 = [VT.createVT(0),VT.createVT(63204),VT.createVT(63204),VT.createVT(63204),VT.createVT(63204),VT.createVT(63204),VT.createVT(63204),VT.createVT(63204),VT.createVT(63204),VT.createVT(14647),VT.createVT(14648),VT.createVT(14651),VT.createVT(14654)];
         PaiNumMax.setValue(9999999);
         vip10.setValue(56);
         vip2990.setValue(2990);
         vipID.setValue(104);
         yueKa30.setValue(198);
         yueKaID.setValue(281);
         xingLingXiShu_Arr = [VT.createVT(0),VT.createVT(0),VT.createVT(0.25),VT.createVT(0.5),VT.createVT(0.75),VT.createVT(1),VT.createVT(2)];
         xL_Arr = [VT.createVT(5),VT.createVT(10),VT.createVT(15),VT.createVT(20),VT.createVT(25),VT.createVT(30),VT.createVT(40),VT.createVT(50)];
         xingLing.setValue(62);
         xingLing_num9.setValue(9);
         xingLing_ID55.setValue(55);
         Temp0.setValue(0);
         Temp01.setValue(0.1);
         Temp5.setValue(5);
         Temp7.setValue(7);
         Temp8.setValue(0.8);
         Temp50.setValue(50);
         Temp100.setValue(100);
         Temp500.setValue(500);
         Temp1000.setValue(1000);
         Temp3000.setValue(3000);
         Temp8000.setValue(8000);
         X002.setValue(0.02);
         fuHua.setValue(60);
         no_jiaoYan.setValue(1000);
         jiaoYan_num.setValue(60);
         BuyNum_0.setValue(0);
         BuyNum_1.setValue(1);
         BuyNum_119.setValue(119);
         BuyNum_20.setValue(20);
         BuyNum_30.setValue(30);
         BuyNum_40.setValue(40);
         BuyNum_50.setValue(66);
         BuyNum_80.setValue(80);
         BuyNum_300.setValue(300);
         BuyNum_2000.setValue(2000);
         BuyNum_80000.setValue(80000);
         BuyNum_18000.setValue(18000);
         cwJiNeng.setValue(57);
         cwShengJi.setValue(59);
         cwWuXing.setValue(58);
         cwXingGe.setValue(130);
         cwBag.setValue(128);
         cwSlot.setValue(129);
         kuoBag.setValue(230);
         chongzhirenwu.setValue(44);
         tongyongjiesuo.setValue(67);
         blue.setValue(63);
         pink.setValue(64);
         gold.setValue(65);
         jlexp.setValue(193);
         chunjie143.setValue(143);
         jifen10.setValue(124);
         jifen30.setValue(125);
         jifen50.setValue(126);
         fiveone1.setValue(246);
         fiveone2.setValue(247);
         fiveone3.setValue(248);
         temai144.setValue(144);
         temai145.setValue(145);
         temai146.setValue(146);
         temai147.setValue(147);
         temai148.setValue(290);
         temai237.setValue(267);
         temai238.setValue(291);
         temai151.setValue(151);
         temai159.setValue(159);
         newyear15.setValue(240);
         zongzi.setValue(0.25);
         for(i = 0; i < 11; i++)
         {
            VT.TempVtArr[i] = VT.createVT(i);
         }
         XX1 = VT.createVT(1);
         DOWNxN = VT.createVT(2);
         DOWNxTime = VT.createVT(0);
         EXPxN = VT.createVT(1.5);
         EXPxTime = VT.createVT(0);
         intervalId = int(setInterval(TimeGo,timeInterval.getValue()));
         qinMiDu_Arr = new Array();
         qinMiDu_Max_Arr = new Array();
         qinMiDu_num = new Array();
         qinMiDu_Time = new Array();
         for(i = 0; i <= 5; i++)
         {
            tempVt = VT.createVT();
            qinMiDu_Arr[i] = tempVt;
            tempVt2 = VT.createVT(50);
            qinMiDu_Max_Arr[i] = tempVt2;
            tempVt3 = VT.createVT(1);
            qinMiDu_num[i] = tempVt3;
            tempVt4 = VT.createVT();
            qinMiDu_Time[i] = tempVt4;
         }
         BuyNum_Money.setValue(61);
         BuyS_Money.setValue(103);
         XiLian_Money.setValue(43);
         EXP_num.setValue(50000);
         EXP_big_num.setValue(500000);
         BuyNum_3.setValue(3);
         Money_init.setValue(4399);
         Money_max.setValue(500000000);
         noTestVar.setValue(1000);
         pkHpMaxNum_Player.setValue(8);
         pkHpMaxNum_Player2.setValue(8);
         shengdanArr = [[3,10],[6,11],[9,12],[13,13],[16,14],[19,15],[23,16],[28,17],[32,19],[35,20],[41,22],[46,23],[50,25]];
         Player.Init();
      }
      
      private static function TimeGo() : *
      {
         if(DOWNxTime.getValue() > 0)
         {
            DOWNxTime.setValue(DOWNxTime.getValue() - 1);
         }
         if(EXPxTime.getValue() > 0)
         {
            EXPxTime.setValue(EXPxTime.getValue() - 1);
         }
         timeNum.setValue(timeNum.getValue() + 1);
      }
      
      public static function DOWNxTimeUp(num:uint) : *
      {
         DOWNxTime.setValue(DOWNxTime.getValue() + num);
      }
      
      public static function EXPxTimeUp(num:uint) : *
      {
         EXPxTime.setValue(EXPxTime.getValue() + num);
      }
      
      public static function SaveInitData() : Array
      {
         return [DOWNxTime.getValue(),EXPxTime.getValue(),qinMiDu_Arr,qinMiDu_Max_Arr,qinMiDu_num,qinMiDu_Time];
      }
      
      public static function LoadInitData(arr:Array) : *
      {
         DOWNxTime.setValue(arr[0]);
         EXPxTime.setValue(arr[1]);
         if(arr[2])
         {
            qinMiDu_Arr = arr[2];
            qinMiDu_num = arr[4];
            qinMiDu_Time = arr[5];
         }
      }
      
      public static function GetDOWNxN() : VT
      {
         if(DOWNxTime.getValue() > 0)
         {
            return DOWNxN;
         }
         return XX1;
      }
      
      public static function GetEXPxN() : VT
      {
         if(EXPxTime.getValue() > 0)
         {
            return EXPxN;
         }
         return XX1;
      }
   }
}

