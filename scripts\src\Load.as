package src
{
   import com.hotpoint.braveManIII.repository.skill.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.npc.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol3948")]
   public class Load extends MovieClip
   {
      
      public static var one:Boolean;
      
      public static var loadingX:Load;
      
      public static var percentLoaded:int;
      
      public static var All_Loaded:Boolean;
      
      public static var loadOk:Boolean;
      
      public static var enemyArrX2:Array;
      
      public static var XMLclass:ClassLoader;
      
      public static var loadName:int = 0;
      
      public static var loadName2:int = 14;
      
      public static var txtArr:Array = ["右手的武器需要将背包中的武器拖动到右手武器槽里才能替换","点击“菜单”栏内的“键位设置”按钮可以设置游戏快捷键","在关卡中可以点击“菜单”栏内的“返回城镇”按钮快速回城","玩家等级达到25级并且拥有“胜利的凭证”与“8000金币”，即可在奥古斯汀处进行转职","转职之后可以学习更多更炫的转职技能","您只能从两个职业中选择一个进行转职","女神碎片可在“冰雪废墟”、“废弃都市”与“幽灵船”关卡内获得","每为女神像修补一块女神碎片便可以开启一个神秘副本","获得女神像碎片后，只需点击女神像相应缺口即可对女神像进行修补","神秘副本的Boss鳄鱼公爵有一定机率掉落转职物品“胜利的凭证”","可以通过“镶嵌属性石”让装备更加强劲","1P和2P的仓库是共用的，可以通过仓库进行1P、2P间的物品交换","“装备商人—道格拉斯”可以进行“强化”“合成”“镶嵌”等功能","如果游戏过程比较卡，可以适当降低游戏画质","在“技能导师—奥古斯汀”处可学习各系职业的“主动技能”和“被动技能”","如果游戏卡在加载界面，建议刷新游戏页面尝试","剑系的“断空追击”“狱火天焰”技能，发动时有无敌状态","属性石分为“防御”“攻击”“生命”“魔法”“暴击”“闪避”六种类型","神秘副本的BOSS都有几率掉落“史诗武器”“强化石”等稀有物品","所有BOSS都有几率掉落该BOSS的“技能石”","通过“强化装备”可以大幅度提升装备属性","转职后装备两把相同类型的武器，可以大幅提升玩家属性噢","熟练连击技巧可以使你更轻松的过关","打低级关卡赚钱是一个很好的赚钱办法噢"];
      
      public static var mapArr:Array = [];
      
      public static var enemyArr:Array = [];
      
      public static var otherArr:Array = [];
      
      private static var 提示time:int = 135;
      
      private static var timeXX:uint = 0;
      
      public static var loadGameNum700X:int = 1;
      
      private static var enemyAll_7001:Array = [8,14,62,13,82,52,59,4];
      
      private static var enemyAll_7002:Array = [59,60,5,16,55,54];
      
      private static var enemyAll_7003:Array = [16,236,12,7,239,29,5];
      
      public static var loadGameNumYY:int = 1;
      
      private static var yaoYuan_All_Arr:Array = [7,4,8,6,5,52,9,59,16,13,5,60,62,22,3000];
      
      public static var loadGameNum:int = 1;
      
      private static var Enemy_All_Arr:Array = [1,2,3,4,5,6,7,8,9,10,11,13,18,19,20,21,22,101,102,103,201,203,1001];
      
      public static var loadGameNumQ:int = 1;
      
      private static var Enemy_All_ArrX:Array = [5,6,7,8,9,10,13,20,24,25,104,213,234,1000];
      
      public static var loadGameNumQX:int = 1;
      
      private static var Enemy_All_ArrXX:Array = [212,24,25,54,21,18,104,16,29,2000];
      
      public static var loadGameNum2015:int = 1;
      
      private static var Enemy_All_Arr2015:Array = [2015];
      
      public static var loadGameNum2:int = 1;
      
      private static var Enemy_All_Arr2:Array = [51,52,53,81];
      
      public static var loadGameNum3:int = 1;
      
      private static var Enemy_All_Arr3:Array = [54,55,56,82];
      
      public static var loadGameNum4:int = 1;
      
      private static var Enemy_All_Arr4:Array = [60,61,84];
      
      public var _mc:MovieClip;
      
      public var gun_txt:TextField;
      
      public var load_mc:MovieClip;
      
      public var load_name_txt:TextField;
      
      public var load_txt:TextField;
      
      public function Load()
      {
         super();
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         if(!loadingX)
         {
            loadingX = new Load();
         }
         Main._stage.addChild(loadingX);
         loadingX.x = xx;
         loadingX.y = yy;
         loadingX.visible = true;
         Loading();
         loadingX._mc.gotoAndPlay(1);
         loadingX.addEventListener(Event.ENTER_FRAME,onLoading);
      }
      
      public static function Close() : *
      {
         if(!loadingX)
         {
            loadingX = new Load();
         }
         loadingX.x = 5000;
         loadingX.y = 5000;
         loadingX.visible = false;
         loadingX._mc.stop();
         loadingX.removeEventListener(Event.ENTER_FRAME,onLoading);
         Main._this._stage_txt.text += "Load.Close() \n";
      }
      
      private static function onLoading(e:*) : *
      {
         loadingX.load_mc.load100_mc.scaleX = Load.percentLoaded / 100;
         loadingX.load_txt.text = Load.percentLoaded + "%";
         if(Load.loadName == -1)
         {
            loadingX.load_name_txt.text = "加载完成";
         }
         else
         {
            loadingX.load_name_txt.text = Load.loadName + "/" + Load.loadName2;
         }
         ++提示time;
         if(提示time % 135 == 0)
         {
            提示();
         }
      }
      
      private static function 提示() : *
      {
         var i:int = Math.random() * txtArr.length;
         loadingX.gun_txt.text = txtArr[i];
      }
      
      public static function Loading(str:int = 0, showYn:Boolean = true) : *
      {
         var str700X:String = null;
         if(!showYn)
         {
            loadingX.visible = false;
         }
         else if(!loadingX.visible)
         {
            loadingX.visible = true;
            return;
         }
         if(Main.gameNum.getValue() == 0)
         {
            if(Main.newPlay == 1)
            {
               loadName2 = 27;
            }
            else
            {
               loadName2 = 23;
            }
         }
         else if(Main.gameNum.getValue() == 81)
         {
            loadName2 = 5;
         }
         else if(Main.gameNum.getValue() == 17)
         {
            loadName2 = Enemy_All_Arr.length + 1;
         }
         else if(Main.gameNum.getValue() == 1000)
         {
            loadName2 = Enemy_All_ArrX.length + 1;
         }
         else if(Main.gameNum.getValue() == 2000)
         {
            loadName2 = Enemy_All_ArrXX.length + 1;
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            loadName2 = yaoYuan_All_Arr.length + 1;
         }
         else if(Main.gameNum.getValue() >= 7001 && Main.gameNum.getValue() <= 7003)
         {
            str700X = "enemyAll_" + Main.gameNum.getValue();
            loadName2 = Load[str700X].length + 1;
         }
         else
         {
            loadName2 = 3;
         }
         if(str == 0)
         {
            loadName = 1;
         }
         else
         {
            loadName = str;
         }
         if(loadName == 1)
         {
            Loading_Map();
         }
         else if(loadName == 2)
         {
            if(Main.gameNum.getValue() == 17)
            {
               Loading_Enemy_All();
            }
            else if(Main.gameNum.getValue() == 1000)
            {
               Loading_Enemy_AllX();
            }
            else if(Main.gameNum.getValue() == 2000)
            {
               Loading_Enemy_AllXX();
            }
            else if(Main.gameNum.getValue() == 81)
            {
               Loading_Enemy_All2();
            }
            else if(Main.gameNum.getValue() == 82)
            {
               Loading_Enemy_All3();
            }
            else if(Main.gameNum.getValue() == 84)
            {
               Loading_Enemy_All4();
            }
            else if(Main.gameNum.getValue() == 999)
            {
               All_Loaded = true;
            }
            else if(Main.gameNum.getValue() == 2015)
            {
               Loading_Enemy2015();
            }
            else if(Main.gameNum.getValue() == 3000)
            {
               Loading_Enemy_All_YY();
            }
            else if(Main.gameNum.getValue() >= 7001 && Main.gameNum.getValue() <= 7003)
            {
               LoadEnemy_700X();
            }
            else
            {
               Loading_Enemy();
            }
         }
         else if(loadName == 3)
         {
            Loading_Data();
         }
         else if(loadName == 4)
         {
            Loading_Player_1();
         }
         else if(loadName == 5)
         {
            Loading_Player_2();
         }
         else if(loadName == 6)
         {
            Loading_Player_3();
         }
         else if(loadName == 7)
         {
            Loading_WuQi_1();
         }
         else if(loadName == 8)
         {
            Loading_WuQi_2();
         }
         else if(loadName == 9)
         {
            Loading_WuQi_3();
         }
         else if(loadName == 10)
         {
            Loading_Data2();
         }
         else if(loadName == 11)
         {
            Loading_Music();
         }
         else if(loadName == 12)
         {
            Loading_ChongWu();
         }
         else if(loadName == 13)
         {
            Loading_ChongWu2();
         }
         else if(loadName == 14)
         {
            if(Main.newPlay == 1)
            {
               Load.Loading(15);
            }
            else
            {
               Loading_SelMap();
            }
         }
         else if(loadName == 15)
         {
            Loading_Npc();
         }
         else if(loadName == 16)
         {
            Load_Other();
         }
         else if(loadName == 17)
         {
            Load_Other2();
         }
         else if(loadName == 18)
         {
            if(Main.newPlay == 1)
            {
               Load.Loading(19);
            }
            else
            {
               Load_ChongZhi();
            }
         }
         else if(loadName == 19)
         {
            Loading_Player_4();
         }
         else if(loadName == 20)
         {
            Loading_WuQi_4();
         }
         else if(loadName == 21)
         {
            NewLoadX();
         }
         else if(loadName == -1)
         {
            if(Main.newPlay == 1)
            {
               Main.newPlay = 2;
            }
            if(showYn)
            {
               All_Loaded = true;
            }
            loadOk = true;
         }
      }
      
      public static function md5_Start() : *
      {
         var tempB:ByteArray = null;
         var tempC:ByteArray = null;
         ++timeXX;
         if(timeXX % 16200 == 15200)
         {
            tempB = Obj_Compare.getObj_ByteArray(SkillFactory.myXml);
            if(Obj_Compare.CompareByteArray(tempB,SkillFactory.myXmlMD5) == false)
            {
               Main.NoGame("修改技能表");
               return;
            }
         }
         else if(timeXX % 16200 == 16100)
         {
            tempC = Obj_Compare.getObj_ByteArray(Enemy.EnemyXmlArr[GameData.gameLV]);
            if(Obj_Compare.CompareByteArray(tempC,Enemy.EnemyXmlArrMd5[GameData.gameLV]) == false)
            {
               Main.NoGame("修改怪物数据");
               return;
            }
         }
      }
      
      public static function LoadEnemy_700X() : *
      {
         var nameStr:String = null;
         var arr:Array = Load["enemyAll_" + Main.gameNum.getValue()];
         var loadGameNumXX:int = int(arr[loadGameNum700X - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX])
         {
            nameStr = enemyArr[loadGameNumXX];
            Enemy.EnemyArr[loadGameNumXX] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX].addEventListener(Event.COMPLETE,onEnemyAll_700X);
            trace("幽灵船长 加载 >>>>>>>>>>>>" + nameStr);
         }
         else
         {
            onEnemyAll_700X();
         }
      }
      
      private static function onEnemyAll_700X(e:* = null) : *
      {
         var str:String = "enemyAll_" + Main.gameNum.getValue();
         var arr:Array = Load[str];
         ++loadGameNum700X;
         loadName = loadGameNum700X;
         if(loadName <= arr.length)
         {
            LoadEnemy_700X();
         }
         else
         {
            loadGameNum700X = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All_YY() : *
      {
         var nameStr:String = null;
         var loadGameNumXX:int = int(yaoYuan_All_Arr[loadGameNumYY - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX])
         {
            nameStr = enemyArr[loadGameNumXX];
            Enemy.EnemyArr[loadGameNumXX] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX].addEventListener(Event.COMPLETE,onLoading_Enemy_All_YY);
         }
         else
         {
            onLoading_Enemy_All_YY();
         }
      }
      
      private static function onLoading_Enemy_All_YY(e:* = null) : *
      {
         ++loadGameNumYY;
         loadName = loadGameNumYY;
         if(loadName <= yaoYuan_All_Arr.length)
         {
            Loading_Enemy_All_YY();
         }
         else
         {
            loadGameNumYY = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All() : *
      {
         var nameStr:String = null;
         var loadGameNumXX:int = int(Enemy_All_Arr[loadGameNum - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX])
         {
            nameStr = enemyArr[loadGameNumXX];
            Enemy.EnemyArr[loadGameNumXX] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX].addEventListener(Event.COMPLETE,onLoading_Enemy_All);
         }
         else
         {
            onLoading_Enemy_All();
         }
      }
      
      private static function onLoading_Enemy_All(e:* = null) : *
      {
         ++loadGameNum;
         loadName = loadGameNum;
         if(loadName <= Enemy_All_Arr.length)
         {
            Loading_Enemy_All();
         }
         else
         {
            loadGameNum = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_AllX() : *
      {
         var nameStr:String = null;
         var loadGameNumXX:int = int(Enemy_All_ArrX[loadGameNumQ - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX])
         {
            nameStr = enemyArr[loadGameNumXX];
            Enemy.EnemyArr[loadGameNumXX] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX].addEventListener(Event.COMPLETE,onLoading_Enemy_AllX);
         }
         else
         {
            onLoading_Enemy_AllX();
         }
      }
      
      private static function onLoading_Enemy_AllX(e:* = null) : *
      {
         ++loadGameNumQ;
         loadName = loadGameNumQ;
         if(loadName <= Enemy_All_ArrX.length)
         {
            Loading_Enemy_AllX();
         }
         else
         {
            loadGameNumQ = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_AllXX() : *
      {
         var nameStr:String = null;
         var loadGameNumXX:int = int(Enemy_All_ArrXX[loadGameNumQX - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX])
         {
            nameStr = enemyArr[loadGameNumXX];
            Enemy.EnemyArr[loadGameNumXX] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX].addEventListener(Event.COMPLETE,onLoading_Enemy_AllXX);
         }
         else
         {
            onLoading_Enemy_AllXX();
         }
      }
      
      private static function onLoading_Enemy_AllXX(e:* = null) : *
      {
         ++loadGameNumQX;
         loadName = loadGameNumQX;
         if(loadName <= Enemy_All_ArrXX.length)
         {
            Loading_Enemy_AllXX();
         }
         else
         {
            loadGameNumQX = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy2015() : *
      {
         var nameStr:String = null;
         var loadGameNumXX2015:int = int(Enemy_All_Arr2015[loadGameNum2015 - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX2015])
         {
            nameStr = enemyArr[loadGameNumXX2015];
            Enemy.EnemyArr[loadGameNumXX2015] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX2015].addEventListener(Event.COMPLETE,onLoading_Enemy_All2015);
         }
         else
         {
            onLoading_Enemy_All2015();
         }
      }
      
      private static function onLoading_Enemy_All2015(e:* = null) : *
      {
         ++loadGameNum2015;
         loadName = loadGameNum2015;
         if(loadName <= Enemy_All_Arr2015.length)
         {
            ChongWu.chongWu_Data[26] = Enemy.EnemyArr[loadGameNumXX2015];
            Loading_Enemy2015();
         }
         else
         {
            loadGameNum2015 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All2() : *
      {
         var nameStr:String = null;
         var loadGameNumXX2:int = int(Enemy_All_Arr2[loadGameNum2 - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX2])
         {
            nameStr = enemyArr[loadGameNumXX2];
            Enemy.EnemyArr[loadGameNumXX2] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX2].addEventListener(Event.COMPLETE,onLoading_Enemy_All2);
         }
         else
         {
            onLoading_Enemy_All2();
         }
      }
      
      private static function onLoading_Enemy_All2(e:* = null) : *
      {
         ++loadGameNum2;
         loadName = loadGameNum2;
         if(loadName <= Enemy_All_Arr2.length)
         {
            Loading_Enemy_All2();
         }
         else
         {
            loadGameNum2 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All3() : *
      {
         var nameStr:String = null;
         var loadGameNumXX3:int = int(Enemy_All_Arr3[loadGameNum3 - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX3])
         {
            nameStr = enemyArr[loadGameNumXX3];
            Enemy.EnemyArr[loadGameNumXX3] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX3].addEventListener(Event.COMPLETE,onLoading_Enemy_All3);
         }
         else
         {
            onLoading_Enemy_All3();
         }
      }
      
      private static function onLoading_Enemy_All3(e:* = null) : *
      {
         ++loadGameNum3;
         loadName = loadGameNum3;
         if(loadName <= Enemy_All_Arr3.length)
         {
            Loading_Enemy_All3();
         }
         else
         {
            loadGameNum3 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Enemy_All4() : *
      {
         var nameStr:String = null;
         var loadGameNumXX4:int = int(Enemy_All_Arr4[loadGameNum4 - 1]);
         if(!Enemy.EnemyArr[loadGameNumXX4])
         {
            nameStr = enemyArr[loadGameNumXX4];
            Enemy.EnemyArr[loadGameNumXX4] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadGameNumXX4].addEventListener(Event.COMPLETE,onLoading_Enemy_All4);
         }
         else
         {
            onLoading_Enemy_All4();
         }
      }
      
      private static function onLoading_Enemy_All4(e:* = null) : *
      {
         ++loadGameNum4;
         loadName = loadGameNum4;
         if(loadName <= Enemy_All_Arr4.length)
         {
            Loading_Enemy_All4();
         }
         else
         {
            loadGameNum4 = 1;
            All_Loaded = true;
         }
      }
      
      public static function Loading_Map() : *
      {
         var gameNumXX:int = 0;
         var nameStr:String = null;
         if(Map.MapArr[Main.gameNum.getValue()])
         {
            Load.Loading(2);
         }
         else
         {
            gameNumXX = int(Main.gameNum.getValue());
            if(gameNumXX > 18 && gameNumXX < 30)
            {
               gameNumXX = 18;
            }
            if(gameNumXX > 5000 && gameNumXX < 5100)
            {
               gameNumXX = 2015;
            }
            trace("Loading_Map ??? >>>",gameNumXX);
            nameStr = mapArr[gameNumXX];
            Map.MapArr[Main.gameNum.getValue()] = new ClassLoader(nameStr);
            Map.MapArr[Main.gameNum.getValue()].addEventListener(Event.COMPLETE,onLoaded_Map);
         }
      }
      
      private static function onLoaded_Map(e:* = null) : *
      {
         Load.Loading(2);
      }
      
      public static function Loading_Enemy() : *
      {
         var loadNum:int = 0;
         var nameStr:String = null;
         if(Main.gameNum.getValue() == 0 || Main.gameNum.getValue() == 7000)
         {
            Load.Loading(3);
         }
         else if(Enemy.EnemyArr[Main.gameNum.getValue()])
         {
            loadName = 3;
            Loading_EnemyX2();
         }
         else
         {
            loadNum = int(Main.gameNum.getValue());
            if(loadNum == 5001)
            {
               loadNum = 3;
            }
            else if(loadNum == 5002)
            {
               loadNum = 4;
            }
            else if(loadNum == 5003)
            {
               loadNum = 8;
            }
            nameStr = enemyArr[loadNum];
            trace("Loading_Enemy????",nameStr);
            Enemy.EnemyArr[loadNum] = new ClassLoader(nameStr);
            Enemy.EnemyArr[loadNum].addEventListener(Event.COMPLETE,onLoaded_Enemy);
         }
      }
      
      private static function onLoaded_Enemy(e:* = null) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            Load.Loading(3);
         }
         else
         {
            loadName = 3;
            Loading_EnemyX2();
         }
      }
      
      public static function Loading_EnemyX2() : *
      {
         var nameStr:String = null;
         var XXid:int = int(Main.wts.getBoss());
         if(XXid == -1 || Boolean(Enemy.EnemyArr[XXid]))
         {
            onLoaded_EnemyX2();
         }
         else
         {
            nameStr = enemyArr[XXid];
            Enemy.EnemyArr[XXid] = new ClassLoader(nameStr);
            Enemy.EnemyArr[XXid].addEventListener(Event.COMPLETE,onLoaded_EnemyX2);
         }
      }
      
      private static function onLoaded_EnemyX2(e:* = null) : *
      {
         All_Loaded = true;
      }
      
      public static function Loading_Data() : *
      {
         if(!XMLclass)
         {
            XMLclass = new ClassLoader(otherArr[0]);
            XMLclass.addEventListener(Event.COMPLETE,onLoaded_Data);
         }
         else
         {
            Load.Loading(4);
         }
      }
      
      private static function onLoaded_Data(e:* = null) : *
      {
         Load.Loading(4);
      }
      
      public static function Loading_Player_1() : *
      {
         if(!Player.PlayerMcArr[0])
         {
            Player.PlayerMcArr[0] = new ClassLoader(otherArr[1]);
            Player.PlayerMcArr[0].addEventListener(Event.COMPLETE,onLoaded_Player1);
         }
         else
         {
            Load.Loading(5);
         }
      }
      
      private static function onLoaded_Player1(e:* = null) : *
      {
         Load.Loading(5);
      }
      
      public static function Loading_Player_2() : *
      {
         if(!Player.PlayerMcArr[1])
         {
            Player.PlayerMcArr[1] = new ClassLoader(otherArr[2]);
            Player.PlayerMcArr[1].addEventListener(Event.COMPLETE,onLoaded_Player2);
         }
         else
         {
            Load.Loading(6);
         }
      }
      
      private static function onLoaded_Player2(e:* = null) : *
      {
         Load.Loading(6);
      }
      
      public static function Loading_Player_3() : *
      {
         if(!Player.PlayerMcArr[2])
         {
            Player.PlayerMcArr[2] = new ClassLoader(otherArr[3]);
            Player.PlayerMcArr[2].addEventListener(Event.COMPLETE,onLoaded_Player3);
         }
         else
         {
            Load.Loading(7);
         }
      }
      
      private static function onLoaded_Player3(e:* = null) : *
      {
         Load.Loading(7);
      }
      
      public static function Loading_Player_4() : *
      {
         if(!Player.PlayerMcArr[3])
         {
            Player.PlayerMcArr[3] = new ClassLoader(otherArr[19]);
            Player.PlayerMcArr[3].addEventListener(Event.COMPLETE,onLoaded_Player4);
         }
         else
         {
            Load.Loading(20);
         }
      }
      
      private static function onLoaded_Player4(e:* = null) : *
      {
         Load.Loading(20);
      }
      
      public static function Loading_WuQi_1() : *
      {
         if(!Skin_WuQi.PlayerMcArr[0])
         {
            Skin_WuQi.PlayerMcArr[0] = new ClassLoader(otherArr[7]);
            Skin_WuQi.PlayerMcArr[0].addEventListener(Event.COMPLETE,onWuQi1);
         }
         else
         {
            Load.Loading(8);
         }
      }
      
      private static function onWuQi1(e:* = null) : *
      {
         Load.Loading(8);
      }
      
      public static function Loading_WuQi_2() : *
      {
         if(!Skin_WuQi.PlayerMcArr[1])
         {
            Skin_WuQi.PlayerMcArr[1] = new ClassLoader(otherArr[8]);
            Skin_WuQi.PlayerMcArr[1].addEventListener(Event.COMPLETE,onWuQi2);
         }
         else
         {
            Load.Loading(9);
         }
      }
      
      private static function onWuQi2(e:* = null) : *
      {
         Load.Loading(9);
      }
      
      public static function Loading_WuQi_3() : *
      {
         if(!Skin_WuQi.PlayerMcArr[2])
         {
            Skin_WuQi.PlayerMcArr[2] = new ClassLoader(otherArr[9]);
            Skin_WuQi.PlayerMcArr[2].addEventListener(Event.COMPLETE,onWuQi3);
         }
         else
         {
            Load.Loading(10);
         }
      }
      
      private static function onWuQi3(e:* = null) : *
      {
         Load.Loading(10);
      }
      
      public static function Loading_WuQi_4() : *
      {
         if(!Skin_WuQi.PlayerMcArr[3])
         {
            Skin_WuQi.PlayerMcArr[3] = new ClassLoader(otherArr[20]);
            Skin_WuQi.PlayerMcArr[3].addEventListener(Event.COMPLETE,onWuQi4);
         }
         else
         {
            Load.Loading(21);
         }
      }
      
      private static function onWuQi4(e:* = null) : *
      {
         Load.Loading(21);
      }
      
      private static function Loading_Data2() : *
      {
         onLoading_Data2();
      }
      
      private static function onLoading_Data2(e:* = null) : *
      {
         Load.Loading(11);
      }
      
      private static function Loading_Music() : *
      {
         if(!MusicBox.MusicData)
         {
            MusicBox.MusicData = new ClassLoader(otherArr[11]);
            MusicBox.MusicData.addEventListener(Event.COMPLETE,onLoading_Music);
         }
         else
         {
            Load.Loading(12);
         }
      }
      
      private static function onLoading_Music(e:* = null) : *
      {
         Load.Loading(12);
      }
      
      private static function Loading_ChongWu() : *
      {
         if(!ChongWu.loadData)
         {
            ChongWu.loadData = new ClassLoader(otherArr[12]);
            ChongWu.loadData.addEventListener(Event.COMPLETE,onLoading_ChongWu);
         }
         else
         {
            Load.Loading(13);
         }
      }
      
      private static function onLoading_ChongWu(e:* = null) : *
      {
         Load.Loading(13);
      }
      
      private static function Loading_ChongWu2() : *
      {
         Load.Loading(14);
      }
      
      private static function onLoading_ChongWu2(e:* = null) : *
      {
         Load.Loading(14);
      }
      
      private static function Loading_SelMap() : *
      {
         if(!SelMap.loadData)
         {
            SelMap.loadData = new ClassLoader(otherArr[17]);
            SelMap.loadData.addEventListener(Event.COMPLETE,onLoading_SelMap);
         }
         else
         {
            Load.Loading(15);
         }
      }
      
      private static function onLoading_SelMap(e:* = null) : *
      {
         Load.Loading(15);
      }
      
      private static function Loading_Npc() : *
      {
         if(!All_Npc.loadData)
         {
            All_Npc.loadData = new ClassLoader(otherArr[18]);
            All_Npc.loadData.addEventListener(Event.COMPLETE,onLoading_Npc);
         }
         else
         {
            Load.Loading(16);
         }
      }
      
      private static function onLoading_Npc(e:* = null) : *
      {
         Load.Loading(16);
      }
      
      public static function Load_Other() : *
      {
         if(!NewLoad.OtherData)
         {
            NewLoad.OtherData = new ClassLoader("Other_v1830.swf");
            NewLoad.OtherData.addEventListener(Event.COMPLETE,LoadOtherOK);
         }
         else
         {
            Load.Loading(17);
         }
      }
      
      public static function LoadOtherOK(e:*) : *
      {
         Load.Loading(17);
         NewLoad.Other_YN = true;
      }
      
      public static function Load_Other2() : *
      {
         if(!NewLoad.XiaoGuoData)
         {
            NewLoad.XiaoGuoData = new ClassLoader("newMc_v1840.swf");
            NewLoad.XiaoGuoData.addEventListener(Event.COMPLETE,LoadOtherOK2);
         }
         else
         {
            Load.Loading(18);
         }
      }
      
      public static function LoadOtherOK2(e:*) : *
      {
         Load.Loading(18);
         NewLoad.Other_YN = true;
      }
      
      public static function Load_ChongZhi() : *
      {
         if(!NewLoad.chongZhiData)
         {
            NewLoad.chongZhiData = new ClassLoader("ChongZhi_v1915.swf");
            NewLoad.chongZhiData.addEventListener(Event.COMPLETE,Load_ChongZhiOK);
         }
         else
         {
            Load.Loading(19);
         }
      }
      
      public static function Load_ChongZhiOK(e:*) : *
      {
         Load.Loading(19);
      }
      
      public static function NewLoadX() : *
      {
         NewLoad.Loading();
      }
      
      public static function newLoad_Ok(e:* = null) : *
      {
         if(Main.newPlay == 1)
         {
            Main.gameNum.setValue(888);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            Loading_Map2();
         }
         else
         {
            Load.Loading(-1);
            one = true;
         }
      }
      
      public static function Loading_Map2() : *
      {
         var gameNumXX:int = 0;
         var nameStr:String = null;
         if(Map.MapArr[Main.gameNum.getValue()])
         {
            loadName = 25;
            Loading_Enemy2();
         }
         else
         {
            loadName = 24;
            gameNumXX = int(Main.gameNum.getValue());
            nameStr = mapArr[gameNumXX];
            Map.MapArr[Main.gameNum.getValue()] = new ClassLoader(nameStr);
            Map.MapArr[Main.gameNum.getValue()].addEventListener(Event.COMPLETE,onLoaded_Map2);
         }
      }
      
      private static function onLoaded_Map2(e:* = null) : *
      {
         Main._this._stage_txt.text += "Load newLoad_Ok......................1() \n";
         loadName = 25;
         Loading_Enemy2();
      }
      
      public static function Loading_Enemy2() : *
      {
         var nameStr:String = null;
         if(Main.gameNum.getValue() == 0)
         {
            onLoaded_Enemy2();
         }
         else
         {
            nameStr = enemyArr[Main.gameNum.getValue()];
            Enemy.EnemyArr[Main.gameNum.getValue()] = new ClassLoader(nameStr);
            Enemy.EnemyArr[Main.gameNum.getValue()].addEventListener(Event.COMPLETE,onLoaded_Enemy2);
         }
      }
      
      private static function onLoaded_Enemy2(e:* = null) : *
      {
         Main._this._stage_txt.text += "Load newLoad_Ok......................2() \n";
         Load.Loading(-1);
      }
   }
}

