package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.petGem.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import flash.utils.*;
   import src.*;
   import src.tool.*;
   
   public class PetBag
   {
      
      private var _bag:Array = new Array();
      
      private var _limit:VT = VT.createVT(18);
      
      public function PetBag()
      {
         super();
      }
      
      public static function createPetBag() : PetBag
      {
         var bg:PetBag = new PetBag();
         for(var i:int = 0; i < 54; i++)
         {
            bg._bag[i] = null;
         }
         return bg;
      }
      
      public function get bag() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._bag;
      }
      
      public function set bag(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._bag = value;
      }
      
      public function get limit() : VT
      {
         return this._limit;
      }
      
      public function set limit(value:VT) : void
      {
         this._limit = value;
      }
      
      public function getPetBag() : Array
      {
         return this._bag.slice();
      }
      
      public function getFromPetBag(num:Number) : Object
      {
         if(this._bag[num] != null)
         {
            return this._bag[num];
         }
         return null;
      }
      
      public function addPetBag(value:Object, num:int = -1) : Boolean
      {
         if(num != -1)
         {
            if(this._bag[num] == null)
            {
               this._bag[num] = value;
               return true;
            }
            return false;
         }
         for(var i:uint = 0; i < this._limit.getValue(); i++)
         {
            if(this._bag[i] == null)
            {
               this._bag[i] = value;
               return true;
            }
         }
         return false;
      }
      
      public function backPetBagNum() : uint
      {
         var num:uint = 0;
         for(var i:int = 0; i < this._limit.getValue(); i++)
         {
            if(this._bag[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function delPetGemBag(ob:PetGem) : *
      {
         for(var i:int = 0; i < this._limit.getValue(); i++)
         {
            if(this._bag[i] is PetGem)
            {
               if(this._bag[i] == ob)
               {
                  this._bag[i] = null;
               }
            }
         }
      }
      
      public function delPetBag(num:Number) : *
      {
         var ob:Object = null;
         if(this._bag[num] != null)
         {
            ob = this._bag[num];
            this._bag[num] = null;
         }
         return ob;
      }
      
      public function getLimit() : Number
      {
         return this._limit.getValue();
      }
      
      public function setLimit2() : Number
      {
         return this._limit.setValue(36);
      }
      
      public function setLimit3() : Number
      {
         return this._limit.setValue(54);
      }
      
      public function zhengliBag() : *
      {
         for(var i:uint = 0; i < this._limit.getValue(); i++)
         {
            if(this._bag[i] == null)
            {
               this._bag.splice(i,1);
               this._bag.push(null);
            }
         }
         for(var x:uint = 0; x < this._limit.getValue(); x++)
         {
            if(this._bag[x] is PetEquip && (this._bag[x] as PetEquip).getId() == 0)
            {
               this._bag[x] = ChongZhi_Interface4.getBoX_200();
               Main.Save();
               TiaoShi.txtShow("打开宠物背包 整理背包 ??? >>>>>>" + x);
            }
         }
      }
   }
}

