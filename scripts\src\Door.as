package src
{
   import com.hotpoint.braveManIII.models.container.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol6584")]
   public class Door extends MovieClip
   {
      
      public var hit:MovieClip;
      
      public var openYN:Boolean = false;
      
      private var inTime:int = -1;
      
      private var time:int = 0;
      
      public function Door()
      {
         super();
         if(this.name == "地图" || this.name == "回村2" || this.name == "继续2")
         {
            this.Open();
         }
         else if(this.name == "苍井空" && (<PERSON>olean(Main.questBag()) || Boolean(Main.getAllQuest())))
         {
            this.Open();
         }
         else if(this.name == "加藤鹰" && Boolean(Main.getAllQuest()))
         {
            this.Open();
         }
         else if(this.name == "竞技场" || this.name == "黑暗神殿")
         {
            this.Open();
         }
         else
         {
            this.Close();
         }
         if(TiaoShi.chuanSongYN)
         {
            this.Open();
         }
      }
      
      public function Open(t:int = 0) : *
      {
         this.openYN = true;
         if(!visible)
         {
            this.time = t;
         }
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Close() : *
      {
         this.openYN = false;
         this.visible = false;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         var p:Player = null;
         if(this.parent != Main.world)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            return;
         }
         if(this.time > 0 && this.name != "竞技场" && this.name != "黑暗神殿")
         {
            --this.time;
            return;
         }
         this.visible = true;
         if(this.inTime > 0)
         {
            --this.inTime;
            return;
         }
         if(this.inTime == 0)
         {
            this.inTime = -1;
            if(this.name != "竞技场")
            {
               removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            }
            this.GoWhere();
            return;
         }
         if(this.inTime == -1 && this.visible == true)
         {
            for(i2 = 0; i2 < Player.All.length; ++i2)
            {
               p = Player.All[i2];
               if(Boolean(p.hit) && this.hit.hitTestObject(p.hit))
               {
                  if(p.getKeyStatus("上",2) && this.parent == Main.world)
                  {
                     if(this.name == "竞技场")
                     {
                        if(Main.player1.level.getValue() < 50 || Boolean(Main.P1P2) && (Main.player1.level.getValue() < 50 || Main.player2.level.getValue() < 50))
                        {
                           NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"等级达到50级才可进入");
                        }
                        else
                        {
                           PK_UI.Open();
                        }
                     }
                     else
                     {
                        this.inTime = 9;
                        Main.player_1.x = Main.player_1.y = -5000;
                        if(Boolean(Main.P1P2) && Boolean(Main.player_2))
                        {
                           Main.player_2.x = Main.player_2.y = -5000;
                        }
                        Play_Interface.interfaceX.black_guoDu_mc.gotoAndPlay(1);
                     }
                  }
               }
            }
         }
      }
      
      public function GoWhere() : *
      {
         var win2:WinShow2 = null;
         var win:WinShow = null;
         if(this.name != "竞技场")
         {
            this.Close();
         }
         Main.allClosePanel();
         if(this.name == "地图")
         {
            SelMap.Open();
         }
         else if(this.name == "继续" || this.name == "继续2")
         {
            Main.gameNum2.setValue(Main.gameNum2.getValue() + 1);
            Main._this.GameStart();
         }
         else if(this.name == "回村")
         {
            if(GameData.gameLV == 5)
            {
               win2 = new WinShow2();
            }
            else
            {
               win = new WinShow();
            }
         }
         else if(this.name == "回村2")
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(0);
            Main._this.GameStart();
         }
         else if(this.name == "苍井空")
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(1);
            Main._this.GameStart();
         }
         else if(this.name == "加藤鹰")
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(2);
            Main._this.GameStart();
         }
         else if(this.name == "黑暗神殿")
         {
            SelMap.Open(0,0,5,4);
         }
      }
   }
}

