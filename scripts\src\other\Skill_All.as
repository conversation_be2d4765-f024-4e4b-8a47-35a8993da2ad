package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_All extends Fly
   {
      
      public static var Skill_p1:Skill_All;
      
      public static var Skill_p2:Skill_All;
      
      public static var gjNum:Number = 1;
      
      public function Skill_All()
      {
         super();
      }
      
      public static function Add_Skill_All(player:Player) : *
      {
         var class_JG:Class = null;
         var objXX:MovieClip = null;
         if(player.data.getEquipSlot().getEquipFromSlot(6) && player.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 451 && player.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            ++player.gjXX_num;
         }
         if(player.gjXX_num < 8 || Main.gameNum.getValue() == 999)
         {
            return;
         }
         player.gjXX_num = 0;
         var numXX:int = 27 * 8 + 1;
         gjNum = 1;
         var eq:Equip = player.data.getEquipSlot().getEquipFromSlot(7);
         if(eq && eq.getFrame() == 452 && eq.getRemainingTime() > 0)
         {
            numXX = 27 * 12 + 1;
            gjNum = 1.5;
         }
         if(player == Main.player_1 && Boolean(Skill_p1))
         {
            Skill_p1.time = numXX;
         }
         else if(Boolean(Main.P1P2) && player == Main.player_2 && Boolean(Skill_p2))
         {
            Skill_p2.time = numXX;
         }
         else
         {
            class_JG = NewLoad.OtherData.getClass("时装全范围伤害") as Class;
            objXX = new class_JG();
            player.skin_W.addChild(objXX);
            if(player == Main.player_1)
            {
               Skill_p1 = objXX;
               Skill_p1.time = numXX;
            }
            else
            {
               Skill_p2 = objXX;
               Skill_p2.time = numXX;
            }
         }
      }
      
      public static function XiaoGuo() : *
      {
         var i2:int = 0;
         var class_XX:Class = null;
         var objXX:MovieClip = null;
         var en:Enemy = null;
         var xxx:int = 0;
         if(Main.gameNum.getValue() == 999)
         {
            Dead();
            return;
         }
         if(Boolean(Skill_p1) || Boolean(Skill_p2))
         {
            for(i2 = 0; i2 < Enemy.All.length; i2++)
            {
               class_XX = NewLoad.OtherData.getClass("Skill_All_mcX") as Class;
               objXX = new class_XX();
               en = Enemy.All[i2];
               Main.world.moveChild_Enemy.addChild(objXX);
               objXX.x = en.x + (Math.random() * 100 - 50);
               objXX.y = en.y + (Math.random() * 30 - 10);
               xxx = Math.random() * 3 + 1;
               objXX.gotoAndPlay(xxx);
            }
         }
      }
      
      override public function onADDED_TO_STAGE(e:* = null) : *
      {
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         for(var i:int = 0; i < All.length; i++)
         {
            if(All[i] == this)
            {
               return;
            }
         }
         All[All.length] = this;
         var parentMC:MovieClip = this;
         while(parentMC != _stage)
         {
            if(parentMC is Skin_WuQi && parentMC.parent is Player)
            {
               who = parentMC.parent;
               this.RL = (who as Player).RL;
               gongJi_hp_MAX = 6000;
               硬直 = 0;
               gongJi_hp = gjNum;
               attTimes = who.skin.attTimes;
               continuous = true;
               runX = 0;
               runTime = who.skin.runTime;
               break;
            }
            parentMC = parentMC.parent as MovieClip;
         }
         this.who.addChild(this);
      }
      
      override public function onENTER_FRAME(e:*) : *
      {
         if(over)
         {
            this.Dead();
            return;
         }
         if(this.currentFrame == 2 || this.currentFrame == 15)
         {
            XiaoGuo();
         }
         if(!over && time != -1)
         {
            --time;
            if(time == -1)
            {
               gotoAndPlay("结束");
               continuous = false;
               over = true;
            }
         }
         if(continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         if(!over)
         {
            this.who.addChild(this);
         }
         otherXX();
      }
      
      override public function Dead() : *
      {
         stop();
         this.visible = false;
         for(i in All)
         {
            if(All[i] == this)
            {
               All.splice(i,1);
            }
         }
         if(Boolean(Main.player_1) && this.who == Main.player_1)
         {
            Skill_p1 = null;
         }
         else if(Main.P1P2 && Main.player_2 && this.who == Main.player_2)
         {
            Skill_p2 = null;
         }
         if(this.parent)
         {
            this.parent.removeChild(this);
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
      
      override public function Dead2() : *
      {
      }
   }
}

