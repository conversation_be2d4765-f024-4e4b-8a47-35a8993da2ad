package src
{
   import com.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.elves.Elves;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.pet.Pet;
   import com.hotpoint.braveManIII.models.petEquip.PetEquip;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.skill.Skill;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.cardPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.suppliesShopPanel.*;
   import flash.display.*;
   import flash.events.*;
   import src.Skin.*;
   import src._data.*;
   import src.other.*;
   import src.tool.*;
   
   public class Player extends MovieClip
   {
      
      public static var LV_data:XML;
      
      public static var enemySkillMC:*;
      
      public static var szCD:int = 0;
      
      public static var skillYAO:int = 0;
      
      public static var upLevelInfo:Boolean = false;
      
      public static var maxLevel:int = 99;
      
      public static var All:Array = [];
      
      public static var PlayerMcLoaded:Boolean = false;
      
      public static var PlayerMcArr:Array = new Array();
      
      public static var num_1:VT = VT.createVT();
      
      public static var num_15:VT = VT.createVT();
      
      public static var num_20:VT = VT.createVT();
      
      public static var num_25:VT = VT.createVT();
      
      public static var num_30:VT = VT.createVT();
      
      public static var num_32:VT = VT.createVT(0.32);
      
      public static var num_35:VT = VT.createVT();
      
      public static var num_7:VT = VT.createVT();
      
      public static var num_5:VT = VT.createVT();
      
      public static var num_27:VT = VT.createVT();
      
      public static var num_13:VT = VT.createVT();
      
      public static var num_65:VT = VT.createVT();
      
      public static var Vip_Point_YN:Boolean = true;
      
      private static var Temp11:VT = VT.createVT(VT.GetTempVT("10+1/10"));
      
      public static var boolYS:Boolean = true;
      
      public var jianCD_lei:Boolean = false;
      
      public var jianCD:Boolean = false;
      
      public var fbChengFa:Boolean = false;
      
      public var darkTime:int = 0;
      
      public var isDarkState:Boolean = false;
      
      public var debuff:int = 0;
      
      public var gongji_UP:Number = 1;
      
      public var jianSang_UP:Number = 1;
      
      public var noYingZhiTime:int = 0;
      
      public var quan11Time:int = 0;
      
      public var cengshu:Array = [];
      
      public var zhongqiuState:Number = 0;
      
      public var jianrenState:Number = 0;
      
      public var shengmingState:Number = 0;
      
      public var reXueType:Boolean = false;
      
      public var lianjiBool:Boolean = false;
      
      public var isDead:Boolean = false;
      
      public var noMove:Boolean = false;
      
      private var tempXXX2:VT = VT.createVT(VT.GetTempVT("8/4"));
      
      public var hpXX2num:int = 0;
      
      public var hpXX2Str:String;
      
      public var hitXX:HitXX;
      
      public var shuangShouBeiShu:Number = 1;
      
      public var energySlot:EnergySlot = new EnergySlot();
      
      public var skin:Skin;
      
      public var cengHao_mc:CengHao = new CengHao();
      
      public var backMC:MovieClip;
      
      public var skin_Z0:MovieClip;
      
      public var skin_Z:Skin_ZhuangBei;
      
      public var skin_Z2:Skin_ZhuangBei;
      
      public var skin_Z3:Skin_ZhuangBei;
      
      public var skin_Z2_V:Boolean = true;
      
      public var skin_Z3_V:Boolean = true;
      
      public var headFrame:int = 1;
      
      public var skin_W:Skin_WuQi;
      
      public var playerCW:ChongWu;
      
      public var playerCW2:ChongWu2;
      
      public var CW2gameNum:int;
      
      public var playerJL:JingLing;
      
      public var hit:MovieClip = null;
      
      private var nodead_C:Class = NewLoad.XiaoGuoData.getClass("undeath") as Class;
      
      private var nodead:MovieClip = new this.nodead_C();
      
      public var deadX:Dead;
      
      public var 职业附加:Array = [1,1,1,1];
      
      public var 套装强化:Array = [0,0,0,0,0,false,false,false,false];
      
      public var 装备附加:Array = [];
      
      public var time:int = 1000;
      
      public var lianJi:VT = VT.createVT(0);
      
      public var lianJiTime:VT = VT.createVT(60);
      
      public var data:PlayerData;
      
      public var nextExp:VT = VT.createVT();
      
      public var speedTemp:VT = VT.createVT(0);
      
      public var hp:VT = VT.createVT();
      
      public var mp:VT = VT.createVT();
      
      public var hp_Max:VT = VT.createVT();
      
      public var mp_Max:VT = VT.createVT();
      
      public var gongji:VT = VT.createVT();
      
      public var fangyu:VT = VT.createVT();
      
      public var baoji:VT = VT.createVT();
      
      public var sanbi:VT = VT.createVT();
      
      public var walk_power:VT = VT.createVT();
      
      public var walk_power2:Number = 1;
      
      public var yingzhi:VT = VT.createVT();
      
      public var gongji2:VT = VT.createVT();
      
      public var fangyu2:VT = VT.createVT();
      
      public var use_hp_Max:VT = VT.createVT();
      
      public var use_mp_Max:VT = VT.createVT();
      
      public var use_gongji:VT = VT.createVT();
      
      public var use_gongji2:VT = VT.createVT();
      
      public var use_fangyu:VT = VT.createVT();
      
      public var use_fangyu2:VT = VT.createVT();
      
      public var use_baoji:VT = VT.createVT();
      
      public var use_sanbi:VT = VT.createVT();
      
      public var temp_hp_Max:VT = VT.createVT();
      
      public var temp_mp_Max:VT = VT.createVT();
      
      public var temp_gongji:VT = VT.createVT();
      
      public var temp_fangyu:VT = VT.createVT();
      
      public var temp_baoji:VT = VT.createVT();
      
      public var temp_sanbi:VT = VT.createVT();
      
      public var jianCDnum:int = 1;
      
      public var jianCDtemp:int = 0;
      
      public var jianCDBool:Boolean = false;
      
      public var hpDownXX:Number = 1;
      
      public var AllSkillCD:Array = new Array();
      
      public var AllSkillCDXX:Array = new Array();
      
      public var All_ObjCD:Array = new Array();
      
      public var All_ObjCDXX:Array = new Array();
      
      public var num_hp:VT = VT.createVT();
      
      public var num_mp:VT = VT.createVT();
      
      public var num_gj:VT = VT.createVT();
      
      public var num_fy:VT = VT.createVT();
      
      public var num_sb:VT = VT.createVT();
      
      public var num_bj:VT = VT.createVT();
      
      public var num_sd:VT = VT.createVT();
      
      public var num_pm:VT = VT.createVT();
      
      public var num_mk:VT = VT.createVT();
      
      public var num_baoSang:VT = VT.createVT();
      
      public var RL:Boolean = true;
      
      public var jump_power:* = 145;
      
      public var jump_time:* = 8;
      
      private var parabola:Number = 0.3;
      
      public var jumping:int = 0;
      
      public var jumpX2:Boolean = false;
      
      public var jumpType:int = 2;
      
      public var gravity:int = 2;
      
      public var gravityNum:* = 0;
      
      public var gjXX_num:int = 0;
      
      private var SwitchingTime:int;
      
      public var paiHangShow:Array = [1,1,1,[0],[0]];
      
      public var fly4004_3_YN:Boolean = false;
      
      public var noHit:Boolean = false;
      
      public var noHit82:Boolean = false;
      
      public var noYingZhi:Boolean = false;
      
      private var XXSupplies:Supplies;
      
      public var jnGoYn:Boolean;
      
      public var guangQiu:Skill_guangQiu;
      
      public var guangDun:Skill_guangDun;
      
      public var buffMC:BuffMC;
      
      public var tsXiaoGuo:Boolean;
      
      public var tsXiaoGuo_time:int;
      
      public var tsXiaoGuo2:Array = [false,false];
      
      public var tsCDnumArr2:Array = [];
      
      public var noMapHit:Boolean = false;
      
      private var heiAnMove:Boolean;
      
      public var heiAnJiNeng_width:int = 0;
      
      public var heiAnJiNeng:Array = [0,false,false,false,false,false,false,false,false];
      
      public var rzJN_hpArr:Array = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
      
      public var fangYuPOWER:VT = VT.createVT();
      
      private var KeyArrStr:Array = ["上","下","左","右","攻击","跳","切换","技能1","技能2","技能3","技能4","消耗1","消耗2","消耗3","转职","怪物"];
      
      private var getAllMoveSpeed:VT = VT.createVT(-99);
      
      private var getAllSuitSkill:Array;
      
      private var getAllEquipSkill:Array;
      
      private var ZB_HpMpUP_Arr:Array = [];
      
      internal var xuemaiTime:int = 0;
      
      internal var xuemaiTime2:int = 0;
      
      private var dkState:MovieClip = new DarkPower();
      
      private var timeXXX:int = 0;
      
      public var noHead:Boolean = false;
      
      public var flagTempMP:Boolean = true;
      
      public var flagTempXL:Boolean = true;
      
      public var flagTempSY:Boolean = true;
      
      public var flagTempSJ:Boolean = true;
      
      public var flagTempRX:Boolean = true;
      
      public var flagTempBJ:Boolean = true;
      
      public var timeTemp:int = 0;
      
      public var timeTempmp:int = 0;
      
      public var rexuewudi:Boolean = false;
      
      internal var kouxue:Boolean = false;
      
      internal var tankaiTime:int = 0;
      
      internal var iceTIME:int = 0;
      
      public var jKey:Boolean = false;
      
      public var flyTime:int = 0;
      
      public var fly:Boolean = false;
      
      public var KeyControl_YN:Boolean = true;
      
      public var newLifeTime:VT = VT.createVT();
      
      internal var tempCXHF:int = 0;
      
      internal var tempVaPer:int = 0;
      
      internal var tempChiXuTime:int = 0;
      
      private var huiFuTime:int = -1;
      
      private var runX:int = 0;
      
      private var runY:int = 0;
      
      private var runArr:Array = new Array();
      
      public var playerYS:YongShi61;
      
      private var feixingTime:int = 0;
      
      private var feixingBool:Boolean = false;
      
      private var class_ps:Class = NewLoad.XiaoGuoData.getClass("高能喷射") as Class;
      
      private var feixingBool2:Boolean = false;
      
      private var feixingBool3:Boolean = false;
      
      private var penSheTime:int = 0;
      
      private var nbTime:int = 0;
      
      public function Player()
      {
         super();
         this.buffMC = new BuffMC(this);
         mouseEnabled = mouseChildren = false;
         this.deadX = new Dead();
         addChild(this.deadX);
         this.deadX.visible = false;
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public static function Init() : *
      {
         num_hp = VT.createVT(1);
         num_mp = VT.createVT(1);
         num_gj = VT.createVT(1);
         num_fy = VT.createVT(1);
         num_sb = VT.createVT(1);
         num_bj = VT.createVT(1);
         num_pm = VT.createVT(1);
         num_mk = VT.createVT(1);
         num_sd = VT.createVT(0);
         num_5 = VT.createVT(0.05);
         num_1 = VT.createVT(0.1);
         num_15 = VT.createVT(0.15);
         num_20 = VT.createVT(0.2);
         num_25 = VT.createVT(0.25);
         num_30 = VT.createVT(0.3);
         num_35 = VT.createVT(0.35);
         num_7 = VT.createVT(0.07);
         num_13 = VT.createVT(0.13);
         num_27 = VT.createVT(0.27);
         num_65 = VT.createVT(1);
      }
      
      public static function getPlayerLvData(pd:PlayerData) : XML
      {
         var i:* = undefined;
         var lv:int = 0;
         for(i in LV_data.角色)
         {
            lv = int(LV_data.角色[i].等级);
            if(lv == pd.getLevel())
            {
               return LV_data.角色[i];
            }
         }
         return null;
      }
      
      public static function 一起信春哥() : *
      {
         Main.player_1.deadX.visible = false;
         Main.player_1.信春哥();
         Main.player_1.energySlot.setZero();
         Main.player_1.runArr = new Array();
         Main.player_1.CW2gameNum = 0;
         if(Main.P1P2)
         {
            Main.player_2.deadX.visible = false;
            Main.player_2.信春哥();
            Main.player_2.energySlot.setZero();
            Main.player_2.runArr = new Array();
            Main.player_2.CW2gameNum = 0;
         }
      }
      
      public static function getEquipDataXX() : *
      {
         Main.player_1.getEquipData(true);
         if(Main.P1P2)
         {
            Main.player_2.getEquipData(true);
         }
      }
      
      public static function Vip_Point() : *
      {
         if(Shop4399.totalPaiedMoney.getValue() < 0)
         {
            if(Vip_Point_YN)
            {
               Api_4399_All.TotalPaied();
               Vip_Point_YN = false;
            }
         }
      }
      
      public function tsXiaoGuoFun() : *
      {
         if(this.tsXiaoGuo)
         {
            this.HpUp(10,2);
            this.MpUp(10,2);
            this.tsXiaoGuo_time = 27 * 5;
         }
      }
      
      public function tsJN_ok(id:int) : Boolean
      {
         if(Boolean(this.tsXiaoGuo2[this.data.skinNum]) && this.tsCDnumArr2[id] == 1)
         {
            return true;
         }
         return false;
      }
      
      public function HeiAnJiNengOk(num:int = 1) : *
      {
         var equipX2:Equip = null;
         var r:Number = NaN;
         var equipX:Equip = this.data.getEquipSlot().getEquipFromSlot(6);
         if(equipX && equipX.getFrame() == 483 && equipX.getRemainingTime() > 0)
         {
            this.heiAnJiNeng[0] = 1;
            equipX2 = this.data.getEquipSlot().getEquipFromSlot(7);
            if(equipX2 && equipX2.getFrame() == 484 && equipX2.getRemainingTime() > 0)
            {
               this.heiAnJiNeng[0] = 2;
            }
            else
            {
               r = Math.random() * 100;
               if(r > 50)
               {
                  return;
               }
            }
         }
         else
         {
            this.heiAnJiNeng[0] = 0;
         }
         if(this.heiAnJiNeng[0])
         {
            if(this.data.skinNum == 1)
            {
               num += 4;
            }
            this.heiAnJiNeng[num] = true;
         }
      }
      
      public function HeiAnJiNeng(num:int = 1) : *
      {
         var mpXX:int = 0;
         if(this.mp.getValue() < 1)
         {
            return;
         }
         if(this.data.skinNum == 1)
         {
            num += 4;
         }
         if(Boolean(this.heiAnJiNeng[0]) && Boolean(this.heiAnJiNeng[num]))
         {
            this.heiAnJiNeng[num] = false;
            if(this.RL)
            {
               this.runPower(350,10,1,"黑暗使徒时装技能");
            }
            else
            {
               this.runPower(-350,10,1,"黑暗使徒时装技能");
            }
            mpXX = this.mp.getValue() - this.use_mp_Max.getValue() * 0.02 * (1 - this.data.getStampSlot().getValueSlot9());
            if(mpXX < 1)
            {
               this.mp.setValue(1);
            }
            else
            {
               this.mp.setValue(mpXX);
            }
         }
      }
      
      public function HeiAnJiNengGo() : *
      {
         var flyC:Class = null;
         var flyX:MovieClip = null;
         var widthXX:* = undefined;
         if(this.heiAnMove)
         {
            flyC = NewLoad.XiaoGuoData.getClass("黑暗使徒时装技能") as Class;
            flyX = new flyC();
            this.addChild(flyX);
            if(this.RL)
            {
               flyX.scaleX = 1;
            }
            else
            {
               flyX.scaleX = -1;
            }
            widthXX = Math.abs(this.heiAnJiNeng_width);
            if(widthXX < 100)
            {
               widthXX = 100;
            }
            flyX.scaleX *= widthXX / 350;
         }
      }
      
      public function RZJN_hpDown() : *
      {
         this.rzJN_hpArr.splice(0,1);
         this.rzJN_hpArr.push(0);
      }
      
      public function RZJN_hpUP(hp:Number) : *
      {
         this.rzJN_hpArr[29] += hp;
      }
      
      public function RZJN() : *
      {
         var hpUp:Number = 0;
         for(var i:* = 0; i < this.rzJN_hpArr.length; i++)
         {
            hpUp += this.rzJN_hpArr[i];
         }
         var hpXX:* = this.hp.getValue() + hpUp;
         var maxHP:int = this.use_hp_Max.getValue();
         if(hpXX < maxHP)
         {
            this.hp.setValue(hpXX);
         }
         else
         {
            this.hp.setValue(maxHP);
         }
         NewMC.Open("回血效果",this,0,60,0,hpUp);
      }
      
      public function hpDown_new(hpX:int, bj:Boolean = false) : *
      {
         var value:Number = NaN;
         var getAllSuitSkill:Array = null;
         var i:int = 0;
         if(this.playerCW2)
         {
            this.playerCW2.hpDown_new(hpX);
            return;
         }
         if(this.guangDun)
         {
            hpX *= 0.8;
         }
         this.RZJN_hpUP(hpX);
         var arrXXX:Array = this.data.getStampSlot().getValueSlot15();
         hpX *= 1 + arrXXX[0];
         var hpHP:int = this.hp.getValue() - hpX;
         if(hpHP <= 0 && this.isDead == false && Main.gameNum.getValue() != 999)
         {
            value = 0;
            getAllSuitSkill = this.data.getEquipSlot().getAllSuitSkill();
            for(i in getAllSuitSkill)
            {
               if(getAllSuitSkill[i] == 57362)
               {
                  if(Boolean(this.getRandom(50)) && this.flagTempRX)
                  {
                     addChild(this.nodead);
                     this.rexuewudi = true;
                     addEventListener(Event.ENTER_FRAME,this.wudi);
                     this.flagTempRX = false;
                     value = 1;
                  }
               }
            }
            this.hp.setValue(value);
         }
         else
         {
            this.hp.setValue(hpHP);
         }
         if(this.hp.getValue() <= 0)
         {
            this.isDead = true;
         }
         var xxx:int = this.x + Math.random() * 50 - 25;
         var yyy:int = this.y + Math.random() * 50 - 25;
         if(hpX == 0)
         {
            NewMC.Open("闪避2",Main.world.moveChild_Other,xxx,yyy,15,0,true,2);
         }
         else if(!bj)
         {
            NewMC.Open("_被打数字",Main.world.moveChild_Other,xxx,yyy,15,hpX,true,2);
         }
         else
         {
            NewMC.Open("_暴击数字",Main.world.moveChild_Other,xxx,yyy,20,hpX,true);
         }
      }
      
      private function onADDED_TO_STAGE(e:*) : *
      {
         Main.world.moveChild_Player.addChild(this);
         All[All.length] = this;
         Main._stage.addEventListener(KeyboardEvent.KEY_DOWN,this.onKEY_DOWN);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGE);
      }
      
      public function CWBianSen() : *
      {
         if(this.playerCW2)
         {
            this.visible = false;
         }
      }
      
      public function 不信春哥() : *
      {
         this.isDead = false;
         this.debuff = 0;
         if(Main.gameNum.getValue() == 0 && this.visible)
         {
            this.复活药使用();
            return;
         }
         if(this.data.buffNine[9] > 0)
         {
            if(this.data.reBorn == 0)
            {
               this.复活药使用();
               this.data.reBorn = 3600;
               return;
            }
         }
         if(!this.deadX.visible)
         {
            NewMC.Open("死亡倒计时",this);
         }
         this.deadX.frame = this.headFrame;
         if(this.noHead)
         {
            this.deadX.head_mc.gotoAndStop(24);
            this.deadX.head_mc.visible = true;
         }
         else
         {
            this.deadX.head_mc.gotoAndStop(this.deadX.frame);
         }
         this.deadX.visible = true;
         this.skin_Z.visible = false;
         this.skin_W.visible = false;
         if(this.skin_Z2)
         {
            this.skin_Z2.visible = false;
         }
         if(this.skin_Z3)
         {
            this.skin_Z3.visible = false;
         }
         this.energySlot.setZero();
      }
      
      public function 信春哥() : *
      {
         this.isDead = false;
         if(this.visible == false)
         {
            return;
         }
         this.scaleX = this.scaleY = 1;
         if(this.deadX && this.skin && this.skin_Z && Boolean(this.skin_W))
         {
            this.deadX.visible = false;
            this.skin.visible = true;
            this.skin_Z.visible = true;
            this.skin_W.visible = true;
            this.skin.GoTo("站",1,true);
         }
         this.PK_hpMax();
         this.hp.setValue(9999999);
         this.mp.setValue(9999999);
         continuousTime = 0;
         if(this.data.getPetSlot().getJilu() >= 0 && this.data.getPetSlot().getPetFromSlot(this.data.getPetSlot().getJilu()) && this.data.getPetSlot().getPetFromSlot(this.data.getPetSlot().getJilu()).getType() > 1)
         {
            this.data.playerCW_Data = this.data.getPetSlot().getPetFromSlot(this.data.getPetSlot().getJilu());
         }
      }
      
      private function onREMOVED_FROM_STAGE(e:*) : *
      {
         this.skin.Over();
      }
      
      public function ExpUP(num:int, type:int = 1) : String
      {
         if(type != 2 && this.hp.getValue() <= 0)
         {
            return;
         }
         if(type == 3)
         {
            num = this.nextExp.getValue() * num / 100;
         }
         if(this.data.buffNine[0] > 0)
         {
            num *= 1.15;
         }
         else if(this.data.buffNine[8] > 0)
         {
            num *= 1.3;
         }
         var expXX:int = Math.abs(this.data.getEXP() + num);
         if(this.data.getEXP() > 99999999)
         {
            expXX = 99999999;
         }
         this.data.setEXP(expXX);
         if(this.data.getLevel() < maxLevel)
         {
            if(this.data.getEXP() >= this.nextExp.getValue())
            {
               return this.LevelUP(type);
            }
         }
         return "ok";
      }
      
      public function LevelUP(type:int = 1) : *
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         if(this.data.getLevel() < Player.maxLevel)
         {
            if(type == 1 || type == 3)
            {
               this.data.setEXP(0);
               this.LevelUP_X();
            }
            else if(type == 2)
            {
               while(this.data.getEXP() >= this.nextExp.getValue() && this.data.getLevel() < Player.maxLevel)
               {
                  this.data.setEXP(this.data.getEXP() - this.nextExp.getValue());
                  this.LevelUP_X();
               }
            }
            NewMC.Open("升级效果",this);
            if(this.data.getLevel() == 2)
            {
               NewMC.Open("文字提示",Main._this,470,500,150,0,true,1,"恭喜你升到2级! 你可以在主城找【奥古斯汀】学习技能啦!");
               upLevelInfo = true;
            }
            if(this.data.getLevel() >= 50)
            {
               this.data.setRebirth();
            }
         }
      }
      
      public function LevelUP_X() : *
      {
         this.data.setLevel(this.data.getLevel() + VT.GetTempVT("8/8"));
         TiaoShi.tempVar = "data.getLevel() = " + this.data.getLevel() + " , data.getEXP() = " + this.data.getEXP();
         if(this.data.isRebirth())
         {
            this.data.addPoint(VT.GetTempVT("10/2"));
         }
         else
         {
            this.data.addPoint(VT.GetTempVT("8/2"));
         }
         this.LoadPlayerLvData();
         this.hp.setValue(9999999);
         this.mp.setValue(9999999);
      }
      
      public function MoneyUP(m:int) : *
      {
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         this.data.addGold(m);
      }
      
      public function Load_All_Player_Data() : *
      {
         trace("读取玩家数据");
         this.LoadPlayerLvData();
         this.GetAllSkillCD();
         this.GetAllObjCD();
         this.newSkin();
         this.信春哥();
         this.left_Reight();
         this.ChongSeng_no50lv();
      }
      
      private function ChongSeng_no50lv() : *
      {
         if(this.data.getLevel() >= 50)
         {
            this.data.setRebirth();
         }
      }
      
      public function left_Reight() : *
      {
         if(this.data.getEquipSkillSlot().getGemFromSkillSlot(0) != null)
         {
            if(Main.gameNum.getValue() == 0)
            {
               this.energySlot.energyLeftNum.setValue(0);
               this.energySlot.energyLeftMax.setValue(SkillFactory.getSkillById(this.data.getEquipSkillSlot().getGemFromSkillSlot(0).getGemSkill()).getEp());
            }
         }
         if(this.data.getEquipSkillSlot().getGemFromSkillSlot(1) != null)
         {
            if(Main.gameNum.getValue() == 0)
            {
               this.energySlot.energyRightNum.setValue(0);
               this.energySlot.energyRightMax.setValue(SkillFactory.getSkillById(this.data.getEquipSkillSlot().getGemFromSkillSlot(1).getGemSkill()).getEp());
            }
         }
      }
      
      public function LoadPlayerLvData() : *
      {
         if(this.data.getLevel() > maxLevel)
         {
            this.data.setLevel(maxLevel);
         }
         this.双手加成();
         var xml:XML = Player.getPlayerLvData(this.data);
         this.nextExp.setValue(int(xml.经验));
         var hpMaxX:int = int(xml.HP) + this.data.getEquipSlot().getAllHP();
         this.hp_Max.setValue(hpMaxX);
         var mpMaxX:int = int(xml.MP) + this.data.getEquipSlot().getAllMP();
         this.mp_Max.setValue(mpMaxX);
         var gongJiX:int = (int(xml.攻击) + this.data.getEquipSlot().getAllAttack()) * this.shuangShouBeiShu;
         this.gongji.setValue(gongJiX);
         var fangyuX:int = int(xml.防御) + this.data.getEquipSlot().getAllDefense();
         this.fangyu.setValue(fangyuX);
         var baojibaojiX:int = int(xml.暴击) + this.data.getEquipSlot().getAllCrit();
         this.baoji.setValue(baojibaojiX);
         var sanbiX:int = int(xml.闪避) + this.data.getEquipSlot().getAllDuck();
         this.sanbi.setValue(sanbiX);
         var yingzhiX:int = int(xml.硬值) + this.data.getEquipSlot().getAllHardValue();
         this.yingzhi.setValue(yingzhiX);
         var gj2:int = this.data.getEquipSlot().getAllPOMO();
         this.gongji2.setValue(gj2);
         var fy2:int = this.data.getEquipSlot().getAllMOKANG();
         this.fangyu2.setValue(fy2);
         this.walk_power.setValue(6 + this.data.getEquipSlot().getAllMoveSpeed());
         this.LoadAll_D_Skill();
      }
      
      private function TestEquipSlot() : *
      {
         if(this.data.getEquipSlot().testAllEquip() == true)
         {
            Main.NoGame("5星装备");
         }
      }
      
      private function LoadAll_D_Skill() : *
      {
         var i:int = 0;
         var s:String = null;
         var arr:Array = null;
         var num:Number = NaN;
         var num2:Number = NaN;
         for(i in this.data.getSkillArr())
         {
            s = (this.data.getSkillArr()[i][0] as String).substr(0,1);
            if(s == "d")
            {
               if(this.data.getSkillArr()[i][0] == "d1" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.jumpX2 = true;
               }
               else if(this.data.getSkillArr()[i][0] == "d2" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.hp_Max.setValue(this.hp_Max.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d3" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.mp_Max.setValue(this.mp_Max.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d4" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.baoji.setValue(this.baoji.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d5" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num = Number((arr[0] as VT).getValue());
                  this.sanbi.setValue(this.sanbi.getValue() + num);
               }
               else if(this.data.getSkillArr()[i][0] == "d6" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[0] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d7" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[1] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d8" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[2] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "k16" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.职业附加[3] = 1 + num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d9" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.套装强化[0] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d10" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.套装强化[1] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d11" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  num2 = Number((arr[0] as VT).getValue());
                  this.套装强化[2] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d12" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  this.套装强化[3] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d13" && this.data.getSkillArr()[i][1] > 0)
               {
                  arr = SkillFactory.getSkillByTypeIAndLevel(this.data.getSkillArr()[i][0],this.data.getSkillArr()[i][1]).getSkillValueArray();
                  this.套装强化[4] = num2;
               }
               else if(this.data.getSkillArr()[i][0] == "d14" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[5] = true;
               }
               else if(this.data.getSkillArr()[i][0] == "d15" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[6] = true;
               }
               else if(this.data.getSkillArr()[i][0] == "d16" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[7] = true;
               }
               else if(this.data.getSkillArr()[i][0] == "k16" && this.data.getSkillArr()[i][1] > 0)
               {
                  this.套装强化[8] = true;
               }
            }
         }
      }
      
      private function getEquipData(now:Boolean = false) : *
      {
         var getAllEquipSkillXX:Array = null;
         var i:int = 0;
         if(this.getAllMoveSpeed.getValue() == -99 || now)
         {
            this.getAllMoveSpeed.setValue(this.data.getEquipSlot().getAllMoveSpeed());
         }
         if(!this.getAllSuitSkill || now)
         {
            this.getAllSuitSkill = this.data.equipSlot.getAllSuitSkill();
         }
         if(!this.getAllEquipSkill || now)
         {
            getAllEquipSkillXX = this.data.getEquipSlot().getAllEquipSkill();
            this.getAllEquipSkill = new Array();
            for(i in getAllEquipSkillXX)
            {
               this.getAllEquipSkill[i] = SkillFactory.getSkillById(getAllEquipSkillXX[i]);
            }
         }
      }
      
      public function getZBxiaoguo_NEW() : *
      {
         this.tsXiaoGuo2 = [false,false];
      }
      
      public function LoadAll_ZB_Skill() : *
      {
         this.getEquipData();
         this.use_hp_Max.setValue(this.hp_Max.getValue());
         this.use_mp_Max.setValue(this.mp_Max.getValue());
         this.use_gongji.setValue(this.gongji.getValue());
         this.use_fangyu.setValue(this.fangyu.getValue());
         this.use_baoji.setValue(this.baoji.getValue());
         this.use_sanbi.setValue(this.sanbi.getValue());
         this.use_fangyu2.setValue(this.fangyu2.getValue());
         this.use_gongji2.setValue(this.gongji2.getValue());
         this.walk_power.setValue(6 + this.getAllMoveSpeed.getValue() + this.speedTemp.getValue());
         if(this.walk_power.getValue() <= 0)
         {
            this.walk_power.setValue(0);
         }
         this.reSet();
         this.zengFu();
         this.huiZhangJiaCheng();
         this.chongwujiacheng();
         this.QiangHuaJiaCheng();
         this.chenghaoJiaCheng();
         this.AnHeiJiaCheng();
         this.ZhuFu3_up();
         this.VIPJiaCheng();
         this.jinglingjiacheng();
         this.cardjiacheng();
         this.CW2JiaCheng();
         this.stampjiacheng();
         this.装备技能();
         this.ALLTempcompute();
         this.TaoZhuangXiaoGuo();
         this.rexueSkill();
         this.ALLcompute();
         this.huanhuaJiaCheng();
         this.stampjiacheng2();
         this.XingLingJiaCeng();
         this.xuemaiJiaCheng();
         this.caiyaoJiaCheng();
         this.nineBuffJiaCheng();
         this.zhongQiuJiaCheng();
         this.jianRenJiaCheng();
         this.shengMingJiaCheng();
         this.YinZhangJiaCeng();
         this.YouLingJiaCeng();
         this.getZBxiaoguo_NEW();
         this.chongwuSQLQ();
         this.PK_hpMax();
         this.ZB_HpMpUP(true);
         this.数值溢出修正();
      }
      
      private function PK_hpMax() : *
      {
         if(Main.gameNum.getValue() == 999)
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue() * InitData.pkHpMaxNum_Player.getValue());
         }
         else
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue());
         }
      }
      
      private function 装备技能() : *
      {
         var i:int = 0;
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         var num:Number = NaN;
         for(i in this.getAllEquipSkill)
         {
            idX = int(this.getAllEquipSkill[i].getSkillActOn());
            sk_ValueArr = this.getAllEquipSkill[i].getSkillValueArray();
            if(idX == 32)
            {
               if(this.lianJi.getValue() > sk_ValueArr[1].getValue())
               {
                  num = Number(sk_ValueArr[0].getValue());
                  this.num_gj.setValue(this.num_gj.getValue() + num);
               }
            }
            else if(idX == 35)
            {
               num = this.use_baoji.getValue() + sk_ValueArr[0].getValue();
               this.use_baoji.setValue(num);
            }
            else if(idX == 200)
            {
               if(Boolean(this.data.getEquipSlot().getEquipFromSlot(6)) && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
               {
                  this.num_fy.setValue(this.num_fy.getValue() + sk_ValueArr[0].getValue());
                  this.num_gj.setValue(this.num_gj.getValue() + sk_ValueArr[1].getValue());
               }
            }
            else if(idX == 201)
            {
               if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  this.num_hp.setValue(this.num_hp.getValue() + sk_ValueArr[0].getValue());
                  this.num_bj.setValue(this.num_bj.getValue() + sk_ValueArr[1].getValue());
               }
            }
         }
      }
      
      private function rexueSkill() : *
      {
         var i:int = 0;
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         var num:Number = NaN;
         var hpXX:int = 0;
         var arr:Array = XingLingFactory.GetTolal_Data_VT();
         var tempHp:uint = uint(arr[1].getValue());
         var tempHp2:uint = this.data.getStampSlot().getValueSlot5();
         for(i in this.getAllEquipSkill)
         {
            idX = int(this.getAllEquipSkill[i].getSkillActOn());
            sk_ValueArr = this.getAllEquipSkill[i].getSkillValueArray();
            if(idX == 31)
            {
               hpXX = (this.temp_hp_Max.getValue() + tempHp + tempHp2) * sk_ValueArr[1].getValue();
               if(this.hp.getValue() < hpXX)
               {
                  num = Number(sk_ValueArr[0].getValue());
                  this.num_gj.setValue(this.num_gj.getValue() + num);
               }
            }
         }
      }
      
      private function ZB_HpMpUP(yn:Boolean = false) : *
      {
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         var numTime:int = 0;
         var numX:int = 0;
         var numTime2:int = 0;
         var numX2:int = 0;
         if(yn)
         {
            for(i in this.getAllEquipSkill)
            {
               idX = int(this.getAllEquipSkill[i].getSkillActOn());
               sk_ValueArr = this.getAllEquipSkill[i].getSkillValueArray();
               if(idX == 33)
               {
                  numTime = sk_ValueArr[0].getValue() - this.套装强化[0];
                  numX = sk_ValueArr[1].getValue() * this.use_mp_Max.getValue();
                  this.ZB_HpMpUP_Arr[0] = [numTime,numX];
               }
               else if(idX == 34)
               {
                  numTime2 = sk_ValueArr[0].getValue() - this.套装强化[1];
                  numX2 = sk_ValueArr[1].getValue() * this.use_hp_Max.getValue();
                  if(PK_UI.PK_ing)
                  {
                     numX2 /= 20;
                  }
                  this.ZB_HpMpUP_Arr[1] = [numTime2,numX2];
               }
            }
         }
         if(this.ZB_HpMpUP_Arr[0] && this.ZB_HpMpUP_Arr[0][1] != 0 && this.time % this.ZB_HpMpUP_Arr[0][0] == 0)
         {
            this.MP_UP(this.ZB_HpMpUP_Arr[0][1]);
         }
         if(this.ZB_HpMpUP_Arr[1] && this.ZB_HpMpUP_Arr[1][1] != 0 && this.time % this.ZB_HpMpUP_Arr[1][0] == 0)
         {
            this.HP_UP(this.ZB_HpMpUP_Arr[1][1]);
         }
      }
      
      private function huiZhangJiaCheng() : *
      {
         this.num_hp.setValue(this.data.getBadgeSlot().getHP() + this.num_hp.getValue());
         this.num_bj.setValue(this.data.getBadgeSlot().getCRIT() + this.num_bj.getValue());
         this.num_gj.setValue(this.data.getBadgeSlot().getATT() + this.num_gj.getValue());
         this.num_fy.setValue(this.data.getBadgeSlot().getDEF() + this.num_fy.getValue());
         this.num_mp.setValue(this.data.getBadgeSlot().getMP() + this.num_mp.getValue());
         this.num_sd.setValue(this.data.getBadgeSlot().getSPEED() + this.num_sd.getValue());
      }
      
      private function VIPJiaCheng() : *
      {
         for(var j:int = 0; j < Main.player1.getTitleSlot().getListLength(); j++)
         {
            if(Main.player1.getTitleSlot().getTitleFromSlot(j).getId() == 25)
            {
               this.num_hp.setValue(num_5.getValue() + this.num_hp.getValue());
               this.num_bj.setValue(num_5.getValue() + this.num_bj.getValue());
               this.num_gj.setValue(num_5.getValue() + this.num_gj.getValue());
               this.num_fy.setValue(num_5.getValue() + this.num_fy.getValue());
               this.num_mp.setValue(num_5.getValue() + this.num_mp.getValue());
               this.num_sb.setValue(num_5.getValue() + this.num_sb.getValue());
               this.num_pm.setValue(num_5.getValue() + this.num_pm.getValue());
               this.num_mk.setValue(num_5.getValue() + this.num_mk.getValue());
               if(this == Main.player_1)
               {
                  Vip_Point();
               }
            }
         }
      }
      
      private function zengFu() : *
      {
         var i:int = 0;
         var idX:int = 0;
         var sk_ValueArr:Array = null;
         var arr:Array = this.data.equipSlot.getAllEquipNewSkill();
         for(i in arr)
         {
            idX = int(SkillFactory.getSkillById(arr[i]).getSkillActOn());
            sk_ValueArr = SkillFactory.getSkillById(arr[i]).getSkillValueArray();
            if(idX == 39)
            {
               this.num_gj.setValue(this.num_gj.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 40)
            {
               this.num_bj.setValue(this.num_bj.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 41)
            {
               Main.NoGame("闪避");
            }
            else if(idX == 42)
            {
               this.num_hp.setValue(this.num_hp.getValue() + sk_ValueArr[0].getValue());
            }
            else if(idX == 43)
            {
               this.num_fy.setValue(this.num_fy.getValue() + sk_ValueArr[0].getValue());
            }
         }
      }
      
      private function reSet() : *
      {
         this.num_gj.setValue(InitData.BuyNum_1.getValue());
         this.num_fy.setValue(InitData.BuyNum_1.getValue());
         this.num_hp.setValue(InitData.BuyNum_1.getValue());
         this.num_mp.setValue(InitData.BuyNum_1.getValue());
         this.num_sb.setValue(InitData.BuyNum_1.getValue());
         this.num_bj.setValue(InitData.BuyNum_1.getValue());
         this.num_pm.setValue(InitData.BuyNum_1.getValue());
         this.num_mk.setValue(InitData.BuyNum_1.getValue());
         this.num_sd.setValue(InitData.BuyNum_0.getValue());
      }
      
      private function ALLcompute() : *
      {
         this.use_hp_Max.setValue(int(this.use_hp_Max.getValue() * this.num_hp.getValue()));
         this.use_mp_Max.setValue(int(this.use_mp_Max.getValue() * this.num_mp.getValue()));
         this.use_gongji.setValue(this.use_gongji.getValue() * this.num_gj.getValue());
         this.use_fangyu.setValue(this.use_fangyu.getValue() * this.num_fy.getValue());
         this.use_sanbi.setValue(this.use_sanbi.getValue() * this.num_sb.getValue());
         this.use_baoji.setValue(this.use_baoji.getValue() * this.num_bj.getValue());
         this.use_gongji2.setValue(this.use_gongji2.getValue() * this.num_pm.getValue());
         this.use_fangyu2.setValue(this.use_fangyu2.getValue() * this.num_mk.getValue());
         this.walk_power.setValue(this.walk_power.getValue() + this.num_sd.getValue());
      }
      
      private function ALLTempcompute() : *
      {
         this.temp_hp_Max.setValue(int(this.use_hp_Max.getValue() * this.num_hp.getValue()));
         this.temp_mp_Max.setValue(int(this.use_mp_Max.getValue() * this.num_mp.getValue()));
         this.temp_gongji.setValue(this.use_gongji.getValue() * this.num_gj.getValue());
         this.temp_fangyu.setValue(this.use_fangyu.getValue() * this.num_fy.getValue());
         this.temp_sanbi.setValue(this.use_sanbi.getValue() * this.num_sb.getValue());
         this.temp_baoji.setValue(this.use_baoji.getValue() * this.num_bj.getValue());
      }
      
      private function chongwuSQLQ() : *
      {
         var petEquipX:PetEquip = null;
         var arr:Array = null;
         if(Boolean(this.playerCW) || Boolean(this.playerCW2))
         {
            petEquipX = this.playerCW ? this.playerCW.data.getPetEquip() : this.playerCW2.data.getPetEquip();
            if(Boolean(petEquipX) && petEquipX.getType() == 21)
            {
               arr = petEquipX.getAffect();
               this.use_fangyu.setValue(this.use_fangyu.getValue() - this.use_fangyu.getValue() * arr[0]);
               this.use_sanbi.setValue(this.use_sanbi.getValue() - this.use_sanbi.getValue() * arr[0]);
            }
         }
      }
      
      private function caiyaoJiaCheng() : *
      {
         var CYjianshang:Number = NaN;
         if(CaiYaoPanel.saveArr[2] > 0)
         {
            CYjianshang = 1 + (CaiYaoPanel.saveArr[2] * CaiYaoPanel.addArr[2] + CaiYaoPanel.saveArr[5] * CaiYaoPanel.addArr[5] + CaiYaoPanel.saveArr[8] * CaiYaoPanel.addArr[8] + CaiYaoPanel.saveArr[11] * CaiYaoPanel.addArr[11] + CaiYaoPanel.saveArr[14] * CaiYaoPanel.addArr[14]) * 0.01;
            this.use_hp_Max.setValue(int(this.use_hp_Max.getValue() * CYjianshang));
         }
      }
      
      private function xuemaiJiaCheng() : *
      {
         if(Boolean(NewPetPanel.XueMai) && (this.playerCW || this.playerCW2))
         {
            if(NewPetPanel.XueMai.isHaveSX(1))
            {
               this.use_gongji.setValue(200 + this.use_gongji.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(2))
            {
               this.use_fangyu.setValue(100 + this.use_fangyu.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(5))
            {
               this.use_mp_Max.setValue(400 + this.use_mp_Max.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(6))
            {
               this.walk_power.setValue(1 + this.walk_power.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(7))
            {
               this.use_fangyu2.setValue(this.use_fangyu2.getValue() + 20);
            }
            if(NewPetPanel.XueMai.isHaveSX(8))
            {
               this.use_gongji2.setValue(this.use_gongji2.getValue() + 20);
            }
            if(NewPetPanel.XueMai.isHaveSX(9))
            {
               this.use_baoji.setValue(300 + this.use_baoji.getValue());
            }
            if(NewPetPanel.XueMai.isHaveSX(10))
            {
               this.use_hp_Max.setValue(700 + this.use_hp_Max.getValue());
            }
         }
      }
      
      private function huanhuaJiaCheng() : *
      {
         if(this.data.getEquipSlot().getEquipFromSlot(5).getHuanHua() == 2)
         {
            this.walk_power.setValue(this.walk_power.getValue() + 1);
         }
      }
      
      private function jianRenJiaCheng() : *
      {
         if(this.jianrenState == 2)
         {
            this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue() * 0.5);
         }
      }
      
      private function nineBuffJiaCheng() : *
      {
         if(this.data.buffNine[1] > 0)
         {
            if(this.data.buffNine[9] > 0)
            {
               this.use_fangyu.setValue(this.use_fangyu.getValue() + this.use_fangyu.getValue() * 0.2);
               this.use_mp_Max.setValue(this.use_mp_Max.getValue() + this.use_mp_Max.getValue() * 0.2);
               this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.use_hp_Max.getValue() * 0.2);
               this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue() * 0.2);
               this.use_gongji2.setValue(this.use_gongji2.getValue() + this.use_gongji2.getValue() * 0.1);
               this.use_baoji.setValue(this.use_baoji.getValue() + this.use_baoji.getValue() * 0.4);
               this.walk_power.setValue(this.walk_power.getValue() + 4);
               this.use_fangyu2.setValue(this.use_fangyu2.getValue() + this.use_fangyu2.getValue() * 0.1);
            }
            else
            {
               if(this.data.buffNine[1] > 0)
               {
                  this.use_fangyu.setValue(this.use_fangyu.getValue() + this.use_fangyu.getValue() * 0.1);
               }
               if(this.data.buffNine[2] > 0)
               {
                  this.use_mp_Max.setValue(this.use_mp_Max.getValue() + this.use_mp_Max.getValue() * 0.1);
               }
               if(this.data.buffNine[3] > 0)
               {
                  this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.use_hp_Max.getValue() * 0.1);
               }
               if(this.data.buffNine[4] > 0)
               {
                  this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue() * 0.1);
               }
               if(this.data.buffNine[5] > 0)
               {
                  this.walk_power.setValue(this.walk_power.getValue() + 2);
               }
               if(this.data.buffNine[6] > 0)
               {
                  this.use_baoji.setValue(this.use_baoji.getValue() + this.use_baoji.getValue() * 0.2);
               }
               if(this.data.buffNine[7] > 0)
               {
                  this.use_fangyu2.setValue(this.use_fangyu2.getValue() + this.use_fangyu2.getValue() * 0.05);
               }
               if(this.data.buffNine[8] > 0)
               {
                  this.use_gongji2.setValue(this.use_gongji2.getValue() + this.use_gongji2.getValue() * 0.05);
               }
            }
         }
      }
      
      private function zhongQiuJiaCheng() : *
      {
         if(this.zhongqiuState == 2)
         {
            this.use_gongji.setValue(this.use_gongji.getValue() + this.use_gongji.getValue());
         }
      }
      
      private function shengMingJiaCheng() : *
      {
         if(this.shengmingState == 2)
         {
            this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.use_hp_Max.getValue() * 0.5);
            this.use_fangyu.setValue(this.use_fangyu.getValue() + this.use_fangyu.getValue() * 0.3);
         }
      }
      
      private function YinZhangJiaCeng() : *
      {
         this.use_gongji2.setValue(this.use_gongji2.getValue() * (1 + this.data.getStampSlot().getValueSlot12()));
         this.use_fangyu2.setValue(this.use_fangyu2.getValue() * (1 + this.data.getStampSlot().getValueSlot13()));
      }
      
      private function YouLingJiaCeng() : *
      {
         this.use_gongji.setValue(this.use_gongji.getValue() + Panel_youling.lvArr[1] * 20);
         this.use_hp_Max.setValue(this.use_hp_Max.getValue() + Panel_youling.lvArr[1] * 200);
         this.use_gongji.setValue(this.use_gongji.getValue() * (1 + Panel_youling.lvArr[2] * 0.002));
         this.use_hp_Max.setValue(this.use_hp_Max.getValue() * (1 + Panel_youling.lvArr[2] * 0.002));
         this.use_gongji2.setValue(this.use_gongji2.getValue() + Panel_youling.lvArr[3] * 2);
         this.use_fangyu2.setValue(this.use_fangyu2.getValue() + Panel_youling.lvArr[3] * 2);
      }
      
      private function chenghaoJiaCheng() : *
      {
         if(this.data.getTitleSlot().getTitleAttrib())
         {
            this.use_hp_Max.setValue(this.data.getTitleSlot().getTitleAttrib().getHP() + this.use_hp_Max.getValue());
            this.use_baoji.setValue(this.data.getTitleSlot().getTitleAttrib().getCrit() + this.use_baoji.getValue());
            this.use_gongji.setValue(this.data.getTitleSlot().getTitleAttrib().getAttack() + this.use_gongji.getValue());
            this.use_fangyu.setValue(this.data.getTitleSlot().getTitleAttrib().getDefense() + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(this.data.getTitleSlot().getTitleAttrib().getMP() + this.use_mp_Max.getValue());
            this.walk_power.setValue(this.data.getTitleSlot().getTitleAttrib().getMoveSpeed() + this.walk_power.getValue());
         }
      }
      
      private function AnHeiJiaCheng() : *
      {
         if(LingHunShi_Interface.lhs_Data)
         {
            if(Boolean(LingHunShi_Interface.lhs_Data[1]) && Boolean(LingHunShi_Interface.lhs_Data[1].getValue()))
            {
               this.num_gj.setValue(this.num_gj.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[1].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[2]) && Boolean(LingHunShi_Interface.lhs_Data[2].getValue()))
            {
               this.num_fy.setValue(this.num_fy.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[2].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[3]) && Boolean(LingHunShi_Interface.lhs_Data[3].getValue()))
            {
               this.num_baoSang.setValue(0.01 * LingHunShi_Interface.lhs_Data[3].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[4]) && Boolean(LingHunShi_Interface.lhs_Data[4].getValue()))
            {
               this.num_hp.setValue(this.num_hp.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[4].getValue());
            }
            if(Boolean(LingHunShi_Interface.lhs_Data[5]) && Boolean(LingHunShi_Interface.lhs_Data[5].getValue()))
            {
               this.num_mk.setValue(this.num_mk.getValue() + 0.01 * LingHunShi_Interface.lhs_Data[5].getValue());
            }
         }
      }
      
      private function jinglingjiacheng() : *
      {
         if(this.data.playerJL_Data)
         {
            this.use_hp_Max.setValue(int(this.data.playerJL_Data.getHP() * 13.3) + this.use_hp_Max.getValue());
            this.use_baoji.setValue(int(this.data.playerJL_Data.getCRIT() * 7.3) + this.use_baoji.getValue());
            this.use_gongji.setValue(int(this.data.playerJL_Data.getATT()) + this.use_gongji.getValue());
            this.use_fangyu.setValue(int(this.data.playerJL_Data.getDEF() * 1.9) + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(int(this.data.playerJL_Data.getMP() * 8.8) + this.use_mp_Max.getValue());
         }
         this.use_hp_Max.setValue(this.data.getElvesSlot().backElvesSkill1() * this.use_hp_Max.getValue() + this.use_hp_Max.getValue());
         this.use_fangyu.setValue(this.data.getElvesSlot().backElvesSkill4() * this.use_fangyu.getValue() + this.use_fangyu.getValue());
      }
      
      private function stampjiacheng() : *
      {
         this.num_gj.setValue(this.data.getStampSlot().getValueSlot3() + this.num_gj.getValue());
         this.num_fy.setValue(this.data.getStampSlot().getValueSlot4() + this.num_fy.getValue());
         this.walk_power.setValue(this.data.getStampSlot().getValueSlot7() + this.walk_power.getValue());
      }
      
      private function stampjiacheng2() : *
      {
         this.use_hp_Max.setValue(this.use_hp_Max.getValue() + this.data.getStampSlot().getValueSlot5());
         this.use_gongji.setValue(this.use_gongji.getValue() + this.data.getStampSlot().getValueSlot6());
         this.use_hp_Max.setValue(this.data.getElvesSlot().backElvesSkill7() + this.use_hp_Max.getValue());
         this.use_gongji.setValue(this.data.getElvesSlot().backElvesSkill9() + this.use_gongji.getValue());
      }
      
      private function XingLingJiaCeng() : *
      {
         var arr:Array = XingLingFactory.GetTolal_Data_VT();
         this.use_hp_Max.setValue(this.use_hp_Max.getValue() + arr[1].getValue());
         this.use_mp_Max.setValue(this.use_mp_Max.getValue() + arr[2].getValue());
         this.use_gongji.setValue(this.use_gongji.getValue() + arr[3].getValue());
         this.use_fangyu.setValue(this.use_fangyu.getValue() + arr[4].getValue());
         this.use_baoji.setValue(this.use_baoji.getValue() + arr[5].getValue());
         this.use_sanbi.setValue(this.use_sanbi.getValue() + arr[6].getValue());
         this.use_gongji2.setValue(this.use_gongji2.getValue() + arr[7].getValue());
         this.use_fangyu2.setValue(this.use_fangyu2.getValue() + arr[8].getValue());
      }
      
      private function cardjiacheng() : *
      {
         if(CardPanel.monsterSlot)
         {
            this.use_hp_Max.setValue(int(CardPanel.monsterSlot.getAllHpup()) + this.use_hp_Max.getValue());
            this.use_baoji.setValue(int(CardPanel.monsterSlot.getAllCritup()) + this.use_baoji.getValue());
            this.use_gongji.setValue(int(CardPanel.monsterSlot.getAllAttup()) + this.use_gongji.getValue());
            this.use_fangyu.setValue(int(CardPanel.monsterSlot.getAllDefup()) + this.use_fangyu.getValue());
            this.use_mp_Max.setValue(int(CardPanel.monsterSlot.getAllMpup()) + this.use_mp_Max.getValue());
         }
      }
      
      private function CW2JiaCheng() : *
      {
         this.use_gongji.setValue(this.use_gongji.getValue() + ChongWu2.getJiaCeng());
      }
      
      private function chongwujiacheng() : *
      {
         var petData:Pet = null;
         if(Boolean(this.playerCW) || Boolean(this.playerCW2))
         {
            petData = this.playerCW ? this.playerCW.data : this.playerCW2.data;
            if(petData)
            {
               this.num_hp.setValue(petData.getLife() + this.num_hp.getValue());
               this.num_bj.setValue(petData.getCrit() + this.num_bj.getValue());
               this.num_gj.setValue(petData.getAtt() + this.num_gj.getValue());
               this.num_fy.setValue(petData.getDef() + this.num_fy.getValue());
            }
         }
      }
      
      private function ZhuFu3_up() : *
      {
         var eX:Equip = null;
         var lv:int = 0;
         var data:Array = null;
         var vipXX:Number = NaN;
         for(i = 0; i < 8; ++i)
         {
            eX = this.data.getEquipSlot().getEquipFromSlot(i);
            if(eX && eX._blessAttrib && eX._blessAttrib.getBeishu() >= 2)
            {
               if(!(this.data.skinNum == 0 && i == 5))
               {
                  if(!(this.data.skinNum == 1 && i == 2))
                  {
                     lv = eX._blessAttrib.getBeishu() - 1;
                     data = Zhufu2Factory.allData[lv];
                     vipXX = 1.5;
                     if(Main.isVip())
                     {
                        vipXX = 1;
                     }
                     if(eX.getPosition() == 0 || eX.getPosition() == 5 || eX.getPosition() == 6 || eX.getPosition() == 7)
                     {
                        this.use_gongji.setValue(this.use_gongji.getValue() + data[6] / vipXX);
                     }
                     else if(eX.getPosition() == 2)
                     {
                        this.use_fangyu.setValue(this.use_fangyu.getValue() + data[7] / vipXX);
                     }
                     else if(eX.getPosition() == 1)
                     {
                        this.use_baoji.setValue(this.use_baoji.getValue() + data[8] / vipXX);
                     }
                     else if(eX.getPosition() == 4)
                     {
                        this.use_fangyu2.setValue(this.use_fangyu2.getValue() + data[9] / vipXX);
                     }
                     else if(eX.getPosition() == 3)
                     {
                        this.use_gongji2.setValue(this.use_gongji2.getValue() + data[10] / vipXX);
                     }
                     else if(eX.getPosition() == 8)
                     {
                        this.use_hp_Max.setValue(this.use_hp_Max.getValue() + data[11] / vipXX);
                     }
                     else if(eX.getPosition() == 9)
                     {
                        this.use_mp_Max.setValue(this.use_mp_Max.getValue() + data[12] / vipXX);
                     }
                  }
               }
            }
         }
      }
      
      private function QiangHuaJiaCheng() : *
      {
         var sLV:int = this.data.getEquipSlot().getSuitStrength();
         if(sLV < 4)
         {
            return;
         }
         if(sLV > 10)
         {
            sLV = 10;
         }
         switch(sLV)
         {
            case 4:
               this.num_hp.setValue(this.num_hp.getValue() + num_1.getValue());
               break;
            case 5:
               this.num_hp.setValue(this.num_hp.getValue() + num_15.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_1.getValue());
               break;
            case 6:
               this.num_hp.setValue(this.num_hp.getValue() + num_15.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_15.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               break;
            case 7:
               this.num_hp.setValue(this.num_hp.getValue() + num_15.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_20.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_7.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               break;
            case 8:
               this.num_hp.setValue(this.num_hp.getValue() + num_20.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_25.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_7.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               break;
            case 9:
               this.num_hp.setValue(this.num_hp.getValue() + num_20.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_25.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_1.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 1);
               this.num_gj.setValue(this.num_gj.getValue() + num_5.getValue());
               break;
            case 10:
               this.num_hp.setValue(this.num_hp.getValue() + num_27.getValue());
               this.num_bj.setValue(this.num_bj.getValue() + num_25.getValue());
               this.num_fy.setValue(this.num_fy.getValue() + num_15.getValue());
               this.num_sd.setValue(this.num_sd.getValue() + 2);
               this.num_gj.setValue(this.num_gj.getValue() + num_32.getValue());
         }
      }
      
      private function TaoZhuangXiaoGuo() : *
      {
         var i:int = 0;
         var hpXX:int = 0;
         var lianjiX:int = 0;
         var attack:Number = NaN;
         var lljj:int = 0;
         var arr:Array = XingLingFactory.GetTolal_Data_VT();
         var tempHp:uint = uint(arr[1].getValue());
         var tempHp2:uint = this.data.getStampSlot().getValueSlot5();
         this.lianjiBool = false;
         this.tsXiaoGuo = false;
         for(i in this.getAllSuitSkill)
         {
            if(this.getAllSuitSkill[i] == 57200)
            {
               hpXX = (this.temp_hp_Max.getValue() + tempHp + tempHp2) * 0.3;
               if(this.hp.getValue() < hpXX)
               {
                  this.num_gj.setValue(this.num_gj.getValue() + num_13.getValue());
               }
            }
            if(this.getAllSuitSkill[i] == 57362)
            {
               hpXX = (this.temp_hp_Max.getValue() + tempHp + tempHp2) * 0.3;
               if(this.hp.getValue() < hpXX)
               {
                  this.num_gj.setValue(this.num_gj.getValue() + num_65.getValue());
               }
            }
            else if(this.getAllSuitSkill[i] == 57201)
            {
               hpXX = (this.temp_hp_Max.getValue() + tempHp + tempHp2) * 0.4;
               if(this.hp.getValue() < hpXX)
               {
                  this.num_fy.setValue(this.num_fy.getValue() + num_20.getValue());
               }
            }
            else if(this.getAllSuitSkill[i] == 57210)
            {
               lianjiX = this.lianJi.getValue();
               if(lianjiX > 2000)
               {
                  lianjiX = 2000;
               }
               attack = lianjiX / 10 * 0.01;
               if(attack > 5)
               {
                  attack = 5;
               }
               this.num_gj.setValue(this.num_gj.getValue() + attack);
            }
            else if(this.getAllSuitSkill[i] == 57360)
            {
               this.lianjiBool = true;
               lianjiX = this.lianJi.getValue();
               if(lianjiX > 500)
               {
                  lianjiX = 500;
               }
               lljj = int(lianjiX / 50);
               attack = lljj * 0.23;
               if(attack > 5)
               {
                  attack = 5;
               }
               this.num_gj.setValue(this.num_gj.getValue() + attack);
            }
            else if(this.getAllSuitSkill[i] == 59580)
            {
               this.tsXiaoGuo = true;
            }
         }
      }
      
      private function 数值溢出修正() : *
      {
         if(this.hp.getValue() > this.use_hp_Max.getValue())
         {
            this.hp.setValue(this.use_hp_Max.getValue());
         }
         if(this.mp.getValue() > this.use_mp_Max.getValue())
         {
            this.mp.setValue(this.use_mp_Max.getValue());
         }
         if(TiaoShi.Hpup > 0)
         {
            this.hp.setValue(this.use_hp_Max.getValue() + TiaoShi.Hpup);
         }
      }
      
      private function HP_UP(num:int) : *
      {
         if(num <= 0)
         {
            return;
         }
         var hpXX:int = num + this.hp.getValue();
         if(hpXX > this.use_hp_Max.getValue())
         {
            this.hp.setValue(this.use_hp_Max.getValue());
         }
         else
         {
            this.hp.setValue(hpXX);
         }
      }
      
      private function MP_UP(num:int) : *
      {
         if(num <= 0)
         {
            return;
         }
         var mpXX:int = num + this.mp.getValue();
         if(mpXX > this.use_mp_Max.getValue())
         {
            this.mp.setValue(this.use_mp_Max.getValue());
         }
         else
         {
            this.mp.setValue(mpXX);
         }
      }
      
      public function 连击计数() : *
      {
         this.lianJiTime = VT.createVT();
         this.lianJi.setValue(this.lianJi.getValue() + 1);
         WinShow.LianJi(this.lianJi.getValue());
         JingLing.lianJiNumXX1010(this);
      }
      
      private function 连击计时() : *
      {
         this.lianJiTime.setValue(this.lianJiTime.getValue() + 1);
         if(this.lianjiBool && this.lianJiTime.getValue() > 87)
         {
            this.lianJi = VT.createVT();
            this.lianJi.setValue(0);
         }
         else if(!this.lianjiBool && this.lianJiTime.getValue() > 60)
         {
            this.lianJi = VT.createVT();
            this.lianJi.setValue(0);
         }
      }
      
      private function jianCDTime() : *
      {
         if(this.jianCDBool)
         {
            ++this.jianCDtemp;
            if(this.jianCDtemp > 270)
            {
               TiaoShi.txtShow("夏日时装减CD");
               this.jianCDnum = 1;
               this.GetAllSkillCD();
               this.jianCDBool = false;
               this.jianCDtemp = 0;
            }
         }
      }
      
      public function GetAllSkillCD() : *
      {
         var i:int = 0;
         this.AllSkillCD = this.data.skillCdArr();
         this.AllSkillCDXX = DeepCopyUtil.clone(this.AllSkillCD);
         for(i in this.AllSkillCD)
         {
            this.tsCDnumArr2[i] = 1;
            this.AllSkillCDXX[i][2] = 1;
            this.AllSkillCDXX[i][1] = 0;
            if(this.data.getSkillLevel(this.AllSkillCDXX[i][0]) > 0)
            {
               this.AllSkillCDXX[i][2] = 50;
               this.AllSkillCDXX[i][1] = this.AllSkillCD[i][1];
            }
         }
         if(this.jianCDnum == 11)
         {
            this.AllSkillCD = this.data.skillCdArr();
            for(i in this.AllSkillCD)
            {
               this.AllSkillCD[i][1] -= 6 * 27;
               if(this.AllSkillCD[i][1] < 0)
               {
                  this.AllSkillCD[i][1] = 0;
               }
               this.AllSkillCDXX[i][2] = 1;
               this.AllSkillCDXX[i][1] = 0;
               if(this.data.getSkillLevel(this.AllSkillCDXX[i][0]) > 0)
               {
                  this.AllSkillCDXX[i][2] = 50;
                  this.AllSkillCDXX[i][1] = this.AllSkillCD[i][1];
               }
            }
            this.jianCDBool = true;
         }
      }
      
      public function GetAllObjCD() : *
      {
         var i:int = 0;
         this.All_ObjCD = this.data.ObjCdArr();
         this.All_ObjCDXX = DeepCopyUtil.clone(this.All_ObjCD);
         for(i in this.All_ObjCD)
         {
            this.All_ObjCDXX[i][2] = 50;
         }
      }
      
      public function GetKeyArr(arr:Array) : *
      {
         var i:int = 0;
         if(arr.length == this.data._keyArr.length)
         {
            for(i = 0; i < this.data._keyArr.length; i++)
            {
               if(arr[i] is int)
               {
               }
            }
            this.KeyArr = this.data._keyArr;
         }
      }
      
      private function xuemaiHuiFu() : *
      {
         if(Boolean(NewPetPanel.XueMai) && Boolean(this.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(11))
            {
               ++this.xuemaiTime;
               if(this.xuemaiTime >= 20 * 27)
               {
                  this.HpUp(500);
                  this.xuemaiTime = 0;
               }
            }
            if(NewPetPanel.XueMai.isHaveSX(12))
            {
               ++this.xuemaiTime2;
               if(this.xuemaiTime2 >= 20 * 27)
               {
                  this.MpUp(200);
                  this.xuemaiTime2 = 0;
               }
            }
         }
      }
      
      private function DKTime() : *
      {
         var maxTime:int = 0;
         if(this.isDarkState)
         {
            ++this.darkTime;
            maxTime = 5 * 27;
            if(this.data.getEquipSlot().getEquipFromSlot(7) && this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 405 && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
            {
               maxTime = 10 * 27;
            }
            if(this.darkTime < maxTime)
            {
               this.addChild(this.dkState);
            }
            else
            {
               this.removeChild(this.dkState);
               this.isDarkState = false;
               this.darkTime = 0;
            }
         }
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         if(this.noYingZhiTime > 0)
         {
            --this.noYingZhiTime;
         }
         if(this.tsXiaoGuo_time > 0)
         {
            --this.tsXiaoGuo_time;
         }
         this.RZJN_hpDown();
         ++this.quan11Time;
         ++szCD;
         ++this.time;
         this.DKTime();
         this.连击计时();
         this.nineBuffTime();
         this.药品使用();
         this.xuemaiHuiFu();
         this.chibangFeiXing();
         this.shizhuangPenShe();
         this.huiFuYinZang();
         if(this.isDead || this.hp.getValue() <= 0)
         {
            this.hp.setValue(0);
            this.isDead = true;
            this.skin.visible = false;
            this.不信春哥();
            return;
         }
         if(!this.isDead && !this.playerCW2)
         {
            if(!this.skin.visible)
            {
               this.skin.visible = true;
            }
         }
         ++this.timeXXX;
         if(this.timeXXX % 40 == 0)
         {
            this.LoadAll_ZB_Skill();
         }
         else
         {
            this.ZB_HpMpUP();
         }
         if(Main.world)
         {
            this.CDtime();
            this.jianCDTime();
            this.SkinValue();
            this.KeyControl();
            this.MoveData();
            this.MoveRun();
            this.HeiAnJiNengGo();
         }
         this.skin_Z0.visible = true;
         var eqSlot_6:Equip = this.data.getEquipSlot().getEquipFromSlot(6);
         if(eqSlot_6 && this.skin_Z2 && this.skin_Z2_V && eqSlot_6.getRemainingTime() > 0)
         {
            this.skin_Z2.visible = true;
            this.skin_Z.visible = false;
            if(eqSlot_6.getFrame() == 498)
            {
               this.skin_Z0.visible = false;
            }
         }
         var eqSlot_9:Equip = this.data.getEquipSlot().getEquipFromSlot(9);
         if(Main.water.getValue() != 1 && !this.skin_Z2_V && eqSlot_9 && eqSlot_9.getFrame() == 525)
         {
            this.skin.visible = false;
         }
         this.CWBianSen();
      }
      
      public function 双手加成() : *
      {
         var arr:Array = null;
         var leftWQ:* = undefined;
         var rightWQ:* = undefined;
         var i:int = 0;
         this.shuangShouBeiShu = 1;
         if(this.data.isTransferOk())
         {
            arr = this.data.getTransferOk();
            leftWQ = this.data.getEquipSlot().getEquipFromSlot(2).getPosition();
            rightWQ = this.data.getEquipSlot().getEquipFromSlot(5).getPosition();
            for(i in arr)
            {
               if(arr[i] == 0)
               {
                  if(leftWQ == rightWQ && rightWQ == 5)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(arr[i] == 1)
               {
                  if(leftWQ == rightWQ && rightWQ == 6)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(arr[i] == 2)
               {
                  if(leftWQ == rightWQ && rightWQ == 7)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
               else if(Boolean(arr[i]) && arr[i] == 3)
               {
                  if(leftWQ == rightWQ && rightWQ == 0)
                  {
                     this.shuangShouBeiShu = Temp11.getValue();
                     return;
                  }
               }
            }
         }
      }
      
      private function HeadXX() : *
      {
         var z2Frame:int = 0;
         var z2Name:String = null;
         var newHeadFrame:int = 0;
         var headEquip:Equip = this.data.getEquipSlot().getEquipFromSlot(0);
         var equipXX:Equip = this.data.getEquipSlot().getEquipFromSlot(1);
         if(Main.water.getValue() != 1)
         {
            headEquip = this.data.getEquipSlot().getEquipFromSlot(8);
         }
         if(this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            z2Frame = int(this.data.getEquipSlot().getEquipFromSlot(6).getClassName2());
            this.headFrame = z2Frame;
            this.noHead = false;
            this.paiHangShow[1] = this.headFrame;
            z2Name = this.data.getEquipSlot().getEquipFromSlot(6).getClassName();
            if(z2Name == "S19" || z2Name == "S23" || z2Name == "S29")
            {
               this.noHead = true;
            }
         }
         else if(Boolean(this.data) && Boolean(headEquip))
         {
            newHeadFrame = int(headEquip.getClassName());
            this.headFrame = newHeadFrame;
            this.noHead = false;
            if(newHeadFrame == 24)
            {
               if(equipXX && equipXX.getClassName() == "甲红10" && Main.water.getValue() == 1)
               {
                  this.paiHangShow[1] = 0;
                  this.noHead = true;
               }
               else
               {
                  this.paiHangShow[1] = this.headFrame;
                  this.noHead = false;
               }
            }
            if(Boolean(equipXX) && equipXX.getClassName() == "甲红6")
            {
               if(Main.water.getValue() != 1)
               {
                  eadFrame = 1;
                  this.noHead = false;
               }
               else
               {
                  eadFrame = 53;
                  this.noHead = true;
               }
               TiaoShi.txtShow("发型1 = " + equipXX.getClassName());
            }
         }
         else
         {
            this.headFrame = 1;
            if(equipXX && equipXX.getClassName() == "甲红10" && Main.water.getValue() == 1)
            {
               this.paiHangShow[1] = 0;
               this.noHead = true;
            }
            else
            {
               this.paiHangShow[1] = 1;
               this.noHead = false;
            }
            if(Boolean(equipXX) && equipXX.getClassName() == "甲红6")
            {
               if(Main.water.getValue() != 1)
               {
                  eadFrame = 1;
                  this.noHead = false;
               }
               else
               {
                  eadFrame = 53;
                  this.noHead = true;
               }
            }
         }
      }
      
      private function CDtime() : *
      {
         var i:int = 0;
         var cdMax:int = 0;
         if(this.jianCD_lei)
         {
            Skill_lei.JianCDXXXX(this);
         }
         for(i in this.AllSkillCDXX)
         {
            cdMax = this.AllSkillCD[i][1] * this.tsCDnumArr2[i];
            if(this.AllSkillCDXX[i][1] < cdMax && this.data.getSkillLevel(this.AllSkillCDXX[i][0]) > 0)
            {
               ++this.AllSkillCDXX[i][1];
               if(this.jianCD)
               {
                  this.AllSkillCDXX[i][1] += 13;
                  this.jianCD = false;
               }
               if(this.AllSkillCDXX[i][1] >= cdMax)
               {
                  this.AllSkillCDXX[i][1] = this.AllSkillCD[i][1];
                  cdMax = int(this.AllSkillCD[i][1]);
                  this.tsCDnumArr2[i] = 1;
               }
               this.AllSkillCDXX[i][2] = int(this.AllSkillCDXX[i][1] / cdMax * 50);
            }
         }
         for(i in this.All_ObjCDXX)
         {
            if(this.All_ObjCDXX[i][1] < this.All_ObjCD[i][1])
            {
               ++this.All_ObjCDXX[i][1];
               this.All_ObjCDXX[i][2] = int(this.All_ObjCDXX[i][1] / this.All_ObjCD[i][1] * 50);
            }
         }
      }
      
      private function selCD(str:String) : int
      {
         var i:int = 0;
         var cdMax:int = 0;
         var strNum:int = 0;
         for(i in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[i][0] == str)
            {
               cdMax = this.AllSkillCD[i][1] * this.tsCDnumArr2[i];
               if(this.AllSkillCDXX[i][1] == cdMax)
               {
                  return i;
               }
               strNum = int(str.substr(1,2));
               if(strNum > 7 && this.tsJN_ok(i))
               {
                  this.tsCDnumArr2[i] = 2;
                  return i;
               }
               return -1;
            }
         }
         return -1;
      }
      
      private function sel_objCD(str:String) : int
      {
         var i:int = 0;
         for(i in this.All_ObjCD)
         {
            if(this.All_ObjCDXX[i][0] == str)
            {
               if(this.All_ObjCDXX[i][1] == this.All_ObjCD[i][1])
               {
                  return i;
               }
               return -1;
            }
         }
         return -1;
      }
      
      public function selCD50(str:String) : int
      {
         var i:int = 0;
         for(i in this.AllSkillCDXX)
         {
            if(this.AllSkillCDXX[i][0] == str)
            {
               return this.AllSkillCDXX[i][2];
            }
         }
         return 50;
      }
      
      public function sel_objCD50(str:String) : int
      {
         var i:int = 0;
         for(i in this.All_ObjCDXX)
         {
            if(this.All_ObjCDXX[i][0] == str)
            {
               return this.All_ObjCDXX[i][2];
            }
         }
         return 50;
      }
      
      public function wudi(e:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 81)
         {
            removeChild(this.nodead);
            this.rexuewudi = false;
            this.timeTemp = 0;
            this.flagTempRX = true;
            removeEventListener(Event.ENTER_FRAME,this.wudi);
         }
      }
      
      public function timeLimitBJ(e:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 54)
         {
            this.timeTemp = 0;
            this.flagTempBJ = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitBJ);
         }
      }
      
      public function timeLimitSJ(e:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 351)
         {
            this.timeTemp = 0;
            this.flagTempSJ = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitSJ);
         }
      }
      
      public function timeLimitSY(e:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 284)
         {
            this.timeTemp = 0;
            this.flagTempSY = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitSY);
         }
      }
      
      public function timeLimitXL(e:*) : *
      {
         ++this.timeTemp;
         if(this.timeTemp > 284)
         {
            this.timeTemp = 0;
            this.flagTempXL = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitXL);
         }
      }
      
      public function timeLimitMP(e:*) : *
      {
         ++this.timeTempmp;
         if(this.timeTempmp > 216)
         {
            this.timeTempmp = 0;
            this.flagTempMP = true;
            removeEventListener(Event.ENTER_FRAME,this.timeLimitMP);
         }
      }
      
      public function setInTimeCDMP() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitMP);
      }
      
      public function setInTimeCDXL() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitXL);
      }
      
      public function setInTimeCDBJ() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitBJ);
      }
      
      public function setInTimeCDSJ() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitSJ);
      }
      
      public function setInTimeCDSY() : *
      {
         addEventListener(Event.ENTER_FRAME,this.timeLimitSY);
      }
      
      public function HpXX(hitX:*, bj:Boolean = false) : *
      {
         var p:Player2 = null;
         var playHPxx:Number = NaN;
         var 职业附加:Number = NaN;
         var fangYU:int = 0;
         var p2:Player = null;
         var XX:int = 0;
         if(this.newLifeTime.getValue() > InitData.BuyNum_0.getValue() + InitData.BuyNum_1.getValue())
         {
            return;
         }
         if(this.noHit)
         {
            this.noHit = false;
            return;
         }
         if(this.noHit82)
         {
            return;
         }
         if(this.rexuewudi)
         {
            return;
         }
         if(hitX is HitXX)
         {
            this.hitXX = hitX;
         }
         ++WinShow.txt_3;
         var gongJi_hpX:Number = Number(hitX.gongJi_hp);
         var attTimes:int = int(hitX.times);
         var hpX:int = 0;
         NewMC.Open("被攻击",this);
         if(gongJi_hpX == -1001)
         {
            gongJi_hpX = int(this.use_hp_Max.getValue() * 0.02);
         }
         if(this.cengshu.length > 0)
         {
            for(i in this.cengshu)
            {
               if(this.cengshu[i].type == 521)
               {
                  gongJi_hpX *= 999;
               }
            }
         }
         if(hitX is HitXX && hitX.who is Player2)
         {
            p = hitX.who as Player2;
            playHPxx = p.use_gongji.getValue();
            职业附加 = 1;
            if(p.data.skinArr[p.data.skinNum] == 0)
            {
               职业附加 = Number(p.职业附加[0]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 1)
            {
               职业附加 = Number(p.职业附加[1]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 2)
            {
               职业附加 = Number(p.职业附加[2]);
            }
            else if(p.data.skinArr[p.data.skinNum] == 3)
            {
               职业附加 = Number(p.职业附加[3]);
            }
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  gongJi_hpX *= 0.3;
               }
            }
            fangYU = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            playHPxx = p.use_gongji.getValue();
            hpX = gongJi_hpX * 职业附加 * playHPxx - fangYU / attTimes;
            if(hpX < 0)
            {
               hpX = 0;
            }
            else
            {
               {};
            }
            hpX += gongJi_hpX * (Math.random() * 3 + 2) / 100;
            if(bj)
            {
               hpX *= this.tempXXX2.getValue();
            }
         }
         else if(Main.gameNum.getValue() == 0 && hitX.who != this && hitX is HitXX && hitX.who is Player)
         {
            p2 = hitX.who as Player;
            playHPxx = p2.use_gongji.getValue();
            职业附加 = 1;
            if(p2.data.skinArr[p2.data.skinNum] == 0)
            {
               职业附加 = Number(p2.职业附加[0]);
            }
            else if(p2.data.skinArr[p2.data.skinNum] == 1)
            {
               职业附加 = Number(p2.职业附加[1]);
            }
            else if(p2.data.skinArr[p2.data.skinNum] == 2)
            {
               职业附加 = Number(p2.职业附加[2]);
            }
            else if(p2.data.skinArr[p2.data.skinNum] == 3)
            {
               职业附加 = Number(p2.职业附加[3]);
            }
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  gongJi_hpX *= 0.3;
               }
            }
            fangYU = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            playHPxx = p2.use_gongji.getValue();
            hpX = gongJi_hpX * 职业附加 * playHPxx - fangYU / attTimes;
            if(hpX < 0)
            {
               hpX = 0;
            }
            else
            {
               {};
            }
            hpX += gongJi_hpX * (Math.random() * 3 + 2) / 100;
            if(bj)
            {
               hpX *= this.tempXXX2.getValue();
            }
         }
         else
         {
            if(this.data.skinArr[this.data.skinNum] == 0 && this.skin.runType == "转职技能1" && this.skin.frame < 20)
            {
               if(this.data.getSkillLevel("a12") > 0)
               {
                  gongJi_hpX *= 0.3;
               }
            }
            fangYU = this.use_fangyu.getValue() + this.fangYuPOWER.getValue();
            hpX = gongJi_hpX - fangYU / attTimes;
            if(hpX < 0)
            {
               hpX = 0;
            }
            else
            {
               {};
            }
            hpX += gongJi_hpX * (Math.random() * 3 + 2) / 100;
            if(bj)
            {
               hpX *= this.tempXXX2.getValue();
            }
         }
         if(hitX.who is Enemy && Main.water.getValue() != 1)
         {
            hpX = hpX * (1 + (hitX.who as Enemy).魔攻力.getValue() / 100) / (1 + this.use_fangyu2.getValue() / 100);
         }
         hpX = this.BuffXXX(hpX,hitX);
         if(Main.gameNum.getValue() == 999 && hpX > hitX.gongJi_hp_MAX)
         {
            hpX = int(hitX.gongJi_hp_MAX);
            if(hitX.gongJi_hp_MAX > 300000 && this.hitXX.who is Player2)
            {
               (this.hitXX.who as Player2).noJiFen = true;
               (this.hitXX.who as Player2).hp.setValue(0);
               TiaoShi.txtShow("清除竞技场作弊玩家,重新加载~~~~");
               return;
            }
         }
         if(Skin.xishouWuDi)
         {
            if(Skin.xishouHP > 0)
            {
               Skin.xishouHP -= hpX;
               NewMC.Open("闪避2",Main.world.moveChild_Other,xxx,yyy,15,0,true,2);
            }
            else
            {
               Skin.xishouWuDi = false;
            }
            return;
         }
         if(GameData.gameLV == 6 && Main.gameNum.getValue() > 5000 && Main.gameNum.getValue() < 5100)
         {
            hpX = int(this.GongHuiBoos_HpXX(hitX.gongJi_hp));
         }
         if(GameData.gameLV == 7 && Main.gameNum.getValue() >= 7001 && Main.gameNum.getValue() <= 7200)
         {
            hpX = int(this.YouLing_HpXX(hitX));
         }
         if(hitX.who is Enemy && (hitX.who as Enemy).id == 2015)
         {
            hpX = this.use_hp_Max.getValue() * 0.02;
         }
         this.hpDown_new(hpX,bj);
         if(Boolean(this.playerJL) && hitX.who is Enemy)
         {
            this.playerJL.BD1009(hitX.who,hpX);
         }
         this.ShiZhuangXiaoGuo();
         if(!this.playerCW2)
         {
            XX = hitX.硬直 - this.skin.被攻击硬直;
            if(Boolean(hitX.who) && hitX.who is Player2)
            {
               XX = XX / 5 * 4;
            }
            if(this.noYingZhi || this.noYingZhiTime > 0)
            {
               XX = -1;
            }
            if(XX > this.skin.continuousTime && XX >= 0 && (!this.guangQiu || this.guangQiu.typeX < 3) && !this.guangDun)
            {
               this.skin.GoTo("被打",XX);
            }
         }
         if(!this.playerCW2)
         {
            this.runArr = new Array();
         }
         if(!this.noYingZhi && !this.playerCW2)
         {
            if(hitX.RL)
            {
               this.runPower(hitX.runArr[0],hitX.runArr[1],hitX.runArr[2]);
            }
            else
            {
               this.runPower(-hitX.runArr[0],hitX.runArr[1],hitX.runArr[2]);
            }
         }
      }
      
      public function BuffXXX(hpX:Number, hitX:*) : Number
      {
         var CYjianshang:Number = NaN;
         var i:int = 0;
         var shuipaokongzhi:Class = null;
         var bool:Boolean = false;
         var shuipaobiaoji:Class = null;
         var xixuefenzi:Class = null;
         var en4004:Enemy = null;
         if(hitX.parent is Fly && (hitX.parent as Fly)._name == "暗黑牢笼")
         {
            hitX.parent.hitWho = this;
         }
         if(this.lianJi.getValue() > 50 && this.data.getElvesSlot().backElvesSkill5() > 0)
         {
            hpX *= 1 - this.data.getElvesSlot().backElvesSkill5();
         }
         if(Boolean(NewPetPanel.XueMai) && Boolean(this.playerCW))
         {
            if(NewPetPanel.XueMai.isHaveSX(4))
            {
               hpX *= 0.95;
            }
         }
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 415)
            {
               hpX *= 0.9;
            }
         }
         if(this.data.getEquipSlot().getEquipFromSlot(7))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 416)
            {
               hpX *= 0.9;
            }
         }
         if(this.speedTemp.getValue() <= -6)
         {
            hpX += this.use_hp_Max.getValue() * 0.02;
         }
         if(this.data.getStampSlot().getValueSlot8() > 0)
         {
            hpX -= hpX * this.data.getStampSlot().getValueSlot8();
         }
         if(this.jianrenState == 2)
         {
            hpX *= 0.7;
         }
         if(CaiYaoPanel.saveArr[0] > 0)
         {
            CYjianshang = 1 - (CaiYaoPanel.saveArr[0] * CaiYaoPanel.addArr[0] + CaiYaoPanel.saveArr[3] * CaiYaoPanel.addArr[3] + CaiYaoPanel.saveArr[6] * CaiYaoPanel.addArr[6] + CaiYaoPanel.saveArr[9] * CaiYaoPanel.addArr[9] + CaiYaoPanel.saveArr[12] * CaiYaoPanel.addArr[12]) * 0.01;
            hpX *= CYjianshang;
         }
         if(hitX.parent is Fly && (hitX.parent as Fly)._name == "灵魂抽取")
         {
            for(i in Enemy.All)
            {
               if(Boolean(Enemy.All[i]) && (Enemy.All[i] as Enemy).id == 3022)
               {
                  (Enemy.All[i] as Enemy).hpUpEnemy(hpX);
                  NewMC.Open("回血效果",Enemy.All[i],0,0,0,hpX);
                  TiaoShi.txtShow("回复" + hpX);
               }
            }
         }
         if(hitX.who is Enemy && (hitX.who as Enemy).id == 4010)
         {
            if(hitX.parent is Fly && (hitX.parent as Fly)._name == "水泡飞行")
            {
               shuipaokongzhi = NewLoad.XiaoGuoData.getClass("水泡控制") as Class;
               this.skin_W.addChild(new shuipaokongzhi());
            }
            for(i in Fly.All)
            {
               if(Fly.All[i]._name == "水泡标记")
               {
                  bool = true;
                  for(i = 0; i < Fly.All.length; i++)
                  {
                     if(Boolean(Fly.All[i]) && Fly.All[i]._name == "水泡控制")
                     {
                        Fly.All[i].time = 54;
                        bool = false;
                     }
                  }
                  if(bool)
                  {
                     shuipaokongzhi = NewLoad.XiaoGuoData.getClass("水泡控制") as Class;
                     this.skin_W.addChild(new shuipaokongzhi());
                  }
               }
            }
         }
         if(hitX.who is Enemy && (hitX.who as Enemy).id == 4010)
         {
            bool = true;
            for(i = 0; i < Fly.All.length; i++)
            {
               if(Boolean(Fly.All[i]) && Fly.All[i]._name == "水泡标记")
               {
                  Fly.All[i].time = 27 * 8;
                  bool = false;
               }
            }
            if(bool)
            {
               shuipaobiaoji = NewLoad.XiaoGuoData.getClass("水泡标记") as Class;
               this.skin_W.addChild(new shuipaobiaoji());
            }
         }
         if(hitX.who is Enemy && (hitX.who as Enemy).id == 1056)
         {
            if((hitX.who as Enemy).jianShangPER < 0.7)
            {
               (hitX.who as Enemy).jianShangPER += 0.015;
            }
            (hitX.who as Enemy).skin.lexAdd += 0.02;
            xixuefenzi = NewLoad.XiaoGuoData.getClass("吸血分子") as Class;
            this.skin_W.addChild(new xixuefenzi());
         }
         if(hitX.who is Enemy && (hitX.who as Enemy).className == "悬赏9")
         {
            if(Main.gameNum.getValue() == 7003)
            {
               EnemyBossXS225.xishou_Value += 50;
            }
            else
            {
               EnemyBossXS225.xishou_Value += hpX * 20;
            }
         }
         if(Main.gameNum.getValue() == 17)
         {
            hpX += this.use_hp_Max.getValue() * 0.03;
         }
         if(hitX.who is Enemy && (hitX.who as Enemy).id == 7007)
         {
            en4004 = hitX.who;
            if(en4004.skin.redTime > 0)
            {
               hpX *= 2;
            }
         }
         hpX *= this.jianSang_UP;
         return hpX * this.hpDownXX;
      }
      
      public function ShiZhuangXiaoGuo() : *
      {
         var szID:int = 0;
         var szFrame:int = 0;
         var rdm:Number = NaN;
         var hxx:HitXX = null;
         var chongjibo:Class = null;
         if(this.playerCW2)
         {
            return;
         }
         Skill_guangDun.addToPlayer(this);
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            szID = this.data.getEquipSlot().getEquipFromSlot(6).getId();
            szFrame = this.data.getEquipSlot().getEquipFromSlot(6).getFrame();
            if(szID == 14455 || szID == 14461 || szID == 14462 || szID == 14463 || szID == 14464 || szID == 14465)
            {
               rdm = Math.random() * 20;
               if(rdm < 1 && this.hp.getValue() > 0)
               {
                  if(Main.gameNum.getValue() == 999)
                  {
                     this.hp.setValue(this.hp.getValue() + Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player.getValue()));
                     NewMC.Open("回血效果",this,0,0,0,Math.round(this.use_hp_Max.getValue() / 5 / InitData.pkHpMaxNum_Player.getValue()));
                  }
                  else
                  {
                     this.hp.setValue(this.hp.getValue() + Math.round(this.use_hp_Max.getValue() / 5));
                     NewMC.Open("回血效果",this,0,0,0,Math.round(this.use_hp_Max.getValue() / 5));
                  }
               }
            }
            else if(szID >= 14622 && szID <= 14627 || szID >= 14613 && szID <= 14618)
            {
               if(this.hitXX.who is Enemy)
               {
                  hxx = new HitXX();
                  hxx.who = this;
                  hxx.type = 102;
                  hxx.space = 81;
                  hxx.totalTime = 81;
                  hxx.numValue = 0;
                  if(this.iceTIME == 0)
                  {
                     addEventListener(Event.ENTER_FRAME,this.iceCD);
                     rdm = Math.round(Math.random() * 100);
                     if(rdm <= 25)
                     {
                        new BuffEnemy(hxx,this.hitXX.who);
                     }
                  }
               }
            }
            else if(szFrame == 415)
            {
               if(szCD > 54)
               {
                  szCD = 0;
                  if(Math.random() * 100 < 30)
                  {
                     chongjibo = NewLoad.XiaoGuoData.getClass("时装冲击波") as Class;
                     this.skin_W.addChild(new chongjibo());
                     this.kouxue = true;
                     this.tankaiTime = 0;
                     addEventListener(Event.ENTER_FRAME,this.tankai);
                  }
               }
            }
         }
      }
      
      private function YouLing_HpXX(hitX:*) : int
      {
         var hitHpXXX:Number = NaN;
         var hpNumXXX:Number = NaN;
         var hpXX:Number = NaN;
         var hpNum:int = 1;
         var hitNum:int = 1;
         if(hitX is HitXX)
         {
            hitHpXXX = (hitX as HitXX).gongJi_hp / 100;
            if(hitHpXXX < 0.005)
            {
               hitHpXXX = 0.005;
            }
            hpNumXXX = this.use_hp_Max.getValue() * hitHpXXX;
            hitNum = int((hitX as HitXX).times);
            hpXX = 1 - this.use_hp_Max.getValue() / 10000 / 100;
            if(hpXX < 0.5)
            {
               hpXX = 0.5;
            }
            hpNum = hpNumXXX * hpXX / hitNum;
         }
         return hpNum;
      }
      
      private function GongHuiBoos_HpXX(hpNum:int) : int
      {
         var fy:int = (this.use_fangyu.getValue() + this.fangYuPOWER.getValue()) / 1000;
         if(fy > 5)
         {
            fy = 5;
         }
         return int((hpNum - fy) * this.hp_Max.getValue() / 100);
      }
      
      public function HpXX2(hpDown:int) : *
      {
         this.hpDown_new(hpDown);
      }
      
      public function Hit3000(num:int, num2:int = 100) : *
      {
         var hpDown:int = this.hp_Max.getValue() * num / num2;
         this.hpDown_new(hpDown);
      }
      
      private function tankai(e:*) : *
      {
         var i:* = undefined;
         var juli:int = 0;
         var hxx:HitXX = null;
         ++this.tankaiTime;
         if(this.tankaiTime < 9)
         {
            for(i in Enemy.All)
            {
               if((Enemy.All[i] as Enemy).isFunction)
               {
                  juli = Enemy.All[i].x - this.x;
                  if(juli < 300 && juli >= 0)
                  {
                     Enemy.All[i].x += 33;
                     if(this.kouxue)
                     {
                        hxx = new HitXX();
                        hxx.who = this;
                        hxx.gongJi_hp = 1;
                        hxx.times = 1;
                        (Enemy.All[i] as Enemy).HpXX(hxx);
                     }
                     if(Math.random() * 100 < 15)
                     {
                        if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 416)
                        {
                           Enemy.All[i].skin.GoTo("被打",27);
                        }
                     }
                  }
                  if(juli > -300 && juli < 0)
                  {
                     Enemy.All[i].x -= 33;
                     if(this.kouxue)
                     {
                        hxx = new HitXX();
                        hxx.who = this;
                        hxx.gongJi_hp = 1;
                        hxx.times = 1;
                        (Enemy.All[i] as Enemy).HpXX(hxx);
                     }
                     if(Math.random() * 100 < 15)
                     {
                        if(Boolean(this.data.getEquipSlot().getEquipFromSlot(7)) && this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 416)
                        {
                           Enemy.All[i].skin.GoTo("被打",27);
                        }
                     }
                  }
               }
            }
            this.kouxue = false;
         }
         else
         {
            removeEventListener(Event.ENTER_FRAME,this.tankai);
         }
      }
      
      private function iceCD(e:*) : *
      {
         ++this.iceTIME;
         if(this.iceTIME == 135)
         {
            this.iceTIME = 0;
            removeEventListener(Event.ENTER_FRAME,this.iceCD);
         }
      }
      
      private function getRandom(num:Number) : Boolean
      {
         var rd:Number = Math.random() * 100;
         if(rd < num)
         {
            return true;
         }
         return false;
      }
      
      private function SkinValue() : *
      {
         if(this.skin != null)
         {
            this.gravity = this.skin.gravity;
            if(this.skin.moveArr != null)
            {
               if(this.RL)
               {
                  this.runPower(this.skin.moveArr[0],this.skin.moveArr[1],this.skin.moveArr[2]);
               }
               else
               {
                  this.runPower(-this.skin.moveArr[0],this.skin.moveArr[1],this.skin.moveArr[2]);
               }
            }
         }
      }
      
      private function onKEY_DOWN(e:KeyboardEvent) : *
      {
         if(e.keyCode == this.data._keyArr[5])
         {
            this.jKey = true;
         }
      }
      
      private function KeyControl() : *
      {
         var perNum:int = 0;
         var flySpeed:int = 0;
         var equipID:uint = 0;
         var mpXX:int = 0;
         var siling:Class = null;
         if(!this.KeyControl_YN)
         {
            return;
         }
         if(DuoKai_Info._noSave)
         {
            return;
         }
         if(!this.skin)
         {
            return;
         }
         if(this.playerCW2)
         {
            return;
         }
         if(Boolean(this.playerJL) && (this == Main.player_1 && Boolean(BasicKey.getKeyState(66,1)) || this == Main.player_2 && Boolean(BasicKey.getKeyState(106,1))))
         {
            this.playerJL.ZD();
         }
         if(this.skin.continuousTime > 0 && !this.playerCW2)
         {
            if(this.data.skinArr[this.data.skinNum] == 3 && this.getKeyStatus("转职",1) && this.selCD("k12") != -1)
            {
               if(!this.getKeyStatus("上",3))
               {
                  if(!this.getKeyStatus("下",2))
                  {
                     if(!this.getKeyStatus("上",2))
                     {
                        this.SkinPlay("转职技能1");
                        this.FbChengFa(3);
                     }
                  }
               }
            }
            return;
         }
         if(!this.skin.Get_StopRun())
         {
            if(this.jumping > 0 && this.skin.runType.substr(0,2) == "攻击")
            {
               this.MoveFun();
            }
            else if(this.data.skinArr[this.data.skinNum] == 3 && this.skin.runType == "转职技能4")
            {
               this.MoveFun();
            }
            return;
         }
         if(this.jumping > 0 && this.getKeyStatus("跳",1) && !PK_UI.PK_ing && Main.gameNum.getValue() != 7003)
         {
            if(this.mp.getValue() < 1)
            {
               this.mp.setValue(1);
               this.fly = false;
               this.flyTime = 0;
            }
            else
            {
               this.fly = true;
            }
         }
         if(this.getKeyStatus("跳",2) == false)
         {
            this.flyTime = 0;
         }
         if(Boolean(PK_UI.PK_ing) || Main.gameNum.getValue() == 7003)
         {
            this.fly = false;
            this.flyTime = 0;
         }
         if(this.fly && this.mp.getValue() > 1 && this.getKeyStatus("跳",2) && this.skin_Z3_V && this.data.getEquipSlot().getEquipFromSlot(7) && this.data.getEquipSlot().getEquipFromSlot(7).getColor() >= 4 && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
         {
            perNum = 2;
            ++this.flyTime;
            if(this.data.getEquipSlot().getEquipFromSlot(6))
            {
               if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 423)
               {
                  perNum = 4;
               }
            }
            if(this.flyTime > 4)
            {
               this.feixingBool3 = true;
               if(this.flyTime % 24 == 10)
               {
                  mpXX = this.mp.getValue() - this.use_mp_Max.getValue() / 100 * perNum * (1 - this.data.getStampSlot().getValueSlot9());
                  if(mpXX < 1)
                  {
                     this.mp.setValue(1);
                  }
                  else
                  {
                     this.mp.setValue(mpXX);
                  }
               }
               this.gravity = this.gravityNum = 0;
               flySpeed = this.walk_power.getValue() * 1.5;
               equipID = this.data.getEquipSlot().getEquipFromSlot(7).getId();
               if(equipID >= 14604 && equipID <= 14609)
               {
                  flySpeed += 2;
               }
               if(this.getKeyStatus("左",2))
               {
                  this.runPower(-flySpeed * this.walk_power2,0,1);
                  this.getRL(false);
               }
               if(this.getKeyStatus("右",2))
               {
                  this.runPower(flySpeed * this.walk_power2,0,1);
                  this.getRL(true);
               }
               if(this.getKeyStatus("上",2))
               {
                  this.runPower(0,8 * this.walk_power2,1);
               }
               if(this.getKeyStatus("下",2))
               {
                  this.runPower(0,-8 * this.walk_power2,1);
               }
               this.SkinPlay("跳");
               return;
            }
         }
         else
         {
            this.feixingBool3 = false;
         }
         if(this.jKey && (this.jumping == 0 || this.jumpX2 && this.jumping < 2))
         {
            this.jKey = false;
            this.runPower(0,this.jump_power,this.jump_time,"跳");
            this.SkinPlay("跳");
            this.jumping += 1;
            if(this.jumpX2 && this.jumping == 2)
            {
               NewMC.Open("二段跳效果",Main.world.moveChild_Other,this.x,this.y);
            }
         }
         else if(this.jKey)
         {
            this.jKey = false;
         }
         var typeStr:String = "站";
         if(this.getKeyStatus("左",3))
         {
            typeStr = "跑";
            if(!this.noMove)
            {
               this.runPower(-this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
            }
            this.getRL(false);
         }
         else if(this.getKeyStatus("右",3))
         {
            typeStr = "跑";
            if(!this.noMove)
            {
               this.runPower(this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
            }
            this.getRL(true);
         }
         else if(this.getKeyStatus("左",2))
         {
            typeStr = "走";
            if(!this.noMove)
            {
               this.runPower(-this.walk_power.getValue() * this.walk_power2,0,1);
            }
            this.getRL(false);
         }
         else if(this.getKeyStatus("右",2))
         {
            typeStr = "走";
            if(!this.noMove)
            {
               this.runPower(this.walk_power.getValue() * this.walk_power2,0,1);
            }
            this.getRL(true);
         }
         if(this.getKeyStatus("攻击",1))
         {
            if(typeStr == "跑" && this.jumping == 0)
            {
               this.SkinPlay("跑攻");
            }
            else if(this.getKeyStatus("上",2))
            {
               this.SkinPlay("上挑");
            }
            else if(this.getKeyStatus("下",2))
            {
               this.SkinPlay("下斩");
            }
            else if(this.jumping > 0)
            {
               this.SkinPlay("攻击1");
               if(this.isDarkState)
               {
                  siling = NewLoad.XiaoGuoData.getClass("时装死灵球") as Class;
                  this.skin_W.addChild(new siling());
               }
            }
            else
            {
               this.skin.timeNum = 25;
               this.SkinPlay("攻击");
               if(this.isDarkState)
               {
                  siling = NewLoad.XiaoGuoData.getClass("时装死灵球") as Class;
                  this.skin_W.addChild(new siling());
               }
            }
            this.FbChengFa(2);
         }
         else if(this.getKeyStatus("怪物",1))
         {
            this.SkinPlay("怪物技能");
         }
         else if(this.getKeyStatus("技能1",1))
         {
            this.SkinPlay("技能1");
            this.FbChengFa(3);
         }
         else if(this.getKeyStatus("技能2",1))
         {
            this.SkinPlay("技能2");
            this.FbChengFa(3);
         }
         else if(this.getKeyStatus("技能3",1))
         {
            this.SkinPlay("技能3");
            this.FbChengFa(3);
         }
         else if(this.getKeyStatus("技能4",1))
         {
            this.SkinPlay("技能4");
            this.FbChengFa(3);
         }
         else if(this.getKeyStatus("转职",1))
         {
            if(this.getKeyStatus("上",3))
            {
               this.SkinPlay("转职技能4");
               this.FbChengFa(3);
            }
            else if(Boolean(this.playerCW) && this.getKeyStatus("下",3))
            {
               ChongWu2.addCW2(this);
            }
            else if(this.getKeyStatus("下",2))
            {
               this.SkinPlay("转职技能3");
               this.FbChengFa(3);
            }
            else if(this.getKeyStatus("上",2))
            {
               this.SkinPlay("转职技能2");
               this.FbChengFa(3);
            }
            else
            {
               this.SkinPlay("转职技能1");
               this.FbChengFa(3);
            }
         }
         else if(this.jumping == 0)
         {
            this.SkinPlay(typeStr);
         }
         else
         {
            this.SkinPlay("跳");
         }
         --this.SwitchingTime;
         if(this.getKeyStatus("切换",1) && this.SwitchingTime <= 0)
         {
            this.SwitchingTime = 6;
            this.QieHuanSkin();
         }
      }
      
      public function FbChengFa(num:int) : *
      {
         if(Main.gameNum.getValue() == 84 && Boss84FP.paiState == num)
         {
            this.fbChengFa = true;
         }
      }
      
      public function QieHuanSkin() : *
      {
         if(NewLoad.loadArr.length > 0)
         {
            return;
         }
         if(this.data.skinNum == 0)
         {
            this.data.skinNum = 1;
            this.newSkin();
         }
         else
         {
            this.data.skinNum = 0;
            this.newSkin();
         }
      }
      
      private function 药品使用() : *
      {
         if(Main.gameNum.getValue() >= 7000 && Main.gameNum.getValue() <= 7200)
         {
            return;
         }
         if(Boolean(GameOver.noFuHuo) || this.visible == false)
         {
            return;
         }
         if(this.playerCW2)
         {
            return;
         }
         if(this.getKeyStatus("消耗1",1))
         {
            this.UserObj(0);
         }
         else if(this.getKeyStatus("消耗2",1))
         {
            this.UserObj(1);
         }
         else if(this.getKeyStatus("消耗3",1))
         {
            this.UserObj(2);
         }
      }
      
      private function 复活药使用() : *
      {
         var i:* = undefined;
         var gameNum2X:int = 0;
         for(i in Enemy.All)
         {
            if(Enemy.All[i].id == 3022)
            {
               Enemy.All[i].rebornTimes = 1;
               Enemy.All[i].skin.runOver = true;
               Enemy.All[i].skin.GoTo("攻击3");
            }
         }
         JiHua_Interface.ppp2_13 = true;
         this.信春哥();
         this.alpha = 0.7;
         this.newLifeTime.setValue(InitData.BuyNum_1.getValue());
         addEventListener(Event.ENTER_FRAME,this.NewLife);
         All[All.length] = this;
         GameData.deadTime = 5;
         this.newSkin();
         if(Main.gameNum.getValue() == 2000)
         {
            GameData.winYN = false;
            gameNum2X = Main.gameNum2.getValue() - 3;
            if(gameNum2X < 1)
            {
               gameNum2X = 1;
            }
            Main.gameNum2.setValue(gameNum2X);
            Main._this.Loading();
         }
      }
      
      private function 刷新背包显示() : *
      {
         BagItemsShow.suppliesShow();
      }
      
      private function NewLife(e:*) : *
      {
         this.newLifeTime.setValue(this.newLifeTime.getValue() + InitData.BuyNum_1.getValue());
         if(this.newLifeTime.getValue() % 10 > 4)
         {
            this.alpha = 1;
         }
         else
         {
            this.alpha = 0.7;
         }
         if(this.newLifeTime.getValue() > InitData.BuyNum_80.getValue())
         {
            this.newLifeTime = VT.createVT();
            this.alpha = 1;
            removeEventListener(Event.ENTER_FRAME,this.NewLife);
         }
      }
      
      private function UserObj(num:int) : *
      {
         var i:int = 0;
         var XX:Supplies = null;
         var mode:int = 0;
         var tempCd:int = 0;
         var mpva:int = 0;
         var MPHPva:int = 0;
         var maxMP:int = 0;
         var hpva:int = 0;
         var maxHP:int = 0;
         var xa:int = 0;
         var va:int = 0;
         var lv:int = 0;
         var xv:int = 0;
         var max:int = 0;
         var max2:int = 0;
         var per:int = 0;
         var pva:int = 0;
         var tempValue:Number = NaN;
         if(GameData.gameLV == 7)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法使用药品");
            return;
         }
         this.FbChengFa(1);
         if(this.data.getSuppliesSlot().getNumFromSuppliesSlot(this.data.getBag(),num) < 1)
         {
            return;
         }
         if(this.hp.getValue() <= 0 && GameData.gameLV != 6)
         {
            XX = this.data.getSuppliesSlot().getFromSuppliesSlot(num);
            mode = XX.getAffectMode();
            if(mode == 2)
            {
               if(Boolean(PK_JiFen_UI._this) && Boolean(PK_JiFen_UI._this.visible))
               {
                  return;
               }
               this.复活药使用();
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
               Main.Save(false);
            }
            return;
         }
         var name:String = this.data.getSuppliesSlot().getFromSuppliesSlot(num).getName();
         if(!this.CanUserObj(name))
         {
            return;
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(num).getAffectMode() == 3)
         {
            if(skillYAO >= 3)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"一关内最多使用3次，已达到上限");
               return;
            }
            if(this.data.skinNum == 0 && Boolean(this.data.getEquipSkillSlot().getGemFromSkillSlot(0)))
            {
               if(this.energySlot.getEnergyPer(this) < 100)
               {
                  this.energySlot.energyLeftNum.setValue(98745305);
               }
               ++skillYAO;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能石充能完毕！");
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
            }
            if(this.data.skinNum == 1 && Boolean(this.data.getEquipSkillSlot().getGemFromSkillSlot(1)))
            {
               if(this.energySlot.getEnergyPer(this) < 100)
               {
                  this.energySlot.energyRightNum.setValue(92145035);
               }
               ++skillYAO;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"技能石充能完毕！");
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(num).getAffectMode() == 5)
         {
            if(JingLingCatch.count == 1)
            {
               if(this.data.getElvesSlot().backElvesSlotNum() <= 0)
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵栏已满，请清理扩充");
                  return;
               }
               if(JingLingCatch.isMove)
               {
                  JingLingCatch.myplayer = this.data;
                  JingLingCatch.catchJL();
                  tempCd = int(this.sel_objCD(name));
                  this.All_ObjCDXX[tempCd][1] = 0;
                  this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
                  Main.Save();
               }
               else
               {
                  NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"精灵正在被捕捉中");
               }
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"没有发现精灵踪迹");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(num).getAffectMode() == 6)
         {
            if(this.zhongqiuState == 0)
            {
               if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
               {
                  this.zhongqiuState = 2;
               }
               else
               {
                  this.zhongqiuState = 1;
               }
               this.jianrenState = 0;
               this.shengmingState = 0;
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已经处于疯狂状态");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(num).getAffectMode() == 7)
         {
            if(this.jianrenState == 0)
            {
               if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
               {
                  this.jianrenState = 2;
               }
               else
               {
                  this.jianrenState = 1;
               }
               this.shengmingState = 0;
               this.zhongqiuState = 0;
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"已经处于坚韧状态");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(num).getAffectMode() == 8)
         {
            if(this.shengmingState == 0)
            {
               if(Main.gameNum.getValue() > 0 && Main.gameNum.getValue() < 999 && GameData.gameLV < 5)
               {
                  this.shengmingState = 2;
               }
               else
               {
                  this.shengmingState = 1;
               }
               this.jianrenState = 0;
               this.zhongqiuState = 0;
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"充斥生命之力状态");
            }
         }
         if(this.data.getSuppliesSlot().getFromSuppliesSlot(num).getAffectMode() == 4)
         {
            mpva = this.use_mp_Max.getValue() * InitData.zongzi.getValue();
            MPHPva = this.mp.getValue() + mpva;
            maxMP = this.use_mp_Max.getValue();
            if(MPHPva < maxMP)
            {
               this.mp.setValue(MPHPva);
            }
            else
            {
               this.mp.setValue(maxMP);
            }
            NewMC.Open("回蓝效果",this,0,0,0,mpva);
            hpva = this.use_hp_Max.getValue() * InitData.zongzi.getValue();
            MPHPva = this.hp.getValue() + hpva;
            maxHP = this.use_hp_Max.getValue();
            if(MPHPva < maxHP)
            {
               this.hp.setValue(MPHPva);
            }
            else
            {
               this.hp.setValue(maxHP);
            }
            NewMC.Open("回血效果",this,0,60,0,hpva);
            tempCd = int(this.sel_objCD(name));
            this.All_ObjCDXX[tempCd][1] = 0;
            this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
         }
         this.XXSupplies = this.data.getSuppliesSlot().getFromSuppliesSlot(num);
         var arr:Array = this.XXSupplies.getAffectAttrib();
         for(i in arr)
         {
            xa = int((arr[i] as SuppliesAffect).getAttribType());
            va = int((arr[i] as SuppliesAffect).getValue());
            lv = int(this.XXSupplies.getUseLevel());
            per = int(this.XXSupplies.getPercent());
            if(xa == 1 && this.data.getLevel() >= lv)
            {
               tempValue = this.data.getStampSlot().getValueSlot2();
               va *= 1 + tempValue;
               xv = this.hp.getValue() + va;
               max = this.use_hp_Max.getValue();
               if(xv < max)
               {
                  this.hp.setValue(xv);
               }
               else
               {
                  this.hp.setValue(max);
               }
               NewMC.Open("回血效果",this,0,0,0,va);
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
               this.tempChiXuTime = this.XXSupplies.getDuration();
               this.tempVaPer = this.XXSupplies.getPercent();
               addEventListener(Event.ENTER_FRAME,this.chixuhuifu);
            }
            else if(xa == 2 && this.data.getLevel() >= lv)
            {
               tempValue = this.data.getStampSlot().getValueSlot1();
               pva = Math.round(this.use_mp_Max.getValue() * (per / 100));
               va = (va + pva) * (1 + tempValue);
               xv = this.mp.getValue() + va;
               max = this.use_mp_Max.getValue();
               if(xv < max)
               {
                  this.mp.setValue(xv);
               }
               else
               {
                  this.mp.setValue(max);
               }
               NewMC.Open("回蓝效果",this,0,0,0,va);
               tempCd = int(this.sel_objCD(name));
               this.All_ObjCDXX[tempCd][1] = 0;
               this.data.getSuppliesSlot().useSupplies(this.data.getBag(),num);
            }
         }
         this.刷新背包显示();
      }
      
      private function chixuhuifu(e:*) : *
      {
         var max:int = 0;
         var va_up:int = 0;
         var xv:int = 0;
         if(this.hp.getValue() <= 0)
         {
            removeEventListener(Event.ENTER_FRAME,this.chixuhuifu);
         }
         else
         {
            ++this.tempCXHF;
            max = this.use_hp_Max.getValue();
            va_up = Math.round(max * (this.tempVaPer / 100));
            xv = this.hp.getValue() + va_up;
            if(this.tempCXHF <= this.tempChiXuTime)
            {
               if(this.tempCXHF % 27 == 0)
               {
                  if(xv < max)
                  {
                     this.hp.setValue(xv);
                  }
                  else
                  {
                     this.hp.setValue(max);
                  }
                  NewMC.Open("回血效果",this,0,0,0,va_up);
               }
            }
            else
            {
               this.tempCXHF = 0;
               removeEventListener(Event.ENTER_FRAME,this.chixuhuifu);
            }
         }
      }
      
      public function CanSkill(skill_ID:String, lv:int) : Boolean
      {
         var tempMP:int = 0;
         var tempCd:int = 0;
         var getAllSuitSkill:Array = null;
         var i:int = 0;
         var arrXXX:Array = null;
         var s:Skill = SkillFactory.getSkillByTypeIAndLevel(skill_ID,lv);
         if(s != null)
         {
            tempMP = this.mp.getValue() - s.getMp() * (1 - this.data.getStampSlot().getValueSlot9());
            if(s.getMp() > 0)
            {
               arrXXX = this.data.getStampSlot().getValueSlot14();
               tempMP -= this.use_mp_Max.getValue() * arrXXX[0];
            }
            if(tempMP <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"魔法不足,无法发动技能");
               return false;
            }
            tempCd = int(this.selCD(skill_ID));
            if(tempCd == -1)
            {
               return false;
            }
            getAllSuitSkill = this.data.getEquipSlot().getAllSuitSkill();
            for(i in getAllSuitSkill)
            {
               if(getAllSuitSkill[i] == 57363)
               {
                  if(this.flagTempMP)
                  {
                     this.flagTempMP = false;
                     this.setInTimeCDMP();
                     this.AllSkillCDXX[tempCd][1] = 0;
                     return true;
                  }
               }
            }
            this.mp.setValue(tempMP);
            this.AllSkillCDXX[tempCd][1] = 0;
            if(this.playerJL)
            {
               this.playerJL.BD1017(tempCd);
            }
            return true;
         }
         return true;
      }
      
      public function CanUserObj(obj_ID:String) : Boolean
      {
         var tempCd:int = int(this.sel_objCD(obj_ID));
         if(tempCd == -1)
         {
            return false;
         }
         return true;
      }
      
      private function huiFuYinZang() : *
      {
         var x10:Number = NaN;
         --this.huiFuTime;
         if(this.huiFuTime == 0)
         {
            x10 = this.data.getStampSlot().getValueSlot10();
            if(x10 > 0)
            {
               this.HpUp(x10,2);
               TiaoShi.txtShow("恢复印章效果!" + x10);
            }
         }
      }
      
      public function huiFuYinZangXX() : *
      {
         if(this.huiFuTime < 0)
         {
            this.huiFuTime = 3 * 27;
         }
      }
      
      public function runPower(numX:Number = 0, numY:Number = 0, timeX:int = 0, str:String = "") : *
      {
         var i:int = 0;
         if(Boolean(this.playerCW2) && str != "宠物变身")
         {
            return;
         }
         if(numX == 0 && numY == 0 && timeX == 0)
         {
            return;
         }
         if(str == "跳")
         {
            for(i = this.runArr.length - 1; i >= 0; i--)
            {
               if(this.runArr[i][5] == "跳")
               {
                  this.runArr.splice(i,1);
               }
            }
         }
         if(timeX <= 3)
         {
            this.runArr[this.runArr.length] = [numX,numY,1,0,0,str];
            return;
         }
         var numXX:* = numX * this.parabola / timeX;
         var numYY:* = numY * this.parabola / timeX;
         var gravityX:Number = numX * (1 - this.parabola) / (timeX * timeX - timeX * (timeX - 1) / 2);
         var gravityY:Number = numY * (1 - this.parabola) / (timeX * timeX - timeX * (timeX - 1) / 2);
         this.runArr[this.runArr.length] = [numXX,numYY,timeX,gravityX,gravityY,str];
      }
      
      private function MoveData() : *
      {
         this.runX = this.runY = 0;
         this.heiAnJiNeng_width = 0;
         this.heiAnMove = false;
         for(var i:int = this.runArr.length - 1; i >= 0; i--)
         {
            if(this.runArr[i][2] > 0)
            {
               this.runX += this.runArr[i][0] + this.runArr[i][3] * this.runArr[i][2];
               this.runY -= this.runArr[i][1] + this.runArr[i][4] * this.runArr[i][2];
               --this.runArr[i][2];
               if(this.runArr[i][5] == "黑暗使徒时装技能")
               {
                  this.heiAnMove = true;
               }
            }
            else
            {
               this.runArr.splice(i,1);
            }
         }
         if(this.runY < 0)
         {
            this.jumpType = 1;
            this.gravityNum = 0;
         }
         else
         {
            this.jumpType = 2;
            if(this.gravity != 0)
            {
               this.gravityNum += 1;
            }
            this.runY += this.gravity * this.gravityNum;
         }
      }
      
      private function MoveRun() : *
      {
         var xxYN:Boolean = false;
         var playerX2:Boolean = false;
         var otherPlayer:Player = null;
         var i:int = 0;
         var AA:Boolean = false;
         var BB2:Boolean = false;
         var AA2:Boolean = false;
         var CC:Boolean = false;
         var AA3:Boolean = false;
         if(this.runX > 0)
         {
            xxYN = true;
         }
         var thisX:int = this.x;
         var xx:int = Math.abs(this.runX);
         var yy:int = Math.abs(this.runY);
         var forecast_x:int = this.x + Main.world.x;
         var forecast_y:int = this.y;
         var mapX:int = int(Main.world.x);
         if(!this.noMapHit)
         {
            for(i = yy; i > 0; i--)
            {
               if(this.jumpType == 1)
               {
                  AA = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 100,true));
                  if(!AA)
                  {
                     forecast_y--;
                  }
               }
               else if(this.jumpType == 2)
               {
                  BB2 = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 3,true));
                  AA2 = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 1,true));
                  CC = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y + 6,true));
                  if(BB2)
                  {
                     forecast_y -= 2;
                     this.jumping = 0;
                  }
                  else if(AA2)
                  {
                     this.runY = 0;
                     this.gravityNum = 0;
                     this.jumping = 0;
                  }
                  else if(CC)
                  {
                     forecast_y += 3;
                     this.jumping = 0;
                  }
                  else
                  {
                     forecast_y++;
                  }
               }
               else if(this.jumpType == 3)
               {
                  AA3 = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 5,true));
                  if(!AA3)
                  {
                     this.jumpType = 2;
                     break;
                  }
                  forecast_y += 2;
               }
            }
            this.y = forecast_y;
         }
         if(Main.player_2)
         {
            if(Main.player_1.hp.getValue() > 0 && Main.player_2.hp.getValue() > 0)
            {
               playerX2 = true;
            }
            else
            {
               playerX2 = false;
            }
            if(this == Main.player_1)
            {
               otherPlayer = Main.player_2;
            }
            else
            {
               otherPlayer = Main.player_1;
            }
         }
         for(var ii:int = xx; ii > 0; ii--)
         {
            if(xxYN && (this.noMapHit || !Main.world.MapData1.hitTestPoint(forecast_x + 20,forecast_y - 50,true)))
            {
               if(!playerX2)
               {
                  if(forecast_x > 520 && mapX > 940 - Main.world.stopX_2)
                  {
                     mapX--;
                  }
                  if(this.noMapHit)
                  {
                     forecast_x++;
                  }
                  else if(forecast_x - Main.world.x < Main.world.stopX_2)
                  {
                     forecast_x++;
                  }
               }
               else
               {
                  if(forecast_x > 520 && mapX > 940 - Main.world.stopX_2 && otherPlayer.x + mapX > 0)
                  {
                     mapX--;
                  }
                  if(this.noMapHit)
                  {
                     forecast_x++;
                  }
                  else if(forecast_x - Main.world.x < Main.world.stopX_2 && Math.abs(forecast_x - otherPlayer.x - mapX + 2) < 940)
                  {
                     forecast_x++;
                  }
               }
            }
            else if(!xxYN && (this.noMapHit || !Main.world.MapData1.hitTestPoint(forecast_x - 20,forecast_y - 50,true)))
            {
               if(!playerX2)
               {
                  if(forecast_x < 420 && mapX < -Main.world.stopX_1)
                  {
                     mapX++;
                  }
                  if(this.noMapHit)
                  {
                     forecast_x--;
                  }
                  else if(forecast_x - Main.world.x > Main.world.stopX_1)
                  {
                     forecast_x--;
                  }
               }
               else
               {
                  if(forecast_x < 420 && mapX < -Main.world.stopX_1 && otherPlayer.x + mapX < 940)
                  {
                     mapX++;
                  }
                  if(this.noMapHit)
                  {
                     forecast_x--;
                  }
                  else if(forecast_x - Main.world.x > Main.world.stopX_1 && Math.abs(forecast_x - otherPlayer.x - mapX - 2) < 940)
                  {
                     forecast_x--;
                  }
               }
            }
         }
         this.x = forecast_x - Main.world.x;
         Main.world.x = mapX;
         if(this.heiAnMove)
         {
            thisX = this.x - thisX;
            this.heiAnJiNeng_width = thisX;
         }
      }
      
      public function getKeyStatus(str:*, type:int = 1, traceX:Boolean = false) : Boolean
      {
         var i:int = 0;
         var bool:Boolean = false;
         var Arr:Array = null;
         var j:int = 0;
         if(str is String)
         {
            for(i = 0; i < this.data._keyArr.length; i++)
            {
               if(this.KeyArrStr[i] == str)
               {
                  return Boolean(BasicKey.getKeyState(this.data._keyArr[i],type));
               }
            }
            return false;
         }
         if(str is Array)
         {
            Arr = new Array();
            for(i = 0; i < (str as Array).length; i++)
            {
               for(j = 0; j < this.data._keyArr.length; j++)
               {
                  if(this.KeyArrStr[j] == str[i])
                  {
                     Arr[Arr.length] = this.data._keyArr[j];
                  }
               }
            }
            return BasicKey.getTargetState(Arr);
         }
         return false;
      }
      
      public function newSkin() : *
      {
         var _who:uint = 1;
         var _type:uint = 2;
         if(this == Main.player_2)
         {
            _who = 2;
            _type = 3;
         }
         NewLoad.Loading(_type,_who);
         Skill_guangQiu.addToPlayer(this);
      }
      
      public function newSkin_LoadEnd() : *
      {
         if(!this.backMC)
         {
            this.backMC = new MovieClip();
            this.addChild(this.backMC);
         }
         this.newZhuangBei3();
         if(this.skin)
         {
            this.skin.gotoAndStop("站");
            this.skin.parent.removeChild(this.skin);
            this.skin = null;
         }
         this.OpenJL();
         this.AddSkin();
         this.newZhuangBei();
         this.HeadXX();
         this.newWuQi();
         this.OpenCW();
         if(SixOne_Interface.state2021.getValue() == 1 || SixOne_Interface.state2021.getValue() == 2)
         {
            this.OpenYS();
         }
         this.LoadPlayerLvData();
         this.getRL(this.RL);
         addChild(this.cengHao_mc);
      }
      
      private function newZhuangBei3() : *
      {
         if(this.skin_Z3)
         {
            this.skin_Z3.parent.removeChild(this.skin_Z3);
            this.skin_Z3 = null;
         }
         this.AddSkin_Z3();
      }
      
      public function newZhuangBei() : *
      {
         if(this.skin_Z2)
         {
            this.skin_Z2.parent.removeChild(this.skin_Z2);
            this.skin_Z2 = null;
         }
         if(this.skin_Z)
         {
            this.skin_Z.parent.removeChild(this.skin_Z);
            this.skin_Z = null;
         }
         this.AddSkin_Z();
      }
      
      public function newWuQi() : *
      {
         if(this.skin_W)
         {
            this.skin_W.parent.removeChild(this.skin_W);
            this.skin_W = null;
         }
         this.AddSkin_W();
      }
      
      private function AddSkin() : *
      {
         var skinNumX:int = int(this.data.skinArr[this.data.skinNum]);
         var classRef:Class = PlayerMcArr[skinNumX].getClass("src.Skin.Skin_player" + skinNumX) as Class;
         this.skin = new classRef();
         this.skin.skinNum = skinNumX;
         this.skin.Xml = Skin.PlayerXml[skinNumX];
         this.skin.playX = this;
         addChild(this.skin);
         this.skin.mouseChildren = this.skin.mouseEnabled = false;
         this.skin_Z0 = this.skin;
      }
      
      private function AddSkin_Z3() : *
      {
         var skinNumX:int = 0;
         var className:* = undefined;
         var numID:* = undefined;
         var loadName:* = undefined;
         var classRef:Class = null;
         if(this.data.getEquipSlot().getEquipFromSlot(7) != null)
         {
            skinNumX = int(this.data.skinArr[this.data.skinNum]);
            className = this.data.getEquipSlot().getEquipFromSlot(7).getClassName();
            numID = this.data.getEquipSlot().getEquipFromSlot(7).getClassName3();
            loadName = this.data.getEquipSlot().getEquipFromSlot(7).getClassName4();
            classRef = NewLoad.zhuangBeiSkin[skinNumX][numID].getClass(className) as Class;
            this.skin_Z3 = new classRef();
            addChild(this.skin_Z3);
            if(this.skin_Z3)
            {
               if(this.skin_Z3_V && this.data.getEquipSlot().getEquipFromSlot(7).getRemainingTime() > 0)
               {
                  this.skin_Z3.visible = true;
                  this.paiHangShow[4] = [numID,loadName,className];
               }
               else
               {
                  this.skin_Z3.visible = false;
               }
            }
         }
      }
      
      private function AddSkin_Z() : *
      {
         var className:String = null;
         var numID:int = 0;
         var loadName:String = null;
         var skinNumX:int = int(this.data.skinArr[this.data.skinNum]);
         this.paiHangShow[0] = skinNumX;
         var xxx:uint = 1;
         if(Main.water.getValue() != 1)
         {
            xxx = 9;
         }
         if(this.data.getEquipSlot().getEquipFromSlot(xxx) == null)
         {
            className = "甲白1";
            numID = 1;
            loadName = "1_v2";
         }
         else
         {
            className = this.data.getEquipSlot().getEquipFromSlot(xxx).getClassName();
            numID = this.data.getEquipSlot().getEquipFromSlot(xxx).getClassName3();
            loadName = this.data.getEquipSlot().getEquipFromSlot(xxx).getClassName4();
         }
         var classRef:Class = NewLoad.zhuangBeiSkin[skinNumX][numID].getClass(className) as Class;
         this.skin_Z = new classRef();
         addChild(this.skin_Z);
         this.skin_Z.mouseChildren = this.skin_Z.mouseEnabled = false;
         this.paiHangShow[3] = [numID,loadName,className];
         var tempShow:Array = new Array();
         if(this.data.getEquipSlot().getEquipFromSlot(6) != null)
         {
            className = this.data.getEquipSlot().getEquipFromSlot(6).getClassName();
            numID = this.data.getEquipSlot().getEquipFromSlot(6).getClassName3();
            loadName = this.data.getEquipSlot().getEquipFromSlot(6).getClassName4();
            classRef = NewLoad.zhuangBeiSkin[skinNumX][numID].getClass(className) as Class;
            this.skin_Z2 = new classRef();
            addChild(this.skin_Z2);
            this.skin_Z.visible = false;
            tempShow = [numID,loadName,className];
         }
         this.skin_Z0.visible = true;
         if(this.skin_Z2 && this.skin_Z2_V && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() > 0)
         {
            this.skin_Z2.visible = true;
            this.skin_Z.visible = false;
            this.skin_Z2.mouseChildren = this.skin_Z2.mouseEnabled = false;
            if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 498)
            {
               this.skin_Z0.visible = false;
            }
            this.paiHangShow[3] = tempShow;
         }
         else if(!this.skin_Z2_V || this.skin_Z2 && this.data.getEquipSlot().getEquipFromSlot(6).getRemainingTime() <= 0)
         {
            if(this.skin_Z2)
            {
               this.skin_Z2.visible = false;
            }
            this.skin_Z.visible = true;
         }
      }
      
      private function AddSkin_W() : *
      {
         var name:String = null;
         var skinNumX:int = this.data.skinNum;
         if(skinNumX == 0)
         {
            name = this.data.getEquipSlot().getEquipFromSlot(2).getClassName();
         }
         else if(skinNumX == 1)
         {
            name = this.data.getEquipSlot().getEquipFromSlot(5).getClassName();
         }
         var classRef:Class = Skin_WuQi.PlayerMcArr[this.data.skinArr[this.data.skinNum]].getClass(name) as Class;
         this.paiHangShow[2] = name;
         this.skin_W = new classRef();
         addChild(this.skin_W);
         this.skin_W.mouseChildren = this.skin_W.mouseEnabled = false;
      }
      
      private function SkinPlay(str:String, timeXX:int = 0) : *
      {
         if(this.skin)
         {
            this.skin.GoTo(str,timeXX);
         }
      }
      
      public function getRL(_RL:Boolean) : *
      {
         this.RL = _RL;
         if(_RL)
         {
            this.skin.scaleX = this.skin_Z.scaleX = this.skin_W.scaleX = -1;
            if(this.skin_Z2)
            {
               this.skin_Z2.scaleX = -1;
            }
            if(this.skin_Z3)
            {
               this.skin_Z3.scaleX = -1;
            }
            if(this.cengHao_mc.frame == 363)
            {
               this.cengHao_mc.scaleX = 1;
            }
         }
         else if(!_RL)
         {
            this.skin.scaleX = this.skin_Z.scaleX = this.skin_W.scaleX = 1;
            if(this.skin_Z2)
            {
               this.skin_Z2.scaleX = 1;
            }
            if(this.skin_Z3)
            {
               this.skin_Z3.scaleX = 1;
            }
            if(this.cengHao_mc.frame == 363)
            {
               this.cengHao_mc.scaleX = -1;
            }
         }
      }
      
      private function MoveFun() : *
      {
         if(this.getKeyStatus("左",3))
         {
            this.runPower(-this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
         }
         else if(this.getKeyStatus("右",3))
         {
            this.runPower(this.walk_power.getValue() * 1.5 * this.walk_power2,0,1);
         }
         else if(this.getKeyStatus("左",2))
         {
            this.runPower(-this.walk_power.getValue() * this.walk_power2,0,1);
         }
         else if(this.getKeyStatus("右",2))
         {
            this.runPower(this.walk_power.getValue() * this.walk_power2,0,1);
         }
      }
      
      public function OpenCW() : *
      {
         if(Boolean(this.data.playerCW_Data) && this.data.playerCW_Data.getType() == 1)
         {
            this.data.playerCW_Data = null;
            return;
         }
         if(this.data.playerCW_Data && !this.playerCW && !this.playerCW2 && this.data.playerCW_Data.getFood() > 0)
         {
            trace("宠物 >>>>>>>>>>>>>>>>>>>>>>>>>>>>>> OpenCW");
            this.NewCW(this.data.playerCW_Data);
         }
      }
      
      public function NewCW(data:Pet) : *
      {
         if(this.playerCW)
         {
            this.playerCW.Close();
         }
         this.data.playerCW_Data = data;
         this.playerCW = new ChongWu(data,this);
      }
      
      public function OpenJL() : *
      {
         if(this.playerJL)
         {
            this.playerJL.JingLingOPEN();
            if(Main.gameNum.getValue() == 0)
            {
               this.playerJL.jiNengZD_num = 0;
            }
         }
         else if(Boolean(this.data.playerJL_Data) && this.visible)
         {
            this.NewJL(this.data.playerJL_Data);
         }
      }
      
      public function NewJL(data:Elves) : *
      {
         if(this.playerJL)
         {
            this.playerJL.Close();
         }
         this.data.playerJL_Data = data;
         this.playerJL = new JingLing(data,this);
      }
      
      public function HpUp(xxx:Number, type:int = 1) : *
      {
         var hpUpNum:int = 0;
         if(this.hp.getValue() <= 0)
         {
            return;
         }
         var max:int = this.use_hp_Max.getValue();
         if(type == 1)
         {
            hpUpNum = this.hp.getValue() + xxx;
         }
         else if(type == 2)
         {
            xxx = max * xxx / 100;
            hpUpNum = this.hp.getValue() + xxx;
         }
         else if(type == 3)
         {
            this.hp.setValue(xxx);
         }
         else
         {
            hpUpNum = xxx = 0;
         }
         if(hpUpNum < max)
         {
            this.hp.setValue(hpUpNum);
         }
         else
         {
            this.hp.setValue(max);
         }
         NewMC.Open("回血效果",this,0,0,0,xxx);
      }
      
      public function MpUp(xxx:Number, type:int = 1) : *
      {
         var hpUpNum:int = 0;
         if(this.hp.getValue() <= 0 || xxx <= 0)
         {
            return;
         }
         var max:int = this.use_mp_Max.getValue();
         if(type == 1)
         {
            mpUpNum = this.mp.getValue() + xxx;
         }
         else if(type == 2)
         {
            xxx = max * xxx / 100;
            mpUpNum = this.mp.getValue() + xxx;
         }
         else
         {
            mpUpNum = xxx = 0;
         }
         if(mpUpNum < max)
         {
            this.mp.setValue(mpUpNum);
         }
         else
         {
            this.mp.setValue(max);
         }
         NewMC.Open("回蓝效果",this,0,0,0,xxx);
      }
      
      public function OpenYS() : *
      {
         if(SixOne_Interface.booltemp)
         {
            if(boolYS)
            {
               if(this.playerYS)
               {
                  this.playerYS.close();
               }
               this.playerYS = new YongShi61(this);
            }
         }
      }
      
      public function CloseYS() : *
      {
         if(this.playerYS)
         {
            this.playerYS.close();
         }
      }
      
      private function chibangFeiXing() : *
      {
         if(this.data.getEquipSlot().getEquipFromSlot(7))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(7).getFrame() == 424)
            {
               ++this.feixingTime;
               if(this.gravity == 0 && this.feixingBool == false && this.jumping > 0)
               {
                  this.speedTemp.setValue(this.speedTemp.getValue() + 20);
                  this.feixingBool = true;
               }
               if(this.gravity > 0)
               {
                  this.rotation = 0;
                  this.speedTemp.setValue(0);
                  this.feixingBool = false;
               }
               if(this.feixingTime >= 3)
               {
                  if(this.gravity == 0 && this.speedTemp.getValue() > 0)
                  {
                     this.feixingTime = 0;
                     this.speedTemp.setValue(this.speedTemp.getValue() - 1);
                  }
               }
            }
         }
      }
      
      private function shizhuangPenShe() : *
      {
         if(this.data.getEquipSlot().getEquipFromSlot(6))
         {
            if(this.data.getEquipSlot().getEquipFromSlot(6).getFrame() == 423)
            {
               if(this.feixingBool3 == true && this.feixingBool2 == false)
               {
                  if(Main.gameNum.getValue() == 0)
                  {
                     return;
                  }
                  this.skin_W.addChild(new this.class_ps());
                  this.feixingBool2 = true;
               }
               if(this.feixingBool3 == false)
               {
                  for(i in Fly.All)
                  {
                     if((Fly.All[i] as Fly)._name == "高能喷射" && (Fly.All[i] as Fly).who == this)
                     {
                        (Fly.All[i] as Fly).life = 0;
                     }
                  }
                  this.feixingBool2 = false;
               }
            }
         }
      }
      
      public function nineBuffTime() : *
      {
         ++this.nbTime;
         if(this.data.buffNine[0] > 0)
         {
            if(this.nbTime % 27 == 0)
            {
               if(this.data.buffNine[9] > 0)
               {
                  --this.data.buffNine[9];
                  if(this.data.buffNine[9] == 0)
                  {
                     this.data.buffNine[8] = 7200;
                  }
                  if(this.data.reBorn > 0)
                  {
                     --this.data.reBorn;
                  }
               }
               if(this.data.buffNine[8] > 0)
               {
                  --this.data.buffNine[8];
                  if(this.data.buffNine[8] == 0)
                  {
                     this.data.buffNine[7] = 7200;
                  }
               }
               else if(this.data.buffNine[7] > 0)
               {
                  --this.data.buffNine[7];
                  if(this.data.buffNine[7] == 0)
                  {
                     this.data.buffNine[6] = 7200;
                  }
               }
               else if(this.data.buffNine[6] > 0)
               {
                  --this.data.buffNine[6];
                  if(this.data.buffNine[6] == 0)
                  {
                     this.data.buffNine[5] = 7200;
                  }
               }
               else if(this.data.buffNine[5] > 0)
               {
                  --this.data.buffNine[5];
                  if(this.data.buffNine[5] == 0)
                  {
                     this.data.buffNine[4] = 7200;
                  }
               }
               else if(this.data.buffNine[4] > 0)
               {
                  --this.data.buffNine[4];
                  if(this.data.buffNine[4] == 0)
                  {
                     this.data.buffNine[3] = 10800;
                  }
               }
               else if(this.data.buffNine[3] > 0)
               {
                  --this.data.buffNine[3];
                  if(this.data.buffNine[3] == 0)
                  {
                     this.data.buffNine[2] = 10800;
                  }
               }
               else if(this.data.buffNine[2] > 0)
               {
                  --this.data.buffNine[2];
                  if(this.data.buffNine[2] == 0)
                  {
                     this.data.buffNine[1] = 10800;
                  }
               }
               else if(this.data.buffNine[1] > 0)
               {
                  --this.data.buffNine[1];
                  if(this.data.buffNine[1] == 0)
                  {
                     this.data.buffNine[0] = 10800;
                  }
               }
               else if(this.data.buffNine[0] > 0)
               {
                  --this.data.buffNine[0];
               }
            }
         }
      }
   }
}

