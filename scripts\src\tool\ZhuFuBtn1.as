package src.tool
{
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class ZhuFuBtn1 extends SimpleButton
   {
      
      public function ZhuFuBtn1()
      {
         super();
         addEventListener(MouseEvent.CLICK,this.onCLICK);
      }
      
      private function onCLICK(e:*) : *
      {
         var xx:int = 0;
         if(Main.gameNum.getValue() == 18)
         {
            xx = 4;
         }
         else if(Main.gameNum.getValue() == 19)
         {
            xx = 3;
         }
         else if(Main.gameNum.getValue() == 20)
         {
            xx = 1;
         }
         else if(Main.gameNum.getValue() == 21)
         {
            xx = 2;
         }
         else if(Main.gameNum.getValue() == 22)
         {
            xx = 5;
         }
         else if(Main.gameNum.getValue() == 23)
         {
            xx = 8;
         }
         else if(Main.gameNum.getValue() == 24)
         {
            xx = 4;
         }
         else if(Main.gameNum.getValue() == 25)
         {
            xx = 3;
         }
         else if(Main.gameNum.getValue() == 26)
         {
            xx = 1;
         }
         else if(Main.gameNum.getValue() == 27)
         {
            xx = 2;
         }
         else if(Main.gameNum.getValue() == 28)
         {
            xx = 5;
         }
         else if(Main.gameNum.getValue() == 29)
         {
            xx = 8;
         }
         JinHuaPanel.open(true,Main.gameNum.getValue() - 17,xx);
      }
   }
}

