package com.hotpoint.braveManIII.models.plan
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.plan.*;
   
   public class Plan
   {
      
      private var _id:VT;
      
      private var _state:VT = VT.createVT(0);
      
      public function Plan()
      {
         super();
      }
      
      public static function creatPlan(id:*) : Plan
      {
         var ppp:Plan = new Plan();
         ppp._id = VT.createVT(id);
         return ppp;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(value:VT) : void
      {
         this._id = value;
      }
      
      public function get state() : VT
      {
         return this._state;
      }
      
      public function set state(value:VT) : void
      {
         this._state = value;
      }
      
      public function getGroupId() : Number
      {
         return PlanFactory.findGroupId(this._id.getValue());
      }
      
      public function getGroup() : Number
      {
         return PlanFactory.findXiaoZu(this._id.getValue());
      }
      
      public function getIntroduction() : String
      {
         return PlanFactory.findIntroduction(this._id.getValue());
      }
      
      public function getRewardType_1() : Number
      {
         return PlanFactory.findRewardType_1(this._id.getValue());
      }
      
      public function getRewardType_2() : Number
      {
         return PlanFactory.findRewardType_2(this._id.getValue());
      }
      
      public function getReward_1() : Number
      {
         return PlanFactory.findReward_1(this._id.getValue());
      }
      
      public function getReward_2() : Number
      {
         return PlanFactory.findReward_2(this._id.getValue());
      }
      
      public function getFrame_1() : Number
      {
         return PlanFactory.findFrame_1(this._id.getValue());
      }
      
      public function getFrame_2() : Number
      {
         return PlanFactory.findFrame_2(this._id.getValue());
      }
      
      public function getCount_1() : Number
      {
         return PlanFactory.findCount_1(this._id.getValue());
      }
      
      public function getCount_2() : Number
      {
         return PlanFactory.findCount_2(this._id.getValue());
      }
      
      public function setState(value:Number) : void
      {
         this._state.setValue(value);
      }
      
      public function getState() : Number
      {
         return this._state.getValue();
      }
   }
}

