package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4203")]
   public class ChongZhi_Interface extends MovieClip
   {
      
      public static var _this:ChongZhi_Interface;
      
      public static var varXX:int = 19;
      
      public static var type:int = 1;
      
      public static var overTime:int = 20231203;
      
      public static var time0:String = "2023-11-27|10:00:01";
      
      public static var time1:String = "2023-12-03|23:59:59";
      
      public static var timeStr:String = "活动时间: 11月27日-12月3日";
      
      public static var lingQuTime:int = 0;
      
      public static var HDpointNUM:VT = VT.createVT(-1);
      
      public static var openYn:Boolean = false;
      
      public static var selYN:Boolean = false;
      
      public var XX_mc:MovieClip;
      
      public var _txt:TextField;
      
      public var close_btn:SimpleButton;
      
      public var go_btn:SimpleButton;
      
      public var lingqu_btn:SimpleButton;
      
      public var skin:MovieClip;
      
      public function ChongZhi_Interface()
      {
         super();
         _this = this;
         var mcX:Class = NewLoad.chongZhiData.getClass("mc1") as Class;
         this.skin = new mcX();
         _this.XX_mc.addChild(this.skin);
         _this.XX_mc.x = 0;
         _this.XX_mc.y = 0;
         addEventListener(Event.ENTER_FRAME,onENTER_FRAME);
      }
      
      public static function Open() : *
      {
         if(!_this)
         {
            new ChongZhi_Interface();
         }
         _this.lingqu_btn.addEventListener(MouseEvent.CLICK,lingQuFun);
         _this.go_btn.addEventListener(MouseEvent.CLICK,OpenWEB);
         _this.close_btn.addEventListener(MouseEvent.CLICK,Close);
         _this.addEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.x = _this.y = 0;
         _this.skin.gotoAndStop(type);
         _this.skin.time_txt.text = timeStr;
         _this.lingqu_btn.visible = false;
         _this.go_btn.visible = true;
         if(!selYN && lingQuTime < overTime)
         {
            Api_4399_All.GetTotalRecharged(17);
            selYN = true;
         }
         Main._stage.addChild(_this);
         Show();
      }
      
      public static function Close(e:* = null) : *
      {
         if(!_this)
         {
            new ChongZhi_Interface();
         }
         _this.lingqu_btn.removeEventListener(MouseEvent.CLICK,lingQuFun);
         _this.go_btn.removeEventListener(MouseEvent.CLICK,OpenWEB);
         _this.close_btn.removeEventListener(MouseEvent.CLICK,Close);
         _this.removeEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.x = _this.y = -5000;
      }
      
      public static function onENTER_FRAME(e:*) : *
      {
         if(!openYn && Main.gameNum.getValue() == 0)
         {
            openYn = true;
            _this.x = _this.y = 0;
         }
         if(Main.gameNum.getValue() != 0)
         {
            _this.x = _this.y = -5000;
         }
      }
      
      public static function lingQuFun(e:* = null) : *
      {
         var strXXX:Array = null;
         var objArr:Array = null;
         if(Main.player1.getBag().backOtherBagNum() >= 1)
         {
            strXXX = [0,"获得\'雷麟龙\'宠物蛋一个, 已放入背包!","获得\'驭炎龙\'宠物蛋一个, 已放入背包!","获得\'欺霜龙\'宠物蛋一个, 已放入背包!"];
            objArr = [0,63280,63291,63339];
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(objArr[type]));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,strXXX[type]);
            lingQuTime = overTime;
            Show();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,请整理背包!");
         }
      }
      
      public static function Show() : *
      {
         if(!_this)
         {
            new ChongZhi_Interface();
         }
         _this.lingqu_btn.visible = false;
         _this.go_btn.visible = true;
         _this.lingqu_btn.x = _this.lingqu_btn.y = -5000;
         if(HDpointNUM.getValue() > 299 && lingQuTime < overTime)
         {
            _this.lingqu_btn.visible = true;
            _this.lingqu_btn.x = 420;
            _this.lingqu_btn.y = 408;
            _this.go_btn.visible = false;
         }
      }
      
      public static function OpenWEB(e:*) : *
      {
         Main.ChongZhi();
         _this.x = _this.y = -5000;
      }
   }
}

