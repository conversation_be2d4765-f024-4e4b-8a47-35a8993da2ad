package src
{
   import com.hotpoint.braveManIII.models.container.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol6583")]
   public class NewDoor extends MovieClip
   {
      
      public static var bool_1:* = true;
      
      public static var bool_2:* = true;
      
      public static var bool_3:* = true;
      
      public static var bool_4:* = true;
      
      public static var bool_0:* = false;
      
      private static var arr:* = [263,515,728,947];
      
      public var hit:MovieClip;
      
      private var time:int = 0;
      
      public function NewDoor()
      {
         super();
         if(this.name == "t1")
         {
            if(bool_1)
            {
               this.Open();
            }
            else
            {
               this.Close();
            }
         }
         if(this.name == "t2")
         {
            if(bool_2)
            {
               this.Open();
            }
            else
            {
               this.Close();
            }
         }
         if(this.name == "t3")
         {
            if(bool_3)
            {
               this.Open();
            }
            else
            {
               this.Close();
            }
         }
         if(this.name == "t4")
         {
            if(bool_4)
            {
               this.Open();
            }
            else
            {
               this.Close();
            }
         }
         if(this.name == "bk2" || this.name == "bk3" || this.name == "bk4" || this.name == "bk5")
         {
            this.Close();
         }
         if(this.name == "t1")
         {
            this.x = arr[0];
            this.y = 547;
         }
         else if(this.name == "t2")
         {
            this.x = arr[1];
            this.y = 547;
         }
         else if(this.name == "t3")
         {
            this.x = arr[2];
            this.y = 547;
         }
         else if(this.name == "t4")
         {
            this.x = arr[3];
            this.y = 547;
         }
      }
      
      private static function randomsort(a:*, b:*) : *
      {
         return Math.random() > 0.5 ? -1 : 1;
      }
      
      public static function setRdm() : *
      {
         arr.sort(randomsort);
      }
      
      public function Open(t:int = 0) : *
      {
         if(!visible)
         {
            this.time = t;
         }
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function Close() : *
      {
         this.visible = false;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         var p:Player = null;
         if(this.parent != Main.world)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            return;
         }
         if(this.time > 0)
         {
            --this.time;
            return;
         }
         this.visible = true;
         for(i2 = 0; i2 < Player.All.length; ++i2)
         {
            p = Player.All[i2];
            if(visible && p.hit && this.hit.hitTestObject(p.hit))
            {
               if(p.getKeyStatus("上",2) && this.parent == Main.world)
               {
                  removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
                  this.GoWhere();
               }
            }
         }
      }
      
      public function GoWhere() : *
      {
         var win:WinShow = null;
         if(this.name == "t1")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(2);
            Main._this.Loading();
         }
         else if(this.name == "t2")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(3);
            Main._this.Loading();
         }
         else if(this.name == "t3")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(4);
            Main._this.Loading();
         }
         else if(this.name == "t4")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(5);
            Main._this.Loading();
         }
         else if(this.name == "bk2")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(1);
            Main._this.Loading();
         }
         else if(this.name == "bk3")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(1);
            Main._this.Loading();
         }
         else if(this.name == "bk4")
         {
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(1);
            Main._this.Loading();
         }
         else if(this.name == "bk5")
         {
            win = new WinShow();
         }
      }
   }
}

