package com.hotpoint.braveManIII.views.storagePanel
{
   import com.hotpoint.braveManIII.events.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.other.Otherobj;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.models.supplies.Supplies;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.ui.*;
   import src.*;
   
   public class StoragePanel extends MovieClip
   {
      
      public static var storage:Storage;
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var dragObj:MovieClip;
      
      public static var storagePanel:MovieClip;
      
      private static var pointx:Number;
      
      private static var pointy:Number;
      
      private static var oldNum:Number;
      
      public static var sp:StoragePanel;
      
      public static var myplayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      public static var returnx:Number = 0;
      
      public static var returny:Number = 0;
      
      public static var isDown:Boolean = false;
      
      private static var itemsType:Number = 1;
      
      private static var myMenu:ContextMenu = new ContextMenu();
      
      private static var yeshu:Number = 0;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "NewCangku_v892.swf";
      
      public function StoragePanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!storagePanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = storagePanel.getChildIndex(storagePanel["s1_" + i]);
            mm.x = storagePanel["s1_" + i].x;
            mm.y = storagePanel["s1_" + i].y;
            mm.name = "s1_" + i;
            storagePanel.removeChild(storagePanel["s1_" + i]);
            storagePanel["s1_" + i] = mm;
            storagePanel.addChild(mm);
            storagePanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 35; i++)
         {
            mm = new Shop_picNEW();
            num = storagePanel.getChildIndex(storagePanel["kc_" + i]);
            mm.x = storagePanel["kc_" + i].x;
            mm.y = storagePanel["kc_" + i].y;
            mm.name = "kc_" + i;
            storagePanel.removeChild(storagePanel["kc_" + i]);
            storagePanel["kc_" + i] = mm;
            storagePanel.addChild(mm);
            storagePanel.setChildIndex(mm,num);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("StorageShow") as Class;
         storagePanel = new classRef();
         sp.addChild(storagePanel);
         InitIcon();
         if(OpenYN)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         if(!storage)
         {
            storage = Storage.createStorage();
         }
         sp = new StoragePanel();
         LoadSkin();
         Main._stage.addChild(sp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         if(!storage)
         {
            storage = Storage.createStorage();
         }
         sp = new StoragePanel();
         Main._stage.addChild(sp);
         OpenYN = false;
      }
      
      public static function open() : void
      {
         Main.allClosePanel();
         if(storagePanel)
         {
            Main.stopXX = true;
            sp.x = 0;
            sp.y = 0;
            myplayer = Main.player1;
            myplayer.getBag().cheatOther();
            myplayer.getBag().cheatGem();
            myplayer.getBag().cheatEquip();
            storage.cheatTesting();
            storage.cheatGem();
            storage.cheatOther();
            addListenerP1();
            if(Main.player2)
            {
               Main.player2.getBag().cheatEquip();
               Main.player2.getBag().cheatOther();
               Main.player2.getBag().cheatGem();
            }
            sp.visible = true;
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(storagePanel)
         {
            sp.visible = false;
            Main.stopXX = false;
            removeListenerP1();
         }
         else
         {
            InitClose();
         }
      }
      
      private static function CloseCK(e:MouseEvent) : void
      {
         close();
      }
      
      public static function addListenerP1() : *
      {
         var i:uint = 0;
         if(Main.player2)
         {
            storagePanel["bag1"].visible = false;
            storagePanel["bag2"].visible = true;
            storagePanel["back_mc"].visible = true;
            storagePanel["bag1"].addEventListener(MouseEvent.CLICK,to1P);
            storagePanel["bag2"].addEventListener(MouseEvent.CLICK,to2P);
            if(Main.player_1.visible == false)
            {
               storagePanel["bag1"].visible = false;
               storagePanel["bag2"].visible = false;
               storagePanel["back_mc"].visible = false;
               myplayer = Main.player2;
            }
            if(Main.player_2.visible == false)
            {
               storagePanel["bag1"].visible = false;
               storagePanel["bag2"].visible = false;
               storagePanel["back_mc"].visible = false;
               myplayer = Main.player1;
            }
         }
         else
         {
            storagePanel["bag1"].visible = false;
            storagePanel["bag2"].visible = false;
            storagePanel["back_mc"].visible = false;
         }
         storagePanel["closeCK"].addEventListener(MouseEvent.CLICK,CloseCK);
         for(i = 0; i < 24; i++)
         {
            storagePanel["s1_" + i].mouseChildren = false;
            storagePanel["s1_" + i]["t_txt"].visible = false;
            storagePanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            storagePanel["s1_" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            storagePanel["s1_" + i].addEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            storagePanel["s1_" + i].addEventListener(MouseEvent.CLICK,putIn);
         }
         for(i = 0; i < 35; i++)
         {
            storagePanel["kc_" + i].mouseChildren = false;
            storagePanel["kc_" + i]["t_txt"].visible = false;
            storagePanel["kc_" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            storagePanel["kc_" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            storagePanel["kc_" + i].addEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            storagePanel["kc_" + i].addEventListener(MouseEvent.CLICK,takeOut);
         }
         for(i = 0; i < 6; i++)
         {
            storagePanel["bagLock" + i].visible = false;
         }
         storagePanel["yeshu_1"].addEventListener(MouseEvent.CLICK,oneGo);
         storagePanel["yeshu_2"].addEventListener(MouseEvent.CLICK,twoGo);
         storagePanel["yeshu_1"].stop();
         storagePanel["yeshu_2"].stop();
         storagePanel.addEventListener(BtnEvent.DO_CHANGE,ckListen);
         storagePanel.addEventListener(Event.DEACTIVATE,loseFocus);
         myMenu.addEventListener(ContextMenuEvent.MENU_SELECT,loseFocus);
         Main._stage.addEventListener(Event.MOUSE_LEAVE,loseFocus);
         Main._stage.addEventListener(MouseEvent.MOUSE_UP,loseFocus);
         storagePanel.contextMenu = myMenu;
         equipShow();
         informationShow();
         allFalse();
         storagePanel["mck_1"].isClick = true;
         itemsType = 1;
      }
      
      public static function removeListenerP1() : *
      {
         if(Main.player2)
         {
            storagePanel["bag1"].removeEventListener(MouseEvent.CLICK,to1P);
            storagePanel["bag2"].removeEventListener(MouseEvent.CLICK,to2P);
         }
         storagePanel["closeCK"].removeEventListener(MouseEvent.CLICK,CloseCK);
         for(var i:uint = 0; i < 24; i++)
         {
            storagePanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            storagePanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            storagePanel["s1_" + i].removeEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            storagePanel["s1_" + i].removeEventListener(MouseEvent.CLICK,putIn);
         }
         for(i = 0; i < 35; i++)
         {
            storagePanel["kc_" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            storagePanel["kc_" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            storagePanel["kc_" + i].removeEventListener(MouseEvent.MOUSE_DOWN,DownHandler);
            storagePanel["kc_" + i].removeEventListener(MouseEvent.CLICK,takeOut);
         }
         storagePanel["yeshu_1"].removeEventListener(MouseEvent.CLICK,oneGo);
         storagePanel["yeshu_2"].removeEventListener(MouseEvent.CLICK,twoGo);
         storagePanel.removeEventListener(Event.DEACTIVATE,loseFocus);
         myMenu.removeEventListener(ContextMenuEvent.MENU_SELECT,loseFocus);
         Main._stage.removeEventListener(Event.MOUSE_LEAVE,loseFocus);
         Main._stage.removeEventListener(MouseEvent.MOUSE_UP,loseFocus);
         storagePanel.contextMenu = myMenu;
         storagePanel.removeEventListener(BtnEvent.DO_CHANGE,ckListen);
      }
      
      private static function oneGo(e:*) : *
      {
         yeshu = 0;
         if(itemsType == 1)
         {
            equipShow();
         }
         else if(itemsType == 2)
         {
            suppliesShow();
         }
         else if(itemsType == 3)
         {
            gemShow();
         }
         else if(itemsType == 4)
         {
            otherobjShow();
         }
      }
      
      private static function twoGo(e:*) : *
      {
         yeshu = 1;
         if(itemsType == 1)
         {
            equipShow();
         }
         else if(itemsType == 2)
         {
            suppliesShow();
         }
         else if(itemsType == 3)
         {
            gemShow();
         }
         else if(itemsType == 4)
         {
            otherobjShow();
         }
      }
      
      private static function loseFocus(e:Event = null) : *
      {
         if(dragObj)
         {
            UpHandler();
         }
      }
      
      private static function informationShow() : *
      {
         storagePanel["gold1"].text = myplayer.getGold();
         storagePanel["kill_txt"].text = myplayer.getKillPoint();
      }
      
      private static function to1P(e:*) : *
      {
         myplayer = Main.player1;
         storagePanel["bag1"].visible = false;
         storagePanel["bag2"].visible = true;
         if(itemsType == 1)
         {
            equipShow();
         }
         else if(itemsType == 2)
         {
            suppliesShow();
         }
         else if(itemsType == 3)
         {
            gemShow();
         }
         else if(itemsType == 4)
         {
            otherobjShow();
         }
      }
      
      private static function to2P(e:*) : *
      {
         myplayer = Main.player2;
         storagePanel["bag1"].visible = true;
         storagePanel["bag2"].visible = false;
         if(itemsType == 1)
         {
            equipShow();
         }
         else if(itemsType == 2)
         {
            suppliesShow();
         }
         else if(itemsType == 3)
         {
            gemShow();
         }
         else if(itemsType == 4)
         {
            otherobjShow();
         }
      }
      
      private static function ckListen(e:BtnEvent) : void
      {
         var btn:MovieClip = e.target as MovieClip;
         var btnNum:uint = uint(btn.name.substr(4,1));
         switch(btnNum)
         {
            case 1:
               allFalse();
               storagePanel["mck_1"].isClick = true;
               equipShow();
               itemsType = 1;
               break;
            case 2:
               allFalse();
               storagePanel["mck_2"].isClick = true;
               suppliesShow();
               itemsType = 2;
               break;
            case 3:
               allFalse();
               storagePanel["mck_3"].isClick = true;
               gemShow();
               itemsType = 3;
               break;
            case 4:
               allFalse();
               storagePanel["mck_4"].isClick = true;
               otherobjShow();
               itemsType = 4;
         }
      }
      
      private static function allFalse() : void
      {
         storagePanel["mck_1"].isClick = false;
         storagePanel["mck_2"].isClick = false;
         storagePanel["mck_3"].isClick = false;
         storagePanel["mck_4"].isClick = false;
      }
      
      public static function equipShow() : *
      {
         var i:uint = 0;
         myplayer.getBag().zhengliBag();
         for(i = 0; i < 24; i++)
         {
            storagePanel["s1_" + i]["t_txt"].visible = false;
            if(myplayer.getBag().getEquipFromBag(i + yeshu * 24) != null)
            {
               storagePanel["s1_" + i].gotoAndStop(myplayer.getBag().getEquipFromBag(i + yeshu * 24).getFrame());
               storagePanel["s1_" + i].visible = true;
            }
            else
            {
               storagePanel["s1_" + i].visible = false;
            }
         }
         for(i = 0; i < 35; i++)
         {
            storagePanel["kc_" + i]["t_txt"].visible = false;
            if(storage.getEquipFromStorage(i) != null)
            {
               storagePanel["kc_" + i].gotoAndStop(storage.getEquipFromStorage(i).getFrame());
               storagePanel["kc_" + i].visible = true;
            }
            else
            {
               storagePanel["kc_" + i].visible = false;
            }
         }
         if(yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = true;
            }
            if(myplayer.getBag().getLimitE() >= 28)
            {
               storagePanel["bagLock0"].visible = false;
            }
            if(myplayer.getBag().getLimitE() >= 32)
            {
               storagePanel["bagLock1"].visible = false;
            }
            if(myplayer.getBag().getLimitE() >= 36)
            {
               storagePanel["bagLock2"].visible = false;
            }
            if(myplayer.getBag().getLimitE() >= 40)
            {
               storagePanel["bagLock3"].visible = false;
            }
            if(myplayer.getBag().getLimitE() >= 44)
            {
               storagePanel["bagLock4"].visible = false;
            }
            if(myplayer.getBag().getLimitE() >= 48)
            {
               storagePanel["bagLock5"].visible = false;
            }
         }
      }
      
      public static function suppliesShow() : *
      {
         var i:uint = 0;
         myplayer.getBag().zhengliBagS();
         for(i = 0; i < 24; i++)
         {
            storagePanel["s1_" + i]["t_txt"].visible = false;
            if(myplayer.getBag().getSuppliesFromBag(i + yeshu * 24) != null)
            {
               storagePanel["s1_" + i].gotoAndStop(myplayer.getBag().getSuppliesFromBag(i + yeshu * 24).getFrame());
               storagePanel["s1_" + i].visible = true;
               if(myplayer.getBag().getSuppliesFromBag(i + yeshu * 24).getTimes() > 1)
               {
                  storagePanel["s1_" + i]["t_txt"].text = myplayer.getBag().getSuppliesFromBag(i + yeshu * 24).getTimes();
                  storagePanel["s1_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               storagePanel["s1_" + i].visible = false;
            }
         }
         for(i = 0; i < 35; i++)
         {
            storagePanel["kc_" + i]["t_txt"].visible = false;
            if(storage.getSuppliesFromStorage(i) != null)
            {
               storagePanel["kc_" + i].gotoAndStop(storage.getSuppliesFromStorage(i).getFrame());
               storagePanel["kc_" + i].visible = true;
               if(storage.getSuppliesFromStorage(i).getTimes() > 1)
               {
                  storagePanel["kc_" + i]["t_txt"].text = storage.getSuppliesFromStorage(i).getTimes();
                  storagePanel["kc_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               storagePanel["kc_" + i].visible = false;
            }
         }
         if(yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = true;
            }
            if(myplayer.getBag().getLimitS() >= 28)
            {
               storagePanel["bagLock0"].visible = false;
            }
            if(myplayer.getBag().getLimitS() >= 32)
            {
               storagePanel["bagLock1"].visible = false;
            }
            if(myplayer.getBag().getLimitS() >= 36)
            {
               storagePanel["bagLock2"].visible = false;
            }
            if(myplayer.getBag().getLimitS() >= 40)
            {
               storagePanel["bagLock3"].visible = false;
            }
            if(myplayer.getBag().getLimitS() >= 44)
            {
               storagePanel["bagLock4"].visible = false;
            }
            if(myplayer.getBag().getLimitS() >= 48)
            {
               storagePanel["bagLock5"].visible = false;
            }
         }
      }
      
      public static function gemShow() : *
      {
         var i:uint = 0;
         myplayer.getBag().zhengliBagG();
         for(i = 0; i < 24; i++)
         {
            storagePanel["s1_" + i]["t_txt"].visible = false;
            if(myplayer.getBag().getGemFromBag(i + yeshu * 24) != null)
            {
               storagePanel["s1_" + i].gotoAndStop(myplayer.getBag().getGemFromBag(i + yeshu * 24).getFrame());
               storagePanel["s1_" + i].visible = true;
               if(myplayer.getBag().getGemFromBag(i + yeshu * 24).getIsPile() == true)
               {
                  storagePanel["s1_" + i]["t_txt"].text = myplayer.getBag().getGemFromBag(i + yeshu * 24).getTimes();
                  storagePanel["s1_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               storagePanel["s1_" + i].visible = false;
            }
         }
         for(i = 0; i < 35; i++)
         {
            storagePanel["kc_" + i]["t_txt"].visible = false;
            if(storage.getGemFromStorage(i) != null)
            {
               storagePanel["kc_" + i].gotoAndStop(storage.getGemFromStorage(i).getFrame());
               storagePanel["kc_" + i].visible = true;
               if(storage.getGemFromStorage(i).getIsPile() == true)
               {
                  storagePanel["kc_" + i]["t_txt"].text = storage.getGemFromStorage(i).getTimes();
                  storagePanel["kc_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               storagePanel["kc_" + i].visible = false;
            }
         }
         if(yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = true;
            }
            if(myplayer.getBag().getLimitG() >= 28)
            {
               storagePanel["bagLock0"].visible = false;
            }
            if(myplayer.getBag().getLimitG() >= 32)
            {
               storagePanel["bagLock1"].visible = false;
            }
            if(myplayer.getBag().getLimitG() >= 36)
            {
               storagePanel["bagLock2"].visible = false;
            }
            if(myplayer.getBag().getLimitG() >= 40)
            {
               storagePanel["bagLock3"].visible = false;
            }
            if(myplayer.getBag().getLimitG() >= 44)
            {
               storagePanel["bagLock4"].visible = false;
            }
            if(myplayer.getBag().getLimitG() >= 48)
            {
               storagePanel["bagLock5"].visible = false;
            }
         }
      }
      
      public static function otherobjShow() : *
      {
         var i:uint = 0;
         myplayer.getBag().zhengliBagO();
         for(i = 0; i < 24; i++)
         {
            storagePanel["s1_" + i]["t_txt"].visible = false;
            if(myplayer.getBag().getOtherobjFromBag(i + yeshu * 24) != null)
            {
               storagePanel["s1_" + i].gotoAndStop(myplayer.getBag().getOtherobjFromBag(i + yeshu * 24).getFrame());
               storagePanel["s1_" + i].visible = true;
               if(myplayer.getBag().getOtherobjFromBag(i + yeshu * 24).getTimes() > 1)
               {
                  storagePanel["s1_" + i]["t_txt"].text = myplayer.getBag().getOtherobjFromBag(i + yeshu * 24).getTimes();
                  storagePanel["s1_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               storagePanel["s1_" + i].visible = false;
            }
         }
         for(i = 0; i < 35; i++)
         {
            storagePanel["kc_" + i]["t_txt"].visible = false;
            if(storage.getOtherobjFromStorage(i) != null)
            {
               storagePanel["kc_" + i].gotoAndStop(storage.getOtherobjFromStorage(i).getFrame());
               storagePanel["kc_" + i].visible = true;
               if(storage.getOtherobjFromStorage(i).getTimes() > 1)
               {
                  storagePanel["kc_" + i]["t_txt"].text = storage.getOtherobjFromStorage(i).getTimes();
                  storagePanel["kc_" + i]["t_txt"].visible = true;
               }
            }
            else
            {
               storagePanel["kc_" + i].visible = false;
            }
         }
         if(yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               storagePanel["bagLock" + i].visible = true;
            }
            if(myplayer.getBag().getLimitO() >= 28)
            {
               storagePanel["bagLock0"].visible = false;
            }
            if(myplayer.getBag().getLimitO() >= 32)
            {
               storagePanel["bagLock1"].visible = false;
            }
            if(myplayer.getBag().getLimitO() >= 36)
            {
               storagePanel["bagLock2"].visible = false;
            }
            if(myplayer.getBag().getLimitO() >= 40)
            {
               storagePanel["bagLock3"].visible = false;
            }
            if(myplayer.getBag().getLimitO() >= 44)
            {
               storagePanel["bagLock4"].visible = false;
            }
            if(myplayer.getBag().getLimitO() >= 48)
            {
               storagePanel["bagLock5"].visible = false;
            }
         }
      }
      
      private static function tooltipOpen(e:*) : *
      {
         var overobj:MovieClip = null;
         var overNum:uint = 0;
         storagePanel.addChild(itemsTooltip);
         if(isDown == false)
         {
            overobj = e.target as MovieClip;
            overNum = uint(overobj.name.substr(3,2));
            itemsTooltip.x = storagePanel.mouseX;
            itemsTooltip.y = storagePanel.mouseY;
            if(overobj.name.substr(0,2) == "s1")
            {
               overNum += yeshu * 24;
               switch(itemsType)
               {
                  case 1:
                     if(myplayer.getBag().getEquipFromBag(overNum))
                     {
                        itemsTooltip.equipTooltip(myplayer.getBag().getEquipFromBag(overNum));
                     }
                     break;
                  case 2:
                     if(myplayer.getBag().getSuppliesFromBag(overNum))
                     {
                        itemsTooltip.suppliesTooltip(myplayer.getBag().getSuppliesFromBag(overNum));
                     }
                     break;
                  case 3:
                     if(myplayer.getBag().getGemFromBag(overNum))
                     {
                        itemsTooltip.gemTooltip(myplayer.getBag().getGemFromBag(overNum));
                     }
                     break;
                  case 4:
                     if(myplayer.getBag().getOtherobjFromBag(overNum))
                     {
                        itemsTooltip.otherTooltip(myplayer.getBag().getOtherobjFromBag(overNum));
                     }
               }
            }
            else
            {
               switch(itemsType)
               {
                  case 1:
                     if(storage.getEquipFromStorage(overNum))
                     {
                        itemsTooltip.equipTooltip(storage.getEquipFromStorage(overNum),3);
                     }
                     break;
                  case 2:
                     if(storage.getSuppliesFromStorage(overNum))
                     {
                        itemsTooltip.suppliesTooltip(storage.getSuppliesFromStorage(overNum),3);
                     }
                     break;
                  case 3:
                     if(storage.getGemFromStorage(overNum))
                     {
                        itemsTooltip.gemTooltip(storage.getGemFromStorage(overNum),3);
                     }
                     break;
                  case 4:
                     if(storage.getOtherobjFromStorage(overNum))
                     {
                        itemsTooltip.otherTooltip(storage.getOtherobjFromStorage(overNum));
                     }
               }
            }
            itemsTooltip.visible = true;
         }
      }
      
      private static function tooltipClose(e:MouseEvent) : void
      {
         if(isDown == false)
         {
            itemsTooltip.visible = false;
         }
      }
      
      private static function isDrag(xx:Number, yy:Number) : Boolean
      {
         if(isDown == true)
         {
            if(storagePanel.mouseX > xx + 1 || storagePanel.mouseX < xx - 1 || storagePanel.mouseY > yy + 1 || storagePanel.mouseY < yy - 1)
            {
               return true;
            }
         }
         return false;
      }
      
      private static function DragHandler(e:MouseEvent) : void
      {
         if(isDown == true)
         {
            if(isDrag(pointx,pointy) == true)
            {
               storagePanel.addChild(dragObj);
               dragObj.x = storagePanel.mouseX - 20;
               dragObj.y = storagePanel.mouseY - 20;
               dragObj.startDrag();
            }
         }
      }
      
      private static function DownHandler(e:MouseEvent) : void
      {
         if(itemsTooltip.visible = true)
         {
            itemsTooltip.visible = false;
            isDown = true;
         }
         dragObj = e.target as MovieClip;
         returnx = dragObj.x;
         returny = dragObj.y;
         pointx = storagePanel.mouseX;
         pointy = storagePanel.mouseY;
         oldNum = uint(dragObj.name.substr(3,2));
      }
      
      private static function UpHandler(e:MouseEvent = null) : void
      {
         if(dragObj)
         {
            dragObj.stopDrag();
            if(inItemsRange() == false && inStorageRange() == false)
            {
               dragObj.x = returnx;
               dragObj.y = returny;
            }
            isDown = false;
         }
         dragObj = null;
      }
      
      private static function inItemsRange() : Boolean
      {
         var bool:Boolean = false;
         var xflag:int = 0;
         var i:uint = 0;
         var bagNum:uint = 0;
         var yflag:int = storagePanel.y + 60;
         for(var j:uint = 0; j < 6; j++)
         {
            bagNum = 4 * j;
            xflag = storagePanel.x + 567;
            if(storagePanel.mouseY > yflag && storagePanel.mouseY < yflag + 60)
            {
               for(i = 0; i < 8; i++)
               {
                  if(storagePanel.mouseX > xflag && storagePanel.mouseX < xflag + 60)
                  {
                     switch(itemsType)
                     {
                        case 1:
                           equipHandle(dragObj,oldNum,bagNum);
                           break;
                        case 2:
                           suppliesHandle(dragObj,oldNum,bagNum);
                           break;
                        case 3:
                           gemHandle(dragObj,oldNum,bagNum);
                           break;
                        case 4:
                           otherHandle(dragObj,oldNum,bagNum);
                     }
                     dragObj.x = returnx;
                     dragObj.y = returny;
                     return bool;
                  }
                  bagNum++;
                  xflag += 75;
               }
            }
            yflag += 72;
         }
         return false;
      }
      
      private static function inStorageRange() : Boolean
      {
         var bool:Boolean = false;
         var xflag:int = 0;
         var i:uint = 0;
         var stotageNum:uint = 0;
         var yflag:int = storagePanel.y + 60;
         for(var j:uint = 0; j < 7; j++)
         {
            stotageNum = 5 * j;
            xflag = storagePanel.x + 100;
            if(storagePanel.mouseY > yflag && storagePanel.mouseY < yflag + 60)
            {
               for(i = 0; i < 5; i++)
               {
                  if(storagePanel.mouseX > xflag && storagePanel.mouseX < xflag + 60)
                  {
                     switch(itemsType)
                     {
                        case 1:
                           equipStorageHandle(dragObj,oldNum,stotageNum);
                           break;
                        case 2:
                           suppliesStorageHandle(dragObj,oldNum,stotageNum);
                           break;
                        case 3:
                           gemStorageHandle(dragObj,oldNum,stotageNum);
                           break;
                        case 4:
                           otherStorageHandle(dragObj,oldNum,stotageNum);
                     }
                     dragObj.x = returnx;
                     dragObj.y = returny;
                     return bool;
                  }
                  stotageNum++;
                  xflag += 75;
               }
            }
            yflag += 72;
         }
         return false;
      }
      
      public static function equipHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var equip:Equip = null;
         if(dragObj.name.substr(0,3) == "s1_")
         {
            begin += yeshu * 24;
            end += yeshu * 24;
            myplayer.getBag().equipBagMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "kc_")
            {
               return false;
            }
            end += yeshu * 24;
            if(myplayer.getBag().getEquipFromBag(end) != null)
            {
               equip = myplayer.getBag().delEquip(end);
               myplayer.getBag().addToEquipBag(storage.getEquipFromStorage(begin),end);
               storage.delEquip(begin);
               storage.addToEquipStorage(equip,begin);
            }
            else
            {
               myplayer.getBag().addToEquipBag(storage.getEquipFromStorage(begin),end);
               storage.delEquip(begin);
            }
         }
         equipShow();
         return true;
      }
      
      public static function equipStorageHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var equip:Equip = null;
         if(dragObj.name.substr(0,3) == "kc_")
         {
            storage.equipStorageMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "s1_")
            {
               return false;
            }
            begin += yeshu * 24;
            if(storage.getEquipFromStorage(end) != null)
            {
               equip = storage.delEquip(end);
               storage.addToEquipStorage(myplayer.getBag().getEquipFromBag(begin),end);
               myplayer.getBag().delEquip(begin);
               myplayer.getBag().addToEquipBag(equip,begin);
            }
            else
            {
               storage.addToEquipStorage(myplayer.getBag().getEquipFromBag(begin),end);
               myplayer.getBag().delEquip(begin);
            }
         }
         equipShow();
         return true;
      }
      
      public static function suppliesHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var supplies:Supplies = null;
         if(dragObj.name.substr(0,3) == "s1_")
         {
            begin += yeshu * 24;
            end += yeshu * 24;
            myplayer.getBag().suppliesBagMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "kc_")
            {
               return false;
            }
            end += yeshu * 24;
            if(myplayer.getBag().getSuppliesFromBag(end) != null)
            {
               supplies = myplayer.getBag().delSupplies(end);
               myplayer.getBag().addToSuppliesBag(storage.getSuppliesFromStorage(begin),end);
               storage.delSupplies(begin);
               storage.addToSuppliesStorage(supplies,begin);
            }
            else
            {
               myplayer.getBag().addToSuppliesBag(storage.getSuppliesFromStorage(begin),end);
               storage.delSupplies(begin);
            }
         }
         suppliesShow();
         return true;
      }
      
      public static function suppliesStorageHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var supplies:Supplies = null;
         if(dragObj.name.substr(0,3) == "kc_")
         {
            storage.suppliesStorageMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "s1_")
            {
               return false;
            }
            begin += yeshu * 24;
            if(storage.getSuppliesFromStorage(end) != null)
            {
               supplies = storage.delSupplies(end);
               storage.addToSuppliesStorage(myplayer.getBag().getSuppliesFromBag(begin),end);
               myplayer.getBag().delSupplies(begin);
               myplayer.getBag().addToSuppliesBag(supplies,begin);
            }
            else
            {
               storage.addToSuppliesStorage(myplayer.getBag().getSuppliesFromBag(begin),end);
               myplayer.getBag().delSupplies(begin);
            }
         }
         suppliesShow();
         return true;
      }
      
      public static function gemHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var gem:Gem = null;
         if(dragObj.name.substr(0,3) == "s1_")
         {
            begin += yeshu * 24;
            end += yeshu * 24;
            myplayer.getBag().gemBagMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "kc_")
            {
               return false;
            }
            end += yeshu * 24;
            if(myplayer.getBag().getGemFromBag(end) != null)
            {
               gem = myplayer.getBag().delGem(end,myplayer.getBag().getGemFromBag(end).getTimes());
               myplayer.getBag().addToGemBag(storage.getGemFromStorage(begin),end);
               storage.addToGemStorage(gem,begin);
            }
            else
            {
               myplayer.getBag().addToGemBag(storage.delGem(begin,storage.getGemFromStorage(begin).getTimes()),end);
            }
         }
         gemShow();
         return true;
      }
      
      public static function gemStorageHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var gem:Gem = null;
         if(dragObj.name.substr(0,3) == "kc_")
         {
            storage.gemStorageMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "s1_")
            {
               return false;
            }
            begin += yeshu * 24;
            if(storage.getGemFromStorage(end) != null)
            {
               gem = storage.delGem(end,storage.getGemFromStorage(end).getTimes());
               storage.addToGemStorage(myplayer.getBag().delGem(begin,myplayer.getBag().getGemFromBag(begin).getTimes()),end);
               myplayer.getBag().addToGemBag(gem,begin);
            }
            else
            {
               storage.addToGemStorage(myplayer.getBag().delGem(begin,myplayer.getBag().getGemFromBag(begin).getTimes()),end);
            }
         }
         gemShow();
         return true;
      }
      
      public static function otherHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var oo:Otherobj = null;
         if(dragObj.name.substr(0,3) == "s1_")
         {
            begin += yeshu * 24;
            end += yeshu * 24;
            myplayer.getBag().otherobjBagMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "kc_")
            {
               return false;
            }
            end += yeshu * 24;
            if(myplayer.getBag().getOtherobjFromBag(end) != null)
            {
               oo = myplayer.getBag().delOtherobj(end,myplayer.getBag().getOtherobjFromBag(end).getTimes());
               myplayer.getBag().addToOtherobjBag(storage.getOtherobjFromStorage(begin),end);
               storage.addToOtherobjStorage(oo,begin);
            }
            else
            {
               myplayer.getBag().addToOtherobjBag(storage.delOtherobj(begin,storage.getOtherobjFromStorage(begin).getTimes()),end);
            }
         }
         otherobjShow();
         return true;
      }
      
      public static function otherStorageHandle(dragObj:MovieClip, begin:Number, end:Number) : Boolean
      {
         var oo:Otherobj = null;
         if(dragObj.name.substr(0,3) == "kc_")
         {
            storage.otherobjStorageMove(begin,end);
         }
         else
         {
            if(dragObj.name.substr(0,3) != "s1_")
            {
               return false;
            }
            begin += yeshu * 24;
            if(storage.getOtherobjFromStorage(end) != null)
            {
               oo = storage.delOtherobj(end,storage.getOtherobjFromStorage(end).getTimes());
               storage.addToOtherobjStorage(myplayer.getBag().delOtherobj(begin,myplayer.getBag().getOtherobjFromBag(begin).getTimes()),end);
               myplayer.getBag().addToOtherobjBag(oo,begin);
            }
            else
            {
               storage.addToOtherobjStorage(myplayer.getBag().delOtherobj(begin,myplayer.getBag().getOtherobjFromBag(begin).getTimes()),end);
            }
         }
         otherobjShow();
         return true;
      }
      
      private static function takeOut(e:MouseEvent) : void
      {
         takeOutHandle(itemsType,oldNum);
         switch(itemsType)
         {
            case 1:
               equipShow();
               break;
            case 2:
               suppliesShow();
               break;
            case 3:
               gemShow();
               break;
            case 4:
               otherobjShow();
         }
         dragObj = null;
      }
      
      public static function takeOutHandle(itemsType:Number, begin:Number) : *
      {
         switch(itemsType)
         {
            case 1:
               if(myplayer.getBag().backequipBagNum() > 0)
               {
                  myplayer.getBag().addEquipBag(storage.delEquip(begin));
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               break;
            case 2:
               if(myplayer.getBag().backSuppliesBagNum() > 0)
               {
                  myplayer.getBag().addSuppliesBag(storage.delSupplies(begin));
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               break;
            case 3:
               if(myplayer.getBag().backGemBagNum() > 0)
               {
                  myplayer.getBag().addGemBag(storage.delGem(begin,storage.getGemFromStorage(begin).getTimes()));
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               break;
            case 4:
               if(myplayer.getBag().backOtherBagNum() > 0)
               {
                  myplayer.getBag().addOtherobjBag(storage.delOtherobj(begin,storage.getOtherobjFromStorage(begin).getTimes()));
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
               break;
         }
      }
      
      private static function putIn(e:MouseEvent) : void
      {
         dragObj = e.target as MovieClip;
         if(dragObj.name.substr(0,2) == "s1")
         {
            putInStorageHandle(itemsType,oldNum);
         }
         switch(itemsType)
         {
            case 1:
               equipShow();
               break;
            case 2:
               suppliesShow();
               break;
            case 3:
               gemShow();
               break;
            case 4:
               otherobjShow();
         }
         dragObj = null;
      }
      
      public static function putInStorageHandle(itemsType:Number, begin:Number) : *
      {
         begin += yeshu * 24;
         switch(itemsType)
         {
            case 1:
               if(storage.backEquipEmptyNum() > 0)
               {
                  storage.addEquipStorage(myplayer.getBag().getEquipFromBag(begin));
                  myplayer.getBag().delEquip(begin);
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
               break;
            case 2:
               if(storage.backSuppliesEmptyNum() > 0)
               {
                  storage.addSuppliesStorage(myplayer.getBag().getSuppliesFromBag(begin));
                  myplayer.getBag().delSupplies(begin);
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
               break;
            case 3:
               if(storage.backGemEmptyNum() > 0)
               {
                  storage.addGemStorage(myplayer.getBag().delGem(begin,myplayer.getBag().getGemFromBag(begin).getTimes()));
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
               break;
            case 4:
               if(storage.backOtherEmptyNum() > 0)
               {
                  storage.addOtherobjStorage(myplayer.getBag().delOtherobj(begin,myplayer.getBag().getOtherobjFromBag(begin).getTimes()));
                  break;
               }
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
               break;
         }
      }
   }
}

