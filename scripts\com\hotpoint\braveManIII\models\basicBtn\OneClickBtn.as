package com.hotpoint.braveManIII.models.basicBtn
{
   import com.hotpoint.braveManIII.events.*;
   import flash.events.MouseEvent;
   
   public class OneClickBtn extends BasicBtn
   {
      
      public var _isClick:Boolean = false;
      
      public function OneClickBtn()
      {
         super();
      }
      
      override protected function clickFn(event:MouseEvent) : void
      {
         this._isClick = !this._isClick;
         if(this._isClick)
         {
            this.gotoAndStop(3);
         }
         else
         {
            this.gotoAndStop(1);
         }
         this.dispatchEvent(new BtnEvent(BtnEvent.DO_ONECLICK,this.id));
      }
      
      override protected function rollOut(event:MouseEvent) : void
      {
         if(this._isClick)
         {
            this.gotoAndStop(3);
         }
         else
         {
            this.gotoAndStop(1);
         }
      }
      
      override protected function rollOver(event:MouseEvent) : void
      {
         if(this._isClick)
         {
            this.gotoAndStop(3);
         }
         else
         {
            this.gotoAndStop(2);
         }
      }
   }
}

