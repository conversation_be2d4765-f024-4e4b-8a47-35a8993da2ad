package src.tool
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class Sel_NanDu3 extends MovieClip
   {
      
      public var Close_btn:SimpleButton;
      
      public var NanDu1_btn:SimpleButton;
      
      public var NanDu2_btn:SimpleButton;
      
      public var NanDu3_btn:SimpleButton;
      
      public var NanDu4_btn:SimpleButton;
      
      public var XXX_1:SimpleButton;
      
      public var XXX_2:SimpleButton;
      
      public var XXX_3:SimpleButton;
      
      public var XXX_4:SimpleButton;
      
      public var killPoint_btn:SimpleButton;
      
      public var killPoint_1:*;
      
      public var killPoint_2:*;
      
      public function Sel_NanDu3()
      {
         super();
         this.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.killPoint_btn.addEventListener(MouseEvent.CLICK,this.BuyKill);
         this.NanDu1_btn.addEventListener(MouseEvent.CLICK,this.NanDu1);
         this.NanDu2_btn.addEventListener(MouseEvent.CLICK,this.NanDu2);
         this.NanDu3_btn.addEventListener(MouseEvent.CLICK,this.NanDu3);
         this.NanDu4_btn.addEventListener(MouseEvent.CLICK,this.NanDu4);
      }
      
      public function Open() : *
      {
         this.x = this.y = 0;
         this.visible = true;
         this.Show();
      }
      
      public function Close(e:* = null) : *
      {
         this.x = this.y = 5000;
         this.visible = false;
      }
      
      private function NanDu1(e:*) : *
      {
         if(Main.guanKa[53] <= 0)
         {
            return;
         }
         var numXX:int = int(GongHui_jiTan.killPointXX(20));
         if(Main.P1P2 == false && Main.player1.killPoint.getValue() >= 20)
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - numXX);
            Main.gameNum.setValue(81);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else if(Main.P1P2 == true && Main.player1.killPoint.getValue() >= 20 && Main.player2.killPoint.getValue() >= 20)
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - numXX);
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - numXX);
            Main.gameNum.setValue(81);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else
         {
            NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"击杀点不足");
         }
      }
      
      private function NanDu2(e:*) : *
      {
         if(Main.guanKa[56] <= 0)
         {
            return;
         }
         if(Main.P1P2 == false && Main.player1.killPoint.getValue() >= InitData.BuyNum_30.getValue())
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - InitData.BuyNum_30.getValue());
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else if(Main.P1P2 == true && Main.player1.killPoint.getValue() >= InitData.BuyNum_30.getValue() && Main.player2.killPoint.getValue() >= InitData.BuyNum_30.getValue())
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - InitData.BuyNum_30.getValue());
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - InitData.BuyNum_30.getValue());
            Main.gameNum.setValue(82);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else
         {
            NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"击杀点不足");
         }
      }
      
      private function NanDu3(e:*) : *
      {
         if(Main.guanKa[59] <= 0)
         {
            return;
         }
         if(Main.P1P2 == false && Main.player1.killPoint.getValue() >= InitData.BuyNum_40.getValue())
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - InitData.BuyNum_40.getValue());
            Main.gameNum.setValue(83);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else if(Main.P1P2 == true && Main.player1.killPoint.getValue() >= InitData.BuyNum_40.getValue() && Main.player2.killPoint.getValue() >= InitData.BuyNum_40.getValue())
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - InitData.BuyNum_40.getValue());
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - InitData.BuyNum_40.getValue());
            Main.gameNum.setValue(83);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else
         {
            NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"击杀点不足");
         }
      }
      
      private function NanDu4(e:*) : *
      {
         if(Main.guanKa[84] <= 0)
         {
            return;
         }
         if(Main.P1P2 == false && Main.player1.killPoint.getValue() >= InitData.Temp50.getValue())
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - InitData.Temp50.getValue());
            Main.gameNum.setValue(84);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else if(Main.P1P2 == true && Main.player1.killPoint.getValue() >= InitData.Temp50.getValue() && Main.player2.killPoint.getValue() >= InitData.Temp50.getValue())
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() - InitData.Temp50.getValue());
            Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() - InitData.Temp50.getValue());
            Main.gameNum.setValue(84);
            Main.gameNum2.setValue(1);
            GameData.gameLV = 4;
            this.GameGo();
         }
         else
         {
            NewMC.Open("文字提示",Main._this,480,290,30,0,true,2,"击杀点不足");
         }
      }
      
      private function GameGo() : *
      {
         SelMap.Close();
         Main.gameNum2.setValue(1);
         Main._this.Loading();
         this.Close();
      }
      
      private function Show() : *
      {
         var ii:uint = 0;
         for(var i:int = 1; i < 5; i++)
         {
            if(this["XXX_" + i])
            {
               ii = uint(50 + i * 3);
               if(Main.guanKa[ii] > 0)
               {
                  this["XXX_" + i].visible = false;
               }
               else
               {
                  this["XXX_" + i].visible = true;
               }
            }
         }
         this.killPoint_1.text = Main.player1.killPoint.getValue();
         if(Main.P1P2)
         {
            this.killPoint_2.text = Main.player2.killPoint.getValue();
         }
      }
      
      private function BuyKill(e:*) : *
      {
         Shop4399.Open(4);
      }
   }
}

