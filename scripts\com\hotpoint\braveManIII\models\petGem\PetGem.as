package com.hotpoint.braveManIII.models.petGem
{
   import src.*;
   
   public class PetGem
   {
      
      private var _xingge:Array = [];
      
      public function PetGem()
      {
         super();
      }
      
      public static function creatPetGem(xg:Number) : PetGem
      {
         var Gem:PetGem = new PetGem();
         if(xg != 0)
         {
            Gem._xingge.push(xg);
         }
         return Gem;
      }
      
      public function getName() : String
      {
         if(this._xingge.length == 0)
         {
            return "未知血脉";
         }
         if(this._xingge.length == 1)
         {
            return "未知血脉石";
         }
         if(this._xingge.length == 2)
         {
            return "血脉精华";
         }
         if(this._xingge.length == 3)
         {
            return "血精石";
         }
         if(this._xingge.length == 4)
         {
            return "血精珠";
         }
         if(this._xingge.length == 5)
         {
            return "爆裂石";
         }
         if(this._xingge.length == 6)
         {
            return "爆裂精华";
         }
         if(this._xingge.length == 7)
         {
            return "血耀石";
         }
         if(this._xingge.length == 8)
         {
            return "血耀精华";
         }
         if(this._xingge.length == 9)
         {
            return "远古圣血";
         }
         if(this._xingge.length == 10)
         {
            return "远古圣血精华";
         }
         if(this._xingge.length == 11)
         {
            return "神兽之精";
         }
         if(this._xingge.length == 12)
         {
            return "神兽血脉";
         }
      }
      
      public function getFrame() : Number
      {
         if(this._xingge.length > 0)
         {
            return this._xingge.length + 81;
         }
         if(this._xingge.length == 0)
         {
            return 94;
         }
      }
      
      public function getType() : Number
      {
         return this._xingge.length;
      }
      
      public function getPrice() : Number
      {
         return 1000;
      }
      
      public function getColor() : Number
      {
         if(this._xingge.length == 0)
         {
            return 1;
         }
         if(this._xingge.length == 1)
         {
            return 1;
         }
         if(this._xingge.length == 2)
         {
            return 1;
         }
         if(this._xingge.length == 3)
         {
            return 1;
         }
         if(this._xingge.length == 4)
         {
            return 2;
         }
         if(this._xingge.length == 5)
         {
            return 2;
         }
         if(this._xingge.length == 6)
         {
            return 2;
         }
         if(this._xingge.length == 7)
         {
            return 3;
         }
         if(this._xingge.length == 8)
         {
            return 3;
         }
         if(this._xingge.length == 9)
         {
            return 3;
         }
         if(this._xingge.length == 10)
         {
            return 4;
         }
         if(this._xingge.length == 11)
         {
            return 4;
         }
         if(this._xingge.length == 12)
         {
            return 4;
         }
      }
      
      public function getSkillDescript() : String
      {
         return "无";
      }
      
      public function getDescript() : String
      {
         if(this._xingge.length == 0)
         {
            return "尚未拥有血脉能量，可在排行榜勇者宝箱中兑换";
         }
         if(this._xingge.length == 1)
         {
            return "由不知名的野兽所留下的血液化石";
         }
         if(this._xingge.length == 2)
         {
            return "提取不知名野兽血液熔炼而成的精华";
         }
         if(this._xingge.length == 3)
         {
            return "熔炼集成血液精华而成珍贵之石";
         }
         if(this._xingge.length == 4)
         {
            return "熔炼锻铸血精石而成的血精珠";
         }
         if(this._xingge.length == 5)
         {
            return "过多能量充斥于血精珠内爆裂而成的珍贵之物";
         }
         if(this._xingge.length == 6)
         {
            return "取至爆裂珍贵之物内精华熔炼提纯而出";
         }
         if(this._xingge.length == 7)
         {
            return "在爆裂精华内添加不知名野兽血液所成功反应的血耀之石";
         }
         if(this._xingge.length == 8)
         {
            return "血耀之石经由绝望之焰粹取出的精华";
         }
         if(this._xingge.length == 9)
         {
            return "经由伽妮雅成功辨别出来的远古血脉的血液";
         }
         if(this._xingge.length == 10)
         {
            return "由各种远古血脉纷乱复杂的结合精华";
         }
         if(this._xingge.length == 11)
         {
            return "至高神兽遗留下的一滴血液精华";
         }
         if(this._xingge.length == 12)
         {
            return "至高神兽遗留下无穷力量的血脉";
         }
      }
      
      public function getXingge() : Array
      {
         return this._xingge;
      }
      
      public function getXinggeSX() : String
      {
      }
      
      public function isHaveSX(sx:int) : *
      {
         var j:* = undefined;
         for(j in this._xingge)
         {
            if(this._xingge[j] == sx)
            {
               return true;
            }
         }
         return false;
      }
      
      public function chongSuXueMai() : *
      {
         var xx:int = 0;
         for(var i:int = 0; i < 1; i++)
         {
            xx = int(Math.random() * 12) + 1;
            if(xx != this._xingge[0])
            {
               this._xingge[0] = xx;
            }
            else
            {
               i--;
            }
         }
      }
      
      public function RongLianXueMai(XM:PetGem) : *
      {
         var j:* = undefined;
         var xx:int = 0;
         var bool:Boolean = true;
         var xm_arr:Array = XM.getXingge();
         for(var i:int = 0; i < xm_arr.length; i++)
         {
            for(j in this._xingge)
            {
               if(this._xingge[j] == xm_arr[i])
               {
                  bool = false;
               }
            }
            if(bool)
            {
               xx = int(Math.random() * 12);
               if(!this._xingge[xx])
               {
                  this._xingge.push(xm_arr[i]);
               }
               else
               {
                  this._xingge[xx] = xm_arr[i];
               }
            }
         }
      }
      
      public function isRongLian(XM:PetGem) : Boolean
      {
         var j:* = undefined;
         var xm_arr:Array = XM.getXingge();
         for(var i:int = 0; i < xm_arr.length; i++)
         {
            for(j in this._xingge)
            {
               if(this._xingge[j] == xm_arr[i])
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      public function get xingge() : Array
      {
         return this._xingge;
      }
      
      public function set xingge(value:Array) : void
      {
         this._xingge = value;
      }
   }
}

