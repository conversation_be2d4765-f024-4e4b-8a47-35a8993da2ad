package com.hotpoint.braveManIII.views.setKeyPanel
{
   import com.hotpoint.braveManIII.events.*;
   import flash.display.MovieClip;
   import flash.events.*;
   import src.*;
   import src.tool.*;
   
   public class SetKeyPanel extends MovieClip
   {
      
      private static var _instance1:SetKeyPanel;
      
      private static var _instance:SetKeyPanel;
      
      private static var loadData:ClassLoader;
      
      private static var open_yn:Boolean;
      
      public static var ysArr:Array = [87,83,65,68,74,75,76,72,85,73,79,49,50,51,32,78];
      
      public static var ysArr2:Array = [38,40,37,39,97,98,99,100,101,102,107,103,104,105,96,34];
      
      private static var loadName:String = "Panel_key_v1.swf";
      
      private var keyNum:uint = 16;
      
      private var sure1:Array = [87,83,65,68,74,75,76,72,85,73,79,49,50,51,32,78];
      
      private var sure2:Array = [38,40,37,39,97,98,99,100,101,102,107,103,104,105,96,34];
      
      private var cchuArr:Array = [];
      
      private var p1ArrKeyArr:Array = [];
      
      private var p2ArrKeyArr:Array = [];
      
      private var clickState:Boolean;
      
      private var overBo:Boolean;
      
      private var oldCurrent:uint;
      
      private var targetId:uint = 1;
      
      public var p1_0:*;
      
      public var p1_1:*;
      
      public var p1_2:*;
      
      public var p1_3:*;
      
      public var p1_4:*;
      
      public var p1_5:*;
      
      public var p1_6:*;
      
      public var p1_7:*;
      
      public var p1_8:*;
      
      public var p1_9:*;
      
      public var p1_10:*;
      
      public var p1_11:*;
      
      public var p1_12:*;
      
      public var p1_13:*;
      
      public var p1_14:*;
      
      public var p1_15:*;
      
      public var p1_btn:*;
      
      public var p2_btn:*;
      
      public var sureMast:*;
      
      public function SetKeyPanel(pvt:PrivateClass)
      {
         super();
      }
      
      public static function Loading() : *
      {
         if(!loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      public static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("com.hotpoint.braveManIII.views.setKeyPanel.SetKeyPanel") as Class;
         SetKeyPanel._instance = new classRef(new PrivateClass());
         SetKeyPanel._instance.initKey();
         Play_Interface.interfaceX["load_mc"].visible = false;
         if(open_yn)
         {
            open();
         }
         else
         {
            close();
         }
      }
      
      public static function open(playerType:Number = 1) : void
      {
         Main.stopXX = true;
         if(SetKeyPanel._instance == null)
         {
            Loading();
            open_yn = true;
            return;
         }
         _instance.setXY();
         Main._stage.addChild(SetKeyPanel._instance);
         _instance.openPlayerData(playerType);
         _instance.visible = true;
         if(!Main.P1P2)
         {
            _instance.p2_btn.visible = false;
         }
      }
      
      public static function close() : void
      {
         Main.stopXX = false;
         if(SetKeyPanel._instance != null)
         {
            if(SetKeyPanel._instance.visible == true)
            {
               SetKeyPanel._instance.visible = false;
            }
         }
         else
         {
            open_yn = false;
         }
      }
      
      private function setXY() : void
      {
         var stageWidth:Number = Main._stage.stageWidth / 2;
         var stageHeight:Number = Main._stage.stageHeight / 2;
      }
      
      private function initKey() : *
      {
         this.initP1Frame();
         for(var i:uint = 0; i < this.keyNum; i++)
         {
            this.p1ArrKeyArr[i] = false;
            this.p2ArrKeyArr[i] = false;
            this["p1_" + i].id = i;
            this["p1_" + i].mouseChildren = false;
            this["p1_" + i].addEventListener(MouseEvent.MOUSE_OVER,this.overHandle);
            this["p1_" + i].addEventListener(MouseEvent.MOUSE_OUT,this.outHandle);
            this["p1_" + i].addEventListener(MouseEvent.CLICK,this.clickHandle);
         }
         this.sureMast.visible = false;
         Main._stage.addEventListener(KeyboardEvent.KEY_DOWN,this.downHandle);
         addEventListener(BtnEvent.DO_CLOSE,this.btnCloseHandle);
         addEventListener(BtnEvent.DO_CHANGE,this.changeHandle);
         addEventListener(BtnEvent.DO_CLICK,this.btnClick);
         this["p1_btn"].addEventListener(MouseEvent.CLICK,this.p1p2_OPEN);
         if(Main.P1P2)
         {
            this["p2_btn"].addEventListener(MouseEvent.CLICK,this.p1p2_OPEN);
         }
         this["p1_btn"].mouseChildren = this["p2_btn"].mouseChildren = false;
      }
      
      public function p1p2_OPEN(e:MouseEvent) : *
      {
         var num:int = int(e.target.name.substr(1,1));
         TiaoShi.txtShow("p1p2_OPEN??" + num);
         if(num == 1)
         {
            this.initP1Frame();
         }
         else
         {
            this.initP2Frame();
         }
      }
      
      private function initP1Frame(e:* = null) : void
      {
         var keyNumX:* = undefined;
         TiaoShi.txtShow("p1键位:" + Main.player1._keyArr);
         for(var i:uint = 0; i < this.keyNum; i++)
         {
            this["p1_" + i].mouseChildren = false;
            this["p1_" + i].buttonMode = true;
            keyNumX = this.XiuZengKey(1,i);
            if(Main.player1._keyArr[i] != -1)
            {
               this["p1_" + i].gotoAndStop("k" + keyNumX);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
         }
      }
      
      private function initP2Frame(e:* = null) : void
      {
         var keyNumX:* = undefined;
         TiaoShi.txtShow("p2键位:" + Main.player2._keyArr);
         for(var i:uint = 0; i < this.keyNum; i++)
         {
            this["p1_" + i].mouseChildren = false;
            this["p1_" + i].buttonMode = true;
            keyNumX = this.XiuZengKey(2,i);
            if(Main.player2._keyArr[i] != -1)
            {
               this["p1_" + i].gotoAndStop("k" + keyNumX);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
         }
      }
      
      public function XiuZengKey(p1p2:int, num:int) : int
      {
         var keyArr:* = Main.player1._keyArr;
         if(p1p2 == 2)
         {
            keyArr = Main.player2._keyArr;
         }
         var keyNumX:int = int(keyArr[num]);
         var xArr:Array = [219,221,220,188,190,191,192,32,186,222,37,38,39,40,48,49,50,51,52,53,54,55,56,57,187,189,96,97,98,99,100,101,102,103,104,105,107,109,111,106,110,34];
         for(var i:* = 0; i < xArr.length; i++)
         {
            if(keyNumX == xArr[i])
            {
               return keyNumX;
            }
         }
         if(keyNumX >= 65 && keyNumX <= 90)
         {
            return keyNumX;
         }
         if(p1p2 == 1)
         {
            Main.player1._keyArr[num] = -1;
         }
         else
         {
            Main.player2._keyArr[num] = -1;
         }
         return -1;
      }
      
      public function KeyYN(keyNum:int) : int
      {
         var xArr:Array = [219,221,220,188,190,191,192,32,186,222,37,38,39,40,48,49,50,51,52,53,54,55,56,57,187,189,96,97,98,99,100,101,102,103,104,105,107,109,111,106,110,34];
         for(var i:* = 0; i < xArr.length; i++)
         {
            if(keyNum == xArr[i])
            {
               return keyNum;
            }
         }
         if(keyNum >= 65 && keyNum <= 90)
         {
            return keyNum;
         }
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"不支持该键位");
         return -1;
      }
      
      private function openPlayerData(playerType:Number = 1) : void
      {
         if(playerType == 1)
         {
            this.p2_btn.gotoAndStop(4);
            this.updateFramep1();
         }
         if(playerType == 2)
         {
            this.p1_btn.gotoAndStop(4);
            this.updateFramep2();
         }
         this.sureMast.visible = false;
      }
      
      private function updateFramep1() : void
      {
         var keyNumX:* = undefined;
         for(var i:uint = 0; i < this.keyNum; i++)
         {
            if(Main.player1._keyArr[i] != -1)
            {
               keyNumX = this.XiuZengKey(1,i);
               this["p1_" + i].gotoAndStop("k" + keyNumX);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
            this.p1ArrKeyArr[i] = false;
            this["p1_" + i].key_mast.gotoAndStop(1);
         }
      }
      
      private function updateFramep2() : void
      {
         var keyNum:* = undefined;
         for(var i:uint = 0; i < keyNum; i++)
         {
            if(Main.player2._keyArr[i] != -1)
            {
               keyNum = this.XiuZengKey(2,i);
               this["p1_" + i].gotoAndStop("k" + keyNumX);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
            this.p2ArrKeyArr[i] = false;
            this["p1_" + i].key_mast.gotoAndStop(1);
         }
      }
      
      private function closeOk() : Boolean
      {
         var i:uint = 0;
         if(Main.player2 == null)
         {
            for(i = 0; i < this.keyNum; i++)
            {
               if(Main.player1._keyArr[i] == -1)
               {
                  return false;
               }
            }
         }
         else
         {
            for(i = 0; i < this.keyNum; i++)
            {
               if(Main.player1._keyArr[i] == -1)
               {
                  return false;
               }
               if(Main.player2._keyArr[i] == -1)
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      private function saveDate() : void
      {
         trace("保存数据");
         if(Main.player2 == null)
         {
            this.sure1 = DeepCopyUtil.clone(Main.player1._keyArr);
         }
         else
         {
            this.sure1 = DeepCopyUtil.clone(Main.player1._keyArr);
            this.sure2 = DeepCopyUtil.clone(Main.player2._keyArr);
         }
         this.visible = false;
      }
      
      private function btnClick(e:BtnEvent) : void
      {
         if(e.target.id == 3)
         {
            if(this.closeOk())
            {
               this.saveDate();
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"键位设置成功,已保存");
            }
            else
            {
               this.sureMast.visible = true;
               trace("键位未设置");
            }
         }
         if(e.target.id == 4)
         {
            if(Main.player2 == null)
            {
               Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
               this.updateFramep1();
            }
            else
            {
               if(this.targetId == 1)
               {
                  Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
                  Main.player2._keyArr = DeepCopyUtil.clone(ysArr2);
                  this.updateFramep1();
               }
               if(this.targetId == 2)
               {
                  Main.player1._keyArr = DeepCopyUtil.clone(ysArr);
                  Main.player2._keyArr = DeepCopyUtil.clone(ysArr2);
                  this.updateFramep2();
               }
            }
         }
      }
      
      private function changeHandle(e:BtnEvent) : void
      {
         this.targetId = e.target.id;
         if(e.target.id == 1)
         {
            this.updateFramep1();
         }
         if(e.target.id == 2)
         {
            this.p1_btn.gotoAndStop(4);
            this.updateFramep2();
         }
      }
      
      private function btnCloseHandle(e:BtnEvent) : void
      {
         if(e.target.id == 1)
         {
            this.visible = false;
            close();
            if(Main.player2 == null)
            {
               Main.player1._keyArr = DeepCopyUtil.clone(this.sure1);
            }
            else
            {
               Main.player1._keyArr = DeepCopyUtil.clone(this.sure1);
               Main.player2._keyArr = DeepCopyUtil.clone(this.sure2);
            }
         }
         if(e.target.id == 2)
         {
            this.sureMast.visible = false;
            trace("确定按钮");
         }
         if(e.target.id == 3)
         {
            this.sureMast.visible = false;
            trace("关闭提示面板");
         }
      }
      
      private function downHandle(e:KeyboardEvent) : void
      {
         var s:uint = 0;
         var numKK:* = undefined;
         for(var i:int = 0; i < this.p1ArrKeyArr.length; i++)
         {
            if(Main.player2 == null)
            {
               if(this.p1ArrKeyArr[i])
               {
                  for(s = 0; s < this.p1ArrKeyArr.length; s++)
                  {
                     if(Main.player1._keyArr[s] == e.keyCode)
                     {
                        Main.player1._keyArr[s] = -1;
                        this["p1_" + s].gotoAndStop("kx");
                     }
                  }
                  Main.player1._keyArr[i] = this.KeyYN(e.keyCode);
                  numKK = Main.player1._keyArr[i];
                  if(numKK == -1)
                  {
                     numKK = 0;
                  }
                  this["p1_" + i].gotoAndStop("k" + numKK);
                  this["p1_" + i].key_mast.gotoAndStop(1);
                  this.p1ArrKeyArr[i] = false;
                  break;
               }
            }
            else if(this.targetId == 1)
            {
               if(this.p1ArrKeyArr[i])
               {
                  for(s = 0; s < this.p1ArrKeyArr.length; s++)
                  {
                     if(Main.player1._keyArr[s] == e.keyCode)
                     {
                        Main.player1._keyArr[s] = -1;
                        this["p1_" + s].gotoAndStop("kx");
                     }
                     if(Main.player2._keyArr[s] == e.keyCode)
                     {
                        trace("按1时 2重复的是:" + e.keyCode);
                        Main.player2._keyArr[s] = -1;
                     }
                  }
                  Main.player1._keyArr[i] = this.KeyYN(e.keyCode);
                  numKK = Main.player1._keyArr[i];
                  if(numKK == -1)
                  {
                     numKK = 0;
                  }
                  this["p1_" + i].gotoAndStop("k" + numKK);
                  this["p1_" + i].key_mast.gotoAndStop(1);
                  this.p1ArrKeyArr[i] = false;
                  break;
               }
            }
            else if(this.targetId == 2)
            {
               if(this.p2ArrKeyArr[i])
               {
                  for(s = 0; s < this.p2ArrKeyArr.length; s++)
                  {
                     if(Main.player2._keyArr[s] == e.keyCode)
                     {
                        Main.player2._keyArr[s] = -1;
                        this["p1_" + s].gotoAndStop("kx");
                     }
                     if(Main.player1._keyArr[s] == e.keyCode)
                     {
                        trace("按2时 1重复的是:" + e.keyCode);
                        Main.player1._keyArr[s] = -1;
                     }
                  }
                  Main.player2._keyArr[i] = this.KeyYN(e.keyCode);
                  numKK = Main.player2._keyArr[i];
                  if(numKK == -1)
                  {
                     numKK = 0;
                  }
                  this["p1_" + i].gotoAndStop("k" + numKK);
                  this["p1_" + i].key_mast.gotoAndStop(1);
                  this.p2ArrKeyArr[i] = false;
                  break;
               }
            }
         }
      }
      
      private function clickHandle(e:MouseEvent) : void
      {
         if(e.target.key_mast)
         {
            if(Main.player2 == null)
            {
               this.p1Click(e.target);
            }
            else if(this.targetId == 1)
            {
               this.p1Click(e.target);
            }
            else if(this.targetId == 2)
            {
               this.p2Click(e.target);
            }
         }
      }
      
      private function p1Click(mc:MovieClip) : void
      {
         for(var i:uint = 0; i < this.keyNum; i++)
         {
            this.p1ArrKeyArr[i] = false;
            this["p1_" + i].key_mast.gotoAndStop(1);
            if(Main.player1._keyArr[i] != -1)
            {
               this["p1_" + i].gotoAndStop("k" + Main.player1._keyArr[i]);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
         }
         mc.gotoAndStop("k0");
         mc.key_mast.gotoAndStop(3);
         this.p1ArrKeyArr[mc.id] = true;
      }
      
      private function p2Click(mc:MovieClip) : void
      {
         for(i = 0; i < this.keyNum; ++i)
         {
            this.p2ArrKeyArr[i] = false;
            this["p1_" + i].key_mast.gotoAndStop(1);
            if(Main.player2._keyArr[i] != -1)
            {
               this["p1_" + i].gotoAndStop("k" + Main.player2._keyArr[i]);
            }
            else
            {
               this["p1_" + i].gotoAndStop("kx");
            }
         }
         mc.gotoAndStop("k0");
         mc.key_mast.gotoAndStop(3);
         this.p2ArrKeyArr[mc.id] = true;
      }
      
      private function outHandle(e:MouseEvent) : void
      {
         if(e.target.key_mast)
         {
            if(Main.player2 == null)
            {
               this.p1out(e.target);
            }
            else if(this.targetId == 1)
            {
               this.p1out(e.target);
            }
            else if(this.targetId == 2)
            {
               this.p2out(e.target);
            }
         }
      }
      
      private function p1out(mc:MovieClip) : void
      {
         if(this.p1ArrKeyArr[mc.id])
         {
            mc.key_mast.gotoAndStop(3);
         }
         else
         {
            mc.key_mast.gotoAndStop(1);
         }
      }
      
      private function p2out(mc:MovieClip) : void
      {
         if(this.p2ArrKeyArr[mc.id])
         {
            mc.key_mast.gotoAndStop(3);
         }
         else
         {
            mc.key_mast.gotoAndStop(1);
         }
      }
      
      private function overHandle(e:MouseEvent) : void
      {
         if(Main.player2 == null)
         {
            this.p1over(e.target);
         }
         else if(this.targetId == 1)
         {
            this.p1over(e.target);
         }
         else if(this.targetId == 2)
         {
            this.p2over(e.target);
         }
      }
      
      private function p1over(mc:MovieClip) : void
      {
         if(this.p1ArrKeyArr[mc.id])
         {
            mc.key_mast.gotoAndStop(3);
         }
         else
         {
            mc.key_mast.gotoAndStop(2);
         }
      }
      
      private function p2over(mc:MovieClip) : void
      {
         if(this.p2ArrKeyArr[mc.id])
         {
            mc.key_mast.gotoAndStop(3);
         }
         else
         {
            mc.key_mast.gotoAndStop(2);
         }
      }
   }
}

class PrivateClass
{
   
   public function PrivateClass()
   {
      super();
   }
}
