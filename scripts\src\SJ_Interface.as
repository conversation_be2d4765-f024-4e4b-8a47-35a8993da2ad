package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class SJ_Interface extends MovieClip
   {
      
      public static var allData:Array;
      
      public static var loadData:ClassLoader;
      
      private static var _this:SJ_Interface;
      
      private static var skin:MovieClip;
      
      public static var myXml:XML = new XML();
      
      public static var loadingOK:int = 0;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "BianQiang.swf";
      
      private static var numX:int = 1;
      
      private static var numX_Max:int = 1;
      
      public function SJ_Interface()
      {
         super();
         Init_XML();
      }
      
      private static function Init_XML() : *
      {
         var property:XML = null;
         var Id:uint = 0;
         var GNId:Number = NaN;
         var GK:Number = NaN;
         var ND:Number = NaN;
         var JM:Number = NaN;
         var LJ:String = null;
         var GM:Number = NaN;
         var MS:String = null;
         var data:Array = null;
         if(!allData)
         {
            allData = new Array();
            myXml = XMLAsset.createXML(Data2.sengJi);
            for each(property in myXml.我要升级)
            {
               Id = uint(property.编号);
               GNId = Number(property.功能ID);
               GK = Number(property.关卡);
               ND = Number(property.难度);
               JM = Number(property.界面);
               LJ = String(property.链接);
               GM = Number(property.购买);
               MS = String(property.描述);
               data = [GNId,GK,ND,JM,LJ,GM,MS];
               allData[Id] = data;
            }
         }
         numX_Max = (allData.length - 1) / 9 + 1;
      }
      
      public static function XX() : *
      {
         Init_XML();
      }
      
      public static function Init_this() : *
      {
         if(!_this)
         {
            _this = new SJ_Interface();
         }
         Main._stage.addChild(_this);
      }
      
      public static function Open() : void
      {
         OpenYN = true;
         Init_this();
         _this.x = _this.y = 0;
         if(skin == null)
         {
            LoadSkin();
            return;
         }
         Show();
      }
      
      public static function Close(e:* = null) : void
      {
         OpenYN = false;
         Init_this();
         _this.x = _this.y = -5000;
      }
      
      public static function LoadSkin() : *
      {
         if(loadingOK == 0)
         {
            loadingOK = 1;
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData,false);
         }
      }
      
      private static function onLoadingOK(e:*) : *
      {
         loadingOK = 2;
         var classRef:Class = loadData.getClass("Skin") as Class;
         skin = new classRef();
         skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
         skin.down_btn.addEventListener(MouseEvent.CLICK,DownFun);
         skin.up_btn.addEventListener(MouseEvent.CLICK,UpFun);
         for(var i:int = 1; i <= 9; i++)
         {
            skin["btn" + i].addEventListener(MouseEvent.CLICK,GoFun);
         }
         _this.addChild(skin);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            Close();
         }
      }
      
      private static function GoFun(e:MouseEvent) : *
      {
         var num:int = int((e.target.name as String).substr(3));
         var num2:int = (numX - 1) * 9 + num;
         gogogo(num2);
      }
      
      private static function DownFun(e:*) : *
      {
         if(numX > 1)
         {
            --numX;
         }
         Show();
      }
      
      private static function UpFun(e:*) : *
      {
         if(numX < numX_Max)
         {
            ++numX;
         }
         Show();
      }
      
      public static function Show() : *
      {
         var i2:int = 0;
         for(var i:int = 1; i <= 9; i++)
         {
            i2 = (numX - 1) * 9 + i;
            if(allData[i2])
            {
               skin["txt" + i].text = allData[i2][6];
               skin["btn" + i].visible = true;
            }
            else
            {
               skin["txt" + i].text = "";
               skin["btn" + i].visible = false;
            }
         }
         skin.num_txt.text = numX + "/" + numX_Max;
      }
      
      private static function gogogo(num:int) : *
      {
         var request:URLRequest = null;
         TiaoShi.txtShow("gogogo num = " + num);
         if(!allData[num])
         {
            TiaoShi.txtShow("allData[" + num + "] 找不到数据!!");
         }
         var type:int = int(allData[num][0]);
         var type1:int = int(allData[num][1]);
         var type2:int = int(allData[num][2]);
         var type3:int = int(allData[num][3]);
         var type4:String = allData[num][4];
         var type5:int = int(allData[num][5]);
         if(type == 1)
         {
            if(Main.guanKa[type1] >= type2)
            {
               GameData.winYN = false;
               Main.gameNum.setValue(type1);
               Main.gameNum2.setValue(1);
               GameData.gameLV = uint(type2);
               Main._this.Loading();
               Close();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"关卡未完成,无法进入");
            }
         }
         else if(type != 2)
         {
            if(type == 3)
            {
               request = new URLRequest(type4);
               navigateToURL(request,"_blank");
            }
            else if(type == 4)
            {
            }
         }
      }
   }
}

