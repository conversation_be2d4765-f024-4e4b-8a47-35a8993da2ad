package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.repository.equip.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4222")]
   public class ChongZhi_Interface2 extends MovieClip
   {
      
      public static var _this:ChongZhi_Interface2;
      
      public static var openYn:Boolean = false;
      
      public static var buyOk:Boolean = false;
      
      public static var openNum:int = 0;
      
      public static var selYN:Boolean = false;
      
      public static var time1:int = 20231127;
      
      public static var time2:int = 20231127;
      
      public static var time3:int = 20231127;
      
      public static var time4:int = 20231127;
      
      public static var time5:int = 20231127;
      
      public static var time6:int = 20231127;
      
      public static var time7:int = 20231127;
      
      public static var time8:int = 20241215;
      
      public static var shopArr:Array = [[],["活动日期: 至2023年12月3日",599,287,14655,14661],["活动日期: 至2023年11月12日",599,287,14775,14781],["活动日期: 至2023年8月13日",599,287,100779,100785],["活动日期: 至2023年2月5日",499,265,14757,14763],["活动日期: 至2023年10月22日",799,288,100791,100797],["活动日期: 至2023年7月16日",599,289,100803,100809],["活动日期: 至2024年2月18日",799,288,100837,100843],["活动日期: 至2024年12月15日",499,306,63509]];
      
      public var XX_mc:MovieClip;
      
      public var close_btn:SimpleButton;
      
      public var go_btn:SimpleButton;
      
      public var lingqu_btn:SimpleButton;
      
      public var skin:MovieClip;
      
      public function ChongZhi_Interface2()
      {
         super();
         _this = this;
         var classX:Class = NewLoad.chongZhiData.getClass("mc2") as Class;
         this.skin = new classX();
         _this.XX_mc.addChild(this.skin);
         _this.XX_mc.x = 136;
         _this.XX_mc.y = 47;
         this.close_btn.addEventListener(MouseEvent.CLICK,Close);
         this.lingqu_btn.addEventListener(MouseEvent.CLICK,lingQuFun);
      }
      
      public static function Open(type:int = 0) : *
      {
         openNum = type;
         if(!_this)
         {
            _this = new ChongZhi_Interface2();
         }
         _this.addEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.x = _this.y = 0;
         _this.skin.gotoAndStop(openNum);
         var strXX:String = ChongZhi_Interface2["time" + type];
         var str1:String = strXX.substr(0,4);
         var str2:String = strXX.substr(4,2);
         var str3:String = strXX.substr(6,2);
         _this.skin.time_txt.text = "活动日期: 至" + str1 + "年" + str2 + "月" + str3 + "日";
         Main._this.addChild(_this);
         Show();
      }
      
      public static function onENTER_FRAME(e:*) : *
      {
         if(!openYn && Main.gameNum.getValue() == 0)
         {
            openYn = true;
            _this.x = _this.y = 0;
         }
      }
      
      public static function Close(e:* = null) : *
      {
         var xxx:ChongZhi_Interface2 = null;
         if(!_this)
         {
            xxx = new ChongZhi_Interface2();
            Main._this.addChild(xxx);
         }
         _this.removeEventListener(Event.ENTER_FRAME,onENTER_FRAME);
         _this.x = _this.y = -5000;
      }
      
      public static function lingQuFun(e:* = null) : *
      {
         if(openNum == 8 && Main.player1.getBag().backOtherBagNum() >= 1)
         {
            if(Shop4399.moneyAll.getValue() >= shopArr[openNum][1])
            {
               Api_4399_All.BuyObj(shopArr[openNum][2]);
               buyOk = true;
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
               buyOk = false;
            }
         }
         else if(Main.player1.getBag().backequipBagNum() >= 2)
         {
            TiaoShi.txtShow("时装购买:" + openNum + ">>>>" + shopArr[openNum]);
            if(Shop4399.moneyAll.getValue() >= shopArr[openNum][1])
            {
               Api_4399_All.BuyObj(shopArr[openNum][2]);
               buyOk = true;
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点卷不足！");
               buyOk = false;
            }
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足,请整理背包!");
         }
         Show();
      }
      
      public static function Buy_OK() : *
      {
         if(buyOk)
         {
            if(openNum == 8)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(shopArr[openNum][3]));
            }
            else
            {
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(shopArr[openNum][3]));
               Main.player1.getBag().addEquipBag(EquipFactory.createEquipByID(shopArr[openNum][4]));
            }
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功!");
            buyOk = false;
            Show();
         }
      }
      
      public static function Show() : *
      {
         _this.lingqu_btn.visible = false;
         _this.lingqu_btn.x = _this.lingqu_btn.y = -5000;
         if(!buyOk)
         {
            _this.lingqu_btn.visible = true;
            _this.lingqu_btn.x = 267;
            _this.lingqu_btn.y = 340;
         }
      }
   }
}

