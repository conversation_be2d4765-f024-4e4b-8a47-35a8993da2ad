package src._data
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import src.*;
   import src.tool.*;
   
   public class ShopFactory
   {
      
      public static var myXml:XML = new XML();
      
      public static var AllData:Array = new Array();
      
      public function ShopFactory()
      {
         super();
      }
      
      public static function creatFactory() : *
      {
         myXml = XMLAsset.createXML(Data2.shopData);
         InitDataX();
      }
      
      private static function InitDataX() : *
      {
         var property:XML = null;
         var Xid:uint = 0;
         for each(property in myXml.点券商城)
         {
            Xid = uint(property.排序);
            Shop4399.ShopArr[Xid] = new Array();
            Shop4399.ShopArr[Xid][0] = VT.createVT(Number(property.点券));
            Shop4399.ShopArr[Xid][1] = String(property.类型);
            Shop4399.ShopArr[Xid][2] = VT.createVT(Number(property.物品ID));
            Shop4399.ShopArr[Xid][3] = int(property.图标);
            Shop4399.ShopArr[Xid][4] = String(property.说明);
            Shop4399.ShopArr[Xid][5] = String(property.名称);
            Shop4399.ShopArr[Xid][6] = VT.createVT(property.商城ID);
            Shop4399.ShopArr[Xid][7] = VT.createVT(uint(property.ID));
            Shop4399.ShopArr[Xid][8] = uint(property.品质);
         }
      }
      
      public static function GetObjData_AllNum() : uint
      {
         var num:uint = 0;
         for(var i:uint = 1; i < Shop4399.ShopArr.length; i++)
         {
            if(Shop4399.ShopArr[i])
            {
               num++;
            }
         }
         return num;
      }
      
      public static function GetObjData(id:uint, num:uint = 1) : Object
      {
         var dataObj:Object = null;
         for(var i:uint = 1; i < Shop4399.ShopArr.length; i++)
         {
            if(Boolean(Shop4399.ShopArr[i]) && (Shop4399.ShopArr[i][7] as VT).getValue() == id)
            {
               dataObj = new Object();
               dataObj.propId = "" + (Shop4399.ShopArr[i][6] as VT).getValue();
               dataObj.count = num;
               dataObj.price = (Shop4399.ShopArr[i][0] as VT).getValue();
               dataObj.idx = Main.saveNum;
               dataObj.tag = "BuyObj_" + dataObj.propId;
               TiaoShi.txtShow(">>>>>>>>>>>>>>>> GetObjData id = " + dataObj.propId + ", dataObj.price = " + dataObj.price);
               return dataObj;
            }
         }
         NewMC.Open("文字提示",Main._stage,400,400,60,0,true,1,"商城数据出错" + id);
         return null;
      }
   }
}

