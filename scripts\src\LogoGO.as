package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol7082")]
   public class LogoGO extends MovieClip
   {
      
      public static var LogoGONum:int;
      
      internal var time:int = 0;
      
      public function LogoGO()
      {
         super();
         addFrameScript(4,this.frame5,45,this.frame46);
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         addEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
      }
      
      private function onENTER_FRAME(e:*) : *
      {
         ++this.time;
         gotoAndStop(5);
         if(this.time > 35)
         {
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            removeEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
            gotoAndPlay(33);
            LogoGONum = 1;
         }
      }
      
      private function onMOUSE_MOVE(e:*) : *
      {
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         removeEventListener(MouseEvent.MOUSE_MOVE,this.onMOUSE_MOVE);
         gotoAndPlay(6);
         LogoGONum = 2;
      }
      
      internal function frame5() : *
      {
         stop();
      }
      
      internal function frame46() : *
      {
         stop();
      }
   }
}

