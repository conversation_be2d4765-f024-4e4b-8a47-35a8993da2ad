package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import src.*;
   
   public class upstarPanel extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var usPanel:MovieClip;
      
      public static var usp:upstarPanel;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var starNum:Number;
      
      public static var oldNum:Number;
      
      public static var strS:String;
      
      private static var loadData:ClassLoader;
      
      public static var saveOk:Boolean;
      
      public static var yeshu:Number = 0;
      
      public static var selbool:Boolean = false;
      
      public static var isPOne:Boolean = true;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_US_v1900.swf";
      
      public function upstarPanel()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!usPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = usPanel.getChildIndex(usPanel["e" + i]);
            mm.x = usPanel["e" + i].x;
            mm.y = usPanel["e" + i].y;
            mm.name = "e" + i;
            usPanel.removeChild(usPanel["e" + i]);
            usPanel["e" + i] = mm;
            usPanel.addChild(mm);
            usPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = usPanel.getChildIndex(usPanel["s" + i]);
            mm.x = usPanel["s" + i].x;
            mm.y = usPanel["s" + i].y;
            mm.name = "s" + i;
            usPanel.removeChild(usPanel["s" + i]);
            usPanel["s" + i] = mm;
            usPanel.addChild(mm);
            usPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = usPanel.getChildIndex(usPanel["select"]);
         mm.x = usPanel["select"].x;
         mm.y = usPanel["select"].y;
         mm.name = "select";
         usPanel.removeChild(usPanel["select"]);
         usPanel["select"] = mm;
         usPanel.addChild(mm);
         usPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("USShow") as Class;
         usPanel = new classRef();
         usp.addChild(usPanel);
         InitIcon();
         if(OpenYN)
         {
            open(isPOne,strS,starNum,oldNum);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         usp = new upstarPanel();
         LoadSkin();
         Main._stage.addChild(usp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         usp = new upstarPanel();
         Main._stage.addChild(usp);
         OpenYN = false;
      }
      
      public static function open(pp:Boolean, str:String, star:Number, num:int) : void
      {
         Main.allClosePanel();
         if(usPanel)
         {
            Main.stopXX = true;
            usp.x = 0;
            usp.y = 0;
            isPOne = pp;
            usPanel["txt"].text = str;
            starNum = star - 1;
            oldNum = num;
            addListenerP1();
            Main._stage.addChild(usp);
            usp.visible = true;
            usPanel["load_mc"].visible = false;
         }
         else
         {
            starNum = star;
            oldNum = num;
            isPOne = pp;
            strS = str;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(usPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            usp.visible = false;
            selbool = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         usPanel["us_btn"].addEventListener(MouseEvent.CLICK,doUS);
         usPanel["close"].addEventListener(MouseEvent.CLICK,closeUS);
         for(var i:uint = 0; i < 24; i++)
         {
            usPanel["e" + i].mouseChildren = false;
            usPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            usPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            usPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            usPanel["s" + i].mouseChildren = false;
            usPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            usPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            usPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         usPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         usPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         usPanel["select"].gotoAndStop(1);
         usPanel["select"].visible = false;
         showAll();
         usPanel["chose"].visible = false;
      }
      
      public static function Star6_upYN(eq:Equip) : Boolean
      {
         var fXX:int = 0;
         if(starNum >= 5)
         {
            fXX = eq.getFrame();
            if((fXX >= 524 && fXX <= 527 || fXX >= 529 && fXX <= 532) && eq.getStar() >= 5 && eq.getStar() < 10)
            {
               return true;
            }
            return false;
         }
         if(eq.getStar() == starNum)
         {
            return true;
         }
         return false;
      }
      
      public static function showAll() : *
      {
         var eq:Equip = null;
         var i:uint = 0;
         var xxx:int = 0;
         var yeshuNum:int = yeshu + 1;
         usPanel["yeshu_txt"].text = yeshuNum + "/2";
         var pX:PlayerData = isPOne ? Main.player1 : Main.player2;
         for(i = 0; i < 24; i++)
         {
            usPanel["e" + i].t_txt.text = "";
            eq = pX.getBag().getEquipFromBag(i + yeshu * 24);
            if(eq != null)
            {
               if(Star6_upYN(eq))
               {
                  usPanel["e" + i].gotoAndStop(eq.getFrame());
                  usPanel["e" + i].visible = true;
               }
               else
               {
                  usPanel["e" + i].visible = false;
               }
            }
            else
            {
               usPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            usPanel["s" + i].t_txt.text = "";
            xxx = int(i);
            if((i == 0 || i == 1 || i == 3 || i == 4) && Main.water.getValue() != 1)
            {
               xxx += 8;
            }
            eq = pX.getEquipSlot().getEquipFromSlot(xxx);
            if(eq != null)
            {
               if(Star6_upYN(eq))
               {
                  usPanel["s" + i].gotoAndStop(eq.getFrame());
                  usPanel["s" + i].visible = true;
               }
               else
               {
                  usPanel["s" + i].visible = false;
               }
            }
            else
            {
               usPanel["s" + i].visible = false;
            }
         }
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         usPanel["close"].removeEventListener(MouseEvent.CLICK,closeUS);
         usPanel["us_btn"].removeEventListener(MouseEvent.CLICK,doUS);
         for(i = 0; i < 24; i++)
         {
            usPanel["e" + i].mouseChildren = false;
            usPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            usPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            usPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            usPanel["s" + i].mouseChildren = false;
            usPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            usPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            usPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         usPanel["up_btn"].removeEventListener(MouseEvent.CLICK,upGo);
         usPanel["down_btn"].removeEventListener(MouseEvent.CLICK,downGo);
      }
      
      public static function closeUS(e:*) : *
      {
         close();
      }
      
      public static function upGo(e:*) : *
      {
         yeshu = 0;
         showAll();
      }
      
      public static function downGo(e:*) : *
      {
         yeshu = 1;
         showAll();
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         usPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(isPOne)
         {
            if(str == "e")
            {
               overNum += 24 * yeshu;
               if(Main.player1.getBag().getEquipFromBag(overNum) != null)
               {
                  itemsTooltip.equipTooltip(Main.player1.getBag().getEquipFromBag(overNum),1);
               }
            }
            else
            {
               itemsTooltip.slotTooltip(overNum,Main.player1.getEquipSlot());
            }
         }
         else if(str == "e")
         {
            overNum += 24 * yeshu;
            if(Main.player2.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(Main.player2.getBag().getEquipFromBag(overNum),2);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(overNum,Main.player2.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = usPanel.mouseX + 10;
         itemsTooltip.y = usPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      private static function doUS(e:*) : *
      {
         if(selbool == false)
         {
            return;
         }
         var pdX:PlayerData = isPOne ? Main.player1 : Main.player2;
         if(nameStr == "e")
         {
            pdX.getBag().addEquipBag(pdX.getBag().delEquip(clickNum).upStarEquip());
         }
         else
         {
            pdX.getEquipSlot().addToSlot(pdX.getEquipSlot().delSlot(clickNum).upStarEquip(),clickNum);
         }
         pdX.getBag().delGem(oldNum,1);
         usPanel["load_mc"].visible = true;
         saveOk = true;
         Main.Save();
      }
      
      public static function UpOK_save() : *
      {
         if(saveOk)
         {
            saveOk = false;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"使用成功");
            usPanel["load_mc"].visible = false;
            close();
         }
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         selbool = true;
         clickObj = e.target as MovieClip;
         usPanel["chose"].visible = true;
         usPanel["chose"].x = clickObj.x - 2;
         usPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         if(nameStr != "e" && (clickNum == 0 || clickNum == 1 || clickNum == 3 || clickNum == 4) && Main.water.getValue() != 1)
         {
            clickNum += 8;
         }
         if(nameStr == "e")
         {
            clickNum += 24 * yeshu;
         }
         usPanel["select"].gotoAndStop(clickObj.currentFrame);
         usPanel["select"].visible = true;
      }
   }
}

