package com.hotpoint.braveManIII.views.itemsPanel
{
   import flash.utils.*;
   import src.*;
   
   public class BagItemsShow
   {
      
      internal var timeOut:int;
      
      public function BagItemsShow()
      {
         super();
      }
      
      public static function allHide() : void
      {
         for(var i:uint = 0; i < 24; i++)
         {
            ItemsPanel.itemsPanel["s1_" + i].t_txt.text = "";
            ItemsPanel.itemsPanel["s1_" + i].t_txt.visible = true;
            ItemsPanel.itemsPanel["s1_" + i].visible = false;
            ItemsPanel.itemsPanel["s1_" + i]["diKuang"].visible = false;
         }
         for(i = 0; i < 8; i++)
         {
            ItemsPanel.itemsPanel["z1_" + i].t_txt.text = "";
            ItemsPanel.itemsPanel["z1_" + i].visible = false;
            ItemsPanel.itemsPanel["k1_" + i].visible = false;
            ItemsPanel.itemsPanel["z1_" + i]["diKuang"].visible = false;
         }
         ItemsPanel.itemsPanel["g1_0"].visible = false;
         ItemsPanel.itemsPanel["g1_1"].visible = false;
      }
      
      public static function skillSlotShow() : void
      {
         for(var i:uint = 0; i < 2; i++)
         {
            if(ItemsPanel.myplayer.data.getEquipSkillSlot().getGemFromSkillSlot(i) != null)
            {
               ItemsPanel.itemsPanel["g1_" + i].visible = true;
            }
            else
            {
               ItemsPanel.itemsPanel["g1_" + i].visible = false;
            }
         }
      }
      
      public static function clearTimeout() : void
      {
         if(timeOut > 0)
         {
            clearTimeout(timeOut);
         }
      }
      
      public static function informationShow() : void
      {
         timeOut = setTimeout(show1P,100);
      }
      
      private static function showLV(level:Number) : *
      {
         var str:String = null;
         var left:int = 0;
         var right:int = 0;
         if(level < 10)
         {
            ItemsPanel.itemsPanel["shiwei"].gotoAndStop(level);
            ItemsPanel.itemsPanel["gewei"].visible = false;
         }
         else
         {
            ItemsPanel.itemsPanel["gewei"].visible = true;
            str = level.toString();
            left = int(str.substring(0,1));
            right = int(str.substring(1,2));
            ItemsPanel.itemsPanel["shiwei"].gotoAndStop(left);
            if(right == 0)
            {
               ItemsPanel.itemsPanel["gewei"].gotoAndStop(10);
            }
            else
            {
               ItemsPanel.itemsPanel["gewei"].gotoAndStop(right);
            }
         }
      }
      
      public static function zyShow(p1p2:int) : String
      {
         for(var i:int = 0; i < 5; i++)
         {
            if(i == 4)
            {
               ItemsPanel.itemsPanel["ZY_txt"].text = "新手";
               return "新手";
            }
            if(Main["player" + p1p2]._transferArr[i])
            {
               if(i == 0)
               {
                  ItemsPanel.itemsPanel["ZY_txt"].text = "杀戮战神";
                  return "杀戮战神";
               }
               if(i == 1)
               {
                  ItemsPanel.itemsPanel["ZY_txt"].text = "汲魂术士";
                  return "汲魂术士";
               }
               if(i == 2)
               {
                  ItemsPanel.itemsPanel["ZY_txt"].text = "毁灭拳神";
                  return "毁灭拳神";
               }
               if(i == 3)
               {
                  ItemsPanel.itemsPanel["ZY_txt"].text = "暗影杀手";
                  return "暗影杀手";
               }
               break;
            }
         }
      }
      
      public static function QiangHuaJiaChengP1() : *
      {
         var sLV:int = int(ItemsPanel.myplayer.data.getEquipSlot().getSuitStrength());
         if(sLV < 4)
         {
            ItemsPanel.itemsPanel["strengthen"].gotoAndStop(1);
            return;
         }
         if(sLV > 10)
         {
            sLV = 10;
         }
         ItemsPanel.itemsPanel["strengthen"].gotoAndStop(sLV - 2);
      }
      
      public static function QiangHuaJiaChengP2() : *
      {
         var sLV:int = int(Main.player2.getEquipSlot().getSuitStrength());
         if(sLV < 4)
         {
            ItemsPanel.itemsPanel["strengthen"].gotoAndStop(1);
            return;
         }
         if(sLV > 10)
         {
            sLV = 10;
         }
         ItemsPanel.itemsPanel["strengthen"].gotoAndStop(sLV - 2);
      }
      
      public static function show1P() : *
      {
         ItemsPanel.myplayer.LoadAll_ZB_Skill();
         if(ItemsPanel.myplayer.data.skinArr[ItemsPanel.myplayer.data.skinNum] == 0)
         {
            ItemsPanel.itemsPanel["p_mc"].gotoAndStop(1);
         }
         else if(ItemsPanel.myplayer.data.skinArr[ItemsPanel.myplayer.data.skinNum] == 1)
         {
            ItemsPanel.itemsPanel["p_mc"].gotoAndStop(3);
         }
         else if(ItemsPanel.myplayer.data.skinArr[ItemsPanel.myplayer.data.skinNum] == 2)
         {
            ItemsPanel.itemsPanel["p_mc"].gotoAndStop(2);
         }
         else if(ItemsPanel.myplayer.data.skinArr[ItemsPanel.myplayer.data.skinNum] == 3)
         {
            ItemsPanel.itemsPanel["p_mc"].gotoAndStop(4);
         }
         if(ItemsPanel.yeshu == 0)
         {
            ItemsPanel.itemsPanel["yeshu_1"].gotoAndStop(2);
            ItemsPanel.itemsPanel["yeshu_2"].gotoAndStop(1);
         }
         else
         {
            ItemsPanel.itemsPanel["yeshu_1"].gotoAndStop(1);
            ItemsPanel.itemsPanel["yeshu_2"].gotoAndStop(2);
         }
         zyShow(1);
         ItemsPanel.itemsPanel["id_txt"].htmlText = Main.logName;
         ItemsPanel.itemsPanel["name_txt"].htmlText = Main.logName2;
         ItemsPanel.itemsPanel["mkPoint"].text = int(ItemsPanel.myplayer.use_fangyu2.getValue());
         ItemsPanel.itemsPanel["pmPoint"].text = int(ItemsPanel.myplayer.use_gongji2.getValue());
         ItemsPanel.itemsPanel["hpPoint"].text = int(ItemsPanel.myplayer.use_hp_Max.getValue());
         ItemsPanel.itemsPanel["mpPoint"].text = int(ItemsPanel.myplayer.use_mp_Max.getValue());
         ItemsPanel.itemsPanel["attackPoint"].text = int(ItemsPanel.myplayer.use_gongji.getValue());
         ItemsPanel.itemsPanel["defensePoint"].text = int(ItemsPanel.myplayer.use_fangyu.getValue());
         ItemsPanel.itemsPanel["critPoint"].text = Math.pow(Number(ItemsPanel.myplayer.use_baoji.getValue() / 70),0.8).toFixed(1) + "%";
         ItemsPanel.itemsPanel["duckPoint"].text = Math.pow(Number(ItemsPanel.myplayer.use_sanbi.getValue() / 100),0.8).toFixed(1) + "%";
         ItemsPanel.itemsPanel["gold1"].text = ItemsPanel.myplayer.data.getGold();
         ItemsPanel.itemsPanel["djPoint"].text = Shop4399.moneyAll.getValue();
         ItemsPanel.itemsPanel["kill_txt"].text = ItemsPanel.myplayer.data.killPoint.getValue();
         ItemsPanel.itemsPanel["expPoint"].text = ItemsPanel.myplayer.data.getEXP() + " / " + ItemsPanel.myplayer.nextExp.getValue();
         ItemsPanel.itemsPanel["jyt"]["zd"].scaleX = ItemsPanel.myplayer.data.getEXP() / ItemsPanel.myplayer.nextExp.getValue();
         if(ItemsPanel.myplayer.data.getTitleSlot().getTitleView())
         {
            ItemsPanel.itemsPanel["chenghao"].gotoAndStop(ItemsPanel.myplayer.data.getTitleSlot().getTitleView().getFrame());
         }
         else
         {
            ItemsPanel.itemsPanel["chenghao"].gotoAndStop(1);
         }
         if(ItemsPanel.myplayer.data.playerJL_Data)
         {
            ItemsPanel.itemsPanel["jingling"].gotoAndStop(ItemsPanel.myplayer.data.playerJL_Data.getFrame() + 1);
         }
         else
         {
            ItemsPanel.itemsPanel["jingling"].gotoAndStop(1);
         }
         if(ItemsPanel.myplayer.data.getElvesSlot().backElvesNum() == 0)
         {
            ItemsPanel.itemsPanel["jingling_btn"].visible = true;
         }
         else
         {
            ItemsPanel.itemsPanel["jingling_btn"].visible = true;
         }
         showLV(ItemsPanel.myplayer.data.getLevel());
         QiangHuaJiaChengP1();
      }
      
      public static function equipShow() : void
      {
         var i:uint = 0;
         ItemsPanel.myplayer.data.getBag().zhengliBag();
         for(i = 0; i < 24; i++)
         {
            ItemsPanel.itemsPanel["s1_" + i].t_txt.text = "";
            if(ItemsPanel.myplayer.data.getBag().getEquipFromBag(i + ItemsPanel.yeshu * 24))
            {
               ItemsPanel.itemsPanel["s1_" + i].gotoAndStop(ItemsPanel.myplayer.data.getBag().getEquipFromBag(i + ItemsPanel.yeshu * 24).getFrame());
               ItemsPanel.itemsPanel["s1_" + i].visible = true;
            }
            else
            {
               ItemsPanel.itemsPanel["s1_" + i].visible = false;
            }
         }
         if(ItemsPanel.yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               ItemsPanel.itemsPanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               ItemsPanel.itemsPanel["bagLock" + i].visible = true;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitE() >= 28)
            {
               ItemsPanel.itemsPanel["bagLock0"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitE() >= 32)
            {
               ItemsPanel.itemsPanel["bagLock1"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitE() >= 36)
            {
               ItemsPanel.itemsPanel["bagLock2"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitE() >= 40)
            {
               ItemsPanel.itemsPanel["bagLock3"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitE() >= 44)
            {
               ItemsPanel.itemsPanel["bagLock4"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitE() >= 48)
            {
               ItemsPanel.itemsPanel["bagLock5"].visible = false;
            }
         }
      }
      
      public static function gemShow() : void
      {
         var i:uint = 0;
         ItemsPanel.myplayer.data.getBag().zhengliBagG();
         for(i = 0; i < 24; i++)
         {
            ItemsPanel.itemsPanel["s1_" + i].t_txt.text = "";
            if(ItemsPanel.myplayer.data.getBag().getGemFromBag(i + ItemsPanel.yeshu * 24) != null)
            {
               ItemsPanel.itemsPanel["s1_" + i].gotoAndStop(ItemsPanel.myplayer.data.getBag().getGemFromBag(i + ItemsPanel.yeshu * 24).getFrame());
               ItemsPanel.itemsPanel["s1_" + i].visible = true;
               if(ItemsPanel.myplayer.data.getBag().getGemFromBag(i + ItemsPanel.yeshu * 24).getIsPile() == true)
               {
                  ItemsPanel.itemsPanel["s1_" + i].t_txt.text = ItemsPanel.myplayer.data.getBag().getGemFromBag(i + ItemsPanel.yeshu * 24).getTimes();
               }
            }
            else
            {
               ItemsPanel.itemsPanel["s1_" + i].visible = false;
            }
         }
         if(ItemsPanel.yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               ItemsPanel.itemsPanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               ItemsPanel.itemsPanel["bagLock" + i].visible = true;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitG() >= 28)
            {
               ItemsPanel.itemsPanel["bagLock0"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitG() >= 32)
            {
               ItemsPanel.itemsPanel["bagLock1"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitG() >= 36)
            {
               ItemsPanel.itemsPanel["bagLock2"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitG() >= 40)
            {
               ItemsPanel.itemsPanel["bagLock3"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitG() >= 44)
            {
               ItemsPanel.itemsPanel["bagLock4"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitG() >= 48)
            {
               ItemsPanel.itemsPanel["bagLock5"].visible = false;
            }
         }
      }
      
      public static function suppliesShow() : void
      {
         var i:uint = 0;
         if(ItemsPanel.myplayer)
         {
            ItemsPanel.myplayer.data.getBag().zhengliBagS();
            if(ItemsPanel.boolFlag == false)
            {
               return;
            }
            ItemsPanel.menuTooltip.visible = false;
            for(i = 0; i < 24; i++)
            {
               ItemsPanel.itemsPanel["s1_" + i].t_txt.text = "";
               if(ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(i + ItemsPanel.yeshu * 24) != null)
               {
                  ItemsPanel.itemsPanel["s1_" + i].gotoAndStop(ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(i + ItemsPanel.yeshu * 24).getFrame());
                  ItemsPanel.itemsPanel["s1_" + i].visible = true;
                  if(ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(i + ItemsPanel.yeshu * 24).getTimes() > 1)
                  {
                     ItemsPanel.itemsPanel["s1_" + i].t_txt.text = ItemsPanel.myplayer.data.getBag().getSuppliesFromBag(i + ItemsPanel.yeshu * 24).getTimes();
                  }
               }
               else
               {
                  ItemsPanel.itemsPanel["s1_" + i].visible = false;
               }
            }
            if(ItemsPanel.yeshu == 0)
            {
               for(i = 0; i < 6; i++)
               {
                  ItemsPanel.itemsPanel["bagLock" + i].visible = false;
               }
            }
            else
            {
               for(i = 0; i < 6; i++)
               {
                  ItemsPanel.itemsPanel["bagLock" + i].visible = true;
               }
               if(ItemsPanel.myplayer.data.getBag().getLimitS() >= 28)
               {
                  ItemsPanel.itemsPanel["bagLock0"].visible = false;
               }
               if(ItemsPanel.myplayer.data.getBag().getLimitS() >= 32)
               {
                  ItemsPanel.itemsPanel["bagLock1"].visible = false;
               }
               if(ItemsPanel.myplayer.data.getBag().getLimitS() >= 36)
               {
                  ItemsPanel.itemsPanel["bagLock2"].visible = false;
               }
               if(ItemsPanel.myplayer.data.getBag().getLimitS() >= 40)
               {
                  ItemsPanel.itemsPanel["bagLock3"].visible = false;
               }
               if(ItemsPanel.myplayer.data.getBag().getLimitS() >= 44)
               {
                  ItemsPanel.itemsPanel["bagLock4"].visible = false;
               }
               if(ItemsPanel.myplayer.data.getBag().getLimitS() >= 48)
               {
                  ItemsPanel.itemsPanel["bagLock5"].visible = false;
               }
            }
         }
      }
      
      public static function slotShow() : void
      {
         var k:int = 0;
         for(var i:uint = 0; i < 8; i++)
         {
            k = int(i);
            if(Main.water.getValue() != 1 && (i == 0 || i == 1 || i == 3 || i == 4))
            {
               k += 8;
            }
            ItemsPanel.itemsPanel["z1_" + i].t_txt.text = "";
            if(ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(k) != null)
            {
               ItemsPanel.itemsPanel["z1_" + i].gotoAndStop(ItemsPanel.myplayer.data.getEquipSlot().getEquipFromSlot(k).getFrame());
               ItemsPanel.itemsPanel["z1_" + i].visible = true;
               ItemsPanel.itemsPanel["z1_" + i]["diKuang"].visible = true;
            }
            else
            {
               ItemsPanel.itemsPanel["z1_" + i].visible = false;
            }
         }
      }
      
      public static function badgeSlotShow() : void
      {
         for(var i:uint = 0; i < 6; i++)
         {
            if(ItemsPanel.myplayer.data.getBadgeSlot().getBadgeFromSlot(i) != null)
            {
               ItemsPanel.itemsPanel["hz" + i].gotoAndStop(ItemsPanel.myplayer.data.getBadgeSlot().getBadgeFromSlot(i).getFrame());
               ItemsPanel.itemsPanel["hz" + i].visible = true;
            }
            else
            {
               ItemsPanel.itemsPanel["hz" + i].visible = false;
            }
         }
      }
      
      public static function questShow() : void
      {
         var i:uint = 0;
         for(i = 0; i < 24; i++)
         {
            ItemsPanel.itemsPanel["s1_" + i].t_txt.text = "";
            if(ItemsPanel.myplayer.data.getBag().getQuestFromBag(i) != null)
            {
               ItemsPanel.itemsPanel["s1_" + i].gotoAndStop(ItemsPanel.myplayer.data.getBag().getQuestFromBag(i).getFrame());
               ItemsPanel.itemsPanel["s1_" + i].visible = true;
               if(ItemsPanel.myplayer.data.getBag().getQuestFromBag(i).isMany() == true)
               {
                  ItemsPanel.itemsPanel["s1_" + i].t_txt.text = ItemsPanel.myplayer.data.getBag().getQuestFromBag(i).getTimes();
               }
            }
            else
            {
               ItemsPanel.itemsPanel["s1_" + i].visible = false;
            }
         }
         for(i = 0; i < 6; i++)
         {
            ItemsPanel.itemsPanel["bagLock" + i].visible = false;
         }
      }
      
      public static function otherobjShow() : void
      {
         var i:uint = 0;
         ItemsPanel.myplayer.data.getBag().zhengliBagO();
         for(i = 0; i < 24; i++)
         {
            ItemsPanel.itemsPanel["s1_" + i].t_txt.text = "";
            if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(i + ItemsPanel.yeshu * 24) != null)
            {
               ItemsPanel.itemsPanel["s1_" + i].gotoAndStop(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(i + ItemsPanel.yeshu * 24).getFrame());
               ItemsPanel.itemsPanel["s1_" + i].visible = true;
               if(ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(i + ItemsPanel.yeshu * 24).getTimes() > 1)
               {
                  ItemsPanel.itemsPanel["s1_" + i].t_txt.text = ItemsPanel.myplayer.data.getBag().getOtherobjFromBag(i + ItemsPanel.yeshu * 24).getTimes();
               }
            }
            else
            {
               ItemsPanel.itemsPanel["s1_" + i].visible = false;
            }
         }
         if(ItemsPanel.yeshu == 0)
         {
            for(i = 0; i < 6; i++)
            {
               ItemsPanel.itemsPanel["bagLock" + i].visible = false;
            }
         }
         else
         {
            for(i = 0; i < 6; i++)
            {
               ItemsPanel.itemsPanel["bagLock" + i].visible = true;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitO() >= 28)
            {
               ItemsPanel.itemsPanel["bagLock0"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitO() >= 32)
            {
               ItemsPanel.itemsPanel["bagLock1"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitO() >= 36)
            {
               ItemsPanel.itemsPanel["bagLock2"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitO() >= 40)
            {
               ItemsPanel.itemsPanel["bagLock3"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitO() >= 44)
            {
               ItemsPanel.itemsPanel["bagLock4"].visible = false;
            }
            if(ItemsPanel.myplayer.data.getBag().getLimitO() >= 48)
            {
               ItemsPanel.itemsPanel["bagLock5"].visible = false;
            }
         }
      }
   }
}

