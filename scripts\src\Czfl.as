package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4225")]
   public class Czfl extends MovieClip
   {
      
      public static var _this:Czfl;
      
      public var close_btn:SimpleButton;
      
      public var go_btn:SimpleButton;
      
      public function Czfl()
      {
         super();
         this["close_btn"].addEventListener(MouseEvent.CLICK,this.onclose);
         this["go_btn"].addEventListener(MouseEvent.CLICK,this.OpenWEB);
      }
      
      public static function Open() : *
      {
         if(!_this)
         {
            _this = new Czfl();
         }
         Main._this.addChild(_this);
         _this.x = _this.y = 0;
      }
      
      public function onclose(e:*) : *
      {
         this.visible = false;
         this.x = this.y = -5000;
      }
      
      public function OpenWEB(e:*) : *
      {
         Main.ChongZhi();
         _this.x = _this.y = -5000;
      }
   }
}

