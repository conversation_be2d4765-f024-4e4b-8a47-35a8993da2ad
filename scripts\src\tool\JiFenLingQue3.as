package src.tool
{
   import flash.display.*;
   import flash.events.*;
   import flash.net.*;
   import flash.text.*;
   import src.*;
   
   public class JiFenLingQue3
   {
      
      public static var urlLoader:URLLoader;
      
      public static var saveTime:String = "2014/7/13";
      
      public static var gameTime:int = 0;
      
      public function JiFenLingQue3()
      {
         super();
      }
      
      public static function Post(go:Boolean = false) : *
      {
         var a:int = 0;
         var b:int = 0;
         var c:int = 0;
         var d:int = 0;
         var e:int = 0;
         var f:int = 0;
         var g:int = 0;
         var str:String = null;
         var urlRequest:URLRequest = null;
         if(Boolean(TimeXX()) || go)
         {
            a = A_fun();
            b = B_fun();
            c = C_fun();
            d = D_fun();
            e = E_fun();
            f = F_fun();
            g = G_fun();
            str = "http://stat.api.4399.com/archive_statistics/log.js?" + "game_id=100016523" + "&uid=" + Main.userId + "&index=" + Main.saveNum + "&a=" + a + "&b=" + b + "&c=" + c + "&d=" + d + "&e=" + e + "&f=" + f + "&g=" + g;
            if(!go)
            {
               gameTime = 0;
            }
            TiaoShi.txtShow("Post str = " + str);
            urlRequest = new URLRequest(str);
            urlRequest.method = URLRequestMethod.POST;
            urlLoader = new URLLoader();
            urlLoader.load(urlRequest);
         }
      }
      
      private static function TimeXX() : Boolean
      {
         var date:Date = new Date();
         var dateStr:String = date.getFullYear() + "/" + (date.getMonth() + 1) + "/" + date.getDate();
         var date1:Date = new Date(dateStr);
         var date2:Date = new Date(saveTime);
         TiaoShi.txtShow("当前时间:" + date1 + ",\n 保存时间:" + date2);
         saveTime = dateStr;
         if(date1 > date2)
         {
            TiaoShi.txtShow("TimeXX ==> true");
            return true;
         }
         TiaoShi.txtShow("TimeXX ==> false");
         return false;
      }
      
      public static function A_fun() : int
      {
         var lv40_UP:int = 0;
         var lv:int = int(Main.player1.level.getValue());
         if(lv <= 40)
         {
            return lv / 10 + 1;
         }
         return int((lv - 40) / 5 + 5);
      }
      
      public static function B_fun() : int
      {
         var num:int = 0;
         for(var i:int = 1; i <= 18; i++)
         {
            if(Main.guanKa[i + 1] > 0 && Main.guanKa[i] > 0)
            {
               num = i;
            }
         }
         for(i = 51; i <= 60; i++)
         {
            if(Main.guanKa[i + 1] > 0 && Main.guanKa[i] > 0)
            {
               num = i;
            }
         }
         return num;
      }
      
      public static function C_fun() : int
      {
         var num:int = 0;
         for(var i:int = 101; i <= 105; i++)
         {
            if(Main.guanKa[i + 1] > 0 && Main.guanKa[i] > 0)
            {
               num = i;
            }
         }
         return num;
      }
      
      public static function D_fun() : int
      {
         var num:int = 0;
         for(var i:int = 1; i <= 18; i++)
         {
            if(Main.guanKa[i] >= 3)
            {
               num = i;
            }
         }
         for(i = 51; i <= 60; i++)
         {
            if(Main.guanKa[i] >= 3)
            {
               num = i;
            }
         }
         return num;
      }
      
      public static function E_fun() : int
      {
         return Main.player1.skinArr[Main.player1.skinNum];
      }
      
      public static function F_fun() : int
      {
         if(Main.P1P2)
         {
            return 2;
         }
         return 1;
      }
      
      public static function G_fun() : int
      {
         var num:int = gameTime / (27 * 60 * 10) + 1;
         TiaoShi.txtShow("G_fun = " + num);
         if(num >= 18)
         {
            num = 18;
         }
         return num;
      }
      
      public static function ioErrorEvent(e:*) : *
      {
      }
      
      public static function onCOMPLETE(e:*) : *
      {
      }
      
      public static function onSECURITY_ERROR(e:*) : *
      {
      }
   }
}

