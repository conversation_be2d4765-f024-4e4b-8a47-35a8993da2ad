package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class Vip_Interface extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      private static var _this:Vip_Interface;
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "VIP_v1320.swf";
      
      public static var vipUserArr:Array = new Array();
      
      private var skin:MovieClip;
      
      private var waBao:MovieClip;
      
      private var vipOK:Boolean = false;
      
      private var selNum:uint = 5;
      
      private var ruCangQuan_Num:uint = 0;
      
      private var chuanSongOk:Boolean = false;
      
      private var selNum1:uint = 1;
      
      private var selNum2:uint = 1;
      
      private var selNum3:uint = 1;
      
      private var guankaArr:Array = [[1,2,3,4,5,6,7,8,9],[10,11,12,13,14,15,16],[51,52,53,54],[101,102,103,104,105]];
      
      public function Vip_Interface()
      {
         super();
         this.LoadSkin();
         _this = this;
      }
      
      private static function InitOpen() : *
      {
         var temp:Vip_Interface = new Vip_Interface();
         Main._stage.addChild(temp);
         OpenYN = true;
         SetData();
      }
      
      public static function SetData() : *
      {
         if(vipUserArr.length == 0 || vipUserArr[0].getValue() < Main.serverTime.getValue())
         {
            vipUserArr[0] = VT.createVT(Main.serverTime.getValue());
            vipUserArr[1] = VT.createVT(3);
            vipUserArr[2] = VT.createVT(5);
         }
      }
      
      public static function Open() : *
      {
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
            _this.skin.vip_mc.visible = false;
            _this.skin._BLACK_mc.visible = false;
            _this.waBao = _this.skin.waBao_mc;
            _this.WaBao_Close();
            _this.Show();
         }
         else
         {
            InitOpen();
         }
      }
      
      public static function CloseX() : *
      {
         if(_this)
         {
            _this.visible = false;
            _this.x = _this.y = 5000;
         }
      }
      
      public static function GetVip() : *
      {
         if(Boolean(_this) && Boolean(_this.vipOK))
         {
            _this.vipOK = false;
            Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63215));
            NewMC.Open("文字提示",_this,400,400,30,0,true,2,"vip称号已放入背包");
            _this.skin._BLACK_mc.visible = false;
         }
      }
      
      public static function DianQuanChuanSong() : *
      {
         var arrX:Array = null;
         if(Boolean(_this) && Boolean(_this.chuanSongOk))
         {
            _this.chuanSongOk = false;
            Main.gameNum.setValue(17);
            arrX = [0,4,8,12,16,18];
            Main.gameNum2.setValue(arrX[_this.selNum]);
            GameData.gameLV = 4;
            Main.water = VT.createVT(1);
            Main._this.Loading();
            _this.Close();
            if(_this.ruCangQuan_Num == 1)
            {
               Main.player1.getBag().delOtherById(63155,1);
            }
            else if(_this.ruCangQuan_Num == 2)
            {
               Main.player2.getBag().delOtherById(63155,1);
            }
         }
      }
      
      private function LoadSkin() : *
      {
         if(!this.skin && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,this.onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private function onLoadingOK(e:*) : *
      {
         Play_Interface.interfaceX["load_mc"].visible = false;
         var classRef:Class = loadData.getClass("Skin") as Class;
         this.skin = new classRef();
         addChild(this.skin);
         this.skin.Close_btn.addEventListener(MouseEvent.CLICK,this.Close);
         this.skin.chuanSong_btn.addEventListener(MouseEvent.CLICK,this.ChuanSong);
         this.skin.chuanSong_btn2.addEventListener(MouseEvent.CLICK,this.ChuanSong2);
         this.skin.cangku_btn.addEventListener(MouseEvent.CLICK,this.Cangku_btn);
         this.skin.waBao_btn.addEventListener(MouseEvent.CLICK,this.WaBao_Open);
         for(var i:uint = 1; i < 6; i++)
         {
            this.skin["btn_" + i].addEventListener(MouseEvent.CLICK,this.SelChuanSong);
         }
         this.skin.buyVip_btn.addEventListener(MouseEvent.CLICK,this.BuyVip);
         if(OpenYN)
         {
            Open();
         }
         else
         {
            this.Close();
         }
      }
      
      private function Close(e:* = null) : *
      {
         CloseX();
      }
      
      private function Show() : *
      {
         for(var i:uint = 1; i < 6; i++)
         {
            if(this.selNum == i)
            {
               this.skin["sel_" + i].visible = true;
            }
            else
            {
               this.skin["sel_" + i].visible = false;
            }
         }
         this.skin.chuanSong_txt.text = "炼狱传送免费次数:" + vipUserArr[1].getValue();
         this.skin.waBao_txt.text = "关卡寻宝剩余次数:" + vipUserArr[2].getValue();
         this.skin["chuanSong_btn2"].visible = false;
         this.skin["cangku_btn"].visible = false;
         if(vipUserArr[1].getValue() <= 0)
         {
            this.skin["chuanSong_btn2"].visible = true;
         }
         var bagVip:Boolean = false;
         if(Main.player1.getBag().getOtherobjNum(63215) > 0 || Boolean(Main.P1P2) && Main.player2.getBag().getOtherobjNum(63215) > 0)
         {
            bagVip = true;
         }
         if(Boolean(Main.isVip()) || bagVip)
         {
            this.skin.buyVip_btn.visible = false;
         }
         if(Main.isVip())
         {
            this.skin["cangku_btn"].visible = true;
         }
      }
      
      private function BuyVip(e:*) : *
      {
         this.skin.vip_mc.visible = true;
         this.skin.vip_mc.close_btn.addEventListener(MouseEvent.CLICK,this.vipClose);
         this.skin.vip_mc.buy_btn.addEventListener(MouseEvent.CLICK,this.vipBuyGo);
      }
      
      private function vipClose(e:*) : *
      {
         this.skin.vip_mc.visible = false;
      }
      
      private function vipBuyGo(e:*) : *
      {
         if(Main.player1.getBag().backOtherBagNum() < 1)
         {
            NewMC.Open("文字提示",this,400,400,30,0,true,2,"其他类背包空间不足!");
            return;
         }
         var money:uint = uint(InitData.vip2990.getValue());
         if(Shop4399.moneyAll.getValue() >= money)
         {
            this.vipOK = true;
            this.skin._BLACK_mc.visible = true;
            Api_4399_All.BuyObj(InitData.vipID.getValue());
            this.skin.vip_mc.visible = false;
            return;
         }
         NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
      }
      
      private function SelChuanSong(e:MouseEvent) : *
      {
         this.selNum = (e.target.name as String).substr(4,1);
         this.Show();
      }
      
      private function ChuanSong(e:*) : *
      {
         var num:uint = 0;
         var arrX:Array = null;
         if(Main.isVip())
         {
            if(Main.player1.getBag().getOtherobjNum(63155) > 0)
            {
               this.ruCangQuan_Num = 1;
            }
            else
            {
               if(!(Boolean(Main.P1P2) && Main.player2.getBag().getOtherobjNum(63155) > 0))
               {
                  NewMC.Open("文字提示",this,400,350,30,0,true,2,"缺少炼狱入场券");
                  return;
               }
               this.ruCangQuan_Num = 2;
            }
            if((vipUserArr[1] as VT).getValue() > 0)
            {
               Main.gameNum.setValue(17);
               arrX = [0,4,8,12,16,18];
               Main.gameNum2.setValue(arrX[this.selNum]);
               GameData.gameLV = 4;
               Main.water = VT.createVT(1);
               Main._this.Loading();
               (vipUserArr[1] as VT).setValue((vipUserArr[1] as VT).getValue() - 1);
               if(this.ruCangQuan_Num == 1)
               {
                  Main.player1.getBag().delOtherById(63155,1);
               }
               else if(this.ruCangQuan_Num == 2)
               {
                  Main.player2.getBag().delOtherById(63155,1);
               }
               this.Close();
               Main.Save();
            }
            num = uint((vipUserArr[1] as VT).getValue());
            NewMC.Open("文字提示",this,400,350,30,0,true,2,"今日可使用次数" + num);
         }
         else
         {
            NewMC.Open("文字提示",this,400,350,30,0,true,2,"vip玩家专用福利,赶快加入吧");
         }
         this.Show();
      }
      
      private function Cangku_btn(e:*) : *
      {
         StoragePanel.open();
      }
      
      private function ChuanSong2(e:*) : *
      {
         if(Main.isVip())
         {
            if(Main.player1.getBag().getOtherobjNum(63155) > 0)
            {
               this.ruCangQuan_Num = 1;
            }
            else
            {
               if(!(Boolean(Main.P1P2) && Main.player2.getBag().getOtherobjNum(63155) > 0))
               {
                  NewMC.Open("文字提示",this,400,350,30,0,true,2,"缺少炼狱入场券");
                  return;
               }
               this.ruCangQuan_Num = 2;
            }
            if(Shop4399.moneyAll.getValue() >= 10)
            {
               this.skin._BLACK_mc.visible = true;
               Api_4399_All.BuyObj(InitData.vip10.getValue());
               this.chuanSongOk = true;
            }
            else
            {
               NewMC.Open("文字提示",this,400,400,30,0,true,2,"点券不足");
               this.skin._BLACK_mc.visible = false;
            }
         }
         else
         {
            NewMC.Open("文字提示",this,400,350,30,0,true,2,"vip玩家专用福利,赶快加入吧");
         }
         this.Show();
      }
      
      private function WaBao_Open(e:* = null) : *
      {
         if(Main.isVip())
         {
            this.WaBao_Close();
            this.waBao.visible = true;
            this.waBao.x = this.waBao.y = 0;
            this.WaBaoInit();
            this.WaBaoShow();
         }
         else
         {
            NewMC.Open("文字提示",this,400,350,30,0,true,2,"vip玩家专用福利,赶快加入吧");
         }
      }
      
      private function WaBao_Close(e:* = null) : *
      {
         this.waBao.visible = false;
         this.waBao.x = this.waBao.y = 5000;
         this.waBao.getObj_mc.visible = this.waBao.get_mc.visible = false;
      }
      
      private function WaBaoInit() : *
      {
         var i:uint = 0;
         this.selNum1 = this.selNum2 = this.selNum3 = 1;
         for(i = 1; i <= 4; i++)
         {
            this.waBao["guanKaBtn_" + i].addEventListener(MouseEvent.CLICK,this.Sel_Num);
         }
         for(i = 1; i <= 8; i++)
         {
            this.waBao["guanka_" + i].addEventListener(MouseEvent.CLICK,this.Sel_Num2);
         }
         this.waBao["close_btn"].addEventListener(MouseEvent.CLICK,this.WaBao_Close);
      }
      
      private function Sel_Num(e:MouseEvent) : *
      {
         var num:uint = uint((e.target.name as String).substr(10,1));
         this.selNum1 = num;
         TiaoShi.txtShow("Sel_Num selNum1 = " + this.selNum1);
         this.WaBaoShow();
      }
      
      private function Sel_Num2(e:MouseEvent) : *
      {
         var num:uint = uint((e.target.name as String).substr(7,1));
         this.selNum2 = num;
         this.WaBaoShow();
      }
      
      private function WaBaoShow() : *
      {
         var gameNum2X:uint = 0;
         var gameNum:uint = 0;
         var nextNum:uint = 0;
         for(var i:uint = 1; i <= 4; i++)
         {
            (this.waBao["guanKaXXX_" + i] as MovieClip).gotoAndStop(1);
            if(this.selNum1 == i)
            {
               (this.waBao["guanKaXXX_" + i] as MovieClip).gotoAndStop(2);
            }
         }
         var numX:uint = (this.guankaArr[this.selNum1 - 1] as Array).length / 8 + 0.99999;
         this.waBao.num_txt.text = this.selNum3 + "/" + numX;
         TiaoShi.txtShow("selNum1 = " + this.selNum1 + ", gameNum2X = " + (this.selNum2 + (numX - 1) * 8));
         for(i = 1; i <= 8; i++)
         {
            gameNum2X = this.selNum2 + (this.selNum2 - 1) * 8 - 1;
            gameNum = uint(this.guankaArr[this.selNum1 - 1][gameNum2X]);
            nextNum = gameNum2X + i;
            TiaoShi.txtShow("gameNum = " + gameNum + ", nextNum = " + nextNum);
            this.waBao["guanka_" + i].visible = this.waBao["guanka_mc_" + i].visible = false;
            if(Main.guanKa[nextNum] > 1)
            {
               this.waBao["guanka_" + i].visible = this.waBao["guanka_mc_" + i].visible = true;
               this.waBao["guanka_mc_" + i].gotoAndStop("d" + nextNum);
            }
         }
         TiaoShi.txtShow("-----------------");
      }
   }
}

