package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.elves.*;
   import com.hotpoint.braveManIII.repository.skill.*;
   
   public class ElvesSlot
   {
      
      private var _slot:Array = new Array();
      
      private var _slotLimit:VT = VT.createVT(5);
      
      public function ElvesSlot()
      {
         super();
      }
      
      public static function createElvesSlot() : ElvesSlot
      {
         var es:ElvesSlot = new ElvesSlot();
         for(var i:int = 0; i < 20; i++)
         {
            es._slot[i] = null;
         }
         return es;
      }
      
      public function get slot() : Array
      {
         return this._slot;
      }
      
      public function set slot(value:Array) : void
      {
         this._slot = value;
      }
      
      public function get slotLimit() : VT
      {
         return this._slotLimit;
      }
      
      public function set slotLimit(value:VT) : void
      {
         this._slotLimit = value;
      }
      
      public function getElvesFromSlot(num:Number) : Elves
      {
         if(this._slot[num] != null)
         {
            return this._slot[num];
         }
         return null;
      }
      
      public function getSlotNum() : int
      {
         return this._slotLimit.getValue();
      }
      
      public function addElvesSlot(value:Elves) : Boolean
      {
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] == null)
            {
               this._slot[i] = value;
               return true;
            }
         }
         return false;
      }
      
      public function delElvesSlot(num:int) : Elves
      {
         var elves:Elves = null;
         if(this._slot[num] != null)
         {
            elves = this._slot[num];
            this._slot[num] = null;
         }
         return elves;
      }
      
      public function backElvesSlotNum() : int
      {
         var num:int = 0;
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function backElvesNum() : int
      {
         var num:int = 0;
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] != null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function addSlotNum(num:int) : *
      {
         this._slotLimit.setValue(this._slotLimit.getValue() + num);
      }
      
      public function backElvesId() : int
      {
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i] != null)
            {
               return i;
            }
         }
         return -1;
      }
      
      public function backChoseElvesId(e:Elves) : int
      {
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i] == e)
            {
               return i;
            }
         }
         return -1;
      }
      
      public function backElvesSkill2() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1003)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill3() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1004)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill3_2() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1004)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[1].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill1() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1006)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill4() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1008)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill5() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1011)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill6() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1013)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill7() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1015)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill8() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1019)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function backElvesSkill9() : Number
      {
         var ee:Elves = null;
         var arr:Array = [];
         for(var i:uint = 0; i < 20; i++)
         {
            if(this._slot[i])
            {
               if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillActOn() == 1021)
               {
                  if(!ee)
                  {
                     ee = this._slot[i];
                  }
                  if(SkillFactory.getSkillById((this._slot[i] as Elves).getSKILL1()).getSkillLevel() > SkillFactory.getSkillById(ee.getSKILL1()).getSkillLevel())
                  {
                     ee = this._slot[i];
                  }
               }
            }
         }
         if(ee)
         {
            arr = SkillFactory.getSkillById(ee.getSKILL1()).getSkillValueArray();
            return arr[0].getValue();
         }
         return 0;
      }
      
      public function plan3_13(lv:int) : int
      {
         for(var i:uint = 0; i < this._slotLimit.getValue(); i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Elves).getLevel() >= lv)
               {
                  return true;
               }
            }
         }
         return false;
      }
   }
}

