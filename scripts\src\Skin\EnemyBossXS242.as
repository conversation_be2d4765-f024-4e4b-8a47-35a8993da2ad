package src.Skin
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.*;
   import src.other.*;
   
   public class EnemyBossXS242 extends EnemySkin
   {
      
      public static var thisX:EnemyBossXS242;
      
      public var gjNum:int = 1;
      
      public var gjHP:int = 0;
      
      public var lifeXX_time:int = 0;
      
      public var lifeXX_num1:int = 0;
      
      public var lifeXX_num2:int = 0;
      
      public function EnemyBossXS242()
      {
         super();
         thisX = this;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_XXX);
         addEventListener(Event.REMOVED_FROM_STAGE,this.onREMOVED_FROM_STAGEXXX);
      }
      
      public static function BeiDong_2(xxx:int, yyy:int) : *
      {
         var tempID:int = 0;
         var classRef:Class = null;
         var mcX:MovieClip = null;
         if(Boolean(thisX) && thisX.who.life.getValue() > 0)
         {
            tempID = int(Main.wts.getBoss());
            classRef = Enemy.EnemyArr[tempID].getClass("回复火球") as Class;
            mcX = new classRef();
            Main.world.moveChild_Other.addChild(mcX);
            mcX.x = xxx;
            mcX.y = yyy - 20;
            mcX.who = thisX;
         }
      }
      
      private function BeiDong_1() : *
      {
         var en:Enemy = null;
         var tempID:int = 0;
         var classRef:Class = null;
         var mcX2:MovieClip = null;
         for(var i:int = 0; i < Enemy.All.length; i++)
         {
            en = Enemy.All[i];
            if(Play_Interface.bossIS == en)
            {
               en.lifeMAX.setValue(who.lifeMAX.getValue());
               en.life.setValue(who.life.getValue());
               tempID = int(Main.wts.getBoss());
               classRef = Enemy.EnemyArr[tempID].getClass("傀儡标记") as Class;
               mcX2 = new classRef();
               en.addChild(mcX2);
               mcX2.y = -300;
            }
         }
      }
      
      private function onENTER_FRAME_XXX(e:*) : *
      {
         ++this.lifeXX_time;
         if(this.lifeXX_time == 20)
         {
            this.BeiDong_1();
         }
         if(this.lifeXX_time % 27 == 0)
         {
            if(Main.player_1.hp.getValue() > 0 && Math.abs(Main.player_1.x - who.x) < 200)
            {
               ++this.lifeXX_num1;
               Main.player_1.Hit3000(this.lifeXX_num1,1000);
            }
            else
            {
               this.lifeXX_num1 = 0;
            }
            if(Boolean(Main.P1P2) && Main.player_2.hp.getValue() > 0 && Math.abs(Main.player_2.x - who.x) < 200)
            {
               ++this.lifeXX_num2;
               Main.player_2.Hit3000(this.lifeXX_num2,1000);
            }
            else
            {
               this.lifeXX_num2 = 0;
            }
         }
      }
      
      public function HpUp() : *
      {
         if(this.gjHP == 0)
         {
            this.gjHP = who.攻击力.getValue();
         }
         if(this.gjNum < 10)
         {
            ++this.gjNum;
         }
         who.攻击力.setValue(this.gjHP + this.gjHP * 0.05 * this.gjNum);
         who.hpUpEnemy(who.lifeMAX.getValue() * 0.025);
         var tempID:int = int(Main.wts.getBoss());
         var classRef:Class = Enemy.EnemyArr[tempID].getClass("回复效果") as Class;
         var mcX:MovieClip = new classRef();
         this.addChild(mcX);
      }
      
      private function onREMOVED_FROM_STAGEXXX(e:*) : *
      {
         thisX = null;
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME_XXX);
      }
      
      override public function otherGoTo(str:String) : *
      {
         if(str == "死亡")
         {
            Main.wts.wantedTaskComplete();
         }
      }
   }
}

