package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.equip.*;
   import flash.utils.*;
   import src.*;
   import src.tool.*;
   
   public class EquipSlot
   {
      
      public var who:PlayerData;
      
      private var _slot:Array = new Array();
      
      internal var arr:Array = [14573,14597,14549,14525,14419,14424,14429];
      
      public function EquipSlot()
      {
         super();
      }
      
      public static function createEquipSlot() : EquipSlot
      {
         var es:EquipSlot = new EquipSlot();
         for(var i:int = 0; i < 13; i++)
         {
            es._slot[i] = null;
         }
         return es;
      }
      
      public function get slot() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._slot;
      }
      
      public function set slot(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._slot = value;
      }
      
      public function getEquipFromSlot(num:Number) : Equip
      {
         if(this._slot[num] != null)
         {
            return this._slot[num];
         }
         return null;
      }
      
      public function addToSlot(value:Equip, num:Number) : Boolean
      {
         if(this._slot[num] == null)
         {
            if(Main.newPlay == 3)
            {
               Main.newPlay = 0;
            }
            this._slot[num] = value;
            return true;
         }
         return false;
      }
      
      public function delSlot(num:Number) : Equip
      {
         var eq:Equip = null;
         if(this._slot[num] != null)
         {
            eq = this._slot[num];
            this._slot[num] = null;
         }
         return eq;
      }
      
      public function slotToSlot() : *
      {
         var temp:Equip = null;
         if(this._slot[2] == null)
         {
            this._slot[2] = this._slot[5];
            this._slot[5] = null;
         }
         else if(this._slot[5] == null)
         {
            this._slot[5] = this._slot[2];
            this._slot[2] = null;
         }
         else
         {
            temp = this._slot[2];
            this._slot[2] = this._slot[5];
            this._slot[5] = temp;
         }
      }
      
      public function getAllHP() : Number
      {
         var hp:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  hp += this._slot[i].getHP();
               }
            }
         }
         return hp + this.getSuitAllAttrib(EquipBaseAttribTypeConst.HP);
      }
      
      public function getAllMP() : Number
      {
         var mp:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  mp += this._slot[i].getMP();
               }
            }
         }
         return mp + this.getSuitAllAttrib(EquipBaseAttribTypeConst.MP);
      }
      
      public function getAllAttack() : Number
      {
         var att:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  att += this._slot[i].getAttack();
                  if((this._slot[i] as Equip).getId() == 14667 || (this._slot[i] as Equip).getId() == 14668 || (this._slot[i] as Equip).getId() == 14669)
                  {
                     att += ShengDan2013.WQwhite((this._slot[i] as Equip).getId(),this.who.getLevel());
                  }
               }
            }
         }
         return att + this.getSuitAllAttrib(EquipBaseAttribTypeConst.ATTACK);
      }
      
      public function getAllAttackIgnoreDefense() : Number
      {
         var datt:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  datt += this._slot[i].getAttackIgnoreDefense();
               }
            }
         }
         return datt + this.getSuitAllAttrib(EquipBaseAttribTypeConst.ATTACKIGNOREDEFENSE);
      }
      
      public function getAllDefense() : Number
      {
         var def:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  def += this._slot[i].getDefense();
               }
            }
         }
         return def + this.getSuitAllAttrib(EquipBaseAttribTypeConst.DEFENSE);
      }
      
      public function getAllMoveSpeed() : Number
      {
         var speed:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  speed += this._slot[i].getMoveSpeed();
               }
            }
         }
         return speed + this.getSuitAllAttrib(EquipBaseAttribTypeConst.MOVESPEED);
      }
      
      public function getAllCrit() : Number
      {
         var crit:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  crit += this._slot[i].getCrit();
               }
            }
         }
         return crit + this.getSuitAllAttrib(EquipBaseAttribTypeConst.CRIT);
      }
      
      public function getAllDuck() : Number
      {
         var duck:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  duck += this._slot[i].getDuck();
               }
            }
         }
         return duck + this.getSuitAllAttrib(EquipBaseAttribTypeConst.DUCK);
      }
      
      public function getAllHardValue() : Number
      {
         var hard:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  hard += this._slot[i].getHardValue();
               }
            }
         }
         return hard + this.getSuitAllAttrib(EquipBaseAttribTypeConst.HARDVALUE);
      }
      
      public function getAllMOKANG() : Number
      {
         var mokang:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  mokang += this._slot[i].getMOKANG();
               }
            }
         }
         return mokang + this.getSuitAllAttrib(EquipBaseAttribTypeConst.MOKANG);
      }
      
      public function getAllPOMO() : Number
      {
         var pomo:Number = 0;
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getRemainingTime() > 0)
               {
                  pomo += this._slot[i].getPOMO();
               }
            }
         }
         return pomo + this.getSuitAllAttrib(EquipBaseAttribTypeConst.POMO);
      }
      
      public function getAllEquipSkill() : Array
      {
         var skillArr:Array = [];
         for(var i:* = 0; i < 13; i++)
         {
            if(this.who.skinNum == 0)
            {
               if(i == 5)
               {
                  continue;
               }
            }
            else if(this.who.skinNum == 1)
            {
               if(i == 2)
               {
                  continue;
               }
            }
            if(this._slot[i] != null)
            {
               if(this._slot[i].getSkillAttrib() != 0)
               {
                  skillArr.push(this._slot[i].getSkillAttrib());
               }
            }
         }
         return skillArr;
      }
      
      public function getAllEquipNewSkill() : Array
      {
         var skillArr:Array = [];
         for(var i:* = 0; i < 13; i++)
         {
            if(this._slot[i] != null)
            {
               if(this._slot[i].getNewSkill() != 0)
               {
                  skillArr.push(this._slot[i].getNewSkill());
               }
            }
         }
         return skillArr;
      }
      
      public function getAllFrame() : Array
      {
         var num:* = undefined;
         var eq:Equip = null;
         var arr:Array = [];
         for(num in this._slot)
         {
            eq = this._slot[num];
            if(eq)
            {
               arr.push(eq.getFrame());
            }
            else
            {
               arr.push(0);
            }
         }
         return arr;
      }
      
      public function getAllSuitSkill() : Array
      {
         var suit:SuitEquipAttrib = null;
         var x:* = undefined;
         var tzID:int = 0;
         var yn:Boolean = false;
         var i:* = undefined;
         var idX:int = 0;
         var eq:Equip = null;
         var tArr:Array = null;
         var arr:Array = [];
         var idArr:Array = [[0,1,3,4],[8,9,11,12]];
         for(x in idArr)
         {
            tzID = 0;
            yn = true;
            for(i in idArr[x])
            {
               idX = int(idArr[x][i]);
               eq = this._slot[idX];
               if(eq == null)
               {
                  yn = false;
                  break;
               }
               if(tzID == 0)
               {
                  tzID = eq.getSuitId();
               }
               if(eq.getSuitId() == 0 || tzID != eq.getSuitId())
               {
                  yn = false;
                  break;
               }
            }
            if(yn)
            {
               suit = EquipFactory.getSuitEquip(this._slot[idArr[x][0]].getSuitId());
               tArr = suit.getSuitSkill();
               arr = arr.concat(tArr);
            }
         }
         return arr;
      }
      
      private function getSuitAllAttrib(attribType:Number) : Number
      {
         var suit:SuitEquipAttrib = null;
         var baseAttrib:EquipBaseAttrib = null;
         var arr:Array = [];
         var count:Number = 0;
         if(this._slot[0] != null && this._slot[1] != null && this._slot[3] != null && this._slot[4] != null)
         {
            if(this._slot[0].getSuitId() == this._slot[1].getSuitId() && this._slot[1].getSuitId() == this._slot[3].getSuitId() && this._slot[3].getSuitId() == this._slot[4].getSuitId() && this._slot[4].getSuitId() != 0)
            {
               suit = EquipFactory.getSuitEquip(this._slot[0].getSuitId());
               arr = suit.getSuitAttrib();
               for each(baseAttrib in arr)
               {
                  if(baseAttrib.getAttribType() == attribType)
                  {
                     count += baseAttrib.getValue();
                  }
               }
            }
         }
         if(this._slot[8] != null && this._slot[9] != null && this._slot[11] != null && this._slot[12] != null)
         {
            if(this._slot[8].getSuitId() == this._slot[9].getSuitId() && this._slot[9].getSuitId() == this._slot[11].getSuitId() && this._slot[11].getSuitId() == this._slot[12].getSuitId() && this._slot[12].getSuitId() != 0)
            {
               suit = EquipFactory.getSuitEquip(this._slot[8].getSuitId());
               arr = suit.getSuitAttrib();
               for each(baseAttrib in arr)
               {
                  if(baseAttrib.getAttribType() == attribType)
                  {
                     count += baseAttrib.getValue();
                  }
               }
            }
         }
         return count;
      }
      
      public function getSuitStrength() : Number
      {
         var lv:int = 0;
         if(this._slot[0] != null && this._slot[1] != null && this._slot[2] != null && this._slot[3] != null && this._slot[4] != null && this._slot[5] != null)
         {
            if(this._slot[0].getReinforceLevel() > 3 && this._slot[1].getReinforceLevel() > 3 && this._slot[3].getReinforceLevel() > 3 && this._slot[4].getReinforceLevel() > 3 && this._slot[2].getReinforceLevel() > 3 && this._slot[5].getReinforceLevel() > 3)
            {
               if(this._slot[0].getReinforceLevel() >= this.slot[1].getReinforceLevel())
               {
                  lv = int(this.slot[1].getReinforceLevel());
               }
               else
               {
                  lv = int(this._slot[0].getReinforceLevel());
               }
               if(lv >= this._slot[2].getReinforceLevel())
               {
                  lv = int(this.slot[2].getReinforceLevel());
               }
               if(lv >= this._slot[5].getReinforceLevel())
               {
                  lv = int(this.slot[5].getReinforceLevel());
               }
               if(lv >= this._slot[3].getReinforceLevel())
               {
                  lv = int(this.slot[3].getReinforceLevel());
               }
               if(lv >= this._slot[4].getReinforceLevel())
               {
                  lv = int(this.slot[4].getReinforceLevel());
               }
            }
         }
         return lv;
      }
      
      public function testAllEquip() : Boolean
      {
         var j:int = 0;
         for(var i:int = 0; i < 13; i++)
         {
            for(j = 0; j < this.arr.length; j++)
            {
               if(Boolean(this._slot[i]) && (this._slot[i] as Equip).getId() == this.arr[j])
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function suitOK() : Boolean
      {
         if(Boolean(this._slot[0]) && Boolean(this._slot[1]) && Boolean(this._slot[3]) && Boolean(this._slot[4]))
         {
            if((this._slot[0] as Equip).getSuitId() != 0)
            {
               if((this._slot[0] as Equip).getSuitId() == (this._slot[1] as Equip).getSuitId() && (this._slot[0] as Equip).getSuitId() == (this._slot[3] as Equip).getSuitId() && (this._slot[0] as Equip).getSuitId() == (this._slot[4] as Equip).getSuitId())
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan4_4() : Boolean
      {
         if(Boolean(this._slot[0]) && Boolean(this._slot[1]) && Boolean(this._slot[3]) && Boolean(this._slot[4]))
         {
            if((this._slot[0] as Equip).getSuitId() >= 21 && (this._slot[0] as Equip).getSuitId() <= 30)
            {
               if((this._slot[0] as Equip).getSuitId() == (this._slot[1] as Equip).getSuitId() && (this._slot[0] as Equip).getSuitId() == (this._slot[3] as Equip).getSuitId() && (this._slot[0] as Equip).getSuitId() == (this._slot[4] as Equip).getSuitId())
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan6_7() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getSuitId() >= 26 && (this._slot[i] as Equip).getSuitId() <= 30)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan2_5(lv:Number) : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getReinforceLevel() >= lv)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan1_5() : Boolean
      {
         for(var i:int = 0; i < 7; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getGrid() == 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan4_15() : Boolean
      {
         for(var i:int = 0; i < 7; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getGrid() == 0)
               {
                  if((this._slot[i] as Equip).getGemSlot().getColor() == 3)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function plan4_16() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getNewSkill() > 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_7() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getStar() > 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_8() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getBlessAttrib())
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_12() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getPosition() == 8)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_13() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getPosition() == 9)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_14() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getColor() >= 4)
               {
                  if((this._slot[i] as Equip).getPosition() == 8)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function plan5_15() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getColor() >= 4)
               {
                  if((this._slot[i] as Equip).getPosition() == 9)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function plan6_10() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getId() >= 20001 && (this._slot[i] as Equip).getId() <= 20024)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan6_15() : int
      {
         var num:int = 0;
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getStar() >= 3)
               {
                  num++;
               }
            }
         }
         return num;
      }
      
      public function plan7_3() : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getId() >= 20079 && (this._slot[i] as Equip).getId() <= 20084)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan7_4() : int
      {
         var num:int = 0;
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getStar() >= 4)
               {
                  num++;
               }
            }
         }
         return num;
      }
      
      public function plan8_4() : int
      {
         var num:int = 0;
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i])
            {
               if((this._slot[i] as Equip).getStar() >= 5)
               {
                  num++;
               }
            }
         }
         return num;
      }
      
      public function getEquipGold(lv:Number) : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getDressLevel() >= lv)
               {
                  if((this._slot[i] as Equip).getColor() >= 4)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function getWuQiGold(lv:Number) : Boolean
      {
         for(var i:int = 0; i < 13; i++)
         {
            if(this._slot[i] != null)
            {
               if((this._slot[i] as Equip).getDressLevel() >= lv)
               {
                  if((this._slot[i] as Equip).getColor() >= 4)
                  {
                     if((this._slot[i] as Equip).getPosition() >= 5 && (this._slot[i] as Equip).getPosition() <= 7)
                     {
                        return true;
                     }
                  }
               }
            }
         }
         return false;
      }
   }
}

