package src
{
   import com.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4053")]
   public class PK_UI_Sel extends MovieClip
   {
      
      public static var _this:PK_UI_Sel;
      
      public static var OtherData:ClassLoader;
      
      public static var openYN:Boolean = false;
      
      public var close_btn:SimpleButton;
      
      public var sel_1_btn:SimpleButton;
      
      public var skin_mc:MovieClip;
      
      public function PK_UI_Sel()
      {
         super();
         this.sel_1_btn.addEventListener(MouseEvent.CLICK,this.In_JingJiPaiHang);
         this.close_btn.addEventListener(MouseEvent.CLICK,Close);
      }
      
      public static function Open() : *
      {
         var classRef:Class = null;
         var xxMov:MovieClip = null;
         if(!NewLoad.Other_YN)
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"加载中,请稍候...");
            return;
         }
         if(!_this)
         {
            _this = new PK_UI_Sel();
         }
         if(!openYN)
         {
            Main.allClosePanel();
            _this.visible = true;
            _this.x = _this.y = 0;
            Main._stage.addChild(_this);
            TiaoShi.txtShow("PK_UI =========> TaskPanel.TiShi_PK_UI()");
            classRef = NewLoad.OtherData.getClass("_PK_UI") as Class;
            xxMov = new classRef();
            _this.skin_mc.addChild(xxMov);
            openYN = true;
         }
      }
      
      public static function Close(e:* = null) : *
      {
         if(!_this)
         {
            return;
         }
         _this.visible = false;
         _this.x = _this.y = 5000;
         openYN = false;
      }
      
      private function In_JingJiPaiHang(e:*) : *
      {
         PK_UI.Open();
      }
   }
}

