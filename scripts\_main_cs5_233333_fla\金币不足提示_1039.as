package _main_cs5_233333_fla
{
   import flash.accessibility.*;
   import flash.display.*;
   import flash.errors.*;
   import flash.events.*;
   import flash.external.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.media.*;
   import flash.net.*;
   import flash.net.drm.*;
   import flash.system.*;
   import flash.text.*;
   import flash.text.ime.*;
   import flash.ui.*;
   import flash.utils.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol3494")]
   public dynamic class 金币不足提示_1039 extends MovieClip
   {
      
      public function 金币不足提示_1039()
      {
         super();
         addFrameScript(33,this.frame34);
      }
      
      internal function frame34() : *
      {
         this.x = this.y = 5000;
      }
   }
}

