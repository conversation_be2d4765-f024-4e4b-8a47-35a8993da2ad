package com.hotpoint.braveManIII.repository.petEquip
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   
   public class PetEquipBasicData
   {
      
      private var _id:VT;
      
      private var _frame:VT;
      
      private var _name:String;
      
      private var _type:VT;
      
      private var _price:VT;
      
      private var _color:VT;
      
      private var _skilldescript:String;
      
      private var _descript:String;
      
      private var _xingge:Array = [];
      
      private var _affect:Array = [];
      
      public function PetEquipBasicData()
      {
         super();
      }
      
      public static function createPetEquipBasicData(id:Number, frame:Number, name:String, type:Number, price:Number, color:Number, skilldescript:String, descript:String, xingge:Array, affect:Array) : PetEquipBasicData
      {
         var petEquipBasicData:PetEquipBasicData = new PetEquipBasicData();
         petEquipBasicData._id = VT.createVT(id);
         petEquipBasicData._frame = VT.createVT(frame);
         petEquipBasicData._name = name;
         petEquipBasicData._type = VT.createVT(type);
         petEquipBasicData._price = VT.createVT(price);
         petEquipBasicData._color = VT.createVT(color);
         petEquipBasicData._skilldescript = skilldescript;
         petEquipBasicData._descript = descript;
         petEquipBasicData._xingge = xingge;
         petEquipBasicData._affect = affect;
         return petEquipBasicData;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getName() : String
      {
         return this._name;
      }
      
      public function getFrame() : Number
      {
         return this._frame.getValue();
      }
      
      public function getType() : Number
      {
         return this._type.getValue();
      }
      
      public function getPrice() : Number
      {
         return this._price.getValue();
      }
      
      public function getColor() : Number
      {
         return this._color.getValue();
      }
      
      public function getSkillDescript() : String
      {
         return this._skilldescript;
      }
      
      public function getDescript() : String
      {
         return this._descript;
      }
      
      public function getAffect() : Array
      {
         return this._affect;
      }
      
      public function getXingge() : Array
      {
         return this._xingge;
      }
      
      public function createPetEquip() : PetEquip
      {
         return PetEquip.creatPetEquip(this._id.getValue());
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(value:VT) : void
      {
         this._id = value;
      }
      
      public function get frame() : VT
      {
         return this._frame;
      }
      
      public function set frame(value:VT) : void
      {
         this._frame = value;
      }
      
      public function get name() : String
      {
         return this._name;
      }
      
      public function set name(value:String) : void
      {
         this._name = value;
      }
      
      public function get type() : VT
      {
         return this._type;
      }
      
      public function set type(value:VT) : void
      {
         this._type = value;
      }
      
      public function get color() : VT
      {
         return this._color;
      }
      
      public function set color(value:VT) : void
      {
         this._color = value;
      }
      
      public function get skilldescript() : String
      {
         return this._skilldescript;
      }
      
      public function set skilldescript(value:String) : void
      {
         this._skilldescript = value;
      }
      
      public function get descript() : String
      {
         return this._descript;
      }
      
      public function set descript(value:String) : void
      {
         this._descript = value;
      }
      
      public function get xingge() : Array
      {
         return this._xingge;
      }
      
      public function set xingge(value:Array) : void
      {
         this._xingge = value;
      }
      
      public function get affect() : Array
      {
         return this._affect;
      }
      
      public function set affect(value:Array) : void
      {
         this._affect = value;
      }
      
      public function get price() : VT
      {
         return this._price;
      }
      
      public function set price(value:VT) : void
      {
         this._price = value;
      }
   }
}

