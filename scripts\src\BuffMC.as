package src
{
   import flash.display.MovieClip;
   import flash.events.*;
   import flash.utils.*;
   import src.Skin.*;
   import src.tool.*;
   
   public class BuffMC extends MovieClip
   {
      
      public var who:Player;
      
      public var ice_mc:MovieClip;
      
      public var iceTime:int = 0;
      
      public var mabi_mc:MovieClip;
      
      public var mabiTime:int = 0;
      
      public var jianShuTime:int = 0;
      
      public function BuffMC(whoX:Player)
      {
         super();
         this.who = whoX;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         if(this.iceTime > 0)
         {
            --this.iceTime;
            if(this.iceTime <= 0 && this.ice_mc && Boolean(this.ice_mc.parent))
            {
               this.ice_mc.parent.removeChild(this.ice_mc);
            }
         }
         if(this.mabiTime > 0)
         {
            --this.mabiTime;
            if(this.mabiTime <= 0 && this.mabi_mc && Boolean(this.mabi_mc.parent))
            {
               this.mabi_mc.parent.removeChild(this.mabi_mc);
            }
         }
         if(this.jianShuTime > 0)
         {
            --this.jianShuTime;
            this.who.walk_power2 = 0.3;
         }
         else
         {
            this.who.walk_power2 = 1;
         }
      }
      
      public function addBuff(type:String, time:int = 0) : *
      {
         var ice_C:Class = null;
         var mabi_C:Class = null;
         if(type == "ice")
         {
            if(!this.ice_mc)
            {
               ice_C = NewLoad.XiaoGuoData.getClass("Iceing") as Class;
               this.ice_mc = new ice_C();
            }
            this.iceTime = time;
            this.who.addChild(this.ice_mc);
            (this.who as Player).skin.GoTo("被打",this.iceTime,true);
         }
         if(type == "mabi")
         {
            if(!this.mabi_mc)
            {
               mabi_C = NewLoad.XiaoGuoData.getClass("Mabi") as Class;
               this.mabi_mc = new mabi_C();
            }
            this.mabiTime = time;
            this.who.addChild(this.mabi_mc);
            (this.who as Player).skin.GoTo("被打",this.mabiTime,true);
         }
         if(type == "jianShu")
         {
            this.jianShuTime = time;
         }
      }
   }
}

