package src
{
   import com.hotpoint.braveManIII.models.container.Bag;
   import com.hotpoint.braveManIII.models.container.EquipSlot;
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.gem.Gem;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.views.strPanel.*;
   import flash.display.*;
   import flash.events.*;
   
   public class XiuZeng
   {
      
      public static var xz_yn:Boolean = false;
      
      public function XiuZeng()
      {
         super();
      }
      
      public static function XiuZengAll() : *
      {
         var eq:Equip = null;
         var fX:int = 0;
         var lv:int = 0;
         var pX:PlayerData = null;
         var eSlot:EquipSlot = null;
         var bagX:Bag = null;
         var i2:int = 0;
         var i4:int = 0;
         var i3:int = 0;
         var i5:int = 0;
         if(xz_yn)
         {
            return;
         }
         xz_yn = true;
         var gemX:Gem = GemFactory.creatGemById(33213);
         for(i = 1; i <= 2; ++i)
         {
            if(Main["player" + i])
            {
               pX = Main["player" + i];
               eSlot = pX.getEquipSlot();
               bagX = pX.getBag();
               for(i2 = 0; i2 <= 12; i2++)
               {
                  eq = eSlot.getEquipFromSlot(i2);
                  if(eq)
                  {
                     fX = eq.getFrame();
                     lv = eq.getReinforceLevel();
                     if(lv > 0 && (fX >= 524 && fX <= 527 || fX >= 529 && fX <= 532))
                     {
                        trace("修正 强化数值错误 装备槽 >>>>",i2,lv);
                        eq.reinforceClear();
                        for(i3 = 0; i3 < lv; i3++)
                        {
                           StrData.strengthenOver(eq,gemX);
                        }
                     }
                  }
               }
               for(i4 = 0; i4 < 24; i4++)
               {
                  eq = bagX.getEquipFromBag(i4);
                  if(eq)
                  {
                     fX = eq.getFrame();
                     lv = eq.getReinforceLevel();
                     if(lv > 0 && (fX >= 524 && fX <= 527 || fX >= 529 && fX <= 532))
                     {
                        trace("修正 强化数值错误 背包 >>>>",i4,lv);
                        eq.reinforceClear();
                        for(i5 = 0; i5 < lv; i5++)
                        {
                           StrData.strengthenOver(eq,gemX);
                        }
                     }
                  }
               }
            }
         }
      }
   }
}

