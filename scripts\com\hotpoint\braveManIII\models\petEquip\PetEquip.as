package com.hotpoint.braveManIII.models.petEquip
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.repository.petEquip.*;
   import src.*;
   import src.tool.*;
   
   public class PetEquip
   {
      
      private var _id:VT;
      
      public function PetEquip()
      {
         super();
      }
      
      public static function creatPetEquip(baseId:Number) : PetEquip
      {
         var equip:PetEquip = new PetEquip();
         equip._id = VT.createVT(baseId);
         return equip;
      }
      
      public function getId() : Number
      {
         return this._id.getValue();
      }
      
      public function getName() : String
      {
         return PetEquipFactory.findName(this._id.getValue());
      }
      
      public function getFrame() : Number
      {
         if(this._id.getValue() == 0)
         {
            return 1;
         }
         return int(PetEquipFactory.findFrame(this._id.getValue()));
      }
      
      public function getType() : Number
      {
         return PetEquipFactory.findType(this._id.getValue());
      }
      
      public function getPrice() : Number
      {
         return PetEquipFactory.findPrice(this._id.getValue());
      }
      
      public function getColor() : Number
      {
         return PetEquipFactory.findColor(this._id.getValue());
      }
      
      public function getSkillDescript() : String
      {
         return PetEquipFactory.findSkillDescript(this._id.getValue());
      }
      
      public function getDescript() : String
      {
         return PetEquipFactory.findDescript(this._id.getValue());
      }
      
      public function getAffect() : Array
      {
         return PetEquipFactory.findAffect(this._id.getValue());
      }
      
      public function getXingge() : Array
      {
         return PetEquipFactory.findXingge(this._id.getValue());
      }
      
      public function getXinggeTxT() : String
      {
         var str:* = "";
         var arr:Array = PetEquipFactory.findXingge(this._id.getValue());
         if(arr.length < 12)
         {
            for(i in arr)
            {
               if(arr[i] == 1)
               {
                  str += "热血 ";
               }
               if(arr[i] == 2)
               {
                  str += "坚韧 ";
               }
               if(arr[i] == 3)
               {
                  str += "领袖 ";
               }
               if(arr[i] == 4)
               {
                  str += "狂傲 ";
               }
               if(arr[i] == 5)
               {
                  str += "倔强 ";
               }
               if(arr[i] == 6)
               {
                  str += "敏锐 ";
               }
               if(arr[i] == 7)
               {
                  str += "激进 ";
               }
               if(arr[i] == 8)
               {
                  str += "聪慧 ";
               }
               if(arr[i] == 9)
               {
                  str += "暴躁 ";
               }
               if(arr[i] == 10)
               {
                  str += "稳重 ";
               }
               if(arr[i] == 11)
               {
                  str += "邪恶 ";
               }
               if(arr[i] == 12)
               {
                  str += "睿智 ";
               }
            }
         }
         else
         {
            str = "全部性格";
         }
         return str;
      }
      
      public function get id() : VT
      {
         return this._id;
      }
      
      public function set id(value:VT) : void
      {
         this._id = value;
      }
   }
}

