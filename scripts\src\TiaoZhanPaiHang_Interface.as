package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.models.petEquip.*;
   import com.hotpoint.braveManIII.models.petGem.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.petPanel.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class TiaoZhanPaiHang_Interface extends MovieClip
   {
      
      public static var _this:TiaoZhanPaiHang_Interface;
      
      public static var skin:MovieClip;
      
      public static var DuiHuanOpenX:int;
      
      public static var playTime:int;
      
      public static var Show1:PlayerShow = new PlayerShow();
      
      public static var Show2:PlayerShow = new PlayerShow();
      
      public static var Show3:PlayerShow = new PlayerShow();
      
      public static var pageNum:int = 1;
      
      public static var pageX:int = 9;
      
      public static var selPageX:int = 50;
      
      public static var selType:int = 0;
      
      public static var selTypeX:int = 0;
      
      public static var paiHangID_Arr:Array = new Array();
      
      public static var selTypeMaxNum:Array = [0,9,7,12,8];
      
      public static var buyPointArr:Array = [[],[0,25,50,25,50],[0,25,250,15,15],[0,50,0,0,0],[0]];
      
      public static var buyPointArr2:Array = [[],[0,10,20,10,20],[0,10,100,6,6],[0,35,0,0,0],[0]];
      
      private static var request:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-38150645.html");
      
      private static var request2:URLRequest = new URLRequest("http://my.4399.com/forums-thread-tagid-81127-id-42764228.html");
      
      private static var money:int = 0;
      
      private static var moneyObj:int = 0;
      
      private static var moneyID:int = 0;
      
      private static var moneyType:int = 1;
      
      public static var buyOkTime:int = 0;
      
      public static var playnum:int = 0;
      
      public static var strObj2:String = "";
      
      public static var getObjMC2:Boolean = false;
      
      public static var noClose:Boolean = false;
      
      public static var dengDai:Boolean = false;
      
      public static var ShowPlayer_selType:int = 0;
      
      public static var ShowPlayer_selTypeX:int = 0;
      
      public function TiaoZhanPaiHang_Interface()
      {
         super();
         pageNum = 1;
         selType = 0;
         selTypeX = 0;
         _this = this;
         visible = false;
      }
      
      public static function InitSkin() : *
      {
         _this.addChild(skin);
         Show1.x = 667;
         Show1.y = 265;
         skin.addChild(Show1);
         Show2.x = 546;
         Show2.y = 340;
         skin.addChild(Show2);
         Show3.x = 776;
         Show3.y = 374;
         skin.addChild(Show3);
         skin.addChild(skin.playName1);
         skin.addChild(skin.playName2);
         skin.addChild(skin.playName3);
         skin.addChild(skin.shuoMing_btn);
         skin.addChild(skin.duiHuan_mc);
         skin.addChild(skin.loading_mc);
         skin.addChild(skin["jfMC"]);
         skin.loading_mc.visible = false;
         skin.close_btn.addEventListener(MouseEvent.CLICK,Close);
         skin.next_btn.addEventListener(MouseEvent.CLICK,NextFun);
         skin.back_btn.addEventListener(MouseEvent.CLICK,BackFun);
         skin.baoXiang_1.addEventListener(MouseEvent.CLICK,DuiHuanOpen);
         skin.baoXiang_2.addEventListener(MouseEvent.CLICK,DuiHuanOpen);
         skin.baoXiang_3.addEventListener(MouseEvent.CLICK,DuiHuanOpen);
         skin.duiHuan_mc.close_btn.addEventListener(MouseEvent.CLICK,DuiHuanClose);
         skin.duiHuan_mc.b1_btn.addEventListener(MouseEvent.CLICK,DuiHuanBtn1);
         skin.duiHuan_mc.b2_btn.addEventListener(MouseEvent.CLICK,DuiHuanBtn2);
         skin.duiHuan_mc.b3_btn.addEventListener(MouseEvent.CLICK,DuiHuanBtn3);
         skin.duiHuan_mc.b4_btn.addEventListener(MouseEvent.CLICK,DuiHuanBtn4);
         skin.duiHuan_mc.b1_btn2.addEventListener(MouseEvent.CLICK,DuiHuanBtn2_1);
         skin.duiHuan_mc.b2_btn2.addEventListener(MouseEvent.CLICK,DuiHuanBtn2_2);
         skin.duiHuan_mc.b3_btn2.addEventListener(MouseEvent.CLICK,DuiHuanBtn2_3);
         skin.duiHuan_mc.b4_btn2.addEventListener(MouseEvent.CLICK,DuiHuanBtn2_4);
         skin.duiHuan_mc.b1_btn2.addEventListener(MouseEvent.MOUSE_MOVE,DuiHuanBtn2_MOVE);
         skin.duiHuan_mc.b2_btn2.addEventListener(MouseEvent.MOUSE_MOVE,DuiHuanBtn2_MOVE);
         skin.duiHuan_mc.b3_btn2.addEventListener(MouseEvent.MOUSE_MOVE,DuiHuanBtn2_MOVE);
         skin.duiHuan_mc.b4_btn2.addEventListener(MouseEvent.MOUSE_MOVE,DuiHuanBtn2_MOVE);
         skin.duiHuan_mc.b1_btn2.addEventListener(MouseEvent.MOUSE_OUT,DuiHuanBtn2_OUT);
         skin.duiHuan_mc.b2_btn2.addEventListener(MouseEvent.MOUSE_OUT,DuiHuanBtn2_OUT);
         skin.duiHuan_mc.b3_btn2.addEventListener(MouseEvent.MOUSE_OUT,DuiHuanBtn2_OUT);
         skin.duiHuan_mc.b4_btn2.addEventListener(MouseEvent.MOUSE_OUT,DuiHuanBtn2_OUT);
         skin.duiHuan_mc.info_btn.addEventListener(MouseEvent.CLICK,Web);
         skin.duiHuan_mc.info_btn2.addEventListener(MouseEvent.CLICK,Web2);
         skin.duiHuan_mc.XX_mc.mouseChildren = false;
         skin["XX0"].addEventListener(MouseEvent.CLICK,SelNum);
         skin["XX1"].addEventListener(MouseEvent.CLICK,SelNum);
         skin["XX2"].addEventListener(MouseEvent.CLICK,SelNum);
         skin["XX3"].addEventListener(MouseEvent.CLICK,SelNum);
         skin["XX4"].addEventListener(MouseEvent.CLICK,SelNum);
         skin["XX0"].mouseChildren = skin["XX1"].mouseChildren = skin["XX2"].mouseChildren = skin["XX3"].mouseChildren = skin["XX4"].mouseChildren = false;
         Show();
      }
      
      private static function Web(e:*) : *
      {
         navigateToURL(request,"_blank");
      }
      
      private static function Web2(e:*) : *
      {
         navigateToURL(request2,"_blank");
      }
      
      private static function DuiHuanBtn2_MOVE(e:*) : *
      {
         var num:int = int(e.target.name.substr(1,1));
         skin.duiHuan_mc.dianQuan_mc.visible = true;
         skin.duiHuan_mc.dianQuan_mc.x = e.target.x - 40;
         skin.duiHuan_mc.dianQuan_mc._txt.text = "消耗" + buyPointArr2[DuiHuanOpenX][num] + "点券可兑换该物品";
      }
      
      private static function DuiHuanBtn2_OUT(e:*) : *
      {
         skin.duiHuan_mc.dianQuan_mc.visible = false;
      }
      
      public static function DuiHuanOpen(e:MouseEvent = null) : *
      {
         if(!_this || !skin)
         {
            return;
         }
         if(e)
         {
            DuiHuanOpenX = (e.target.name as String).substr(9,1);
            TiaoShi.txtShow("? ============> DuiHuanOpenX = " + DuiHuanOpenX);
         }
         skin.duiHuan_mc.x = skin.duiHuan_mc.y = 0;
         skin.duiHuan_mc.gotoAndStop(DuiHuanOpenX);
         skin.duiHuan_mc.obj_mc.x = skin.duiHuan_mc.obj_mc.x = -5000;
         for(var i:int = 1; i < 5; i++)
         {
            skin.duiHuan_mc["X" + i].gotoAndStop(DuiHuanOpenX);
            skin.duiHuan_mc["t" + i + "_txt"].text = "x" + buyPointArr[DuiHuanOpenX][i];
         }
         Duihuan_BtnInit();
      }
      
      private static function Duihuan_BtnInit() : *
      {
         var num:int = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
         TiaoShi.txtShow("积分" + DuiHuanOpenX + " = " + num);
         skin.duiHuan_mc.dianQuan_mc.visible = false;
         for(var i:int = 1; i <= 4; i++)
         {
            if(buyPointArr[DuiHuanOpenX][i])
            {
               skin.duiHuan_mc["X" + i].visible = true;
               if(Boolean(buyPointArr[DuiHuanOpenX][i]) && num >= buyPointArr[DuiHuanOpenX][i])
               {
                  skin.duiHuan_mc["b" + i + "_btn"].visible = true;
                  skin.duiHuan_mc["b" + i + "_btn2"].visible = false;
               }
               else
               {
                  skin.duiHuan_mc["b" + i + "_btn"].visible = false;
                  skin.duiHuan_mc["b" + i + "_btn2"].visible = true;
               }
            }
            else
            {
               skin.duiHuan_mc["b" + i + "_btn"].visible = false;
               skin.duiHuan_mc["b" + i + "_btn2"].visible = false;
               skin.duiHuan_mc["X" + i].visible = false;
            }
         }
      }
      
      public static function DuiHuanClose(e:MouseEvent) : *
      {
         skin.duiHuan_mc.x = skin.duiHuan_mc.y = -5000;
         skin.duiHuan_mc.obj_mc.x = skin.duiHuan_mc.obj_mc.x = -5000;
      }
      
      private static function DianQuanDuiHuanOpen(num:int = 1, num2:int = 1, num3:int = 1, num4:int = 1) : *
      {
         money = num2;
         moneyID = num3;
         moneyType = num4;
         TiaoZhanPaiHang_Interface.moneyObj = moneyType;
         if(Shop4399.moneyAll.getValue() >= money)
         {
            if((moneyType == 1 || moneyType == 3 || moneyType == 4) && NewPetPanel.bag.backPetBagNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物背包已满!");
               return;
            }
            if((moneyType == 5 || moneyType == 6) && StoragePanel.storage.backSuppliesEmptyNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库\'消耗栏\' 空间不足!");
               return;
            }
            if((moneyType == 7 || moneyType == 8) && StoragePanel.storage.backOtherEmptyNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库\'其他栏\' 空间不足");
               return;
            }
            if(moneyType == 9 && StoragePanel.storage.backGemEmptyNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库\'宝石栏\' 空间不足");
               return;
            }
            skin.loading_mc.visible = true;
            Api_4399_All.BuyObj(moneyID);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"点券不足");
            TiaoZhanPaiHang_Interface.moneyObj = 0;
         }
      }
      
      public static function DianQuanDuiGetObj() : *
      {
         buyOkTime = 0;
         skin.addEventListener(Event.ENTER_FRAME,buyOkTimeGo);
      }
      
      public static function buyOkTimeGo(e:*) : *
      {
         var xArr:Array = null;
         var numXXX:int = 0;
         var num:int = 0;
         var rId:int = 0;
         var nameArr:Array = null;
         var strXXX:* = null;
         ++buyOkTime;
         if(buyOkTime < 27)
         {
            return;
         }
         skin.removeEventListener(Event.ENTER_FRAME,buyOkTimeGo);
         var showYn:Boolean = false;
         if(moneyObj == 1)
         {
            xArr = GetObjID();
            numXXX = int(xArr[0].getValue());
            playnum = xArr[1];
            NewPetPanel.bag.addPetBag(PetEquip.creatPetEquip(numXXX));
            showYn = true;
         }
         else if(moneyObj == 2)
         {
            NewPetPanel.XGkey.setValue(NewPetPanel.XGkey.getValue() + 1);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功!");
         }
         else if(moneyObj == 3)
         {
            playnum = 82;
            num = int(Math.random() * 12) + 1;
            NewPetPanel.bag.addPetBag(PetGem.creatPetGem(num));
            showYn = true;
         }
         else if(moneyObj == 4)
         {
            playnum = 95;
            NewPetPanel.LVkey.setValue(NewPetPanel.LVkey.getValue() + 1);
            showYn = true;
         }
         else if(moneyObj == 5)
         {
            StoragePanel.storage.addSuppliesStorage(SuppliesFactory.getSuppliesById(21226));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
         }
         else if(moneyObj == 6)
         {
            StoragePanel.storage.addSuppliesStorage(SuppliesFactory.getSuppliesById(21227));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
         }
         else if(moneyObj == 7)
         {
            StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63290));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
         }
         else if(moneyObj == 8)
         {
            StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63289));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
         }
         else
         {
            if(moneyObj != 9)
            {
               return;
            }
            rId = geiSuiPianID();
            nameArr = ["破魔印章碎片1","破魔印章碎片2","破魔印章碎片3","魔抗印章碎片1","魔抗印章碎片2","魔抗印章碎片3","神王印章碎片1","神王印章碎片2","神王印章碎片3","天煞印章碎片1","天煞印章碎片2","天煞印章碎片3"];
            StoragePanel.storage.addGemStorage(GemFactory.creatGemById(rId));
            strXXX = "获得:" + nameArr[rId - 34800] + " 已放入仓库";
            NewMC.Open("文字提示",Main._stage,480,400,60,0,true,1,strXXX);
         }
         if(showYn)
         {
            playTime = 60;
            skin.addEventListener(Event.ENTER_FRAME,PlayGO);
            skin.duiHuan_mc.obj_mc.x = skin.duiHuan_mc.obj_mc.y = 0;
            skin.duiHuan_mc.obj_mc.pic_mc.gotoAndStop(playnum);
         }
         skin.loading_mc.visible = false;
         moneyObj = 0;
         Duihuan_BtnInit();
         Show();
      }
      
      public static function GeiObjMC_Play() : *
      {
         if(noClose)
         {
            noClose = false;
            skin.loading_mc.visible = false;
            playTime = 60;
            skin.addEventListener(Event.ENTER_FRAME,PlayGO);
            skin.duiHuan_mc.obj_mc.x = skin.duiHuan_mc.obj_mc.y = 0;
            skin.duiHuan_mc.obj_mc.pic_mc.gotoAndStop(playnum);
            Show();
         }
      }
      
      public static function PlayGO(e:*) : *
      {
         --playTime;
         if(playTime <= 0)
         {
            skin.duiHuan_mc.obj_mc.x = skin.duiHuan_mc.obj_mc.y = -5000;
            skin.removeEventListener(Event.ENTER_FRAME,PlayGO);
         }
      }
      
      public static function GeiObjMC_Play2() : *
      {
         if(getObjMC2)
         {
            getObjMC2 = false;
            skin.loading_mc.visible = false;
            NewMC.Open("文字提示",Main._stage,480,400,60,0,true,1,strObj2);
         }
      }
      
      public static function DuiHuanBtn1(e:MouseEvent) : *
      {
         var num:int = 0;
         var xArr:Array = null;
         var numXXX:int = 0;
         var i3:int = 0;
         var i4:int = 0;
         var rId:int = 0;
         var nameArr:Array = null;
         var strXXX:* = null;
         if(DuiHuanOpenX == 1)
         {
            if(NewPetPanel.bag.backPetBagNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物背包已满!");
               return;
            }
            num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
            PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 25);
            xArr = GetObjID();
            numXXX = int(xArr[0].getValue());
            playnum = xArr[1];
            skin.loading_mc.visible = true;
            noClose = true;
            NewPetPanel.bag.addPetBag(PetEquip.creatPetEquip(numXXX));
            Duihuan_BtnInit();
            Main.Save();
         }
         else if(DuiHuanOpenX == 2)
         {
            i3 = int(StoragePanel.storage.backSuppliesEmptyNum());
            if(i3 >= 1)
            {
               StoragePanel.storage.addSuppliesStorage(SuppliesFactory.getSuppliesById(21226));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
               num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
               PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 25);
               Duihuan_BtnInit();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
            }
         }
         else if(DuiHuanOpenX == 3)
         {
            i4 = int(StoragePanel.storage.backGemEmptyNum());
            if(i4 >= 1)
            {
               rId = geiSuiPianID();
               skin.loading_mc.visible = true;
               nameArr = ["破魔印章碎片1","破魔印章碎片2","破魔印章碎片3","魔抗印章碎片1","魔抗印章碎片2","魔抗印章碎片3","神王印章碎片1","神王印章碎片2","神王印章碎片3","天煞印章碎片1","天煞印章碎片2","天煞印章碎片3"];
               StoragePanel.storage.addGemStorage(GemFactory.creatGemById(rId));
               strXXX = "获得:" + nameArr[rId - 34800] + " 已放入仓库";
               strObj2 = strXXX;
               num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
               PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - buyPointArr[3][1]);
               Duihuan_BtnInit();
               skin.loading_mc.visible = true;
               getObjMC2 = true;
               Main.Save();
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
            }
         }
         Show();
      }
      
      public static function DuiHuanBtn2(e:MouseEvent) : *
      {
         var num:int = 0;
         var i3:int = 0;
         if(DuiHuanOpenX == 1)
         {
            num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
            PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 50);
            NewPetPanel.XGkey.setValue(NewPetPanel.XGkey.getValue() + 1);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功!");
            Main.Save();
            Show();
         }
         else if(DuiHuanOpenX == 2)
         {
            i3 = int(StoragePanel.storage.backSuppliesEmptyNum());
            if(i3 >= 1)
            {
               StoragePanel.storage.addSuppliesStorage(SuppliesFactory.getSuppliesById(21227));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
               num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
               PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 250);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
            }
         }
         Duihuan_BtnInit();
         Show();
      }
      
      public static function DuiHuanBtn3(e:MouseEvent) : *
      {
         var num:int = 0;
         if(DuiHuanOpenX == 1)
         {
            if(NewPetPanel.bag.backPetBagNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物背包已满!");
               return;
            }
            num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
            PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 25);
            playnum = 82;
            skin.loading_mc.visible = true;
            noClose = true;
            num = int(Math.random() * 12) + 1;
            NewPetPanel.bag.addPetBag(PetGem.creatPetGem(num));
            Main.Save();
            Show();
         }
         else if(DuiHuanOpenX == 2)
         {
            if(StoragePanel.storage.backOtherEmptyNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
               return;
            }
            StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63290));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
            num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
            PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 15);
         }
         Duihuan_BtnInit();
         Show();
      }
      
      public static function DuiHuanBtn4(e:MouseEvent) : *
      {
         var num:int = 0;
         if(DuiHuanOpenX == 1)
         {
            if(NewPetPanel.bag.backPetBagNum() <= 0)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"宠物背包已满!");
               return;
            }
            num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
            PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 50);
            skin.loading_mc.visible = true;
            playnum = 95;
            noClose = true;
            NewPetPanel.LVkey.setValue(NewPetPanel.LVkey.getValue() + 1);
            Main.Save();
            Show();
         }
         else if(DuiHuanOpenX == 2)
         {
            if(StoragePanel.storage.backOtherEmptyNum() < 1)
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"仓库空间不足");
               return;
            }
            StoragePanel.storage.addOtherobjStorage(OtherFactory.creatOther(63289));
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"购买成功 物品已放入仓库");
            num = int(PaiHang_Data.jiFenArr[DuiHuanOpenX].getValue());
            PaiHang_Data.jiFenArr[DuiHuanOpenX].setValue(num - 15);
         }
         Duihuan_BtnInit();
         Show();
      }
      
      public static function DuiHuanBtn2_1(e:MouseEvent) : *
      {
         if(DuiHuanOpenX == 1)
         {
            DianQuanDuiHuanOpen(1,10,131,1);
         }
         else if(DuiHuanOpenX == 2)
         {
            DianQuanDuiHuanOpen(1,10,226,5);
         }
         else if(DuiHuanOpenX == 3)
         {
            DianQuanDuiHuanOpen(1,35,292,9);
         }
      }
      
      public static function DuiHuanBtn2_2(e:MouseEvent) : *
      {
         if(DuiHuanOpenX == 1)
         {
            DianQuanDuiHuanOpen(1,20,132,2);
         }
         else if(DuiHuanOpenX == 2)
         {
            DianQuanDuiHuanOpen(1,100,227,6);
         }
      }
      
      public static function DuiHuanBtn2_3(e:MouseEvent) : *
      {
         if(DuiHuanOpenX == 1)
         {
            DianQuanDuiHuanOpen(1,10,133,3);
         }
         else if(DuiHuanOpenX == 2)
         {
            DianQuanDuiHuanOpen(1,6,229,7);
         }
      }
      
      public static function DuiHuanBtn2_4(e:MouseEvent) : *
      {
         if(DuiHuanOpenX == 1)
         {
            DianQuanDuiHuanOpen(1,20,134,4);
         }
         else if(DuiHuanOpenX == 2)
         {
            DianQuanDuiHuanOpen(1,6,228,8);
         }
      }
      
      public static function geiSuiPianID() : int
      {
         var arrX:Array = [10,20,30,40,50,60,68,76,84,90,95,100];
         var rr:int = Math.random() * 100;
         for(var xx:int = 0; xx < arrX.length; xx++)
         {
            if(rr < arrX[xx])
            {
               rr = xx;
               break;
            }
         }
         return rr + 34800;
      }
      
      public static function GetObjID() : Array
      {
         var num:int = Math.random() * 10000 + 1;
         var numX:int = 0;
         for(var i:int = 1; i < PaiHang_Data.AllData.length; i++)
         {
            numX += PaiHang_Data.AllData[i][2].getValue();
            if(num <= numX)
            {
               return PaiHang_Data.AllData[i];
            }
         }
         return new Array();
      }
      
      public static function SelNum(e:MouseEvent) : *
      {
         var x:int = int((e.target.name as String).substr(2,1));
         selType = x;
         selTypeX = 0;
         TiaoShi.txtShow("大陆选择 = " + selType + "," + selTypeX);
         Show(true,true);
      }
      
      public static function Open() : *
      {
         LoadInGame.Open(TiaoZhan_Interface.loadData);
         Main._stage.addChild(_this);
         _this.visible = true;
         _this.x = _this.y = 0;
         Show(false,true);
      }
      
      public static function Close(e:* = null) : *
      {
         if(noClose)
         {
            return;
         }
         _this.visible = false;
         _this.x = _this.y = 5000;
      }
      
      public static function NextFun(e:* = null) : *
      {
         if(pageNum < 6)
         {
            ++pageNum;
            Show(false);
         }
      }
      
      public static function BackFun(e:* = null) : *
      {
         if(pageNum > 1)
         {
            --pageNum;
            Show(false);
         }
      }
      
      public static function Show(BtnInit_YN:Boolean = false, showPlay:Boolean = false) : *
      {
         var i:int = 0;
         var pId:int = 0;
         var p1p2:int = 0;
         var x:int = 0;
         var tmpObj1:Object = null;
         var tmpObj:* = undefined;
         if(!skin)
         {
            return;
         }
         skin["x0"].text = "查询中";
         skin["s0"].text = "查询中";
         for(i = 1; i < 10; i++)
         {
            x = (pageNum - 1) * 9 + i;
            skin["x" + i].text = "";
            skin["name" + i].text = "";
            skin["save" + i].text = "";
            skin["s" + i].text = "";
         }
         if(!_this || _this.visible == false)
         {
            return;
         }
         PaiHang_Data.InitSave();
         JifenUP();
         Btn_Init(BtnInit_YN);
         if(Main.P1P2)
         {
            p1p2 = 2;
         }
         else
         {
            p1p2 = 1;
         }
         var strXX:String = "gameNum_x" + p1p2 + "_" + selType;
         pId = int(PaiHang_Data[strXX][selTypeX]);
         var showPId:int = int(PaiHang_Data[strXX][0]);
         if(!PaiHang_Data.paiHangArr[pId])
         {
            Api_4399_All.GetOneRankInfo(Main.logName,pId,1);
            return;
         }
         if(!PaiHang_Data.paiHangArr[pId][1])
         {
            Api_4399_All.GetRankListsData(1,50,pId);
            return;
         }
         if(PaiHang_Data.paiHangArr[pId][0])
         {
            tmpObj1 = PaiHang_Data.paiHangArr[pId][0];
            skin["x0"].text = tmpObj1.rank;
            skin["s0"].text = tmpObj1.score;
            TiaoShi.txtShow("显示排行榜分数>>" + strXX + "," + tmpObj1.score + "," + selType + "," + selTypeX);
            if(Main.tiaoShiYN)
            {
               PaiHang_Data.getJiFen(selType,selTypeX,tmpObj1.score);
            }
         }
         else
         {
            skin["x0"].text = "未上榜";
            skin["s0"].text = "未上榜";
         }
         for(i = 1; i < 10; i++)
         {
            x = (pageNum - 1) * 9 + i;
            if(PaiHang_Data.paiHangArr[pId][x])
            {
               tmpObj = PaiHang_Data.paiHangArr[pId][x];
               skin["x" + i].text = x;
               skin["name" + i].text = tmpObj.userName;
               skin["save" + i].text = uint(tmpObj.index) + 1;
               skin["s" + i].text = tmpObj.score;
               if(x <= 3)
               {
                  TiaoZhanPaiHang_Interface["ShowArr" + x] = tmpObj.extra;
               }
            }
         }
         if(pageNum != 1)
         {
            skin["xxx_mc"].visible = false;
         }
         else
         {
            skin["xxx_mc"].visible = true;
         }
         skin["jf0"].text = PaiHang_Data.jiFenArr[1].getValue() + "/500";
         skin["jf1"].text = PaiHang_Data.jiFenArr[2].getValue() + "/500";
         skin["jf2"].text = PaiHang_Data.jiFenArr[3].getValue() + "/500";
         skin["jf3"].text = PaiHang_Data.jiFenArr[4].getValue() + "/500";
         if(showPlay)
         {
            ShowPlayerLoad(showPId);
         }
      }
      
      public static function JifenUP() : Boolean
      {
         var strArr:Array = null;
         var daLu:int = 0;
         var num:int = 0;
         var pId:int = 0;
         var p1p2:int = 0;
         var tmpObj1:Object = null;
         var rank:int = 0;
         var numX:int = 0;
         var jf:int = 0;
         if(dengDai)
         {
            return false;
         }
         if(PaiHang_Data.jiFenArr[0].getValue() < Main.serverTime.getValue())
         {
            if(Main.P1P2)
            {
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x2_1[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x2_1[0],1);
                  dengDai = true;
                  return false;
               }
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x2_2[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x2_2[0],1);
                  dengDai = true;
                  return false;
               }
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x2_3[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x2_3[0],1);
                  dengDai = true;
                  return false;
               }
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x2_0[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x2_0[0],1);
                  dengDai = true;
                  return false;
               }
            }
            else
            {
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x1_1[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x1_1[0],1);
                  dengDai = true;
                  return false;
               }
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x1_2[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x1_2[0],1);
                  dengDai = true;
                  return false;
               }
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x1_3[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x1_3[0],1);
                  dengDai = true;
                  return false;
               }
               if(!PaiHang_Data.paiHangArr[PaiHang_Data.gameNum_x1_0[0]])
               {
                  Api_4399_All.GetOneRankInfo(Main.logName,PaiHang_Data.gameNum_x1_0[0],1);
                  dengDai = true;
                  return false;
               }
            }
            strArr = ["总战绩排名:","勇者大陆排名:","暗黑大陆排名:","失落大陆排名:"];
            daLu = int(strArr.length - 1);
            for(num = 0; num <= daLu; num++)
            {
               if(Main.P1P2)
               {
                  p1p2 = 2;
               }
               else
               {
                  p1p2 = 1;
               }
               pId = int(PaiHang_Data["gameNum_x" + p1p2 + "_" + num][0]);
               skin["jfMC"]["jfx" + num].text = "0";
               skin["jfMC"]["x_" + num].text = strArr[num] + " 未进榜";
               if(Boolean(PaiHang_Data.paiHangArr[pId]) && Boolean(PaiHang_Data.paiHangArr[pId][0]))
               {
                  tmpObj1 = PaiHang_Data.paiHangArr[pId][0];
                  rank = int(tmpObj1.rank);
                  numX = num;
                  if(numX == 0)
                  {
                     numX = 4;
                  }
                  jf = int((PaiHang_Data.jiFenArr[numX] as VT).getValue());
                  if(rank == InitData.tiaoZhanJF_1.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(jf + InitData.tiaoZhanJF_100.getValue());
                     skin["jfMC"]["jfx" + num].text = "100";
                  }
                  else if(rank == InitData.tiaoZhanJF_2.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(jf + InitData.tiaoZhanJF_80.getValue());
                     skin["jfMC"]["jfx" + num].text = "80";
                  }
                  else if(rank == InitData.tiaoZhanJF_3.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(jf + InitData.tiaoZhanJF_60.getValue());
                     skin["jfMC"]["jfx" + num].text = "60";
                  }
                  else if(rank < InitData.tiaoZhanJF_10.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(jf + InitData.tiaoZhanJF_40.getValue());
                     skin["jfMC"]["jfx" + num].text = "40";
                  }
                  else if(rank < InitData.tiaoZhanJF_100.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(jf + InitData.tiaoZhanJF_20.getValue());
                     skin["jfMC"]["jfx" + num].text = "20";
                  }
                  else if(rank <= InitData.tiaoZhanJF_500.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(jf + InitData.tiaoZhanJF_10.getValue());
                     skin["jfMC"]["jfx" + num].text = "10";
                  }
                  if(PaiHang_Data.jiFenArr[numX].getValue() > InitData.tiaoZhanJF_500.getValue())
                  {
                     PaiHang_Data.jiFenArr[numX] = VT.createVT(InitData.tiaoZhanJF_500.getValue());
                  }
                  if(rank > 0 && rank <= 5000)
                  {
                     skin["jfMC"]["x_" + num].text = strArr[num] + " 第" + rank + "名";
                  }
                  skin["jfMC"].x = skin["jfMC"].y = 0;
                  skin["jfMC"].ok_btn.addEventListener(MouseEvent.CLICK,jfMC_Close);
               }
               if(num == daLu)
               {
                  PaiHang_Data.jiFenArr[0].setValue(Main.serverTime.getValue());
                  Main.Save();
                  Show(false,true);
                  return true;
               }
            }
         }
         return true;
      }
      
      private static function jfMC_Close(e:*) : *
      {
         skin["jfMC"].x = skin["jfMC"].y = -5000;
         Show();
      }
      
      private static function Btn_Init(BtnInit_YN:Boolean) : *
      {
         var i:int = 0;
         var btn:MovieClip = null;
         var mcNum:int = selTypeX;
         for(i = 0; i < 5; i++)
         {
            if(selType == i)
            {
               skin["XX" + i].gotoAndStop(3);
            }
            else
            {
               skin["XX" + i].gotoAndStop(2);
            }
         }
         var max:int = int(selTypeMaxNum[selType]);
         for(i = 0; i <= 999; i++)
         {
            if(!skin["btn" + i])
            {
               break;
            }
            btn = skin["btn" + i];
            btn.mouseChildren = false;
            btn.addEventListener(MouseEvent.CLICK,btnCLICK);
            btn.addEventListener(MouseEvent.MOUSE_MOVE,btnMOVE);
            btn.addEventListener(MouseEvent.MOUSE_OUT,btnMOUSE_OUT);
            if(i > 0 && BtnInit_YN)
            {
               btn.x = (i - 1) * 28 + 61;
            }
            btn.gotoAndStop(1);
            if(selTypeX == i)
            {
               btn.gotoAndStop(3);
            }
            if(i <= max && btn.x > 60 && btn.x < 365)
            {
               btn.visible = true;
            }
            else
            {
               btn.visible = false;
            }
            if(i == 0)
            {
               btn.visible = true;
            }
         }
         var mcNumX:int = 1;
         if(selType == 2)
         {
            mcNum += 9;
         }
         else if(selType == 3)
         {
            mcNum += 50;
         }
         else if(selType == 4)
         {
            mcNum += 17;
         }
         if(selTypeX == 0)
         {
            skin["guanka_mc"].gotoAndStop("x" + selType);
         }
         else
         {
            skin["guanka_mc"].gotoAndStop("d" + mcNum);
         }
      }
      
      private static function btnCLICK(e:MouseEvent) : *
      {
         var move:int = 0;
         var i:int = 0;
         var num:int = int((e.target.name as String).substr(3));
         e.target.gotoAndStop(3);
         pageNum = 1;
         selTypeX = num;
         TiaoShi.txtShow("选择" + num);
         var btn:MovieClip = e.target;
         var max:int = int(selTypeMaxNum[selType]);
         var one:int = 1;
         if(max > 10)
         {
            if(btn.x < 90)
            {
               one -= 9;
               if(one < 1)
               {
                  one = 1;
               }
            }
            else if(btn.x > 300)
            {
               one += 9;
               if(one > max - 9)
               {
                  one = max - 9;
               }
            }
            move = (one - 1) * 28;
            TiaoShi.txtShow("one = " + one + ", move = " + move);
            for(i = 1; i <= max; i++)
            {
               btn = skin["btn" + i];
               btn.x = (i - 1) * 28 + 61 - move;
            }
         }
         Show();
      }
      
      private static function btnMOVE(e:MouseEvent) : *
      {
         var num:int = int((e.target.name as String).substr(3));
         if(selTypeX != num)
         {
            e.target.gotoAndStop(2);
         }
      }
      
      private static function btnMOUSE_OUT(e:MouseEvent) : *
      {
         var num:int = int((e.target.name as String).substr(3));
         if(selTypeX != num)
         {
            e.target.gotoAndStop(1);
         }
      }
      
      public static function ShowPlayerLoad(pId:int) : *
      {
         var mc:MovieClip = null;
         var tmpObj:* = undefined;
         var arrXX:Array = null;
         var yn:Boolean = false;
         TiaoShi.txtShow("ShowPlayerLoad >>" + pId);
         for(var i:int = 1; i <= 3; i++)
         {
            mc = TiaoZhanPaiHang_Interface["Show" + i];
            mc.visible = false;
         }
         var loadNum:int = 0;
         if(PaiHang_Data.paiHangArr[pId])
         {
            for(i = 1; i <= 3; i++)
            {
               if(PaiHang_Data.paiHangArr[pId][i])
               {
                  tmpObj = PaiHang_Data.paiHangArr[pId][i];
                  if(tmpObj.extra)
                  {
                     TiaoShi.txtShow("--tmpObj.extra:  " + tmpObj.extra);
                     if(tmpObj.extra is String)
                     {
                        arrXX = (tmpObj.extra as String).split(",");
                        tmpObj.extra = [arrXX[0],arrXX[1],arrXX[2],[arrXX[3],arrXX[4],arrXX[5]],[arrXX[6],arrXX[7],arrXX[8]]];
                     }
                     if(tmpObj.extra is Array)
                     {
                        yn = Boolean((TiaoZhanPaiHang_Interface["Show" + i] as PlayerShow).Loading(tmpObj.extra));
                        if(yn)
                        {
                           loadNum++;
                        }
                     }
                     else
                     {
                        if(tmpObj.extra is String)
                        {
                           TiaoShi.txtShow("--tmpObj.extra: is String ???? ====> " + i);
                        }
                        else if(tmpObj.extra is Object)
                        {
                           TiaoShi.txtShow("--tmpObj.extra: is Object ???? ====> " + i);
                        }
                        (TiaoZhanPaiHang_Interface["Show" + i] as PlayerShow).Loading();
                        loadNum++;
                     }
                  }
                  else
                  {
                     (TiaoZhanPaiHang_Interface["Show" + i] as PlayerShow).Loading();
                     loadNum++;
                  }
               }
            }
         }
         ShowPlayer_selType = selType;
         ShowPlayer_selTypeX = selTypeX;
         if(loadNum >= 3 && Boolean(NewLoad.loadingYN))
         {
            ShowPlayer();
         }
      }
      
      public static function ShowPlayer() : *
      {
         var p1p2:int = 0;
         var mc:MovieClip = null;
         TiaoShi.txtShow("ShowPlayer xxxxxxx");
         if(Main.P1P2)
         {
            p1p2 = 2;
         }
         else
         {
            p1p2 = 1;
         }
         var pId:int = int(PaiHang_Data["gameNum_x" + p1p2 + "_" + selType][0]);
         for(i = 1; i <= 3; ++i)
         {
            mc = TiaoZhanPaiHang_Interface["Show" + i];
            mc.visible = false;
            (TiaoZhanPaiHang_Interface["Show" + i] as PlayerShow).AddSkin();
            if(Boolean(PaiHang_Data.paiHangArr[pId]) && Boolean(PaiHang_Data.paiHangArr[pId][i]) && Boolean(PaiHang_Data.paiHangArr[pId][i].userName))
            {
               skin["playName" + i].text = PaiHang_Data.paiHangArr[pId][i].userName;
            }
            else
            {
               skin["playName" + i].text = "";
            }
         }
         skin.loading_mc.visible = false;
      }
   }
}

