package src._data
{
   import com.hotpoint.braveManIII.Tool.*;
   import com.hotpoint.braveManIII.models.common.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.utils.ByteArray;
   import src.*;
   import src.tool.*;
   
   public class Data_KillShop extends MovieClip
   {
      
      public static var data:XML;
      
      public static var data2:XML;
      
      public static var dataXXX:ByteArray;
      
      public static var buyTime:VT = VT.createVT(0);
      
      public static var buyArr:Array = new Array();
      
      public function Data_KillShop()
      {
         super();
      }
      
      public static function Init() : *
      {
         data = XMLAsset.createXML(Data2.data1XX);
         trace("Data_Xml ok");
         data2 = XMLAsset.createXML(Data2.data2XX);
         trace("Data_Xml2 ok");
         Xml_KillShopArr();
         trace("Xml_KillShopArr");
         TestData_Init();
         trace("TestData_Init");
      }
      
      private static function TestData_Init() : *
      {
         var arr:Array = [data,data2];
         dataXXX = Obj_Compare.getObj_ByteArray(arr);
      }
      
      public static function TestData() : *
      {
         var arr:Array = [data,data2];
         var dataXXX2:ByteArray = Obj_Compare.getObj_ByteArray(arr);
         if(Obj_Compare.CompareByteArray(dataXXX,dataXXX2) == false)
         {
            Main.NoGame();
         }
         trace("检测通过");
         TiaoShi.txtShow("检测通过");
      }
      
      public static function BuyArrInit() : *
      {
         var numVT:VT = null;
         var j:int = 0;
         var i:int = 0;
         var i2:int = 0;
         if(Main.serverTime.getValue() > buyTime.getValue())
         {
            numVT = VT.createVT(3);
            for(j = 0; j < Main.player1.getTitleSlot().getListLength(); j++)
            {
               if(Main.player1.getTitleSlot().getTitleFromSlot(j).getId() == 25)
               {
                  numVT = VT.createVT(4);
               }
            }
            buyTime.setValue(Main.serverTime.getValue());
            for(i = 1; i < 5; i++)
            {
               buyArr[i] = new Array();
               buyArr[i][0] = VT.createVT(numVT.getValue());
               for(i2 = 1; i2 < ShopKillPoint.ShopArr2.length + 1; i2++)
               {
                  buyArr[i][i2] = false;
               }
            }
            ShopKillPoint.shopX.Show2();
         }
         else
         {
            buyTime.setValue(Main.serverTime.getValue());
         }
      }
      
      private static function Xml_KillShopArr() : *
      {
         var i2:int = 0;
         SelType();
         var arr2:Array = [-1];
         for(i2 in data2.击杀商城)
         {
            TestData_2(i2);
            arr2[i2 + 1] = new Array();
            arr2[i2 + 1][1] = int(data2.击杀商城[i2].ID);
            arr2[i2 + 1][2] = String(data2.击杀商城[i2].类型);
            arr2[i2 + 1][3] = VT.createVT(int(data2.击杀商城[i2].物品ID));
            arr2[i2 + 1][4] = String(data2.击杀商城[i2].名称);
            arr2[i2 + 1][5] = int(data2.击杀商城[i2].图标);
            arr2[i2 + 1][6] = String(data2.击杀商城[i2].说明);
            arr2[i2 + 1][7] = VT.createVT(int(data2.击杀商城[i2].金币));
            arr2[i2 + 1][8] = VT.createVT(int(data2.击杀商城[i2].击杀点));
         }
         ShopKillPoint.ShopArr2 = arr2;
      }
      
      public static function SelType(selType:int = 0) : *
      {
         var i:int = 0;
         var arr:Array = [-1];
         var total:int = 1;
         var temp:int = 0;
         arr[total] = new Array();
         for(i in data.击杀商城)
         {
            if(!(selType == 1 && String(data.击杀商城[i].类型) != "宝石类"))
            {
               if(!(selType == 2 && String(data.击杀商城[i].类型) != "时装类"))
               {
                  if(!(selType == 3 && String(data.击杀商城[i].类型) != "其他类"))
                  {
                     if(temp == ShopKillPoint.numX)
                     {
                        total++;
                        arr[total] = new Array();
                        temp = 0;
                     }
                     temp++;
                     TestData_1(i);
                     arr[total][temp] = new Array();
                     arr[total][temp][1] = int(data.击杀商城[i].ID);
                     arr[total][temp][2] = String(data.击杀商城[i].类型);
                     arr[total][temp][3] = VT.createVT(int(data.击杀商城[i].物品ID));
                     arr[total][temp][4] = String(data.击杀商城[i].名称);
                     arr[total][temp][5] = int(data.击杀商城[i].图标);
                     arr[total][temp][6] = String(data.击杀商城[i].说明);
                     arr[total][temp][7] = VT.createVT(int(data.击杀商城[i].金币));
                     arr[total][temp][8] = VT.createVT(int(data.击杀商城[i].击杀点));
                  }
               }
            }
         }
         ShopKillPoint.ShopArr = arr;
         ShopKillPoint.numTotal = total;
      }
      
      private static function TestData_1(i:uint) : *
      {
         var numAll:int = int(data.击杀商城[i].验证);
         var num1:int = int(data.击杀商城[i].随机数);
         var num2:int = int(data.击杀商城[i].击杀点);
         var num3:int = int(data.击杀商城[i].金币);
         var num4:int = int(data.击杀商城[i].图标);
         var num5:int = int(data.击杀商城[i].物品ID);
         if(numAll != num1 + num2 + num3 + num4 + num5)
         {
            Main.NoGame("击杀点商城数据错误 #1");
         }
      }
      
      private static function TestData_2(i:uint) : *
      {
         var numAll:int = int(data2.击杀商城[i].验证);
         var num1:int = int(data2.击杀商城[i].随机数);
         var num2:int = int(data2.击杀商城[i].击杀点);
         var num3:int = int(data2.击杀商城[i].金币);
         var num4:int = int(data2.击杀商城[i].图标);
         var num5:int = int(data2.击杀商城[i].物品ID);
         if(numAll != num1 + num2 + num3 + num4 + num5)
         {
            Main.NoGame("击杀点商城数据错误 #2");
         }
      }
   }
}

