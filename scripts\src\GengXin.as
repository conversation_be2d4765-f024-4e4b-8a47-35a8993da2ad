package src
{
   import com.*;
   import com.adobe.serialization.json.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.net.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class GengXin extends MovieClip
   {
      
      public static var _this:GengXin;
      
      public static var yn:Boolean;
      
      public static var saveVar:int = 0;
      
      public static var saveTime:String = "2014/07/10";
      
      public static var saveTimeNow:String = "2014/07/10";
      
      public static var timeX:int = 0;
      
      public static var timeMax:int = 27 * 60 * 30;
      
      public static var selNum:int = 0;
      
      public static var selVar:int = 0;
      
      public static var g1:GengXin1 = new GengXin1();
      
      public static var g2:GengXin2 = new GengXin2();
      
      public function GengXin()
      {
         super();
         addEventListener(Event.ENTER_FRAME,onTime);
      }
      
      public static function CaXun() : *
      {
         if(!_this)
         {
            _this = new GengXin();
         }
         _this.addEventListener(Event.ENTER_FRAME,onTime);
      }
      
      public static function onTime(e:*) : *
      {
         var tempArr:Array = null;
         ++timeX;
         if(timeX >= timeMax && Boolean(TimeXX()) && selNum < 7)
         {
            timeX = 0;
            tempArr = [57];
            Api_4399_GongHui.getNum(tempArr);
            ++selNum;
            TiaoShi.txtShow("GengXin 查询次数:" + selNum);
         }
      }
      
      public static function Go(num:int = 0, varX:int = 0) : *
      {
         TiaoShi.txtShow("GengXin.Go() ===> " + num);
         if(num % 4 == 0)
         {
            TiaoShi.txtShow("Go 默认状态 timeMax = " + timeMax);
         }
         else if(num % 4 == 1)
         {
            TiaoShi.txtShow("Go 更新提醒");
            if(!Main.tiaoShiYN && Main.gameNum.getValue() == 0 && saveVar < varX)
            {
               g1.x = g1.y = 0;
               Main._stage.addChild(g1);
               GengXin.saveTime = GengXin.saveTimeNow;
               saveVar = varX;
               Main.Save();
            }
            _this.addEventListener(Event.ENTER_FRAME,onTime);
         }
         else if(num % 4 == 2)
         {
            TiaoShi.txtShow("Go 刷新游戏");
            if(!Main.tiaoShiYN && Main.gameNum.getValue() == 0 && saveVar < varX)
            {
               g2.x = g2.y = 0;
               Main._stage.addChild(g2);
               GengXin.saveTime = GengXin.saveTimeNow;
               saveVar = varX;
               Main.Save();
            }
            _this.addEventListener(Event.ENTER_FRAME,onTime);
         }
         else if(num % 4 == 3)
         {
            TiaoShi.txtShow("Go 停止侦听");
            _this.removeEventListener(Event.ENTER_FRAME,onTime);
         }
      }
      
      private static function TimeXX() : Boolean
      {
         var date:Date = new Date();
         var dateStr:String = date.getFullYear() + "/" + (date.getMonth() + 1) + "/" + date.getDate();
         var date1:Date = new Date(dateStr);
         var date2:Date = new Date(saveTime);
         saveTimeNow = date1;
         if(date1 > date2)
         {
            return true;
         }
         return false;
      }
      
      public static function reGame() : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            navigateToURL(new URLRequest("javascript:window.location.reload( false );"),"_self");
         }
         _this.addEventListener(Event.ENTER_FRAME,reGameGO);
      }
      
      public static function reGameGO(e:*) : *
      {
         if(Main.gameNum.getValue() == 0)
         {
            navigateToURL(new URLRequest("javascript:window.location.reload( false );"),"_self");
         }
      }
   }
}

