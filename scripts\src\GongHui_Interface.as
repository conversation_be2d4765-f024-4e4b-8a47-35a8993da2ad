package src
{
   import com.*;
   import com.efnx.fps.*;
   import com.hotpoint.braveManIII.models.container.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.repository.supplies.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import com.hotpoint.braveManIII.views.setKeyPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.*;
   import src.tool.*;
   
   public class GongHui_Interface extends MovieClip
   {
      
      private static var loadData:ClassLoader;
      
      public static var _this:GongHui_Interface;
      
      public static var LoadingYN:Boolean;
      
      public static var gongHui_ID:int;
      
      public static var playerInfo:Object;
      
      public static var ListInfo:Object;
      
      public static var BHCY_List:Array;
      
      public static var senHeListInfo:Object;
      
      private static var init:MovieClip;
      
      private static var senQing:MovieClip;
      
      private static var chuangJian:MovieClip;
      
      private static var senHe:MovieClip;
      
      public static var JieMian:MovieClip;
      
      public static var DaTing:MovieClip;
      
      private static var JuanXian:MovieClip;
      
      private static var RenWu:MovieClip;
      
      private static var HuoYue:MovieClip;
      
      private static var DaTing_mc:MovieClip;
      
      private static var selNum:int;
      
      public static var chuanJian_mc:MovieClip;
      
      private static var loadName:String = "GongHui_v1080.swf";
      
      public static var senhe_page:int = 1;
      
      public static var GHLB_page:int = 1;
      
      private static var gh_Page:int = 1;
      
      private static var gh_PageNum:int = 13;
      
      private static var sq_Page:int = 1;
      
      private static var sq_PageNum:int = 10;
      
      private static var sh_Page:int = 1;
      
      private static var sh_PageNum:int = 10;
      
      private static var gongHuiXXX:Boolean = false;
      
      public static var gongHuiShow:Boolean = false;
      
      public static var gongHuiNumMax:int = 0;
      
      public static var guoLv:Api4399WordCheck = new Api4399WordCheck();
      
      public static var dengJiArr:Array = [0,"80","168","360","568","904","1024","1152","1424","1712","2320","2480","2816","3168","3720","4488","4888","5512","6376","7496","8616","9728","10840","11952","13064","暂未开启该等级"];
      
      public static var dengJiArr2:Array = [0,"20","22","24","26","28","30","32","34","36","38","40","42","44","46","48","50","52","54","56","58","60","62","64","66","68"];
      
      public static var xxArr:Array = [0,0,0];
      
      public function GongHui_Interface()
      {
         super();
         _this = this;
      }
      
      private static function onClose(e:MouseEvent) : *
      {
         var tempMc:MovieClip = e.target.parent;
         tempMc.visible = false;
         if(tempMc == senHe)
         {
            senHeListInfo = null;
            BHCY_List = null;
         }
         if(tempMc == init || tempMc == JieMian)
         {
            playerInfo = null;
            gongHuiShow = false;
         }
         if(tempMc == DaTing)
         {
            GongGaoXX();
            JuanXian.visible = false;
         }
      }
      
      public static function Open() : *
      {
         Main.DuoKai_Fun();
         LoadSkin();
         if(_this)
         {
            _this.visible = true;
            _this.x = _this.y = 0;
         }
      }
      
      private static function LoadSkin() : *
      {
         loadData = new ClassLoader(loadName);
         loadData.addEventListener(Event.COMPLETE,onSkinOK);
         LoadInGame.Open(loadData);
      }
      
      private static function onSkinOK(e:*) : *
      {
         var classRef1:Class = loadData.getClass("创建申请界面") as Class;
         init = new classRef1();
         var classRef2:Class = loadData.getClass("申请加入界面") as Class;
         senQing = new classRef2();
         var classRef3:Class = loadData.getClass("创建公会界面") as Class;
         chuangJian = new classRef3();
         var classRef4:Class = loadData.getClass("审核界面") as Class;
         senHe = new classRef4();
         var classRef5:Class = loadData.getClass("公会界面") as Class;
         JieMian = new classRef5();
         JieMian.visible = false;
         var classRef6:Class = loadData.getClass("公会大厅") as Class;
         DaTing = new classRef6();
         var classRef7:Class = loadData.getClass("捐献界面") as Class;
         JuanXian = new classRef7();
         JuanXian.visible = false;
         var classRef8:Class = loadData.getClass("任务界面") as Class;
         RenWu = new classRef8();
         var classRef9:Class = loadData.getClass("活跃度界面") as Class;
         HuoYue = new classRef9();
         var classRef10:Class = loadData.getClass("公会活动") as Class;
         GongHuiTiaoZan.HuoDong = new classRef10();
         var classRef11:Class = loadData.getClass("公会挑战") as Class;
         GongHuiTiaoZan.TiaoZhan = new classRef11();
         var classRef12:Class = loadData.getClass("公会祭坛") as Class;
         GongHui_jiTan.jiTan = new classRef12();
         var temp:GongHui_Interface = new GongHui_Interface();
         Main._stage.addChild(temp);
         LoadingYN = true;
         TiaoShi.txtShow("加载图形完成");
         Api_4399_GongHui.SelUserInfo();
         TiaoShi.txtShow("加载玩家信息");
      }
      
      public static function PlayerInfoOk(objX:Object) : *
      {
         playerInfo = objX;
         TiaoShi.txtShow("加载玩家信息完成");
         if(objX.unionInfo)
         {
            gongHui_ID = objX.unionInfo.id;
            TiaoShi.txtShow("公会ID" + gongHui_ID);
            if(gongHuiShow || JieMian.visible)
            {
               TiaoShi.txtShow("公会界面 刷新显示");
               ShowExp();
               gongHuiShow = false;
            }
            else
            {
               TiaoShi.txtShow("公会界面 打开");
               JieMianOpen();
            }
         }
         else
         {
            TiaoShi.txtShow("无公会");
            gongHuiXXX = true;
            InitOpen();
         }
      }
      
      public static function InfoXX() : *
      {
         var str:String = GetInfo();
         var arr:Array = (playerInfo.member.extra as String).split("$");
         if(playerInfo.member.extra != str)
         {
            Api_4399_GongHui.InfoXX(str);
            TiaoShi.txtShow("修改个人信息" + str);
            playerInfo.member.extra = str;
         }
      }
      
      public static function GetInfo() : String
      {
         var str:String = "v1" + "$" + Main.player1.getLevel() + "$" + Main.player1.GetZY() + "$" + Main.logName2;
         TiaoShi.txtShow("获取个人信息" + str);
         return str;
      }
      
      public static function ListOk(objX:Object = null) : *
      {
         if(objX)
         {
            ListInfo = objX;
            gongHuiNumMax = objX.rowCount;
            TiaoShi.txtShow("加载公会列表完成");
            ListInfoPaiXu();
            SenQingOpen();
         }
         else
         {
            TiaoShi.txtShow("加载公会列表失败");
         }
      }
      
      public static function ListInfoPaiXu() : *
      {
         var arr:Array = ListInfo.unionList;
         for(var i:int = 0; i < arr.length; i++)
         {
            TiaoShi.txtShow(arr[i].title + "," + arr[i].level + "," + arr[i].count);
         }
         arr.sortOn(["level","count"],Array.DESCENDING | Array.NUMERIC);
      }
      
      public static function SenHeListOk(objX:Object = null) : *
      {
         if(objX)
         {
            senHeListInfo = objX;
            TiaoShi.txtShow("加载待审核列表完成");
            SenHeOpen();
         }
         else
         {
            TiaoShi.txtShow("加载待审核列表失败");
         }
      }
      
      public static function CengYuanListOk(arr:Array) : *
      {
         if(arr)
         {
            BHCY_List = arr;
            TiaoShi.txtShow("加载帮会成员列表完成");
            daTing_Open();
         }
         else
         {
            TiaoShi.txtShow("加载帮会成员列表失败");
         }
      }
      
      public static function JieMianOpen() : *
      {
         gongHuiXXX = false;
         InfoXX();
         GongHuiOpen();
      }
      
      public static function GongHuiOpen() : *
      {
         var tempArr:Array = [157,158,159,160,161,162,163];
         Api_4399_GongHui.getNum(tempArr);
         _this.x = _this.y = 0;
         JieMian.visible = true;
         JieMian.x = JieMian.y = 0;
         _this.addChild(JieMian);
         if(playerInfo.unionInfo.uId != playerInfo.member.uId || playerInfo.member.roleId == "10")
         {
            JieMian.senHe_btn.visible = false;
         }
         JieMian.senHe_btn.addEventListener(MouseEvent.CLICK,SenHeOpen);
         JieMian.daTing_btn.addEventListener(MouseEvent.CLICK,daTing_Open);
         JieMian.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         JieMian.renWu_btn.addEventListener(MouseEvent.CLICK,RenWu_Open);
         JieMian.huoYue_btn.addEventListener(MouseEvent.CLICK,HuoYue_Open);
         JieMian.qt_btn.addEventListener(MouseEvent.CLICK,QiTa_Open);
         JieMian.huoDong_btn.addEventListener(MouseEvent.CLICK,GongHuiTiaoZan.Open_HD);
         JieMian.jiTan_btn.addEventListener(MouseEvent.CLICK,GongHui_jiTan.Open);
      }
      
      public static function QiTa_Open(e:*) : *
      {
         JiaRu_Open();
      }
      
      public static function InitOpen() : *
      {
         _this.x = _this.y = 0;
         _this.addChild(init);
         init.visible = true;
         init.x = init.y = 0;
         init.jiaRu_btn.addEventListener(MouseEvent.CLICK,JiaRu_Open);
         init.chuanjian_btn.addEventListener(MouseEvent.CLICK,Chuanjian_Open);
         init.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function SenQingOpen() : *
      {
         var txtX:Object = null;
         var numX:int = 0;
         TiaoShi.txtShow("公会列表界面 SenQingOpen()");
         init.visible = false;
         _this.x = _this.y = 0;
         senQing.visible = true;
         senQing.x = senQing.y = 0;
         _this.addChild(senQing);
         var arr:Array = ListInfo.unionList;
         for(var i:int = 1; i <= sq_PageNum; i++)
         {
            numX = (sq_Page - 1) * sq_PageNum + i - 1;
            txtX = senQing["num_" + i];
            txtX._txt1.text = txtX._txt2.text = txtX._txt3.text = txtX._txt4.text = txtX._txt5.text = "";
            txtX._btn6.visible = txtX._mc7.visible = false;
            if(arr[numX])
            {
               txtX._txt1.text = numX + 1;
               txtX._txt2.text = arr[numX].title;
               txtX._txt3.text = arr[numX].level;
               txtX._txt4.text = arr[numX].username;
               txtX._txt5.text = arr[numX].count;
               if(gongHuiXXX)
               {
                  txtX._btn6.addEventListener(MouseEvent.CLICK,senQingFun);
                  txtX._btn6.visible = txtX._mc7.visible = true;
                  if(arr[numX].noBtn)
                  {
                     txtX._btn6.visible = false;
                  }
               }
            }
         }
         senQing.page_txt.text = sq_Page + "/" + int(arr.length / sq_PageNum + 1);
         TiaoShi.txtShow("帮会总数 " + ListInfo.rowCount);
         senQing.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         senQing.back_btn.addEventListener(MouseEvent.CLICK,SQ_onBack);
         senQing.next_btn.addEventListener(MouseEvent.CLICK,SQ_onNext);
      }
      
      public static function JiaRu_Open(e:* = null) : *
      {
         if(!ListInfo)
         {
            Api_4399_GongHui.getList();
         }
         else
         {
            SenQingOpen();
            init.visible = false;
         }
      }
      
      public static function SQ_onBack(e:*) : *
      {
         if(sq_Page > 1)
         {
            --sq_Page;
            SenQingOpen();
         }
      }
      
      public static function SQ_onNext(e:*) : *
      {
         var max:int = ListInfo.unionList.length / sq_PageNum + 1;
         if(sq_Page < max)
         {
            ++sq_Page;
            SenQingOpen();
         }
      }
      
      public static function senQingFun(e:MouseEvent) : *
      {
         var nameXX:String = (e.target as SimpleButton).parent.name;
         var num:int = int(nameXX.substr(4,3)) + (sq_Page - 1) * sq_PageNum;
         TiaoShi.txtShow("申请加入公会请求" + nameXX + "-" + num);
         var arr:Array = ListInfo.unionList;
         var idX:int = int(arr[num - 1].unionId);
         var str:String = GetInfo();
         TiaoShi.txtShow("申请加入公会ID" + idX + ",描述:" + str);
         (e.target as SimpleButton).visible = false;
         arr[num - 1].noBtn = true;
         Api_4399_GongHui.SenQing(idX,str);
      }
      
      public static function daTing_Open(e:* = null) : *
      {
         var txtX:Object = null;
         var i:int = 0;
         var expNum:int = 0;
         var numX:int = 0;
         var tempArr:Array = null;
         var gxNum:int = 0;
         var str:String = null;
         if(!BHCY_List)
         {
            TiaoShi.txtShow("查询成员列表gongHui_ID = " + gongHui_ID);
            Api_4399_GongHui.getBHCY_List(gongHui_ID);
         }
         else
         {
            TiaoShi.txtShow("打开公会大厅 BHCY_List.length = " + BHCY_List.length);
            DaTing._tiShi_mc.visible = false;
            DaTing.No_Mc.visible = true;
            DaTing_mc = DaTing.info_mc;
            if(playerInfo.unionInfo.uId == playerInfo.member.uId || playerInfo.member.roleId == "10")
            {
               DaTing_mc = DaTing.info_mc2;
               TiaoShi.txtShow("DaTing.info_mc2");
               if(playerInfo.unionInfo.uId == playerInfo.member.uId)
               {
                  playerInfo.member.roleId = "11";
                  DaTing_mc = DaTing.info_mc3;
                  TiaoShi.txtShow("DaTing.info_mc3");
               }
               DaTing.No_Mc.visible = false;
            }
            DaTing_mc["X1_btn"].addEventListener(MouseEvent.CLICK,X1_fun);
            if(DaTing_mc == DaTing.info_mc2)
            {
               DaTing_mc["X2_btn"].addEventListener(MouseEvent.CLICK,X2_fun);
            }
            else if(DaTing_mc == DaTing.info_mc3)
            {
               TiaoShi.txtShow("DaTing.info_mc3 x2");
               DaTing_mc["X2_btn"].addEventListener(MouseEvent.CLICK,X2_fun);
               DaTing_mc["X3_btn"].addEventListener(MouseEvent.CLICK,X3_fun);
               DaTing_mc["X4_btn"].addEventListener(MouseEvent.CLICK,X4_fun);
            }
            DaTing_mc.x_mc.addEventListener(MouseEvent.MOUSE_OUT,DaTing_mc_Out);
            DaTing_mc.x = DaTing_mc.y = -5000;
            for(i = 1; i <= gh_PageNum; i++)
            {
               numX = (gh_Page - 1) * gh_PageNum + i - 1;
               txtX = DaTing["num_" + i];
               txtX.removeEventListener(MouseEvent.MOUSE_OVER,MouseIn);
               txtX.removeEventListener(MouseEvent.ROLL_OUT,MouseOut);
               txtX.removeEventListener(MouseEvent.CLICK,MouseCLICK);
               txtX._txt1.text = txtX._txt2.text = txtX._txt3.text = txtX._txt4.text = txtX._txt5.text = "";
               txtX.sel_mc.visible = false;
               txtX.mouseChildren = false;
               if(BHCY_List[numX])
               {
                  TiaoShi.txtShow("BHCY_List[numX].extra = " + BHCY_List[numX].extra);
                  tempArr = (BHCY_List[numX].extra as String).split("$");
                  txtX._txt1.text = BHCY_List[numX].nickName;
                  if(tempArr[1])
                  {
                     txtX._txt2.text = tempArr[1];
                  }
                  if(tempArr[2])
                  {
                     txtX._txt3.text = tempArr[2];
                  }
                  txtX._txt4.text = GetQX_str(BHCY_List[numX]);
                  gxNum = int(BHCY_List[numX].contribution) / 10;
                  txtX._txt5.text = gxNum;
                  txtX.numX = numX;
                  txtX.addEventListener(MouseEvent.MOUSE_OVER,MouseIn);
                  txtX.addEventListener(MouseEvent.ROLL_OUT,MouseOut);
                  txtX.addEventListener(MouseEvent.CLICK,MouseCLICK);
               }
            }
            _this.addChild(DaTing);
            if(JuanXian.visible)
            {
               _this.addChild(JuanXian);
               TiaoShi.txtShow("JuanXian~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~!!!!");
            }
            DaTing.visible = true;
            DaTing.name_txt.text = playerInfo.unionInfo.title;
            DaTing.lv_txt.text = playerInfo.unionInfo.level;
            DaTing.renShu_txt.text = BHCY_List.length + "/" + dengJiArr2[playerInfo.unionInfo.level];
            expNum = int(playerInfo.unionInfo.experience) / 10;
            DaTing.exp_txt.text = expNum + "/" + dengJiArr[playerInfo.unionInfo.level];
            DaTing.gongGao_txt.text = playerInfo.unionInfo.extra;
            DaTing.close_btn.addEventListener(MouseEvent.CLICK,onClose);
            DaTing.back_btn.addEventListener(MouseEvent.CLICK,dt_onBack);
            DaTing.next_btn.addEventListener(MouseEvent.CLICK,dt_onNext);
            DaTing.juanXian_btn.addEventListener(MouseEvent.CLICK,JuanXian_Open);
            DaTing.tuiChu_btn.addEventListener(MouseEvent.CLICK,tuiChu_Fun);
            DaTing.jieSan_btn.addEventListener(MouseEvent.CLICK,jieSan_Fun);
            DaTing.jieSan_btn2.addEventListener(MouseEvent.CLICK,QuXiaoJieSan);
            DaTing.tuiChu_btn.visible = DaTing.jieSan_btn.visible = DaTing.jieSan_btn2.visible = false;
            if(playerInfo.unionInfo.uId == playerInfo.member.uId)
            {
               if(playerInfo.unionInfo.dissolveDate != 0)
               {
                  DaTing.jieSan_btn2.visible = true;
               }
               else
               {
                  DaTing.jieSan_btn.visible = true;
               }
            }
            else
            {
               DaTing.tuiChu_btn.visible = true;
            }
            DaTing.jieSan_time.text = "";
            if(playerInfo.unionInfo.dissolveDate != 0)
            {
               str = transDate(playerInfo.unionInfo.dissolveDate);
               DaTing.jieSan_time.text = "此公会将于" + str + "解散";
            }
            DaTing.page_txt.text = gh_Page + "/" + int(BHCY_List.length / gh_PageNum + 1);
         }
      }
      
      public static function transDate(num:Number) : String
      {
         date = new Date(num * 1000);
         return date.fullYear + "." + (date.month + 1) + "." + date.date;
      }
      
      public static function QuXiaoJieSan(e:*) : *
      {
         Api_4399_GongHui.JieSan(0);
         playerInfo.unionInfo.dissolveDate = 0;
         daTing_Open();
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"取消解散公会");
      }
      
      public static function jieSan_Fun(e:*) : *
      {
         DaTing._tiShi_mc.visible = true;
         DaTing._tiShi_mc.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         DaTing._tiShi_mc.ok_btn.addEventListener(MouseEvent.CLICK,jieSan_OK);
         DaTing._tiShi_mc._txt.text = "是否解散公会, 确认后3天生效, 此期间可以取消";
      }
      
      public static function jieSan_OK(e:*) : *
      {
         Api_4399_GongHui.JieSan();
         gongHuiShow = true;
         DaTing.tuiChu_btn.visible = DaTing.jieSan_btn.visible = DaTing.jieSan_btn2.visible = false;
         DaTing._tiShi_mc.visible = false;
         DaTing.jieSan_time.text = "此公会将于3天后解散";
         var timeXXXX:uint = uint(setTimeout(Api_4399_GongHui.SelUserInfo,2000));
      }
      
      public static function GetQX_str(objX:Object) : String
      {
         if(objX.roleId == "11" || playerInfo.unionInfo.uId == objX.uId)
         {
            TiaoShi.txtShow("GetQX_str 会长");
            return "会长";
         }
         if(objX.roleId == "10")
         {
            TiaoShi.txtShow("GetQX_str 副会长");
            return "副会长";
         }
         TiaoShi.txtShow("GetQX_str 成员");
         return "成员";
      }
      
      public static function dt_onBack(e:*) : *
      {
         if(gh_Page > 1)
         {
            --gh_Page;
            daTing_Open();
         }
      }
      
      public static function dt_onNext(e:*) : *
      {
         var max:int = BHCY_List.length / gh_PageNum + 1;
         if(gh_Page < max)
         {
            ++gh_Page;
            daTing_Open();
         }
      }
      
      public static function MouseIn(e:MouseEvent) : *
      {
         var txtX:MovieClip = null;
         for(var i:int = 1; i <= gh_PageNum; i++)
         {
            txtX = DaTing["num_" + i];
            txtX.sel_mc.visible = false;
         }
         (e.target as MovieClip).sel_mc.visible = true;
         var num:int = int((e.target.name as String).substr(4,3));
         selNum = (gh_Page - 1) * gh_PageNum + num - 1;
      }
      
      public static function MouseOut(e:*) : *
      {
         var txtX:MovieClip = null;
         for(var i:int = 1; i <= gh_PageNum; i++)
         {
            txtX = DaTing["num_" + i];
            txtX.sel_mc.visible = false;
         }
      }
      
      public static function MouseCLICK(e:*) : *
      {
         DaTing_mc.x = DaTing.mouseX - 50;
         DaTing_mc.y = DaTing.mouseY - 50;
         DaTing_mc.numX = e.target.numX;
         TiaoShi.txtShow("e.target.numX:" + e.target.numX + ",选中:" + selNum);
         TiaoShi.txtShow("DaTing_mc.x:" + DaTing_mc.x + ", DaTing_mc.y:" + DaTing_mc.y);
      }
      
      public static function DaTing_mc_Out(e:MouseEvent) : *
      {
         DaTing_mc.x = DaTing_mc.y = -5000;
         TiaoShi.txtShow("关闭菜单");
      }
      
      public static function X1_fun(e:MouseEvent) : *
      {
         DaTing_mc.x = DaTing_mc.y = -5000;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"功能暂未开放");
      }
      
      public static function X2_fun(e:MouseEvent) : *
      {
         if(BHCY_List[selNum].uId == playerInfo.member.uId)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法对自己执行该操作");
            return;
         }
         var x1:int = int(BHCY_List[selNum].roleId);
         var x2:int = int(playerInfo.member.roleId);
         if(x2 <= x1 || playerInfo.unionInfo.uId == BHCY_List[selNum].uId)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法执行该操作");
            return;
         }
         DaTing._tiShi_mc.visible = true;
         DaTing._tiShi_mc.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         DaTing._tiShi_mc.ok_btn.addEventListener(MouseEvent.CLICK,X2_Ok);
         DaTing._tiShi_mc._txt.text = "是否将" + BHCY_List[selNum].nickName + "逐出公会";
      }
      
      public static function X2_Ok(e:MouseEvent) : *
      {
         Api_4399_GongHui.TiRen(BHCY_List[selNum].uId,BHCY_List[selNum].index);
         TiaoShi.txtShow("踢人 uId:" + BHCY_List[selNum].uId + ", index:" + BHCY_List[selNum].index);
         BHCY_List.splice(selNum,1);
         daTing_Open();
         DaTing_mc.x = DaTing_mc.y = -5000;
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"移除成员操作成功");
         DaTing._tiShi_mc.visible = false;
         DaTing._tiShi_mc.ok_btn.removeEventListener(MouseEvent.CLICK,X2_Ok);
      }
      
      public static function tuiChu_Fun(e:*) : *
      {
         DaTing._tiShi_mc.visible = true;
         DaTing._tiShi_mc.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         DaTing._tiShi_mc.ok_btn.addEventListener(MouseEvent.CLICK,tuiChu_Ok);
         DaTing._tiShi_mc._txt.text = "是否退出公会, 退出后24小时内无法加入公会";
      }
      
      public static function tuiChu_Ok(e:MouseEvent) : *
      {
         Api_4399_GongHui.TuiChu();
         DaTing.visible = false;
         JieMian.visible = false;
         playerInfo = null;
         DaTing._tiShi_mc.ok_btn.removeEventListener(MouseEvent.CLICK,tuiChu_Ok);
      }
      
      public static function X3_fun(e:MouseEvent) : *
      {
         if(BHCY_List[selNum].uId == playerInfo.member.uId)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"无法对自己执行该操作");
            return;
         }
         if(BHCY_List[selNum].roleId == "10")
         {
            Api_4399_GongHui.set_QX(BHCY_List[selNum].uId,BHCY_List[selNum].index,0);
            BHCY_List[selNum].roleId = "0";
            daTing_Open();
            TiaoShi.txtShow("任命 uId:" + BHCY_List[selNum].uId + ", index:" + BHCY_List[selNum].index);
         }
         else
         {
            Api_4399_GongHui.set_QX(BHCY_List[selNum].uId,BHCY_List[selNum].index,10);
            BHCY_List[selNum].roleId = "10";
            daTing_Open();
         }
         DaTing_mc.x = DaTing_mc.y = -5000;
      }
      
      public static function X4_fun(e:MouseEvent) : *
      {
         NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"转让公会功能暂未开放");
      }
      
      public static function GongGaoXX(e:* = null) : *
      {
         if(Boolean(playerInfo) && playerInfo.unionInfo.extra != DaTing.gongGao_txt.text)
         {
            TiaoShi.txtShow("修改公会公告!!!");
            Api_4399_GongHui.GH_InfoXX(DaTing.gongGao_txt.text);
            playerInfo.unionInfo.extra = DaTing.gongGao_txt.text;
         }
      }
      
      public static function SenHeOpen(e:* = null) : *
      {
         var arr:Array = null;
         var txtX:Object = null;
         var i:int = 0;
         var numX:int = 0;
         var tempArr:Array = null;
         if(!senHeListInfo)
         {
            Api_4399_GongHui.getSenHe();
         }
         else
         {
            TiaoShi.txtShow("审核界面");
            _this.addChild(senHe);
            senHe.visible = true;
            senHe.close_btn.addEventListener(MouseEvent.CLICK,onClose);
            senHe.back_btn.addEventListener(MouseEvent.CLICK,SH_onBack);
            senHe.next_btn.addEventListener(MouseEvent.CLICK,SH_onNext);
            arr = senHeListInfo.applyList;
            TiaoShi.txtShow("信息数:" + arr.length);
            for(i = 1; i <= sh_PageNum; i++)
            {
               numX = (sh_Page - 1) * sh_PageNum + i - 1;
               txtX = senHe["num_" + i];
               txtX._txt1.text = txtX._txt2.text = txtX._txt3.text = "";
               txtX._btn4.visible = txtX._btn5.visible = false;
               if(arr[numX])
               {
                  txtX._txt1.text = txtX._txt2.text = txtX._txt3.text = "暂无数据";
                  tempArr = arr[numX].extra.split("$");
                  if(tempArr[3])
                  {
                     txtX._txt1.text = tempArr[3];
                  }
                  if(tempArr[1])
                  {
                     txtX._txt2.text = tempArr[1];
                  }
                  if(tempArr[2])
                  {
                     txtX._txt3.text = tempArr[2];
                  }
                  txtX._btn4.visible = true;
                  txtX._btn5.visible = true;
                  txtX._btn4.addEventListener(MouseEvent.CLICK,SenHeFun_OK);
                  txtX._btn5.addEventListener(MouseEvent.CLICK,SenHeFun_No);
                  txtX.alpha = 1;
               }
            }
            senHe.page_txt.text = sh_Page + "/" + int(arr.length / sh_PageNum + 1);
         }
      }
      
      public static function SenHeFun_OK(e:*) : *
      {
         var mcX:MovieClip = (e.target as SimpleButton).parent;
         var nameXX:String = mcX.name;
         var num:int = int(nameXX.substr(4,3));
         num += (sh_Page - 1) * sh_PageNum;
         TiaoShi.txtShow("审核 同意加入公会请求" + nameXX + "-" + num);
         var objX:Object = senHeListInfo.applyList[num - 1];
         Api_4399_GongHui.SenHe(objX.uId,objX.index);
         senHeListInfo.applyList.splice(num - 1,1);
         SenHeOpen();
      }
      
      public static function SenHeFun_No(e:*) : *
      {
         var mcX:MovieClip = (e.target as SimpleButton).parent;
         var nameXX:String = mcX.name;
         var num:int = int(nameXX.substr(4,3));
         num += (sh_Page - 1) * sh_PageNum;
         TiaoShi.txtShow("审核 拒绝加入公会请求" + nameXX + "-" + num);
         var objX:Object = senHeListInfo.applyList[num - 1];
         Api_4399_GongHui.SenHe(objX.uId,objX.index,0);
         senHeListInfo.applyList.splice(num - 1,1);
         SenHeOpen();
      }
      
      public static function SH_onBack(e:*) : *
      {
         if(sh_Page > 1)
         {
            --sh_Page;
            SenHeOpen();
         }
      }
      
      public static function SH_onNext(e:*) : *
      {
         var max:int = senHeListInfo.applyList.length / sh_PageNum + 1;
         if(sh_Page < max)
         {
            ++sh_Page;
            SenHeOpen();
         }
      }
      
      public static function Chuanjian_Open(e:*) : *
      {
         init.visible = false;
         chuangJian.visible = true;
         _this.addChild(chuangJian);
         chuangJian.chuangJian_btn.addEventListener(MouseEvent.CLICK,Chuanjian_Fun);
         chuangJian.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function Chuanjian_Fun(e:MouseEvent) : *
      {
         chuanJian_mc = e.target.parent;
         var str:String = chuanJian_mc.name_txt.text;
         guoLv.checkWord(str,OKOKOk);
         chuangJian.chuangJian_btn.removeEventListener(MouseEvent.CLICK,Chuanjian_Fun);
      }
      
      public static function OKOKOk(num:int) : void
      {
         var str:String = null;
         chuangJian.chuangJian_btn.addEventListener(MouseEvent.CLICK,Chuanjian_Fun);
         if(num == 0)
         {
            str = chuanJian_mc.name_txt.text;
            Api_4399_GongHui.AddGongHui(str,GetInfo());
            chuangJian.visible = false;
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2," 公会名称不能包含\'敏感词\' ");
         }
      }
      
      public static function JuanXian_Open(e:* = null) : *
      {
         JuanXian.visible = true;
         _this.addChild(JuanXian);
         JuanXian._txt1.text = playerInfo.unionInfo.level;
         var expNum:int = int(playerInfo.unionInfo.experience) / 10;
         JuanXian._txt2.text = expNum + "/" + dengJiArr[playerInfo.unionInfo.level];
         JuanXian._txt3.text = "消耗:" + GetJinBi() + "金币";
         JuanXian.J_btn1.addEventListener(MouseEvent.CLICK,JinBi_fun);
         JuanXian.J_btn2.addEventListener(MouseEvent.CLICK,DianQuan_fun);
         JuanXian.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function JinBi_fun(e:* = null) : *
      {
         if(GongHuiRenWu.rwArr2[4])
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币捐献每天只能执行1次");
            return;
         }
         if(Main.player1.getGold() >= GetJinBi())
         {
            Main.player1.payGold(GetJinBi());
            Api_4399_GongHui.setRW(68);
            Api_4399_GongHui.upNum(158);
            JuanXian_Open();
            Main.Save(false);
            GongHuiRenWu.rwArr2[4] = true;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币捐献成功");
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"金币不足");
         }
      }
      
      private static function GetJinBi() : int
      {
         var num:int = 2000;
         if(Main.player1.level.getValue() >= 80)
         {
            num = 80000;
         }
         else if(Main.player1.level.getValue() >= 70)
         {
            num = 50000;
         }
         else if(Main.player1.level.getValue() >= 60)
         {
            num = 20000;
         }
         else if(Main.player1.level.getValue() >= 40)
         {
            num = 15000;
         }
         else if(Main.player1.level.getValue() >= 20)
         {
            num = 8000;
         }
         else if(Main.player1.level.getValue() >= 10)
         {
            num = 5000;
         }
         return num;
      }
      
      public static function DianQuan_fun(e:* = null) : *
      {
         if(Shop4399.moneyAll.getValue() >= 10)
         {
            Api_4399_GongHui.DuiHuanGX(10);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"点券不足");
         }
      }
      
      public static function RenWu_Open(e:* = null) : *
      {
         var mc:MovieClip = null;
         var tempArr:* = undefined;
         RenWu.visible = true;
         _this.addChild(RenWu);
         GongHuiRenWu.SX(Main.player1.getLevel());
         GongHuiRenWu.rwArr[4] = true;
         RenWu.all_mc.visible = false;
         for(var i:int = 1; i <= 3; i++)
         {
            mc = RenWu["rw_num" + i];
            if(GongHuiRenWu.rwArr[i][4])
            {
               mc.visible = false;
            }
            else
            {
               tempArr = GongHuiRenWu.isType(GongHuiRenWu.rwArr[i][0]);
               mc.t1.text = tempArr[2];
               mc.t2.text = tempArr[8];
               mc.t3.text = tempArr[9];
               if(tempArr[10] == 1)
               {
                  mc.t4.text = GongHuiRenWu.rwArr[i][1] + "/" + tempArr[5];
               }
               else
               {
                  mc.t4.text = GongHuiRenWu.rwArr[i][2] + "/" + tempArr[7];
               }
               mc.lingQu_btn.addEventListener(MouseEvent.CLICK,lingQu);
            }
         }
         if(!RenWu["rw_num1"].visible && !RenWu["rw_num2"].visible && !RenWu["rw_num3"].visible)
         {
            RenWu.all_mc.visible = true;
         }
         RenWu.close_btn.addEventListener(MouseEvent.CLICK,onClose);
      }
      
      public static function lingQu(e:MouseEvent = null) : *
      {
         var num:int = int((e.target.parent.name as String).substr(6,1));
         TiaoShi.txtShow("领取奖励 = " + num + ", rwArr.length = " + GongHuiRenWu.rwArr.length);
         var tempArr:Array = GongHuiRenWu.isType(GongHuiRenWu.rwArr[num][0]);
         if(Boolean(GongHuiRenWu.rwArr[num][3]) && !GongHuiRenWu.rwArr[num][4])
         {
            Main.player1.addGold(tempArr[8]);
            Main.player1.setEXP(Main.player1.getEXP() + tempArr[9]);
            if(Main.P1P2)
            {
               Main.player2.setEXP(Main.player2.getEXP() + tempArr[9]);
            }
            GongHuiRenWu.rwArr[num][4] = true;
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"领取成功");
            MCgo(e.target.parent);
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"任务尚未完成");
         }
      }
      
      public static function HuoYue_Show() : *
      {
         if(HuoYue)
         {
            HuoYue.t1.text = xxArr[0] + "/10";
            HuoYue.t2.text = xxArr[0] + "/20";
            HuoYue.t3.text = xxArr[1] + "/5";
            HuoYue.t4.text = xxArr[2] + "/3";
         }
      }
      
      public static function HuoYue_Open(e:* = null) : *
      {
         HuoYue.visible = true;
         _this.addChild(HuoYue);
         HuoYue.close_btn.addEventListener(MouseEvent.CLICK,onClose);
         HuoYue.t1.text = xxArr[0] + "/10";
         HuoYue.t2.text = xxArr[0] + "/20";
         HuoYue.t3.text = xxArr[1] + "/5";
         HuoYue.t4.text = xxArr[2] + "/3";
         var num:int = 0;
         if(xxArr[0] >= 10)
         {
            HuoYue.mc1.gotoAndStop(2);
            num += 10;
         }
         if(xxArr[0] >= 20)
         {
            HuoYue.mc2.gotoAndStop(2);
            num += 10;
         }
         if(xxArr[1] >= 5)
         {
            HuoYue.mc3.gotoAndStop(2);
            num += 10;
         }
         if(xxArr[2] >= 3)
         {
            HuoYue.mc4.gotoAndStop(2);
            num += 20;
         }
         HuoYue.num_txt.text = num;
         HuoYue.jinDu.gotoAndStop(num / 10 + 1);
         HuoYue.LQ_1.visible = HuoYue.LQ_2.visible = HuoYue.LQ_3.visible = false;
         HuoYue.ok1.visible = HuoYue.ok2.visible = HuoYue.ok3.visible = false;
         HuoYue.no_1.addEventListener(MouseEvent.CLICK,onLingQuNo);
         HuoYue.no_2.addEventListener(MouseEvent.CLICK,onLingQuNo);
         HuoYue.no_3.addEventListener(MouseEvent.CLICK,onLingQuNo);
         if(num >= 20)
         {
            if(GongHuiRenWu.rwArr2[1])
            {
               HuoYue.ok1.visible = true;
            }
            else
            {
               HuoYue.LQ_1.visible = true;
               HuoYue.LQ_1.addEventListener(MouseEvent.CLICK,onLingQu);
            }
         }
         if(num >= 30)
         {
            if(GongHuiRenWu.rwArr2[2])
            {
               HuoYue.ok2.visible = true;
            }
            else
            {
               HuoYue.LQ_2.visible = true;
               HuoYue.LQ_2.addEventListener(MouseEvent.CLICK,onLingQu);
            }
         }
         if(num >= 50)
         {
            if(GongHuiRenWu.rwArr2[3])
            {
               HuoYue.ok3.visible = true;
            }
            else
            {
               HuoYue.LQ_3.visible = true;
               HuoYue.LQ_3.addEventListener(MouseEvent.CLICK,onLingQu);
            }
         }
      }
      
      public static function onLingQu(e:MouseEvent = null) : *
      {
         var num:int = int(e.target.name.substr(3,1));
         TiaoShi.txtShow("onLingQu " + num);
         if(num == 1)
         {
            Main.player1.killPoint.setValue(Main.player1.killPoint.getValue() + 100);
            if(Main.P1P2)
            {
               Main.player2.killPoint.setValue(Main.player2.killPoint.getValue() + 100);
            }
            Main.Save(false);
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得100击杀点");
            GongHuiRenWu.rwArr2[num] = true;
         }
         else if(num == 2)
         {
            if(Main.player1.getBag().backSuppliesBagNum() >= 6)
            {
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21223));
               Main.player1.getBag().addSuppliesBag(SuppliesFactory.getSuppliesById(21221));
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得野性之血5个,复活药1个");
               GongHuiRenWu.rwArr2[num] = true;
               Main.Save(false);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         else if(num == 3)
         {
            if(Main.player1.getBag().backOtherBagNum() >= 1)
            {
               Main.player1.getBag().addOtherobjBag(OtherFactory.creatOther(63385));
               GongHuiRenWu.rwArr2[num] = true;
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"获得公会星灵礼包1个");
               Main.Save(false);
            }
            else
            {
               NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"背包空间不足");
            }
         }
         HuoYue_Open();
      }
      
      public static function onLingQuNo(e:MouseEvent = null) : *
      {
         NewMC.Open("文字提示",Main._stage,480,350,30,0,true,1,"活跃度未达成");
      }
      
      public static function ShowExp() : *
      {
         JuanXian._txt1.text = DaTing.lv_txt.text = playerInfo.unionInfo.level;
         var expNum:int = int(playerInfo.unionInfo.experience) / 10;
         JuanXian._txt2.text = DaTing.exp_txt.text = expNum + "/" + dengJiArr[playerInfo.unionInfo.level];
         if(DaTing.visible && JieMian.visible)
         {
            daTing_Open();
            TiaoShi.txtShow("ShowExp~~~~~~~~打开大厅");
         }
      }
      
      public static function MCgo(mc:MovieClip) : *
      {
         mc.addEventListener(Event.ENTER_FRAME,MCgoXXX);
      }
      
      public static function MCgoXXX(e:Event) : *
      {
         var mcX:MovieClip = e.target;
         mcX.y -= 12;
         mcX.alpha -= 0.1;
         if(mcX.alpha <= 0)
         {
            mcX.addEventListener(Event.ENTER_FRAME,MCgoXXX);
         }
      }
   }
}

