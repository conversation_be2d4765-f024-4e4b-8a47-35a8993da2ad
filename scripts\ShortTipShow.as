package
{
   import flash.display.MovieClip;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol4854")]
   public dynamic class ShortTipShow extends MovieClip
   {
      
      public var mc_1:MovieClip;
      
      public var mc_10:MovieClip;
      
      public var mc_11:MovieClip;
      
      public var mc_12:MovieClip;
      
      public var mc_13:MovieClip;
      
      public var mc_14:MovieClip;
      
      public var mc_15:MovieClip;
      
      public var mc_16:MovieClip;
      
      public var mc_17:MovieClip;
      
      public var mc_19:MovieClip;
      
      public var mc_2:MovieClip;
      
      public var mc_20:MovieClip;
      
      public var mc_21:MovieClip;
      
      public var mc_22:MovieClip;
      
      public var mc_23:MovieClip;
      
      public var mc_24:MovieClip;
      
      public var mc_25:MovieClip;
      
      public var mc_27:MovieClip;
      
      public var mc_28:MovieClip;
      
      public var mc_29:MovieClip;
      
      public var mc_3:MovieClip;
      
      public var mc_30:MovieClip;
      
      public var mc_31:MovieClip;
      
      public var mc_32:MovieClip;
      
      public var mc_33:MovieClip;
      
      public var mc_34:MovieClip;
      
      public var mc_35:MovieClip;
      
      public var mc_36:MovieClip;
      
      public var mc_37:MovieClip;
      
      public var mc_38:MovieClip;
      
      public var mc_39:MovieClip;
      
      public var mc_4:MovieClip;
      
      public var mc_40:MovieClip;
      
      public var mc_41:MovieClip;
      
      public var mc_42:MovieClip;
      
      public var mc_43:MovieClip;
      
      public var mc_44:MovieClip;
      
      public var mc_45:MovieClip;
      
      public var mc_46:MovieClip;
      
      public var mc_5:MovieClip;
      
      public var mc_6:MovieClip;
      
      public var mc_7:MovieClip;
      
      public var mc_8:MovieClip;
      
      public var mc_9:MovieClip;
      
      public function ShortTipShow()
      {
         super();
      }
   }
}

