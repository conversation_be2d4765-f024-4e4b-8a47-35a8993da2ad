package src.other
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   import src.tool.*;
   
   public class Cw34_Fly3x extends Fly
   {
      
      public static var moveNum:int;
      
      public static var moveXX:Number;
      
      public function Cw34_Fly3x()
      {
         super();
      }
      
      public static function Move(cw:ChongWu) : *
      {
         if(cw.skin.currentFrame == 234)
         {
            moveNum = 4;
         }
         if(moveNum > 0 && cw.skin.currentFrame == 507)
         {
            --moveNum;
            cw.skin.gotoAndPlay(242);
            trace("冲撞次数:",moveNum);
         }
         if(cw.skin.currentFrame > 240 && cw.skin.currentFrame < 500)
         {
            if(cw.RL)
            {
               cw.scaleX = 1;
               moveXX = 20;
            }
            else
            {
               cw.scaleX = -1;
               moveXX = -20;
            }
            MoveRun2(cw);
         }
      }
      
      public static function MoveRun2(cw:ChongWu) : *
      {
         var i:int = 0;
         var BB:Boolean = false;
         var AA:Boolean = false;
         var CC:Boolean = false;
         var forecast_x:int = cw.x + Main.world.x;
         var forecast_y:int = cw.y;
         for(i = 20; i > 0; i--)
         {
            BB = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 3,true));
            AA = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y - 1,true));
            CC = Boolean(Main.world.MapData.hitTestPoint(forecast_x,forecast_y + 6,true));
            if(BB)
            {
               forecast_y -= 2;
            }
            else if(CC)
            {
               forecast_y += 3;
            }
            else
            {
               if(AA)
               {
                  break;
               }
               forecast_y++;
            }
         }
         cw.y = forecast_y;
         var noMove:Boolean = true;
         for(i = Math.abs(moveXX); i > 0; i--)
         {
            AA = Boolean(Main.world.MapData1.hitTestPoint(forecast_x,forecast_y - 30,true));
            if(!AA)
            {
               if(cw.RL)
               {
                  forecast_x--;
               }
               else
               {
                  forecast_x++;
               }
               noMove = false;
            }
         }
         var xxx:int = forecast_x - Main.world.x;
         if(xxx > Main.world.stopX_2)
         {
            cw.x = Main.world.stopX_2;
            noMove = true;
         }
         else if(xxx < Main.world.stopX_1)
         {
            cw.x = Main.world.stopX_1;
            noMove = true;
         }
         else if(xxx > Main.world._width + 100)
         {
            cw.x = Main.world._width + 100;
            noMove = true;
         }
         else if(xxx < -100)
         {
            cw.x = -100;
            noMove = true;
         }
         else
         {
            cw.x = xxx;
         }
         if(noMove)
         {
            cw.skin.gotoAndPlay(501);
            cw.RL = cw.RL ? false : true;
         }
      }
      
      override public function otherXX() : *
      {
         var cw:ChongWu = null;
         if(who is ChongWu)
         {
            cw = who;
            this.RL = cw.RL;
            this.scaleX = cw.scaleX;
            this.x = who.x;
            this.y = who.y;
            if(cw.skin.currentFrame > 500)
            {
               life = 0;
            }
         }
      }
   }
}

