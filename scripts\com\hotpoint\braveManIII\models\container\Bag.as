package com.hotpoint.braveManIII.models.container
{
   import com.hotpoint.braveManIII.models.common.*;
   import com.hotpoint.braveManIII.models.equip.*;
   import com.hotpoint.braveManIII.models.gem.*;
   import com.hotpoint.braveManIII.models.other.*;
   import com.hotpoint.braveManIII.models.quest.*;
   import com.hotpoint.braveManIII.models.supplies.*;
   import com.hotpoint.braveManIII.repository.gem.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.storagePanel.*;
   import flash.utils.*;
   import src.*;
   import src.tool.*;
   
   public class Bag
   {
      
      private var _equipBag:Array = new Array();
      
      private var _suppliesBag:Array = new Array();
      
      private var _gemBag:Array = new Array();
      
      private var _questBag:Array = new Array();
      
      public var _otherobjBag:Array = new Array();
      
      private var _limitE:VT = VT.createVT(24);
      
      private var _limitS:VT = VT.createVT(24);
      
      private var _limitG:VT = VT.createVT(24);
      
      private var _limitO:VT = VT.createVT(24);
      
      public function Bag()
      {
         super();
      }
      
      public static function createBag() : Bag
      {
         var bg:Bag = new Bag();
         for(var i:int = 0; i < 48; i++)
         {
            bg._equipBag[i] = null;
            bg._suppliesBag[i] = null;
            bg._gemBag[i] = null;
            bg._questBag[i] = null;
            bg._otherobjBag[i] = null;
         }
         return bg;
      }
      
      public function get equipBag() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._equipBag;
      }
      
      public function set equipBag(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._equipBag = value;
      }
      
      public function get suppliesBag() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._suppliesBag;
      }
      
      public function set suppliesBag(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._suppliesBag = value;
      }
      
      public function get gemBag() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._gemBag;
      }
      
      public function set gemBag(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._gemBag = value;
      }
      
      public function get questBag() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._questBag;
      }
      
      public function set questBag(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._questBag = value;
      }
      
      public function get otherobjBag() : Array
      {
         trace("禁止get:",getQualifiedClassName(this));
         return this._otherobjBag;
      }
      
      public function set otherobjBag(value:Array) : void
      {
         trace("禁止get:",getQualifiedClassName(this));
         this._otherobjBag = value;
      }
      
      public function get limitE() : VT
      {
         return this._limitE;
      }
      
      public function set limitE(value:VT) : void
      {
         this._limitE = value;
      }
      
      public function get limitS() : VT
      {
         return this._limitS;
      }
      
      public function set limitS(value:VT) : void
      {
         this._limitS = value;
      }
      
      public function get limitG() : VT
      {
         return this._limitG;
      }
      
      public function set limitG(value:VT) : void
      {
         this._limitG = value;
      }
      
      public function get limitO() : VT
      {
         return this._limitO;
      }
      
      public function set limitO(value:VT) : void
      {
         this._limitO = value;
      }
      
      public function getLimitE() : int
      {
         return this._limitE.getValue();
      }
      
      public function getLimitS() : int
      {
         return this._limitS.getValue();
      }
      
      public function getLimitG() : int
      {
         return this._limitG.getValue();
      }
      
      public function getLimitO() : int
      {
         return this._limitO.getValue();
      }
      
      public function addLimitE() : *
      {
         if(this._limitE.getValue() < 48)
         {
            this._limitE.setValue(this._limitE.getValue() + 4);
         }
      }
      
      public function addLimitS() : *
      {
         if(this._limitS.getValue() < 48)
         {
            this._limitS.setValue(this._limitS.getValue() + 4);
         }
      }
      
      public function addLimitG() : *
      {
         if(this._limitG.getValue() < 48)
         {
            this._limitG.setValue(this._limitG.getValue() + 4);
         }
      }
      
      public function addLimitO() : *
      {
         if(this._limitO.getValue() < 48)
         {
            this._limitO.setValue(this._limitO.getValue() + 4);
         }
      }
      
      public function zhengliBag() : *
      {
         var j:* = undefined;
         for(var i:* = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] == null)
            {
               for(j = i + 1; j < this._limitE.getValue(); j++)
               {
                  if(this._equipBag[j] != null)
                  {
                     this._equipBag[i] = this._equipBag[j];
                     this._equipBag[j] = null;
                     break;
                  }
               }
            }
         }
      }
      
      public function zhengliBagS() : *
      {
         var j:* = undefined;
         for(var i:* = 0; i < this._limitS.getValue(); i++)
         {
            if(this._suppliesBag[i] == null)
            {
               for(j = i + 1; j < this._limitS.getValue(); j++)
               {
                  if(this._suppliesBag[j] != null)
                  {
                     this._suppliesBag[i] = this._suppliesBag[j];
                     this._suppliesBag[j] = null;
                     break;
                  }
               }
            }
         }
      }
      
      public function zhengliBagG() : *
      {
         var j:* = undefined;
         for(var i:* = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] == null)
            {
               for(j = i + 1; j < this._limitG.getValue(); j++)
               {
                  if(this._gemBag[j] != null)
                  {
                     this._gemBag[i] = this._gemBag[j];
                     this._gemBag[j] = null;
                     break;
                  }
               }
            }
         }
      }
      
      public function zhengliBagO() : *
      {
         var j:* = undefined;
         for(var i:* = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] == null)
            {
               for(j = i + 1; j < this._limitO.getValue(); j++)
               {
                  if(this._otherobjBag[j] != null)
                  {
                     this._otherobjBag[i] = this._otherobjBag[j];
                     this._otherobjBag[j] = null;
                     break;
                  }
               }
            }
         }
      }
      
      public function getEquipBag() : Array
      {
         return this._equipBag.slice();
      }
      
      public function getEquipFromBag(num:Number) : Equip
      {
         if(this._equipBag[num] != null && this._equipBag[num] is Equip)
         {
            return this._equipBag[num];
         }
         return null;
      }
      
      public function getSuppliesBag() : Array
      {
         return this._suppliesBag.slice();
      }
      
      public function getSuppliesFromBag(num:Number) : Supplies
      {
         if(this._suppliesBag[num] != null)
         {
            return this._suppliesBag[num];
         }
         return null;
      }
      
      public function getGemBag() : Array
      {
         return this._gemBag.slice();
      }
      
      public function getGemFromBag(num:Number) : Gem
      {
         if(this._gemBag[num] != null)
         {
            return this._gemBag[num];
         }
         return null;
      }
      
      public function getQuestBag() : Array
      {
         return this._questBag.slice();
      }
      
      public function getQuestFromBag(num:Number) : Quest
      {
         if(this._questBag[num] != null)
         {
            return this._questBag[num];
         }
         return null;
      }
      
      public function getOtherobjFromBag(num:Number) : Otherobj
      {
         if(this._otherobjBag[num] != null)
         {
            return this._otherobjBag[num];
         }
         return null;
      }
      
      public function getOtherobjBag() : Array
      {
         return this._otherobjBag.slice();
      }
      
      public function addEquipBag(value:Equip, num:int = -1) : Boolean
      {
         if(num != -1)
         {
            if(this._equipBag[num] == null)
            {
               this._equipBag[num] = value;
               return true;
            }
            return false;
         }
         for(var i:uint = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] == null)
            {
               this._equipBag[i] = value;
               return true;
            }
         }
         return false;
      }
      
      public function addToEquipBag(value:Equip, num:Number) : Boolean
      {
         if(this._equipBag[num] == null)
         {
            this._equipBag[num] = value;
            return true;
         }
         return false;
      }
      
      public function addSuppliesBag(value:Supplies) : Boolean
      {
         for(var i:uint = 0; i < this._limitS.getValue(); i++)
         {
            if(this._suppliesBag[i] == null)
            {
               this._suppliesBag[i] = value;
               return true;
            }
         }
         return false;
      }
      
      public function addToSuppliesBag(value:Supplies, num:Number) : Boolean
      {
         if(this._suppliesBag[num] == null)
         {
            this._suppliesBag[num] = value;
            return true;
         }
         return false;
      }
      
      public function backequipBagNum() : uint
      {
         var num:uint = 0;
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function backQuestBagNum() : uint
      {
         var num:uint = 0;
         for(var i:int = 0; i < 24; i++)
         {
            if(this._questBag[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function backSuppliesBagNum() : uint
      {
         var num:uint = 0;
         for(var i:int = 0; i < this._limitS.getValue(); i++)
         {
            if(this._suppliesBag[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function backGemBagNum() : uint
      {
         var num:uint = 0;
         for(var i:int = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function getQN_YN(type:int = 0) : Boolean
      {
         var typeXX:int = 0;
         var num:uint = 0;
         for(var i:int = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i])
            {
               typeXX = this.getGemFromBag(i).getType();
               if(type == 0 && typeXX >= 7 && typeXX <= 20)
               {
                  return true;
               }
               if(type == typeXX)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function canPutGemNum(gemId:Number) : uint
      {
         var n:uint = 0;
         var gem:Gem = GemFactory.creatGemById(gemId);
         var times:uint = gem.getTimes();
         var alltimes:uint = gem.getPileLimit();
         var num:uint = 0;
         for(var i:int = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] == null)
            {
               num += alltimes;
            }
            else if(gem.compareGem(this._gemBag[i]) == true)
            {
               n = alltimes - this._gemBag[i].getTimes();
               num += n;
            }
         }
         return num;
      }
      
      public function backOtherBagNum() : uint
      {
         var num:uint = 0;
         for(var i:int = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] == null)
            {
               num++;
            }
         }
         return num;
      }
      
      public function canPutOtherNum(otherId:Number) : uint
      {
         var n:uint = 0;
         var oo:Otherobj = OtherFactory.creatOther(otherId);
         var times:uint = oo.getTimes();
         var alltimes:uint = oo.getPileLimit();
         var num:uint = 0;
         for(var i:int = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] == null)
            {
               num += alltimes;
            }
            else if(oo.compareOtherobj(this._otherobjBag[i]) == true)
            {
               n = alltimes - this._otherobjBag[i].getTimes();
               num += n;
            }
         }
         return num;
      }
      
      public function isGemBagEmpty(value:Gem) : Boolean
      {
         var isSameIn:Boolean = false;
         var i:int = 0;
         if(value.getIsPile() == true)
         {
            isSameIn = true;
            for(i = 0; i < this._limitG.getValue(); i++)
            {
               if(this._gemBag[i] == null)
               {
                  return true;
               }
               if(this._gemBag[i].compareGem(value) == true)
               {
                  if(this._gemBag[i].getTimes() + value.getTimes() < value.getPileLimit())
                  {
                     return true;
                  }
                  if(i == 23)
                  {
                     return false;
                  }
               }
            }
         }
         else
         {
            for(i = 0; i < this._limitG.getValue(); i++)
            {
               if(this._gemBag[i] == null)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function addGemBag(value:Gem) : Boolean
      {
         var temp:int = 0;
         var isSameIn:Boolean = false;
         var i:int = 0;
         if(value.getIsPile() == true)
         {
            temp = -1;
            isSameIn = true;
            for(i = 0; i < this._limitG.getValue(); i++)
            {
               if(this._gemBag[i] != null)
               {
                  if(this._gemBag[i].compareGem(value) == true)
                  {
                     while(this._gemBag[i].getTimes() < value.getPileLimit())
                     {
                        this._gemBag[i].addGem(1);
                        if(value.useGem(1) == false)
                        {
                           break;
                        }
                     }
                     if(value.getTimes() == 0)
                     {
                        break;
                     }
                  }
               }
               else if(temp == -1)
               {
                  temp = i;
               }
            }
            if(value.getTimes() > 0)
            {
               this._gemBag[temp] = value;
            }
         }
         else
         {
            for(i = 0; i < this._limitG.getValue(); i++)
            {
               if(this._gemBag[i] == null)
               {
                  this._gemBag[i] = value;
                  return true;
               }
            }
         }
         return false;
      }
      
      public function addToGemBag(gem:Gem, num:Number) : Boolean
      {
         if(this._gemBag[num] == null)
         {
            this._gemBag[num] = gem;
            return true;
         }
         return false;
      }
      
      public function addToQuestBag(quest:Quest, num:Number) : Boolean
      {
         if(this._questBag[num] == null)
         {
            this._questBag[num] = quest;
            return true;
         }
         return false;
      }
      
      public function fallQusetBag(fallLevel:Number) : Number
      {
         var num:Number = 0;
         for(var i:uint = 0; i < 24; i++)
         {
            if(this._questBag[i] != null)
            {
               if((this._questBag[i] as Quest).compareById(fallLevel))
               {
                  if(this._questBag[i].isMany() == true)
                  {
                     num += this._questBag[i].getTimes();
                  }
                  else
                  {
                     num += 1;
                  }
               }
            }
         }
         return num;
      }
      
      public function clearQuest(fallLevel:Number) : void
      {
         for(var i:uint = 0; i < 24; i++)
         {
            if(this._questBag[i] != null)
            {
               if((this._questBag[i] as Quest).getFallLevel() == fallLevel)
               {
                  this._questBag[i] = null;
               }
            }
         }
      }
      
      public function addOtherobjBag(value:Otherobj) : Boolean
      {
         var temp:int = 0;
         var isSameIn:Boolean = false;
         var i:int = 0;
         if(value.isMany() == true)
         {
            temp = -1;
            isSameIn = true;
            for(i = 0; i < this._limitO.getValue(); i++)
            {
               if(this._otherobjBag[i] != null)
               {
                  if(this._otherobjBag[i].compareOtherobj(value) == true)
                  {
                     while(this._otherobjBag[i].getTimes() < value.getPileLimit())
                     {
                        this._otherobjBag[i].addOtherobj(1);
                        if(value.useOtherobj(1) == false)
                        {
                           break;
                        }
                     }
                     if(value.getTimes() == 0)
                     {
                        break;
                     }
                  }
               }
               else if(temp == -1)
               {
                  temp = i;
               }
            }
            if(value.getTimes() > 0)
            {
               this._otherobjBag[temp] = value;
            }
         }
         else
         {
            for(i = 0; i < this._limitO.getValue(); i++)
            {
               if(this._otherobjBag[i] == null)
               {
                  this._otherobjBag[i] = value;
                  return true;
               }
            }
         }
         return false;
      }
      
      public function addQuestBag(value:Quest) : Boolean
      {
         var temp:int = 0;
         var isSameIn:Boolean = false;
         var i:int = 0;
         if(value.isMany() == true)
         {
            temp = -1;
            isSameIn = true;
            for(i = 0; i < 24; i++)
            {
               if(this._questBag[i] != null)
               {
                  if(this._questBag[i].compareQuest(value) == true)
                  {
                     while(this._questBag[i].getTimes() < value.getPileLimit())
                     {
                        this._questBag[i].addQuest(1);
                        if(value.useQuest(1) == false)
                        {
                           break;
                        }
                     }
                     if(value.getTimes() == 0)
                     {
                        break;
                     }
                  }
               }
               else if(temp == -1)
               {
                  temp = i;
               }
            }
            if(value.getTimes() > 0)
            {
               this._questBag[temp] = value;
            }
         }
         else
         {
            for(i = 0; i < 24; i++)
            {
               if(this._questBag[i] == null)
               {
                  this._questBag[i] = value;
                  return true;
               }
            }
         }
         return false;
      }
      
      public function addToOtherobjBag(otherobj:Otherobj, num:Number) : Boolean
      {
         if(this._otherobjBag[num] == null)
         {
            this._otherobjBag[num] = otherobj;
            return true;
         }
         return false;
      }
      
      public function delEquip(num:Number) : Equip
      {
         var eq:Equip = null;
         if(this._equipBag[num] != null)
         {
            eq = this._equipBag[num];
            this._equipBag[num] = null;
         }
         return eq;
      }
      
      public function delSupplies(num:Number) : Supplies
      {
         var su:Supplies = null;
         if(this._suppliesBag[num] != null)
         {
            su = this._suppliesBag[num];
            this._suppliesBag[num] = null;
         }
         return su;
      }
      
      public function delGem(num:Number, times:Number) : Gem
      {
         var gem:Gem = null;
         gem = this._gemBag[num];
         if(gem.getIsPile() == true)
         {
            gem.useGem(times);
            if(gem.getTimes() == 0)
            {
               this._gemBag[num] = null;
            }
         }
         else
         {
            this._gemBag[num] = null;
         }
         return gem.cloneGem(times);
      }
      
      public function delQuset(num:Number) : Quest
      {
         var quest:Quest = null;
         if(this._questBag[num] != null)
         {
            quest = this._questBag[num];
            this._questBag[num] = null;
         }
         return quest;
      }
      
      public function delOtherobj(num:Number, times:Number = 1) : Otherobj
      {
         var otherobj:Otherobj = null;
         otherobj = this._otherobjBag[num];
         if(otherobj.isMany() == true)
         {
            otherobj.useOtherobj(times);
            if(otherobj.getTimes() == 0)
            {
               this._otherobjBag[num] = null;
            }
         }
         else
         {
            this._otherobjBag[num] = null;
         }
         return otherobj.cloneOtherobj(times);
      }
      
      public function equipBagMove(begin:Number, end:Number) : *
      {
         var temp:Equip = null;
         if(this._equipBag[end] == null)
         {
            this._equipBag[end] = this._equipBag[begin];
            this._equipBag[begin] = null;
         }
         else
         {
            temp = this._equipBag[end];
            this._equipBag[end] = this._equipBag[begin];
            this._equipBag[begin] = temp;
         }
      }
      
      public function suppliesBagMove(begin:Number, end:Number) : *
      {
         var temp:Supplies = null;
         if(this._suppliesBag[end] == null)
         {
            this._suppliesBag[end] = this._suppliesBag[begin];
            this._suppliesBag[begin] = null;
         }
         else
         {
            temp = this._suppliesBag[end];
            this._suppliesBag[end] = this._suppliesBag[begin];
            this._suppliesBag[begin] = temp;
         }
      }
      
      public function gemBagMove(begin:Number, end:Number) : *
      {
         var tt:uint = 0;
         var temp:Gem = null;
         if(begin != end)
         {
            if(this._gemBag[end] == null)
            {
               this._gemBag[end] = this._gemBag[begin];
               this._gemBag[begin] = null;
            }
            else if(this._gemBag[end].getIsPile() == true && this._gemBag[end].compareGem(this._gemBag[begin]) == true)
            {
               if(this._gemBag[end].getTimes() < this._gemBag[end].getPileLimit())
               {
                  tt = this._gemBag[end].getTimes() + this._gemBag[begin].getTimes();
                  if(tt > this._gemBag[end].getPileLimit())
                  {
                     this._gemBag[begin].useGem(this._gemBag[end].getPileLimit() - this._gemBag[end].getTimes());
                     this._gemBag[end].addGem(this._gemBag[end].getPileLimit() - this._gemBag[end].getTimes());
                  }
                  else
                  {
                     this._gemBag[end].addGem(this._gemBag[begin].getTimes());
                     this._gemBag[begin] = null;
                  }
               }
            }
            else
            {
               temp = this._gemBag[end];
               this._gemBag[end] = this._gemBag[begin];
               this._gemBag[begin] = temp;
            }
         }
      }
      
      public function questBagMove(begin:Number, end:Number) : *
      {
         var temp:Otherobj = null;
         if(this._questBag[end] == null)
         {
            this._questBag[end] = this._questBag[begin];
            this._questBag[begin] = null;
         }
         else
         {
            temp = this._questBag[end];
            this._questBag[end] = this._questBag[begin];
            this._questBag[begin] = temp;
         }
      }
      
      public function otherobjBagMove(begin:Number, end:Number) : *
      {
         var tt:uint = 0;
         var temp:Otherobj = null;
         if(begin != end)
         {
            if(this._otherobjBag[end] == null)
            {
               this._otherobjBag[end] = this._otherobjBag[begin];
               this._otherobjBag[begin] = null;
            }
            else if(this._otherobjBag[end].isMany() == true && this._otherobjBag[end].compareOtherobj(this._otherobjBag[begin]) == true)
            {
               if(this._otherobjBag[end].getTimes() < this._otherobjBag[end].getPileLimit())
               {
                  tt = this._otherobjBag[end].getTimes() + this._otherobjBag[begin].getTimes();
                  if(tt > this._otherobjBag[end].getPileLimit())
                  {
                     this._otherobjBag[begin].useOtherobj(this._otherobjBag[end].getPileLimit() - this._otherobjBag[end].getTimes());
                     this._otherobjBag[end].addOtherobj(this._otherobjBag[end].getPileLimit() - this._otherobjBag[end].getTimes());
                  }
                  else
                  {
                     this._otherobjBag[end].addOtherobj(this._otherobjBag[begin].getTimes());
                     this._otherobjBag[begin] = null;
                  }
               }
            }
            else
            {
               temp = this._otherobjBag[end];
               this._otherobjBag[end] = this._otherobjBag[begin];
               this._otherobjBag[begin] = temp;
            }
         }
      }
      
      public function isHaveOtherobj(id:Number) : Boolean
      {
         for(var i:uint = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).compareById(id) == true)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function isHaveEquip(id:Number) : Boolean
      {
         for(var i:uint = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).compareById(id) == true)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function isHaveSupplies(id:Number) : Boolean
      {
         for(var i:uint = 0; i < this._limitS.getValue(); i++)
         {
            if(this._suppliesBag[i] != null)
            {
               if((this._suppliesBag[i] as Supplies).compareById(id) == true)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function isHavesQuest(id:Number) : Boolean
      {
         for(var i:uint = 0; i < 24; i++)
         {
            if(this._questBag[i] != null)
            {
               if((this._questBag[i] as Quest).compareById(id) == true)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function isHaveGem(id:Number) : Boolean
      {
         for(var i:uint = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] != null)
            {
               if((this._gemBag[i] as Gem).compareById(id) == true)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function getOtherobjNum(id:Number) : Number
      {
         var num:Number = 0;
         for(var i:uint = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).compareById(id))
               {
                  if(this._otherobjBag[i].isMany() == true)
                  {
                     num += this._otherobjBag[i].getTimes();
                  }
                  else
                  {
                     num += 1;
                  }
               }
            }
         }
         return num;
      }
      
      public function getGemNum(id:Number) : Number
      {
         var num:Number = 0;
         for(var i:uint = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] != null)
            {
               if((this._gemBag[i] as Gem).compareById(id))
               {
                  if(this._gemBag[i].getIsPile() == true)
                  {
                     num += this._gemBag[i].getTimes();
                  }
                  else
                  {
                     num += 1;
                  }
               }
            }
         }
         return num;
      }
      
      public function getEquipObjNum(id:Number) : Number
      {
         var num:Number = 0;
         for(var i:uint = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).compareById(id))
               {
                  num += 1;
               }
            }
         }
         return num;
      }
      
      public function getSupObjNum(id:Number) : Number
      {
         var num:Number = 0;
         for(var i:uint = 0; i < this._limitS.getValue(); i++)
         {
            if(this._suppliesBag[i] != null)
            {
               if((this._suppliesBag[i] as Supplies).compareById(id))
               {
                  num += 1;
               }
            }
         }
         return num;
      }
      
      public function getAndUseOtherobj(id:Number, num:Number = 1) : void
      {
         var i:uint = 0;
         for(var j:uint = 0; j < num; j++)
         {
            for(i = 0; i < this._limitO.getValue(); i++)
            {
               if(this._otherobjBag[i] != null)
               {
                  if((this._otherobjBag[i] as Otherobj).compareById(id))
                  {
                     if(this._otherobjBag[i].isMany() == true)
                     {
                        this._otherobjBag[i].useOtherobj(1);
                        if(this._otherobjBag[i].getTimes() == 0)
                        {
                           this._otherobjBag[i] = null;
                        }
                     }
                     else
                     {
                        this._otherobjBag[i] = null;
                     }
                     break;
                  }
               }
            }
         }
      }
      
      public function delGemById(id:Number, times:Number) : Array
      {
         var t:int = 0;
         var arr:Array = [];
         for(var i:uint = 0; i < this._limitG.getValue(); i++)
         {
            if(times <= 0)
            {
               break;
            }
            if(this._gemBag[i] != null)
            {
               if((this._gemBag[i] as Gem).compareById(id))
               {
                  if(this._gemBag[i].getIsPile() == true)
                  {
                     for(t = 1; t <= times; t++)
                     {
                        (this._gemBag[i] as Gem).useGem(1);
                        if((this._gemBag[i] as Gem).getTimes() == 0)
                        {
                           arr.push((this._gemBag[i] as Gem).cloneGem(t));
                           times -= t;
                           this._gemBag[i] = null;
                           break;
                        }
                        if(t == times)
                        {
                           arr.push((this._gemBag[i] as Gem).cloneGem(times));
                           times = 0;
                           break;
                        }
                     }
                  }
                  else
                  {
                     times--;
                     arr.push((this._gemBag[i] as Gem).cloneGem(1));
                     this._gemBag[i] = null;
                  }
               }
            }
         }
         return arr;
      }
      
      public function delOtherById(id:Number, times:Number = 1) : Array
      {
         var t:int = 0;
         var arr:Array = [];
         for(var i:uint = 0; i < this._limitO.getValue(); i++)
         {
            if(times <= 0)
            {
               break;
            }
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).compareById(id))
               {
                  if(this._otherobjBag[i].isMany() == true)
                  {
                     for(t = 1; t <= times; t++)
                     {
                        (this._otherobjBag[i] as Otherobj).useOtherobj(1);
                        if((this._otherobjBag[i] as Otherobj).getTimes() == 0)
                        {
                           arr.push((this._otherobjBag[i] as Otherobj).cloneOtherobj(t));
                           times -= t;
                           this._otherobjBag[i] = null;
                           break;
                        }
                        if(t == times)
                        {
                           arr.push((this._otherobjBag[i] as Otherobj).cloneOtherobj(times));
                           times = 0;
                           break;
                        }
                     }
                  }
                  else
                  {
                     times--;
                     arr.push((this._otherobjBag[i] as Otherobj).cloneOtherobj(1));
                     this._otherobjBag[i] = null;
                  }
               }
            }
         }
         return arr;
      }
      
      public function clearOther(id:Number) : void
      {
         for(var i:uint = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).getId() == id)
               {
                  this._otherobjBag[i] = null;
               }
            }
         }
      }
      
      public function getEquipById(id:Number) : Array
      {
         var arr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).compareById(id))
               {
                  arr1.push(this._equipBag[i]);
                  arr2.push(i);
               }
            }
         }
         arr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return arr;
      }
      
      public function getEquipByIdTOW(id:Number) : Array
      {
         var arr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if(Boolean((this._equipBag[i] as Equip).compareById(id)) && (this._equipBag[i] as Equip).getRemainingTime() > 0)
               {
                  arr1.push(this._equipBag[i]);
                  arr2.push(i);
               }
            }
         }
         arr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return arr;
      }
      
      public function getOtherobjById(id:Number) : Array
      {
         var arr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).compareById(id))
               {
                  arr1.push(this._otherobjBag[i]);
                  arr2.push(i);
               }
            }
         }
         arr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return arr;
      }
      
      public function getQuestById(id:Number) : Array
      {
         var arr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < 24; i++)
         {
            if(this._questBag[i] != null)
            {
               if((this._questBag[i] as Quest).compareById(id))
               {
                  arr1.push(this._questBag[i]);
                  arr2.push(i);
               }
            }
         }
         arr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return arr;
      }
      
      public function getGemById(id:Number) : Array
      {
         var arr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] != null)
            {
               if((this._gemBag[i] as Gem).compareById(id))
               {
                  arr1.push(this._gemBag[i]);
                  arr2.push(i);
               }
            }
         }
         arr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return arr;
      }
      
      public function getSupById(id:Number) : Array
      {
         var arr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitS.getValue(); i++)
         {
            if(this._suppliesBag[i] != null)
            {
               if((this._suppliesBag[i] as Supplies).compareById(id))
               {
                  arr1.push(this._suppliesBag[i]);
                  arr2.push(i);
               }
            }
         }
         arr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return arr;
      }
      
      public function getGemByType(type:Number) : Array
      {
         var gemArr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] != null)
            {
               if((this._gemBag[i] as Gem).getType() == type)
               {
                  arr1.push(this._gemBag[i]);
                  arr2.push(i);
               }
            }
         }
         gemArr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return gemArr;
      }
      
      public function getOtherByType(type:Number) : Array
      {
         var gemArr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).getType() == type)
               {
                  arr1.push(this._otherobjBag[i]);
                  arr2.push(i);
               }
            }
         }
         gemArr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return gemArr;
      }
      
      public function getEquipAndPoint() : Array
      {
         var EArr:Array = [];
         var arr1:Array = [];
         var arr2:Array = [];
         for(var i:uint = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               arr1.push(this._equipBag[i]);
               arr2.push(i);
            }
         }
         EArr.push(arr1,arr2);
         if(arr1.length < 1)
         {
            return null;
         }
         return EArr;
      }
      
      public function delEquipById(id:Number, times:Number) : Array
      {
         var i:uint = 0;
         var eArr:Array = [];
         var num:Number = 0;
         if(times > 0)
         {
            for(i = 0; i < this._limitE.getValue(); i++)
            {
               if(this._equipBag[i] != null)
               {
                  if((this._equipBag[i] as Equip).compareById(id))
                  {
                     eArr.push((this._equipBag[i] as Equip).getClone());
                     this._equipBag[i] = null;
                     num++;
                     if(num >= times)
                     {
                        break;
                     }
                  }
               }
            }
         }
         return eArr;
      }
      
      public function delSuppliesById(id:Number, times:Number) : Array
      {
         var i:uint = 0;
         var SArr:Array = [];
         var num:Number = 0;
         if(times > 0)
         {
            for(i = 0; i < this._limitS.getValue(); i++)
            {
               if(this._suppliesBag[i] != null)
               {
                  if((this._suppliesBag[i] as Supplies).compareById(id))
                  {
                     SArr.push((this._suppliesBag[i] as Supplies).getClone());
                     this._suppliesBag[i] = null;
                     num++;
                     if(num >= times)
                     {
                        break;
                     }
                  }
               }
            }
         }
         return SArr;
      }
      
      public function cheatTesting() : *
      {
         if(Main.NoLogInfo[4])
         {
            return;
         }
         for(var i:uint = 0; i < this._limitG.getValue(); i++)
         {
            if(this._gemBag[i] != null)
            {
               if((this._gemBag[i] as Gem).getTimes() >= 1000)
               {
                  SaveXX.Save(4,(this._gemBag[i] as Gem).getTimes());
                  break;
               }
            }
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).getTimes() >= 1000)
               {
                  SaveXX.Save(4,(this._otherobjBag[i] as Otherobj).getTimes());
                  break;
               }
            }
         }
      }
      
      public function cheatEquip() : *
      {
         var j:uint = 0;
         var k:uint = 0;
         for(var i:uint = 0; i < 23; i++)
         {
            for(j = uint(i + 1); j < this._limitE.getValue(); j++)
            {
               if(this._equipBag[i])
               {
                  if((this._equipBag[i] as Equip).testEquip(this._equipBag[j]))
                  {
                     this._equipBag[i] = null;
                  }
               }
            }
            for(k = 0; k < 35; k++)
            {
               if(Boolean(this._equipBag[i]) && Boolean(StoragePanel.storage.getEquipFromStorage(k)))
               {
                  if((this._equipBag[i] as Equip).testEquip(StoragePanel.storage.getEquipFromStorage(k)))
                  {
                     this._equipBag[i] = null;
                  }
               }
            }
         }
      }
      
      public function cheatGem() : *
      {
         var j:uint = 0;
         var k:uint = 0;
         for(var i:uint = 0; i < 23; i++)
         {
            for(j = uint(i + 1); j < this._limitG.getValue(); j++)
            {
               if(this._gemBag[i])
               {
                  (this._gemBag[i] as Gem).testGem(this._gemBag[j]);
               }
            }
            for(k = 0; k < 35; k++)
            {
               if(Boolean(this._gemBag[i]) && Boolean(StoragePanel.storage.getGemFromStorage(k)))
               {
                  (this._gemBag[i] as Gem).testGem(StoragePanel.storage.getGemFromStorage(k));
               }
            }
         }
      }
      
      public function cheatOther() : *
      {
         var j:uint = 0;
         var k:uint = 0;
         for(var i:uint = 0; i < 23; i++)
         {
            for(j = uint(i + 1); j < this._limitO.getValue(); j++)
            {
               if(this._otherobjBag[i])
               {
                  (this._otherobjBag[i] as Otherobj).testOtherobj(this._otherobjBag[j]);
               }
            }
            for(k = 0; k < 35; k++)
            {
               if(Boolean(this._otherobjBag[i]) && Boolean(StoragePanel.storage.getOtherobjFromStorage(k)))
               {
                  (this._otherobjBag[i] as Otherobj).testOtherobj(StoragePanel.storage.getOtherobjFromStorage(k));
               }
            }
         }
      }
      
      public function plan1_5() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getGrid() == 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan4_15() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getGrid() == 0)
               {
                  if((this._equipBag[i] as Equip).getGemSlot().getColor() == 3)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function getEquipGold(lv:Number) : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getDressLevel() >= lv)
               {
                  if((this._equipBag[i] as Equip).getColor() >= 4)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function getWuQiGold(lv:Number) : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getDressLevel() >= lv)
               {
                  if((this._equipBag[i] as Equip).getColor() >= 4)
                  {
                     if((this._equipBag[i] as Equip).getPosition() >= 5 && (this._equipBag[i] as Equip).getPosition() <= 7)
                     {
                        return true;
                     }
                  }
               }
            }
         }
         return false;
      }
      
      public function plan2_5(lv:Number) : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getReinforceLevel() >= lv)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan4_16() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getNewSkill() > 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_7() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getStar() > 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_8() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getBlessAttrib())
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_12() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getPosition() == 8)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_13() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getPosition() == 9)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan5_14() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getColor() >= 4)
               {
                  if((this._equipBag[i] as Equip).getPosition() == 8)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function plan5_15() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getColor() >= 4)
               {
                  if((this._equipBag[i] as Equip).getPosition() == 9)
                  {
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function plan6_10() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getId() >= 20001 && (this._equipBag[i] as Equip).getId() <= 20024)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan6_14() : Boolean
      {
         for(var i:int = 0; i < this._limitO.getValue(); i++)
         {
            if(this._otherobjBag[i] != null)
            {
               if((this._otherobjBag[i] as Otherobj).getId() >= 63163 && (this._otherobjBag[i] as Otherobj).getId() <= 63168)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan6_15() : int
      {
         var num:int = 0;
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getStar() >= 3)
               {
                  num++;
               }
            }
         }
         return num;
      }
      
      public function plan6_7() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getSuitId() >= 26 && (this._equipBag[i] as Equip).getSuitId() <= 30)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan7_3() : Boolean
      {
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getId() >= 20079 && (this._equipBag[i] as Equip).getId() <= 20084)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function plan7_4() : int
      {
         var num:int = 0;
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getStar() >= 4)
               {
                  num++;
               }
            }
         }
         return num;
      }
      
      public function plan8_4() : int
      {
         var num:int = 0;
         for(var i:int = 0; i < this._limitE.getValue(); i++)
         {
            if(this._equipBag[i] != null)
            {
               if((this._equipBag[i] as Equip).getStar() >= 5)
               {
                  num++;
               }
            }
         }
         return num;
      }
   }
}

