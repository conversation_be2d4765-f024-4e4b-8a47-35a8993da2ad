package src.npc
{
   import com.hotpoint.braveManIII.views.composePanel.*;
   import com.hotpoint.braveManIII.views.equipShopPanel.*;
   import com.hotpoint.braveManIII.views.itemsPanel.*;
   import com.hotpoint.braveManIII.views.skillPanel.*;
   import com.hotpoint.braveManIII.views.strPanel.*;
   import com.hotpoint.braveManIII.views.strengthenPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.*;
   import src.tool.*;
   
   public class 装备商人界面 extends MovieClip
   {
      
      private static var only:装备商人界面;
      
      public var Q_p1:SimpleButton;
      
      public var Q_p3:SimpleButton;
      
      public var Q_p4:SimpleButton;
      
      public var buy_btn:SimpleButton;
      
      public var close1:SimpleButton;
      
      public var close2:SimpleButton;
      
      public var temp_btn:SimpleButton;
      
      public var temp2_btn:SimpleButton;
      
      public var getObj_mc:MovieClip;
      
      public var song_btn:SimpleButton;
      
      public var close:SimpleButton;
      
      public var jieMian2:MovieClip;
      
      public var ling_btn:SimpleButton;
      
      public var quxiao_btn:SimpleButton;
      
      public var A_mc:MovieClip;
      
      public var numUP_btn:SimpleButton;
      
      public var numDOWN_btn:SimpleButton;
      
      public var xili_p1:SimpleButton;
      
      public var xili_p2:SimpleButton;
      
      public var _txt:TextField;
      
      public var jindutiao_mc:MovieClip;
      
      public var qinMiDu_txt:TextField;
      
      public function 装备商人界面()
      {
         super();
         this.Q_p1.addEventListener(MouseEvent.CLICK,this.P1QQ);
         this.Q_p3.addEventListener(MouseEvent.CLICK,this.P3QQ);
         this.Q_p4.addEventListener(MouseEvent.CLICK,this.P4QQ);
         this.buy_btn.addEventListener(MouseEvent.CLICK,this.BUY);
         this.close1.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.close2.addEventListener(MouseEvent.CLICK,this.CloseXX);
         this.temp_btn.addEventListener(MouseEvent.CLICK,this.getObj_mc_Open);
         this.temp2_btn.addEventListener(MouseEvent.CLICK,this.jieMian2_Open);
         this.getObj_mc.song_btn.addEventListener(MouseEvent.CLICK,this.GetNpc_Obj);
         this.getObj_mc.close.addEventListener(MouseEvent.CLICK,this.getObj_mc_Close);
         this.jieMian2.ling_btn.addEventListener(MouseEvent.CLICK,this.GetPlayer_Obj);
         this.jieMian2.quxiao_btn.addEventListener(MouseEvent.CLICK,this.jieMian2_Close);
         this.getObj_mc.A_mc.numUP_btn.addEventListener(MouseEvent.CLICK,this.SongNumUP);
         this.getObj_mc.A_mc.numDOWN_btn.addEventListener(MouseEvent.CLICK,this.SongNumDOWN);
         this.xili_p1.addEventListener(MouseEvent.CLICK,this.P1XL);
         this.xili_p2.addEventListener(MouseEvent.CLICK,this.P2XL);
         if(!Main.P1P2)
         {
            this.xili_p2.visible = false;
         }
      }
      
      public static function Open(xx:int = 0, yy:int = 0) : *
      {
         var classRef:Class = null;
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         MusicBox.MusicPlay2("m4");
         if(!only)
         {
            classRef = All_Npc.loadData.getClass("src.npc.装备商人界面") as Class;
            only = new classRef();
            for(i = 1; i < 8; i++)
            {
               mm = new Shop_picNEW();
               num = int(only.jieMian2["s1_" + i].getChildIndex(only.jieMian2["s1_" + i].pic_xx));
               mm.x = only.jieMian2["s1_" + i].pic_xx.x;
               mm.y = only.jieMian2["s1_" + i].pic_xx.y;
               mm.name = "pic_xx";
               only.jieMian2["s1_" + i].removeChild(only.jieMian2["s1_" + i].pic_xx);
               only.jieMian2["s1_" + i].pic_xx = mm;
               only.jieMian2["s1_" + i].addChild(mm);
               only.jieMian2["s1_" + i].setChildIndex(mm,num);
            }
         }
         TiaoShi.txtShow("src.npc.装备商人界面x2");
         Main._stage.addChild(only);
         only.x = xx;
         only.y = yy;
         only.visible = true;
         only.ShowJinDu();
         only.xiaoLiDaiNum();
         only.LingQuYN();
         TiaoShi.txtShow("src.npc.装备商人界面x3");
      }
      
      public static function Close() : *
      {
         if(only)
         {
            only.x = 5000;
            only.y = 5000;
            only.visible = false;
         }
      }
      
      private function GetNpc_Obj(e:*) : *
      {
         this.getObj_mc_Open();
         this.Use_xiaoLiDaiNum(int(this.getObj_mc.A_mc._txt.text));
         this.ShowJinDu();
         TaskData.isOk();
      }
      
      private function GetPlayer_Obj(e:*) : *
      {
         All_Npc.NpcGetPlayerObj(2);
         this.LingQuYN();
         this.ShowJinDu();
      }
      
      private function P1XL(e:*) : *
      {
         xilianPanel.open(true);
      }
      
      private function P2XL(e:*) : *
      {
         xilianPanel.open(false);
      }
      
      private function BUY(e:*) : *
      {
         EquipShopPanel.open();
         装备商人界面.Close();
      }
      
      private function P1QQ(e:*) : *
      {
         XiangQianPanel.open();
         装备商人界面.Close();
      }
      
      private function P3QQ(e:*) : *
      {
         ComPosePanel.open();
         装备商人界面.Close();
      }
      
      private function P4QQ(e:*) : *
      {
         StrPanel.open();
         装备商人界面.Close();
      }
      
      private function CloseXX(e:*) : *
      {
         Close();
      }
      
      private function ShowJinDu() : *
      {
         this.qinMiDu_txt.text = InitData.qinMiDu_Arr[2].getValue() + "/" + InitData.qinMiDu_Max_Arr[2].getValue();
         if(InitData.qinMiDu_Arr[2].getValue() <= InitData.qinMiDu_Max_Arr[2].getValue())
         {
            this.jindutiao_mc.XX_mc.scaleX = InitData.qinMiDu_Arr[2].getValue() / InitData.qinMiDu_Max_Arr[2].getValue();
         }
         else
         {
            this.jindutiao_mc.XX_mc.scaleX = 1;
         }
      }
      
      private function getObj_mc_Open(e:* = null) : *
      {
         this.getObj_mc.x = 0;
         this.getObj_mc.y = 0;
         this.getObj_mc.visible = true;
         this.jieMian2_Close();
      }
      
      private function getObj_mc_Close(e:* = null) : *
      {
         this.getObj_mc.x = 5000;
         this.getObj_mc.y = 5000;
         this.getObj_mc.visible = false;
      }
      
      private function jieMian2_Open(e:* = null) : *
      {
         this.jieMian2.x = 242;
         this.jieMian2.y = 166;
         this.jieMian2.visible = true;
         this.getObj_mc_Close();
         this.ShowPicAndNum();
         if(InitData.qinMiDu_Arr[2].getValue() < InitData.qinMiDu_Max_Arr[2].getValue())
         {
            this.jieMian2.ling_btn.visible = false;
         }
         else if(this.LingQuYN())
         {
            this.jieMian2.ling_btn.visible = true;
         }
      }
      
      private function jieMian2_Close(e:* = null) : *
      {
         this.jieMian2.x = 5000;
         this.jieMian2.y = 5000;
         this.jieMian2.visible = false;
      }
      
      private function SongNumUP(e:* = null) : *
      {
         if(int(this.getObj_mc.A_mc._txt.text) < 20)
         {
            this.getObj_mc.A_mc._txt.text = int(this.getObj_mc.A_mc._txt.text) + 1;
         }
         else
         {
            this.getObj_mc.A_mc._txt.text = 1;
         }
      }
      
      private function SongNumDOWN(e:* = null) : *
      {
         if(int(this.getObj_mc.A_mc._txt.text) > 1)
         {
            this.getObj_mc.A_mc._txt.text = int(this.getObj_mc.A_mc._txt.text) - 1;
         }
         else
         {
            this.getObj_mc.A_mc._txt.text = 20;
         }
      }
      
      private function xiaoLiDaiNum() : int
      {
         var p1num:int = int(Main.player1.getBag().getOtherobjNum(63102));
         var p2num:int = 0;
         if(Main.P1P2)
         {
            p2num = int(Main.player2.getBag().getOtherobjNum(63102));
         }
         var 数量:int = p1num + p2num;
         if(数量 >= 1)
         {
            this.temp_btn.visible = true;
         }
         return 数量;
      }
      
      private function Use_xiaoLiDaiNum(ii:int) : *
      {
         var 点数:int = 0;
         var p1num:int = int(Main.player1.getBag().getOtherobjNum(63102));
         var p2num:int = 0;
         if(Main.P1P2)
         {
            p2num = int(Main.player2.getBag().getOtherobjNum(63102));
         }
         var 数量:int = p1num + p2num;
         if(数量 >= ii)
         {
            if(p1num <= ii)
            {
               Main.player1.getBag().getAndUseOtherobj(63102,p1num);
               if(Main.P1P2)
               {
                  Main.player2.getBag().getAndUseOtherobj(63102,ii - p1num);
               }
               else
               {
                  trace("扣除小礼袋数量 #出现异常！");
               }
            }
            else
            {
               Main.player1.getBag().getAndUseOtherobj(63102,ii);
            }
            点数 = ii * 3;
            InitData.qinMiDu_Arr[2].setValue(InitData.qinMiDu_Arr[2].getValue() + 点数);
         }
         else
         {
            NewMC.Open("文字提示",this,480,290,30,0,true,2,"小礼袋不足");
         }
      }
      
      private function LingQuYN() : Boolean
      {
         if(InitData.qinMiDu_Time[2].getValue() >= Main.serverTime.getValue())
         {
            this.jieMian2.ling_btn.visible = false;
            return false;
         }
         return true;
      }
      
      private function ShowPicAndNum() : *
      {
         var arr:Array = All_Npc.get_AllObj_Pic_And_Num(2);
         trace("InitData.qinMiDu_num[2] = " + InitData.qinMiDu_num[2].getValue());
         var xxx:* = InitData.qinMiDu_num[2].getValue() % 7;
         if(xxx == 0)
         {
            xxx = 7;
         }
         for(var xx:int = 1; xx < 8; xx++)
         {
            trace("!!!!!!!!!!!!~~~~~~~~~~~xxx = " + xxx + ",arr[xxx][1] = " + arr[xxx][1]);
            this.jieMian2["s1_" + xx].pic_xx.gotoAndStop(arr[xxx][1]);
            this.jieMian2["s1_" + xx].howNum.text = "";
            if(xx < 2)
            {
               this.jieMian2["_txt" + xx].text = "x " + arr[xxx][0];
            }
            xxx++;
            if(xxx > 7)
            {
               xxx = 1;
            }
         }
      }
   }
}

