package com.hotpoint.braveManIII.views.itemsPanel
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import com.hotpoint.braveManIII.models.player.PlayerData;
   import com.hotpoint.braveManIII.repository.*;
   import com.hotpoint.braveManIII.repository.other.*;
   import com.hotpoint.braveManIII.views.itemsTooltip.*;
   import flash.display.*;
   import flash.events.*;
   import flash.text.*;
   import src.*;
   
   public class JinHuaPanel2 extends MovieClip
   {
      
      public static var itemsTooltip:ItemsTooltip;
      
      public static var jhPanel:MovieClip;
      
      public static var jhp:JinHuaPanel2;
      
      public static var clickObj:MovieClip;
      
      private static var nameStr:String;
      
      public static var clickNum:Number;
      
      public static var starNum:Number;
      
      public static var myplayer:PlayerData;
      
      private static var loadData:ClassLoader;
      
      public static var yeshu:Number = 0;
      
      public static var selbool:Boolean = false;
      
      public static var isPOne:Boolean = true;
      
      public static var mapNum:Number = 1;
      
      public static var partNum:Number = 1;
      
      public static var count:int = 0;
      
      public static var arr_cl:Array = [];
      
      private static var OpenYN:Boolean = false;
      
      private static var loadName:String = "Panel_XLZF_2_v1320.swf";
      
      public static var selObj_LV:int = 1;
      
      public static var numArr:Array = [10,3,3,5];
      
      public static var maxLV:int = 12;
      
      public static var upOK:Boolean = false;
      
      public static var saveOK:Boolean = false;
      
      public static var timeXX:int = 0;
      
      public function JinHuaPanel2()
      {
         super();
         itemsTooltip = new ItemsTooltip();
         itemsTooltip.visible = false;
      }
      
      private static function LoadSkin() : *
      {
         if(!jhPanel && !loadData)
         {
            loadData = new ClassLoader(loadName);
            loadData.addEventListener(Event.COMPLETE,onLoadingOK);
            LoadInGame.Open(loadData);
         }
      }
      
      private static function InitIcon() : *
      {
         var i:uint = 0;
         var mm:MovieClip = null;
         var num:int = 0;
         for(i = 0; i < 24; i++)
         {
            mm = new Shop_picNEW();
            num = jhPanel.getChildIndex(jhPanel["e" + i]);
            mm.x = jhPanel["e" + i].x;
            mm.y = jhPanel["e" + i].y;
            mm.name = "e" + i;
            jhPanel.removeChild(jhPanel["e" + i]);
            jhPanel["e" + i] = mm;
            jhPanel.addChild(mm);
            jhPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 4; i++)
         {
            mm = new Shop_picNEW();
            num = jhPanel.getChildIndex(jhPanel["x" + i]);
            mm.x = jhPanel["x" + i].x;
            mm.y = jhPanel["x" + i].y;
            mm.name = "x" + i;
            jhPanel.removeChild(jhPanel["x" + i]);
            jhPanel["x" + i] = mm;
            jhPanel.addChild(mm);
            jhPanel.setChildIndex(mm,num);
         }
         for(i = 0; i < 8; i++)
         {
            mm = new Shop_picNEW();
            num = jhPanel.getChildIndex(jhPanel["s" + i]);
            mm.x = jhPanel["s" + i].x;
            mm.y = jhPanel["s" + i].y;
            mm.name = "s" + i;
            jhPanel.removeChild(jhPanel["s" + i]);
            jhPanel["s" + i] = mm;
            jhPanel.addChild(mm);
            jhPanel.setChildIndex(mm,num);
         }
         mm = new Shop_picNEW();
         num = jhPanel.getChildIndex(jhPanel["select"]);
         mm.x = jhPanel["select"].x;
         mm.y = jhPanel["select"].y;
         mm.name = "select";
         jhPanel.removeChild(jhPanel["select"]);
         jhPanel["select"] = mm;
         jhPanel.addChild(mm);
         jhPanel.setChildIndex(mm,num);
      }
      
      private static function onLoadingOK(e:*) : *
      {
         var classRef:Class = loadData.getClass("JHShow") as Class;
         jhPanel = new classRef();
         jhp.addChild(jhPanel);
         InitIcon();
         jhPanel["_BLACK_mc"].visible = false;
         if(OpenYN)
         {
            open(isPOne);
         }
         else
         {
            close();
         }
      }
      
      private static function InitOpen() : *
      {
         jhp = new JinHuaPanel2();
         LoadSkin();
         Main._stage.addChild(jhp);
         OpenYN = true;
      }
      
      private static function InitClose() : *
      {
         jhp = new JinHuaPanel2();
         Main._stage.addChild(jhp);
         OpenYN = false;
      }
      
      public static function open(pp:Boolean) : void
      {
         Main.allClosePanel();
         if(jhPanel)
         {
            clickObj = null;
            Main.stopXX = true;
            jhp.x = 0;
            jhp.y = 0;
            isPOne = pp;
            if(isPOne)
            {
               myplayer = Main.player1;
            }
            addListenerP1();
            Main._stage.addChild(jhp);
            jhp.visible = true;
         }
         else
         {
            isPOne = pp;
            InitOpen();
         }
      }
      
      public static function close() : void
      {
         if(jhPanel)
         {
            Main.stopXX = false;
            removeListenerP1();
            jhp.visible = false;
            selbool = false;
         }
         else
         {
            InitClose();
         }
      }
      
      public static function addListenerP1() : *
      {
         if(Main.P1P2)
         {
            jhPanel["bagOne"].visible = false;
            jhPanel["bagTwo"].visible = true;
            jhPanel["back_mc"].visible = true;
            jhPanel["bagOne"].addEventListener(MouseEvent.CLICK,to1p);
            jhPanel["bagTwo"].addEventListener(MouseEvent.CLICK,to2p);
         }
         else
         {
            jhPanel["bagOne"].visible = false;
            jhPanel["bagTwo"].visible = false;
            jhPanel["back_mc"].visible = false;
         }
         jhPanel["up_btn"].addEventListener(MouseEvent.CLICK,upGo);
         jhPanel["down_btn"].addEventListener(MouseEvent.CLICK,downGo);
         jhPanel["jh_btn"].visible = false;
         jhPanel["jh_btn"].addEventListener(MouseEvent.CLICK,doJH);
         jhPanel["close"].addEventListener(MouseEvent.CLICK,closeJH);
         for(var i:uint = 0; i < 24; i++)
         {
            jhPanel["e" + i].mouseChildren = false;
            jhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["e" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["e" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            jhPanel["s" + i].mouseChildren = false;
            jhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["s" + i].addEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["s" + i].addEventListener(MouseEvent.CLICK,selected);
         }
         jhPanel["select"].gotoAndStop(1);
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function playShow() : *
      {
         var numXX:* = undefined;
         var e1:Equip = null;
         var e2:Equip = null;
         var yeshuNum:int = yeshu + 1;
         jhPanel["yeshu_txt"].text = yeshuNum + "/2";
         for(var i:int = 0; i < 24; i++)
         {
            jhPanel["e" + i].t_txt.text = "";
            numXX = i + 24 * yeshu;
            e1 = myplayer.getBag().getEquipFromBag(numXX);
            jhPanel["e" + i].equipX = e1;
            if(e1 && e1.getBlessAttrib() && e1._blessAttrib.getBeishu() > 0 && e1._blessAttrib.getBeishu() <= maxLV && e1.getColor() >= 5)
            {
               trace("祝福3阶:",i,e1._blessAttrib.getBeishu(),e1._blessAttrib.getBeishuValue(),e1.getColor());
               jhPanel["e" + i].gotoAndStop(e1.getFrame());
               jhPanel["e" + i].visible = true;
            }
            else
            {
               jhPanel["e" + i].visible = false;
            }
         }
         for(i = 0; i < 8; i++)
         {
            jhPanel["s" + i].t_txt.text = "";
            e2 = myplayer.getEquipSlot().getEquipFromSlot(i);
            jhPanel["s" + i].equipX = e2;
            if(e2 && e2._blessAttrib && e2._blessAttrib.getBeishu() > 0 && e2._blessAttrib.getBeishu() <= maxLV && e2.getColor() >= 5)
            {
               trace("祝福3阶:",i,e2._blessAttrib.getBeishu(),e2._blessAttrib.getBeishuValue(),e2.getColor());
               jhPanel["s" + i].gotoAndStop(e2.getFrame());
               jhPanel["s" + i].visible = true;
            }
            else
            {
               jhPanel["s" + i].visible = false;
            }
         }
      }
      
      private static function tooltipOpen(e:MouseEvent) : void
      {
         var overobj:MovieClip = e.target as MovieClip;
         if(!e.target.visible)
         {
            return;
         }
         jhPanel.addChild(itemsTooltip);
         var overNum:uint = uint(overobj.name.substr(1,2));
         var str:String = overobj.name.substr(0,1);
         if(str == "e")
         {
            overNum += 24 * yeshu;
            if(myplayer.getBag().getEquipFromBag(overNum) != null)
            {
               itemsTooltip.equipTooltip(myplayer.getBag().getEquipFromBag(overNum),1);
            }
         }
         else
         {
            itemsTooltip.slotTooltip(overNum,myplayer.getEquipSlot());
         }
         itemsTooltip.visible = true;
         itemsTooltip.x = jhPanel.mouseX + 10;
         itemsTooltip.y = jhPanel.mouseY;
         itemsTooltip.setTooltipPoint();
      }
      
      public static function otherShow() : *
      {
         var eX:Equip = null;
         var numXX2:* = undefined;
         var otherID:Number = NaN;
         count = 0;
         if(Boolean(clickObj) && Boolean(clickObj.equipX))
         {
            eX = clickObj.equipX;
            JinHuaPanel2.selObj_LV = eX._blessAttrib.getBeishu() < 2 ? 1 : int(eX._blessAttrib.getBeishu());
            numXX2 = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV][1];
            jhPanel["cgl_txt"].text = "成功率:" + numXX2 + "%";
         }
         arr_cl = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV];
         trace("需求物品显示?",JinHuaPanel2.selObj_LV,arr_cl);
         for(var i:uint = 0; i < 4; i++)
         {
            otherID = Number(arr_cl[i + 2]);
            jhPanel["x" + i].gotoAndStop(OtherFactory.getFrame(otherID));
            jhPanel["n" + i].text = numArr[i];
            if(myplayer.getBag().getOtherobjNum(otherID) >= numArr[i])
            {
               jhPanel["c" + i].text = myplayer.getBag().getOtherobjNum(otherID);
               ColorX(jhPanel["c" + i],"0xFFFF00");
               ++count;
            }
            else
            {
               jhPanel["c" + i].text = myplayer.getBag().getOtherobjNum(otherID);
               ColorX(jhPanel["c" + i],"0xFF0000");
            }
         }
         if(count >= 4 && clickObj && Boolean(clickObj.equipX))
         {
            jhPanel["jh_btn"].visible = true;
         }
         else
         {
            jhPanel["jh_btn"].visible = false;
            jhPanel["cgl_txt"].text = "";
         }
      }
      
      public static function doJH(e:*) : *
      {
         var e3:Equip = null;
         for(var i:int = 0; i < 4; i++)
         {
            myplayer.getBag().delOtherById(arr_cl[i + 2],numArr[i]);
         }
         var numXX:int = Math.random() * 100 + 1;
         var numXX2:* = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV][1];
         trace("祝福成功率:",numXX,numXX2);
         if(numXX > numXX2)
         {
            upOK = false;
         }
         else
         {
            upOK = true;
            if(nameStr == "e")
            {
               e3 = myplayer.getBag().getEquipFromBag(clickNum);
               e3.setBlessAttribLV3();
            }
            else
            {
               e3 = myplayer.getEquipSlot().getEquipFromSlot(clickNum);
               e3.setBlessAttribLV3();
            }
         }
         Main.Save();
         jhPanel["_BLACK_mc"].visible = true;
         jhPanel.addEventListener(Event.ENTER_FRAME,onTime);
      }
      
      private static function onTime(e:*) : *
      {
         ++timeXX;
         if(timeXX > 50 && saveOK)
         {
            SaveOKOK();
         }
      }
      
      public static function SaveOKOK() : *
      {
         jhPanel["_BLACK_mc"].visible = false;
         saveOK = false;
         timeXX = 0;
         jhPanel.removeEventListener(Event.ENTER_FRAME,onTime);
         if(upOK)
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备祝福成功");
            close();
         }
         else
         {
            NewMC.Open("文字提示",Main._stage,480,400,30,0,true,2,"装备祝福失败!");
            JinHuaPanel2.otherShow();
         }
      }
      
      public static function to1p(e:*) : *
      {
         myplayer = Main.player1;
         jhPanel["bagOne"].visible = false;
         jhPanel["bagTwo"].visible = true;
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         jhPanel["jh_btn"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function to2p(e:*) : *
      {
         myplayer = Main.player2;
         jhPanel["bagOne"].visible = true;
         jhPanel["bagTwo"].visible = false;
         jhPanel["select"].visible = false;
         jhPanel["chose"].visible = false;
         jhPanel["jh_btn"].visible = false;
         playShow();
         otherShow();
      }
      
      public static function upGo(e:*) : *
      {
         yeshu = 0;
         playShow();
         otherShow();
      }
      
      public static function downGo(e:*) : *
      {
         yeshu = 1;
         playShow();
         otherShow();
      }
      
      private static function ColorX(t:*, col:String) : *
      {
         var myTextFormat:* = new TextFormat();
         myTextFormat.color = col;
         t.setTextFormat(myTextFormat);
      }
      
      public static function removeListenerP1() : *
      {
         var i:uint = 0;
         jhPanel["close"].removeEventListener(MouseEvent.CLICK,closeJH);
         jhPanel["jh_btn"].removeEventListener(MouseEvent.CLICK,doJH);
         for(i = 0; i < 24; i++)
         {
            jhPanel["e" + i].mouseChildren = false;
            jhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["e" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["e" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         for(i = 0; i < 8; i++)
         {
            jhPanel["s" + i].mouseChildren = false;
            jhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OVER,tooltipOpen);
            jhPanel["s" + i].removeEventListener(MouseEvent.MOUSE_OUT,tooltipClose);
            jhPanel["s" + i].removeEventListener(MouseEvent.CLICK,selected);
         }
         jhPanel["up_btn"].removeEventListener(MouseEvent.CLICK,upGo);
         jhPanel["down_btn"].removeEventListener(MouseEvent.CLICK,downGo);
      }
      
      public static function closeJH(e:*) : *
      {
         close();
      }
      
      private static function tooltipClose(e:*) : *
      {
         itemsTooltip.visible = false;
      }
      
      private static function selected(e:MouseEvent) : void
      {
         selbool = true;
         clickObj = e.target as MovieClip;
         jhPanel["chose"].visible = true;
         jhPanel["chose"].x = clickObj.x - 2;
         jhPanel["chose"].y = clickObj.y - 1;
         clickNum = uint(clickObj.name.substr(1,2));
         nameStr = clickObj.name.substr(0,1);
         jhPanel["select"].gotoAndStop(clickObj.currentFrame);
         jhPanel["select"].visible = true;
         if(nameStr == "e")
         {
            clickNum += 24 * yeshu;
         }
         if(count >= 4)
         {
            jhPanel["jh_btn"].visible = true;
         }
         var eX:Equip = clickObj.equipX;
         JinHuaPanel2.selObj_LV = eX._blessAttrib.getBeishu() < 2 ? 1 : int(eX._blessAttrib.getBeishu());
         trace("选择",clickObj.name,eX,JinHuaPanel2.selObj_LV);
         JinHuaPanel2.otherShow();
         var numXX2:* = Zhufu2Factory.allData[JinHuaPanel2.selObj_LV][1];
         jhPanel["cgl_txt"].text = "成功率:" + numXX2 + "%";
      }
   }
}

