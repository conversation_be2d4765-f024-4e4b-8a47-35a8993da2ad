package src.Skin
{
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.utils.*;
   import src.*;
   
   public class EnemyBoss52 extends EnemySkin
   {
      
      public static var kaiguan:MovieClip = new KaiGuan();
      
      internal var rushNum:int = 0;
      
      public function EnemyBoss52()
      {
         super();
         Main.world.moveChild_ChongWu.addChild(kaiguan);
         kaiguan.gotoAndStop(1);
         kaiguan.x = 5400;
         kaiguan.y = 500;
      }
      
      override public function otherGoTo(str:String) : *
      {
         if(str == "攻击4")
         {
            this.hit.y = this.y + 200;
            this.addEventListener(Event.ENTER_FRAME,this.yincang);
         }
         if(str == "攻击3")
         {
            this.addEventListener(Event.ENTER_FRAME,this.move3);
         }
      }
      
      override public function onENTER_FRAME(e:*) : *
      {
         HitKG.HitFly();
         if(this.currentFrame == this.totalFrames)
         {
            stop();
            (this.parent as Enemy).Dead();
            return;
         }
         if(continuousTime > 0)
         {
            --continuousTime;
         }
         else if(runType == "被打")
         {
            GoTo("站立");
         }
         GoToPlay();
      }
      
      internal function move3(e:Event) : *
      {
         this.rushNum += 1;
         if(this.rushNum > 22 && this.rushNum < 90 && runType == "攻击3")
         {
            if(this.parent.RL == true)
            {
               this.parent.x += 16;
            }
            else
            {
               this.parent.x -= 16;
            }
         }
         if(this.rushNum > 90)
         {
            this.rushNum = 0;
            this.removeEventListener(Event.ENTER_FRAME,this.move3);
         }
      }
      
      internal function yincang(e:Event) : *
      {
         this.rushNum += 1;
         if(this.rushNum > 28)
         {
            (this.parent as Enemy).lifeMC.visible = false;
            this.alpha = 0;
            this.rushNum = 0;
            this.removeEventListener(Event.ENTER_FRAME,this.yincang);
         }
      }
   }
}

