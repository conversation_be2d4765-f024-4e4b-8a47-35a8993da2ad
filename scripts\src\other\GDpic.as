package src.other
{
   import com.greensock.*;
   import com.greensock.easing.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol157")]
   public class GDpic extends MovieClip
   {
      
      private var timeX:int = 28;
      
      public function GDpic(xx:int, yy:int, where:MovieClip, who:Player)
      {
         super();
         this.x = xx;
         this.y = yy;
         where.addChild(this);
         xx = 580;
         if(who == Main.player_1)
         {
            xx = 357;
         }
         TweenMax.to(this,1,{
            "bezier":[{
               "x":xx,
               "y":52
            }],
            "ease":Cubic.easeOut
         });
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         --this.timeX;
         if(this.timeX <= 0)
         {
            this.parent.removeChild(this);
            removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         }
      }
   }
}

