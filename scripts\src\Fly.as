package src
{
   import flash.display.*;
   import flash.events.*;
   import flash.utils.*;
   import src.other.*;
   import src.tool.*;
   
   public class Fly extends MovieClip
   {
      
      public static var FlyData:XML;
      
      public static var FlyData2:Object = new Object();
      
      public static var All:Array = [];
      
      public var isCleanOut:Boolean = true;
      
      public var life:int = -1;
      
      public var hit:HitXX;
      
      public var time:int = -1;
      
      public var continuous:Boolean;
      
      public var speedX:int;
      
      public var speedY:int;
      
      public var moveYN:Boolean = true;
      
      public var objArr:Array = [];
      
      public var gongJi_hp:Number;
      
      public var gongJi_hp_MAX:Number;
      
      public var runArr:Array = [];
      
      public var cross:Boolean = true;
      
      public var 硬直:int;
      
      public var runX:int;
      
      public var runY:int;
      
      public var runTime:int;
      
      public var who:Object;
      
      public var getWho:Object;
      
      public var _stage:MovieClip;
      
      public var RL:Boolean;
      
      public var over:Boolean;
      
      public var over2:Boolean;
      
      public var attTimes:int;
      
      public var type:int = 0;
      
      public var space:int;
      
      public var totalTime:int;
      
      public var numValue:int;
      
      public var _name:String;
      
      private var XXX:int;
      
      private var YYY:int;
      
      public var nameXX:String = "";
      
      public var bingDong:Boolean = false;
      
      public var jnGoYn:Boolean;
      
      public var jiNengStr:String = "";
      
      public function Fly(newYN:Boolean = false)
      {
         super();
         this._stage = Main._this;
         addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         if(newYN)
         {
            return;
         }
         this.getData();
         addEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         if(GameData.gameLV == 6 && Main.gameNum.getValue() != 999)
         {
            this.scaleX = this.scaleY = 1.5;
         }
      }
      
      public static function getFlyNum(strf:String) : int
      {
         var num:int = 0;
         for(i in Fly.All)
         {
            if((Fly.All[i] as Fly)._name == strf)
            {
               num++;
            }
         }
         return num;
      }
      
      public function onADDED_TO_STAGE(e:* = null) : *
      {
         var i:int = 0;
         var name:String = null;
         var pGj:Number = NaN;
         var cwGJ:Number = NaN;
         var cw2Num:Number = NaN;
         var gameNumX:int = 0;
         removeEventListener(Event.ADDED_TO_STAGE,this.onADDED_TO_STAGE);
         for(i = 0; i < All.length; i++)
         {
            if(All[i] == this)
            {
               return;
            }
         }
         All[All.length] = this;
         var parentMC:MovieClip = this;
         while(parentMC != this._stage)
         {
            if(parentMC is Skin_WuQi && parentMC.parent is Player)
            {
               this.who = parentMC.parent;
               this.RL = (this.who as Player).RL;
               this.硬直 = this.who.skin.硬直;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.gongJi_hp_MAX = this.who.skin.hpMax;
               this.attTimes = this.who.skin.attTimes;
               this.runX = this.who.skin.runX;
               this.runY = this.who.skin.runY;
               this.runTime = this.who.skin.runTime;
               this.jiNengStr = parentMC.currentLabel;
               if((this.who as Player).jnGoYn)
               {
                  this.jnGoYn = true;
               }
               this.otherXXX_2();
               break;
            }
            if(parentMC is Fly && parentMC.parent is Fly)
            {
               this.who = parentMC.parent.who;
               this.硬直 = parentMC.parent.硬直;
               this.gongJi_hp = parentMC.parent.gongJi_hp;
               this.attTimes = parentMC.parent.attTimes;
               this.runX = parentMC.parent.runX;
               this.runY = parentMC.parent.runY;
               this.XXX = parentMC.parent.x;
               this.YYY = parentMC.parent.y;
               this.runTime = parentMC.parent.runTime;
               break;
            }
            if(parentMC is Skin_WuQi && parentMC.parent is Player2)
            {
               this.who = parentMC.parent;
               this.RL = (this.who as Player2).RL;
               this.硬直 = this.who.skin.硬直;
               this.gongJi_hp = this.who.skin.hpXX.getValue();
               this.attTimes = this.who.skin.attTimes;
               this.runX = this.who.skin.runX;
               this.runY = this.who.skin.runY;
               this.runTime = this.who.skin.runTime;
               break;
            }
            if(parentMC is EnemySkin && parentMC.parent is Enemy)
            {
               this.who = parentMC.parent;
               this.RL = (this.who as Enemy).RL;
               this.硬直 = this.who.skin.硬直;
               this.attTimes = this.who.skin.attTimes;
               this.gongJi_hp = (parentMC as EnemySkin).hpX;
               this.runX = (parentMC as EnemySkin).runX;
               this.runY = (parentMC as EnemySkin).runY;
               this.runTime = (parentMC as EnemySkin).runTime;
               this.type = (parentMC as EnemySkin).type;
               this.space = (parentMC as EnemySkin).space;
               this.totalTime = (parentMC as EnemySkin).totalTime;
               this.numValue = (parentMC as EnemySkin).numValue;
               break;
            }
            if(parentMC is ChongWu || parentMC is ChongWu2)
            {
               this.RL = parentMC.RL;
               this.who = parentMC;
               for(i in ChongWu.dataXml.宠物攻击)
               {
                  name = String(ChongWu.dataXml.宠物攻击[i].名称);
                  if(getQualifiedClassName(this) == name)
                  {
                     this.moveYN = String(ChongWu.dataXml.宠物攻击[i].独立).toString() == "true" ? true : false;
                     this.life = int(ChongWu.dataXml.宠物攻击[i].HP);
                     this.continuous = String(ChongWu.dataXml.宠物攻击[i].循环).toString() == "true" ? true : false;
                     this.cross = String(ChongWu.dataXml.宠物攻击[i].穿越).toString() == "true" ? true : false;
                     this.time = int(ChongWu.dataXml.宠物攻击[i].时间);
                     this.speedX = int(ChongWu.dataXml.宠物攻击[i].移动x);
                     this.speedY = int(ChongWu.dataXml.宠物攻击[i].移动y);
                     pGj = Number(parentMC.who.gongji.getValue());
                     cwGJ = Number(ChongWu.dataXml.宠物攻击[i].攻击HP);
                     cw2Num = 0;
                     if(parentMC is ChongWu2)
                     {
                        cw2Num += (parentMC as ChongWu2).gjUP;
                     }
                     this.gongJi_hp = pGj * (cwGJ + cw2Num) * (100 + Math.random() * 2) / 100;
                     if(parentMC is ChongWu2)
                     {
                        this.gongJi_hp = cwGJ + cw2Num;
                     }
                     gameNumX = int(Main.gameNum.getValue());
                     if(gameNumX >= 1 && gameNumX <= 9)
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[i].伤害限定);
                     }
                     else if(gameNumX >= 10 && gameNumX <= 16)
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[i].伤害限定2);
                     }
                     else if(gameNumX >= 51 && gameNumX <= 62)
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[i].伤害限定3);
                     }
                     else
                     {
                        this.gongJi_hp_MAX = int(ChongWu.dataXml.宠物攻击[i].伤害限定);
                     }
                     this.runArr = [int(ChongWu.dataXml.宠物攻击[i].震退),int(ChongWu.dataXml.宠物攻击[i].挑高),int(ChongWu.dataXml.宠物攻击[i].持续)];
                     break;
                  }
               }
               break;
            }
            parentMC = parentMC.parent as MovieClip;
         }
         if(this.moveYN)
         {
            this.y += this.who.y;
            if(this.RL)
            {
               scaleX *= -1;
               this.x = this.who.x - this.x;
            }
            else
            {
               scaleX *= 1;
               this.x = this.who.x + this.x;
            }
            if(this.name == "R_mc")
            {
               this.RL = true;
               this.x = this.who.x + 120;
               this.scaleX = -1;
            }
            if(this.name == "L_mc")
            {
               this.RL = false;
               this.x = this.who.x - 120;
               this.scaleX = 1;
            }
            if(!(this is Boss108_FlyXX))
            {
               Main.world.moveChild_Other.addChild(this);
            }
            this.otherXXXXX();
         }
      }
      
      public function otherXXX_2() : *
      {
         if(getQualifiedClassName(this) == "天煞剑下斩波")
         {
            this.runX = this.runY = this.runTime = 0;
         }
         else if(getQualifiedClassName(this) == "天煞拳位移火焰")
         {
            if(this.who is Player && (this.who as Player).quan11Time > 80)
            {
               (this.who as Player).quan11Time = 0;
            }
            else
            {
               this.visible = false;
               this.life = 0;
            }
         }
      }
      
      private function otherXXXXX() : *
      {
         var p:Player = null;
         var cc:ChongWu = null;
         trace("");
         if(getQualifiedClassName(this) == "猎犬火焰")
         {
            this.y = this.YYY + 50;
            this.x = this.XXX;
         }
         if(getQualifiedClassName(this) == "宠3毒雾")
         {
            this.y = 0;
            this.scaleX = 1;
            this.x = this.who.x;
         }
         else if(getQualifiedClassName(this) == "宠3猫头鹰")
         {
            this.y = 500;
            this.x = this.who.x + Math.random() * 800 - 400;
         }
         else if(getQualifiedClassName(this) == "火焰之路" || getQualifiedClassName(this) == "寒冰之路")
         {
            Main.world.moveChild_Back.addChild(this);
         }
         else if(getQualifiedClassName(this) == "Cw29_Fly2")
         {
            p = this.who.who;
            this.RL = p.RL;
            if(p.RL)
            {
               this.x = p.x;
               this.scaleX = 1;
            }
            else
            {
               this.x = p.x;
               this.scaleX = -1;
            }
         }
         else if(getQualifiedClassName(this) == "Cw29_Fly4")
         {
            cc = this.who;
            this.x = cc.gongJiEnemy.x;
            this.y = cc.gongJiEnemy.y;
         }
      }
      
      public function getData() : *
      {
         var nameXX:* = getQualifiedClassName(this);
         this._name = nameXX;
         if(Fly.FlyData2[nameXX])
         {
            this.life = Fly.FlyData2[nameXX][0];
            this.continuous = Fly.FlyData2[nameXX][1];
            this.cross = Fly.FlyData2[nameXX][2];
            this.time = Fly.FlyData2[nameXX][3];
            this.speedX = Fly.FlyData2[nameXX][4];
            this.speedY = Fly.FlyData2[nameXX][5];
         }
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         if(getQualifiedClassName(this) == "Cw24_Fly2" || getQualifiedClassName(this) == "Cw24_Fly3")
         {
            this.x = this.who.x;
            this.y = this.who.y;
         }
         if(this.who is Player2 && (this.who as Player2).hp.getValue() == 0)
         {
            this.Dead();
            return;
         }
         if(this.over && this.currentLabel != "结束")
         {
            this.Dead();
            return;
         }
         if(!this.over && this.life == 0 && this.isCleanOut)
         {
            this.life = -1;
            gotoAndPlay("结束");
            this.continuous = false;
            this.over = true;
         }
         if(!this.over && this.time != -1)
         {
            --this.time;
            if(this.time == -1)
            {
               this.time = -1;
               gotoAndPlay("结束");
               this.continuous = false;
               this.over = true;
            }
         }
         if(this.continuous && this.currentLabel != "运行")
         {
            gotoAndPlay("运行");
         }
         else if(!this.continuous && this.currentLabel != "运行")
         {
            this.over = true;
         }
         if(this.over && this.currentLabel == "结束" && currentFrame == totalFrames)
         {
            this.over2 = true;
         }
         if(!this.over)
         {
            this.Move();
         }
         this.otherXX();
      }
      
      public function otherXX() : *
      {
         if(this._name == "转职2技能飞镖2" && this.over)
         {
            this.runX = this.runY = this.runTime = 0;
         }
         if(this._name == "暗器风暴3" && Boolean(this.who))
         {
            this.x = this.who.x;
            this.y = this.who.y;
         }
      }
      
      public function Move() : *
      {
         this.y += this.speedY;
         if(this.RL)
         {
            this.x += this.speedX;
         }
         else
         {
            this.x -= this.speedX;
         }
         if(this.x > Main.world._width + 2000 || this.x < -2000)
         {
            this.life = 0;
         }
      }
      
      public function Dead() : *
      {
         stop();
         for(i in All)
         {
            if(All[i] == this)
            {
               All.splice(i,1);
            }
         }
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         if(this.who is Player2 || this.who is Player || this.who is Enemy || this.who is ChongWu || this.who is ChongWu2)
         {
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
         }
      }
      
      public function Dead2() : *
      {
         stop();
         removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function 震动(num:int = 4) : *
      {
         if(Main.world)
         {
            Main.world.Quake(num);
         }
      }
   }
}

