package src
{
   import fl.motion.*;
   import flash.display.*;
   import flash.events.*;
   import flash.filters.*;
   import flash.utils.*;
   import src.Skin.*;
   import src.tool.*;
   
   public class BuffEffect extends MovieClip
   {
      
      public static var AllBuffArr:Array = [];
      
      public static var isIce:Boolean = false;
      
      public var type:int;
      
      public var space:int;
      
      public var totalTime:int;
      
      public var numValue:int;
      
      private var effectMC:MovieClip;
      
      private var castMC:MovieClip;
      
      private var timeTemp:int = 0;
      
      private var tempNum:Number = 0;
      
      private var poisoning_C:Class = NewLoad.XiaoGuoData.getClass("Poisoning") as Class;
      
      private var iceing_C:Class = NewLoad.XiaoGuoData.getClass("Iceing") as Class;
      
      private var iceBroken_C:Class = NewLoad.XiaoGuoData.getClass("IceBroken") as Class;
      
      private var ghosting_C:Class = NewLoad.XiaoGuoData.getClass("Ghosting") as Class;
      
      private var ruanni_C:Class = NewLoad.XiaoGuoData.getClass("Ruanni") as Class;
      
      private var ruanniDead_C:Class = NewLoad.XiaoGuoData.getClass("RuanniDead") as Class;
      
      private var burn_C:Class = NewLoad.XiaoGuoData.getClass("Burn") as Class;
      
      private var mabi_C:Class = NewLoad.XiaoGuoData.getClass("Mabi") as Class;
      
      private var shandian_C:Class = NewLoad.XiaoGuoData.getClass("Shandian") as Class;
      
      private var zhongdu_C:Class = NewLoad.XiaoGuoData.getClass("ZhongDu") as Class;
      
      private var nianye_C:Class = NewLoad.XiaoGuoData.getClass("Nianye") as Class;
      
      private var jiansu_C:Class = NewLoad.XiaoGuoData.getClass("Jiansu") as Class;
      
      private var jiasu_C:Class = NewLoad.XiaoGuoData.getClass("加速buff") as Class;
      
      private var mozhao_C:Class = NewLoad.XiaoGuoData.getClass("魔爪标记") as Class;
      
      private var guangming_C:Class = NewLoad.XiaoGuoData.getClass("光明标记") as Class;
      
      private var poisoning:MovieClip = new this.poisoning_C();
      
      private var iceing:MovieClip = new this.iceing_C();
      
      private var iceBroken:MovieClip = new this.iceBroken_C();
      
      private var ghosting:MovieClip = new this.ghosting_C();
      
      private var ruanni:MovieClip = new this.ruanni_C();
      
      private var ruanniDead:MovieClip = new this.ruanniDead_C();
      
      private var burn:MovieClip = new this.burn_C();
      
      private var mabi:MovieClip = new this.mabi_C();
      
      private var shandian:MovieClip = new this.shandian_C();
      
      private var zhongdu:MovieClip = new this.zhongdu_C();
      
      private var nianye:MovieClip = new this.nianye_C();
      
      private var jiansu:MovieClip = new this.jiansu_C();
      
      private var jiasu:MovieClip = new this.jiasu_C();
      
      private var guangming:MovieClip = new this.guangming_C();
      
      private var mozhao:MovieClip = new this.mozhao_C();
      
      private var tempV:int = 0;
      
      public function BuffEffect(mc:MovieClip, toMc:MovieClip)
      {
         super();
         this.type = (mc as HitXX).type;
         this.space = (mc as HitXX).space;
         this.totalTime = (mc as HitXX).totalTime;
         this.numValue = (mc as HitXX).numValue;
         this.timeTemp = (mc as HitXX).space;
         this.castMC = (mc as HitXX).who;
         this.effectMC = toMc;
         if(this.type == 15)
         {
            if((this.effectMC as Player).debuff < 3)
            {
               ++(this.effectMC as Player).debuff;
            }
            return;
         }
         this.effectMC.addChild(this);
         this.x = 0;
         this.y = 0;
         this.zhongdu.x = this.x;
         this.zhongdu.y = this.y;
         this.poisoning.x = this.x;
         this.poisoning.y = this.y;
         this.iceing.x = this.x;
         this.iceing.y = this.y;
         this.iceBroken.x = this.x;
         this.iceBroken.y = this.y;
         this.ghosting.x = this.x;
         this.ghosting.y = this.y;
         this.ruanni.x = this.x;
         this.ruanni.y = this.y;
         this.ruanniDead.x = this.x;
         this.ruanniDead.y = this.y;
         this.shandian.x = this.x;
         this.shandian.y = this.y;
         this.burn.x = this.x;
         this.burn.y = this.y;
         this.jiansu.x = this.x;
         this.jiansu.y = this.y;
         if(this.type < 500)
         {
            addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
            AllBuffArr[AllBuffArr.length] = this;
         }
         else if(this.type >= 500)
         {
            if((this.effectMC as Player).cengshu.length == 0)
            {
               addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
               (this.effectMC as Player).cengshu[(this.effectMC as Player).cengshu.length] = this;
            }
            else if((this.effectMC as Player).cengshu.length >= 1)
            {
               for(i in (this.effectMC as Player).cengshu)
               {
                  if((this.effectMC as Player).cengshu[0].type == this.type)
                  {
                     (this.effectMC as Player).cengshu[0].totalTime = this.totalTime;
                  }
               }
            }
         }
      }
      
      public function onENTER_FRAME(e:Event) : *
      {
         if(this.effectMC is Player)
         {
            this.realEffect();
         }
      }
      
      internal function removeRuanniDead(event:Event) : *
      {
         if(event.target.currentFrame == MovieClip(event.target).totalFrames)
         {
            this.effectMC.removeChild(this.ruanniDead);
            event.target.removeEventListener(Event.ENTER_FRAME,this.removeRuanniDead);
         }
      }
      
      internal function removeIceBroken(event:Event) : *
      {
         if(event.target.currentFrame == MovieClip(event.target).totalFrames)
         {
            this.effectMC.removeChild(this.iceBroken);
            event.target.removeEventListener(Event.ENTER_FRAME,this.removeIceBroken);
         }
      }
      
      internal function bianXiao(e:Event) : *
      {
         this.effectMC.scaleX -= 0.05;
         this.effectMC.scaleY -= 0.05;
         if(this.effectMC.scaleX <= 0.5 && this.effectMC.scaleY <= 0.5)
         {
            removeEventListener(Event.ENTER_FRAME,this.bianXiao);
         }
      }
      
      internal function zhongXie(e:Event) : *
      {
         if((this.castMC as Enemy).skin.runType == "被打")
         {
            (this.effectMC as Player).skin.GoTo("被打",20,true);
         }
      }
      
      private function realEffect() : *
      {
         var hpX:* = undefined;
         var hpHP:int = 0;
         var classRef:Class = null;
         var i:* = undefined;
         var sp:int = 0;
         var bh_Matrix:ColorMatrix = null;
         var bh_Filter:ColorMatrixFilter = null;
         if((this.effectMC as Player).playerCW2)
         {
            return;
         }
         --this.totalTime;
         if(Boolean(this.effectMC) && (this.effectMC as Player).hp.getValue() <= 0)
         {
            this.dead();
         }
         if(this.type == 7)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if(AllBuffArr.length > 3)
                  {
                     isIce = true;
                     this.addChild(this.iceing);
                     (this.effectMC as Player).skin.GoTo("被打",81,true);
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
               if(AllBuffArr.length <= 0 && isIce == true)
               {
                  isIce = false;
                  this.effectMC.addChild(this.iceBroken);
                  this.iceBroken.gotoAndPlay(0);
                  this.iceBroken.addEventListener(Event.ENTER_FRAME,this.removeIceBroken);
               }
            }
         }
         else if(this.type == 6)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if(AllBuffArr.length > 0)
                  {
                     isIce = true;
                     this.addChild(this.iceing);
                     (this.effectMC as Player).skin.GoTo("被打",81,true);
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
               if(AllBuffArr.length <= 0 && isIce == true)
               {
                  isIce = false;
                  this.effectMC.addChild(this.iceBroken);
                  this.iceBroken.gotoAndPlay(0);
                  this.iceBroken.addEventListener(Event.ENTER_FRAME,this.removeIceBroken);
               }
            }
         }
         else if(this.type == 1)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  hpX = this.numValue;
                  if(hpX > 0)
                  {
                     hpHP = (this.effectMC as Player).hp.getValue() - hpX;
                     if(hpHP < 0)
                     {
                        (this.effectMC as Player).hp.setValue(0);
                     }
                     else
                     {
                        (this.effectMC as Player).hp.setValue(hpHP);
                     }
                  }
                  this.timeTemp = 0;
                  HPdown.Open(hpX,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.addChild(this.zhongdu);
               }
            }
            else
            {
               this.removeChild(this.zhongdu);
               this.dead();
            }
         }
         else if(this.type == 11)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  this.timeTemp = 0;
                  (this.effectMC as Player).skin.GoTo("被打",18,true);
                  this.addChild(this.mabi);
               }
            }
            else
            {
               this.removeChild(this.mabi);
               this.dead();
            }
         }
         else if(this.type == 5)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  hpX = this.numValue;
                  if(hpX > 0)
                  {
                     hpHP = (this.effectMC as Player).hp.getValue() - hpX;
                     if(hpHP < 0)
                     {
                        (this.effectMC as Player).hp.setValue(0);
                     }
                     else
                     {
                        (this.effectMC as Player).hp.setValue(hpHP);
                     }
                  }
                  this.timeTemp = 0;
                  HPdown.Open(hpX,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.addChild(this.burn);
               }
            }
            else
            {
               this.removeChild(this.burn);
               this.dead();
            }
         }
         else if(this.type == 504)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  hpX = this.numValue;
                  if(hpX > 0)
                  {
                     hpHP = (this.effectMC as Player).hp.getValue() - hpX;
                     if(hpHP < 0)
                     {
                        (this.effectMC as Player).hp.setValue(0);
                     }
                     else
                     {
                        (this.effectMC as Player).hp.setValue(hpHP);
                     }
                  }
                  this.timeTemp = 0;
                  HPdown.Open(hpX,this.effectMC.x,this.effectMC.y - this.effectMC.height);
                  this.addChild(this.shandian);
               }
            }
            else
            {
               this.removeChild(this.shandian);
               this.dead();
            }
         }
         else if(this.type == 10)
         {
            if(this.totalTime >= 0)
            {
               classRef = Enemy.EnemyArr[82].getClass("视野丢失") as Class;
               (this.effectMC as Player).skin_W.addChild(new classRef());
               this.type = 0;
            }
            else
            {
               this.dead();
            }
         }
         else if(this.type == 95)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  for(i in Fly.All)
                  {
                     if(Fly.All[i]._name == "视野丢失")
                     {
                        return;
                     }
                  }
                  classRef = Enemy.EnemyArr[29].getClass("视野丢失") as Class;
                  (this.effectMC as Player).skin_W.addChild(new classRef());
               }
            }
            else
            {
               this.dead();
            }
         }
         else if(this.type == 66)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).speedTemp.setValue(this.numValue);
                  this.addChild(this.jiasu);
                  this.timeTemp = 0;
               }
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 8)
         {
            if(this.totalTime >= 0)
            {
               (this.effectMC as Player).speedTemp.setValue(-5);
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 9)
         {
            if(this.totalTime >= 0)
            {
               if(this.tempV == 0)
               {
                  this.tempV = (this.effectMC as Player).mp.getValue();
               }
               (this.effectMC as Player).mp.setValue(1);
            }
            else
            {
               (this.effectMC as Player).mp.setValue(this.tempV);
               this.tempV = 0;
               this.dead();
            }
         }
         else if(this.type == 2)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).skin.GoTo("被打",72,true);
                  this.addEventListener(Event.ENTER_FRAME,this.bianXiao);
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.effectMC.scaleX = 1;
               this.effectMC.scaleY = 1;
               this.dead();
            }
         }
         else if(this.type == 3)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  this.addEventListener(Event.ENTER_FRAME,this.zhongXie);
                  this.timeTemp = 0;
                  this.addChild(this.ghosting);
               }
            }
            else
            {
               this.removeEventListener(Event.ENTER_FRAME,this.zhongXie);
               this.dead();
            }
         }
         else if(this.type == 4)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.castMC as Enemy).life.setValue(0);
                  (this.castMC as Enemy).Dead();
                  this.timeTemp = 0;
                  this.addChild(this.ruanni);
               }
            }
            else
            {
               this.effectMC.addChild(this.ruanniDead);
               this.ruanniDead.gotoAndPlay(0);
               this.ruanniDead.addEventListener(Event.ENTER_FRAME,this.removeRuanniDead);
               this.dead();
            }
         }
         else if(this.type == 100)
         {
            if(this.totalTime >= 0)
            {
               (this.effectMC as Player).speedTemp.setValue(4);
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 23)
         {
            if(this.totalTime == 0)
            {
               ++Boss83.hongNUM;
            }
         }
         else if(this.type == 24)
         {
            if(this.totalTime == 0)
            {
               ++Boss83.lanNUM;
            }
         }
         else if(this.type == 25)
         {
            if(this.totalTime >= 0)
            {
               if(this.tempV == 0)
               {
                  this.tempV = int((this.effectMC as Player).mp.getValue());
               }
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).mp.setValue(1);
               }
            }
            else
            {
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).mp.setValue(this.tempV);
               }
               this.tempV = 0;
               this.dead();
            }
         }
         else if(this.type == 26)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  if(this.tempV == 0)
                  {
                     this.tempV = int((this.effectMC as Player).hp.getValue());
                  }
                  if((this.effectMC as Player).hp.getValue() > 0)
                  {
                     (this.effectMC as Player).hp.setValue(100);
                  }
                  this.timeTemp = 0;
               }
               if((this.effectMC as Player).hp.getValue() <= 0)
               {
                  this.dead();
               }
            }
            else
            {
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).HpUp(this.tempV);
               }
               this.tempV = 0;
               this.dead();
            }
         }
         else if(this.type == 27)
         {
            if(this.totalTime >= 0)
            {
               if(this.tempV == 0)
               {
                  this.tempV = (this.effectMC as Player).fangyu.getValue();
               }
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).fangyu.setValue(1);
               }
            }
            else
            {
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).fangyu.setValue(this.tempV);
               }
               this.tempV = 0;
               this.dead();
            }
         }
         else if(this.type == 28)
         {
            if(this.totalTime >= 0)
            {
               if(this.tempV == 0)
               {
                  this.tempV = (this.effectMC as Player).gongji.getValue();
               }
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).gongji.setValue(1);
               }
            }
            else
            {
               if((this.effectMC as Player).hp.getValue() > 0)
               {
                  (this.effectMC as Player).gongji.setValue(this.tempV);
               }
               this.tempV = 0;
               this.dead();
            }
         }
         else if(this.type == 16)
         {
            if(this.totalTime >= 0)
            {
               (this.effectMC as Player).speedTemp.setValue(-6);
               if(this.timeTemp == this.space)
               {
                  this.addChild(this.jiansu);
                  this.timeTemp = 0;
               }
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 22)
         {
            if(this.totalTime >= 0)
            {
               sp = (this.effectMC as Player).walk_power.getValue() * 0.5;
               (this.effectMC as Player).speedTemp.setValue(-sp);
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).HpUp(10,2);
                  this.addChild(this.jiansu);
                  this.timeTemp = 0;
               }
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 521)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).HpUp(5,2);
                  this.addChild(this.guangming);
                  this.timeTemp = 0;
               }
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 522)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).skin.GoTo("被打",72,true);
                  this.addChild(this.mozhao);
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
            }
         }
         else if(this.type == 18)
         {
            if(this.totalTime >= 0)
            {
               if(AllBuffArr.length == 1)
               {
                  (this.effectMC as Player).speedTemp.setValue(-2);
               }
               if(AllBuffArr.length == 2)
               {
                  (this.effectMC as Player).speedTemp.setValue(-4);
               }
               if(AllBuffArr.length == 3)
               {
                  (this.effectMC as Player).speedTemp.setValue(-6);
               }
               if(AllBuffArr.length == 4)
               {
                  (this.effectMC as Player).speedTemp.setValue(-8);
               }
               if(AllBuffArr.length >= 5)
               {
                  (this.effectMC as Player).speedTemp.setValue(-10);
               }
               if(this.timeTemp == this.space)
               {
                  this.addChild(this.jiansu);
                  this.timeTemp = 0;
               }
            }
            else
            {
               (this.effectMC as Player).speedTemp.setValue(0);
               this.dead();
            }
         }
         else if(this.type == 17)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).skin.GoTo("被打",81,true);
                  this.addChild(this.nianye);
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
            }
         }
         else if(this.type == 59)
         {
            if(this.totalTime >= 0)
            {
               if(this.timeTemp == this.space)
               {
                  (this.effectMC as Player).skin.GoTo("被打",54,true);
                  bh_Matrix = new ColorMatrix();
                  bh_Filter = new ColorMatrixFilter();
                  bh_Matrix.SetSaturationMatrix(0);
                  bh_Filter.matrix = bh_Matrix.GetFlatArray();
                  if((this.effectMC as Player).skin)
                  {
                     (this.effectMC as Player).skin.filters = [bh_Filter];
                  }
                  if((this.effectMC as Player).skin_Z)
                  {
                     (this.effectMC as Player).skin_Z.filters = [bh_Filter];
                  }
                  if((this.effectMC as Player).skin_Z2)
                  {
                     (this.effectMC as Player).skin_Z2.filters = [bh_Filter];
                  }
                  if((this.effectMC as Player).skin_Z3)
                  {
                     (this.effectMC as Player).skin_Z3.filters = [bh_Filter];
                  }
                  if((this.effectMC as Player).skin_W)
                  {
                     (this.effectMC as Player).skin_W.filters = [bh_Filter];
                  }
                  this.timeTemp = 0;
               }
            }
            else
            {
               this.dead();
            }
         }
         ++this.timeTemp;
      }
      
      private function dead() : *
      {
         var i:int = 0;
         if(this.type == 59)
         {
            if((this.effectMC as Player).skin)
            {
               (this.effectMC as Player).skin.filters = [];
            }
            if((this.effectMC as Player).skin_Z)
            {
               (this.effectMC as Player).skin_Z.filters = [];
            }
            if((this.effectMC as Player).skin_Z2)
            {
               (this.effectMC as Player).skin_Z2.filters = [];
            }
            if((this.effectMC as Player).skin_Z3)
            {
               (this.effectMC as Player).skin_Z3.filters = [];
            }
            if((this.effectMC as Player).skin_W)
            {
               (this.effectMC as Player).skin_W.filters = [];
            }
         }
         this.removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         if(Boolean(this) && Boolean(this.parent))
         {
            this.parent.removeChild(this);
         }
         for(i = 0; i < AllBuffArr.length; i++)
         {
            if(AllBuffArr[i] == this)
            {
               AllBuffArr.splice(i,1);
            }
         }
         if(this.type == 521 || this.type == 18 || this.type == 22 || this.type == 16 || this.type == 100 || this.type == 8)
         {
            (this.effectMC as Player).speedTemp.setValue(0);
         }
         for(i = 0; i < (this.effectMC as Player).cengshu.length; i++)
         {
            if((this.effectMC as Player).cengshu[i] == this)
            {
               (this.effectMC as Player).cengshu.splice(i,1);
            }
         }
      }
   }
}

