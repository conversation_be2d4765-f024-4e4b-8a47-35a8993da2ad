package src.other
{
   import com.hotpoint.braveManIII.models.equip.Equip;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import src.*;
   
   public class Skill_guangDun extends MovieClip
   {
      
      public static var noTime:int;
      
      public var who:Player;
      
      public var gjTime:int;
      
      public function Skill_guangDun()
      {
         super();
      }
      
      public static function addToPlayer(p:Player) : *
      {
         var classX:Class = null;
         var rr:Number = Math.random() * 100;
         if(rr > 35 || noTime > 0)
         {
            if(rr > 35 && noTime <= 0)
            {
               noTime = 80;
            }
            return;
         }
         if(EquipYN(p))
         {
            classX = NewLoad.OtherData.getClass("圣光护壁") as Class;
            p.guangDun = new classX();
            p.guangDun.who = p;
            p.guangDun.init();
            Main.world.moveChild_Other.addChild(p.guangDun);
         }
      }
      
      public static function EquipYN(p:Player) : Boolean
      {
         var equipX:Equip = null;
         if(<PERSON>olean(p) && Boolean(p.guangDun))
         {
            p.guangDun.removeX();
         }
         if(Boolean(p) && Boolean(p.data.getEquipSlot().getEquipFromSlot(6)))
         {
            equipX = p.data.getEquipSlot().getEquipFromSlot(6);
            if(equipX && equipX.getFrame() == 485 && equipX.getRemainingTime() > 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function init() : *
      {
         this.gjTime = 8 * 27;
         this.addEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
      }
      
      public function removeX() : *
      {
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
         this.who.guangDun = null;
         this.removeEventListener(Event.ENTER_FRAME,this.onENTER_FRAME);
         this.gjTime = 0;
      }
      
      public function onENTER_FRAME(e:*) : *
      {
         --this.gjTime;
         if(this.gjTime <= 0)
         {
            this.removeX();
         }
         this.Move();
      }
      
      public function Move() : *
      {
         this.x = this.who.x;
         this.y = this.who.y;
      }
   }
}

