package src
{
   import com.hotpoint.braveManIII.repository.skill.*;
   import com.hotpoint.braveManIII.views.achPanel.*;
   import com.hotpoint.braveManIII.views.caiyaoPanel.*;
   import com.hotpoint.braveManIII.views.taskPanel.*;
   import flash.display.*;
   import flash.events.*;
   import flash.geom.*;
   import flash.text.*;
   import src.tool.*;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol2656")]
   public class WinShow extends MovieClip
   {
      
      public static var txt_1:int = 0;
      
      public static var txt_2:int = 0;
      
      public static var txt_3:int = 0;
      
      public static var txt_4:int = 0;
      
      public static var txt_5:int = 0;
      
      public var XX_txt:TextField;
      
      public var back_btn:SimpleButton;
      
      public var lianji_txt:TextField;
      
      public var num2_txt:TextField;
      
      public var num_txt:TextField;
      
      public var p_txt:TextField;
      
      public var re_btn:SimpleButton;
      
      public var time_txt:TextField;
      
      public function WinShow()
      {
         super();
         GameData.GuanKaXX();
         this.TextShow();
         Main._stage.addChild(this);
         Main._stage.frameRate = 0;
         var gNum:int = int(Main.gameNum.getValue());
         if(gNum == 2015 || gNum == 888 || gNum > 5000 && gNum < 5100)
         {
            this.re_btn.visible = false;
         }
         else
         {
            this.re_btn.visible = true;
         }
         this.re_btn.addEventListener(MouseEvent.CLICK,this.RePlay);
         this.back_btn.addEventListener(MouseEvent.CLICK,this.goHome);
         AchData.setTg();
         AchData.gkOk();
         TaskData.setTg();
         TaskData.isOk();
         JingLing.QingChuLengQue();
      }
      
      public static function All_0() : *
      {
         txt_1 = txt_2 = txt_3 = txt_4 = txt_5 = 0;
      }
      
      public static function LianJi(num:int) : *
      {
         if(num > txt_2)
         {
            txt_2 = num;
         }
      }
      
      private function TextShow() : *
      {
         if(Main.gameNum.getValue() == SixOne_Interface.guankaNum && SixOne_Interface.state2021.getValue() == 2)
         {
            SixOne_Interface.state2021.setValue(3);
            SixOne_Interface.okTimes2021.setValue(SixOne_Interface.okTimes2021.getValue() + 1);
         }
         this.time_txt.text = this.TimeNum(txt_1);
         this.lianji_txt.text = "" + txt_2;
         this.num_txt.text = "" + txt_3;
         this.num2_txt.text = "" + txt_4;
         this.p_txt.text = "" + txt_5;
         var lv:String = " / 1P等级" + Main.player_1.data.getLevel();
         if(Main.P1P2)
         {
            lv += "/ 2P等级" + Main.player_2.data.getLevel();
         }
         var GameNumXX:String = " / 关卡:" + Main.gameNum.getValue();
         if(Main.gameNum.getValue() > 100 && Main.gameNum.getValue() < 200)
         {
            GameNumXX = " / 副本:" + (Main.gameNum.getValue() - 100);
         }
         else if(Main.gameNum.getValue() == 17)
         {
            GameNumXX = " / 挑战关:" + (Main.gameNum.getValue() - 16);
         }
         else if(Main.gameNum.getValue() > 17 && Main.gameNum.getValue() < 50)
         {
            GameNumXX = " / 星灵擂台:" + (Main.gameNum.getValue() - 17);
         }
         else if(Main.gameNum.getValue() > 50 && Main.gameNum.getValue() < 81)
         {
            GameNumXX = " / 海底:" + (Main.gameNum.getValue() - 50);
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            GameNumXX = " / 海底副本:" + (Main.gameNum.getValue() - 80);
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            GameNumXX = " / 药园";
         }
         else if(GameData.gameLV == 6)
         {
            GameNumXX = " / 公会boss";
         }
         else
         {
            GameNumXX = " / 关卡:" + Main.gameNum.getValue();
         }
         var 难度:String = "";
         if(GameData.gameLV == 1)
         {
            难度 = " / 难度★";
         }
         else if(GameData.gameLV == 2)
         {
            难度 = " / 难度★★";
         }
         else if(GameData.gameLV == 3)
         {
            难度 = " / 难度★★★";
         }
         else if(GameData.gameLV == 4)
         {
            难度 = " / 难度★★★★";
         }
         else if(GameData.gameLV == 5)
         {
            难度 = " / 难度★★★★★";
         }
         else
         {
            难度 = " / 难度??";
         }
         this.XX_txt.text = "版本" + Main.varX / 100 + lv + GameNumXX + 难度;
      }
      
      private function TimeNum(num:int) : String
      {
         num /= 27;
         var t1:int = num / 3600;
         var t2:int = (num - t1 * 3600) / 60;
         var t3:int = num - t1 * 3600 - t2 * 60;
         return t1 + ":" + t2 + ":" + t3;
      }
      
      private function goHome(e:* = null) : *
      {
         GongHuiRenWu.gameOK(Main.gameNum.getValue(),GameData.gameLV);
         Main._stage.frameRate = 27;
         Main.gameNum.setValue(0);
         Main.gameNum2.setValue(0);
         Load.loadGameNum = 1;
         Load.loadGameNum2 = 1;
         Player.一起信春哥();
         removeEventListener(MouseEvent.CLICK,this.goHome);
         removeEventListener(MouseEvent.CLICK,this.RePlay);
         Main._this.Loading();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         this.parent.removeChild(this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         noFuHuo = false;
      }
      
      private function RePlay(e:*) : *
      {
         GongHuiRenWu.gameOK(Main.gameNum.getValue(),GameData.gameLV);
         Player.skillYAO = 0;
         Main._stage.frameRate = 27;
         Main.gameNum2.setValue(1);
         Player.一起信春哥();
         GameData.deadMcYN = false;
         GameData.deadTime = 5;
         Main.allClosePanel();
         this.parent.removeChild(this);
         WinShow.All_0();
         PaiHang_Data.All_0();
         Main.Save();
         if(Main.gameNum.getValue() >= 18 && Main.gameNum.getValue() < 50)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.sel_GuanKaXX_Open();
         }
         else if(Main.gameNum.getValue() == 17)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 1000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 2000)
         {
            SelMap.Open(0,0,3,2);
            SelMap.selMapX.Sel_nanDu_mc4.Open();
         }
         else if(Main.gameNum.getValue() == 3000)
         {
            Main.gameNum.setValue(0);
            Main.gameNum2.setValue(0);
            Main._this.GameStart();
            CaiYaoPanel.open();
         }
         else if(Main.gameNum.getValue() > 80 && Main.gameNum.getValue() < 100)
         {
            SelMap.Open(0,0,3,3);
            SelMap.selMapX.Sel_nanDu_mc3.Open();
         }
         else
         {
            removeEventListener(MouseEvent.CLICK,this.goHome);
            removeEventListener(MouseEvent.CLICK,this.RePlay);
            Main._this.GameStart();
         }
         noFuHuo = false;
      }
   }
}

